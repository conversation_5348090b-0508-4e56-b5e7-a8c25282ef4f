import{T as e,p as t,s,U as i}from"./index-CBCsGYoT.js";import{_ as n}from"./_plugin-vue_export-helper.BCo6x5W8.js";const a=n({data:()=>({url:"",page:null}),onLoad(e){this.billingSubmitFn(),this.setmeal=e.expiredType,this.knowledgeIds=e.List.split(",")},methods:{billingSubmitFn(){try{let n=this;e({success(e){n.$api.billingSubmit({method:"post",data:{jsCode:e.code,businessCode:"vip",item:{industryKnowledgeIds:n.knowledgeIds,investmentKnowledgeIds:[],level:1,expiredType:n.setmeal},method:1}}).then((e=>{this.submitLoading=!1,wx.requestPayment({timeStamp:e.result.timeStamp,nonceStr:e.result.nonceStr,package:e.result.packageStr,signType:"RSA",paySign:e.result.paySign,success(e){t({title:"支付成功",icon:"none",duration:2e3}),s("isrefreshMember",!0),setTimeout((()=>{i({delta:1})}),1e3)},fail(e){this.submitLoading=!1,t({title:"您已取消支付",icon:"none"}),setTimeout((()=>{i({delta:1})}),0)}})}))}})}catch(n){}}}},[["render",function(e,t,s,i,n,a){return null}]]);export{a as default};
