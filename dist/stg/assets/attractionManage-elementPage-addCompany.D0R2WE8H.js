import{p as e,z as s,A as a,a as i,f as t,w as r,F as o,i as l,o as n,B as d,H as u,I as p,g as c,t as m,b as h,c as f,S as b}from"./index-HcMwrp5e.js";import{_ as v}from"./page-meta.X-Lr6csD.js";import{r as I}from"./uni-app.es.DFp0WTX7.js";import{_ as g}from"./uni-easyinput.CAhWtyu2.js";import{_ as C,a as S}from"./uni-forms.i1XM9iHP.js";import{t as A}from"./index.B6Rbewql.js";import{a as y}from"./alertPop.EfFZ3jW0.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.B5Z3RbO2.js";import"./returnFn.BYkANsDr.js";import"./uni-popup.BdZPMDVN.js";import"./uni-transition.CHOJlBbg.js";const _=N({components:{tabBar:A,alertPop:y},data:()=>({baseInfo:{clueSource:"2",businessScope:"",nationalStandardIndustry:"",enterpriseAddress:"",mobile:"",introduction:"",enterpriseName:"",uniCode:""},AssociativeList:[],showAssociativeList:!1,enterpriseTotal:0,enterpriseId:"",forbiddenCode:!1,enterpriseTime:null,regionName:"",title:"添加企业",showMap:!1,delItem:{},rules:{mobile:{rules:[{required:!0,errorMessage:"请输入联系方式"}]},enterpriseName:{rules:[{required:!0,errorMessage:"请输入企业名称"}]},uniCode:{rules:[{required:!0,errorMessage:"请输入统一信用代码"}]},introduction:{rules:[{required:!0,errorMessage:"请输入企业简介"}]},enterpriseAddress:{rules:[{required:!0,errorMessage:"请输入注册地址"}]},nationalStandardIndustry:{rules:[{required:!0,errorMessage:"请输入所属行业"}]}}}),onLoad(e){},methods:{enterpriseNameBlur(){this.AssociativeList.length>0?this.AssociativeList.forEach((e=>{e.enterpriseName===this.baseInfo.enterpriseName&&(this.baseInfo=e,this.baseInfo.uniCode=e.enterpriseUniCode||e.unifiedSocialCreditCode||"",this.forbiddenCode=!0,this.enterpriseId=e.id)})):(this.AssociativeList=[],this.showAssociativeList=!1)},scrolltolowerFn(){this.pages>this.pageNum&&(this.pageNum++,this.requestSearch(this.keyText,this.pageNum))},enterpriseNameFocus(){this.baseInfo.enterpriseName&&(this.showAssociativeList=!0)},clearAssociativeList(){this.showAssociativeList=!1},SelectEn(e){this.showAssociativeList=!1,this.forbiddenCode=!0,this.baseInfo=e,this.baseInfo.uniCode=e.unifiedSocialCreditCode,this.enterpriseId=e.id},changeenterpriseName(e){this.enterpriseTotal=0,this.forbiddenCode=!1,this.baseInfo.uniCode="",this.enterpriseId="",null!=this.enterpriseTime&&clearTimeout(this.enterpriseTime),this.enterpriseTime=setTimeout((async()=>{e?(this.keyText=e,this.pageNum=1,this.pages=0,this.requestSearch(e,1)):(this.pageNum=1,this.pages=0,this.AssociativeList=[],this.enterpriseTotal=0)}),400)},requestSearch(e,s){this.$api.newEnterpriseSearchAPI({data:{keyword:e,pageNum:s,pageSize:10},method:"POST"}).then((e=>{1==s&&(this.enterpriseTotal=0,this.AssociativeList=[]),this.AssociativeList=this.AssociativeList.concat(e.result.records),this.enterpriseTotal=e.result.total,this.pages=e.result.pages,this.baseInfo.enterpriseName?this.showAssociativeList=!0:this.showAssociativeList=!1}))},submit(s){let a=this;this.$refs[s].validate().then((()=>{let s=a.baseInfo;s.clueSource="2",a.$api.addEnterpriseAPI_investment({data:s,method:"post"}).then((s=>{"SUCCESS"==s.code?(s.result?e({title:"添加成功！系统已自动添加企业相关信息",icon:"none"}):e({title:"添加企业成功！",icon:"none"}),setTimeout((()=>{var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{type:"refresh"},path:"goBack"}))}),1600)):a.$refs.alertPop.opens({title:s.msg})}))}))}}},[["render",function(e,A,y,N,_,w){const L=I(s("page-meta"),v),V=I(s("uni-easyinput"),g),T=l,q=b,k=I(s("uni-forms-item"),C),j=I(s("uni-forms"),S),x=a("alertPop");return n(),i(o,null,[t(L,{"page-style":"background-color: transparent"}),t(T,{class:"container"},{default:r((()=>[t(q,{class:"example","scroll-y":""},{default:r((()=>[t(j,{ref:"valiForm",rules:_.rules,"model-value":_.baseInfo,"label-position":"top","label-width":"100"},{default:r((()=>[t(k,{required:"",label:"企业名称",name:"enterpriseName"},{default:r((()=>[d("div",{style:{position:"relative"}},[t(V,{modelValue:_.baseInfo.enterpriseName,"onUpdate:modelValue":A[0]||(A[0]=e=>_.baseInfo.enterpriseName=e),trim:"all",clearable:!1,placeholder:"请输入企业名称",onFocus:w.enterpriseNameFocus,onBlur:w.enterpriseNameBlur,onInput:w.changeenterpriseName},null,8,["modelValue","onFocus","onBlur","onInput"]),u(t(T,{class:"associativeList"},{default:r((()=>[t(T,{class:"enterpriseTotal"},{default:r((()=>[t(T,null,{default:r((()=>[c(" 搜索到"),d("span",{style:{color:"#3370ff"}},m(_.enterpriseTotal),1),c("条结果 ")])),_:1}),t(T,{class:"enterpriseBack",onClick:w.clearAssociativeList},{default:r((()=>[c(" 取消 ")])),_:1},8,["onClick"])])),_:1}),t(q,{class:"associativeList2","scroll-y":!0,onScrolltolower:w.scrolltolowerFn},{default:r((()=>[(n(!0),i(o,null,h(_.AssociativeList,((e,s)=>(n(),i("div",{key:s,class:"associativeItem",onClick:s=>w.SelectEn(e)},[d("div",{class:"name"},m(e.enterpriseName),1),d("div",{class:"man"},"法定代表人："+m(e.legalPerson),1),d("div",{class:"code"}," 统一社会信用代码："+m(e.unifiedSocialCreditCode),1)],8,["onClick"])))),128))])),_:1},8,["onScrolltolower"])])),_:1},512),[[p,_.showAssociativeList]])])])),_:1}),t(k,{required:"",label:"统一社会信用代码",name:"uniCode"},{default:r((()=>[_.forbiddenCode?(n(),i("div",{key:1,class:"enterpriseUniCode"},m(_.baseInfo.uniCode),1)):(n(),f(V,{key:0,modelValue:_.baseInfo.uniCode,"onUpdate:modelValue":A[1]||(A[1]=e=>_.baseInfo.uniCode=e),trim:"all",clearable:!1,placeholder:"请输入"},null,8,["modelValue"]))])),_:1}),t(k,{label:"企业简介",required:"",name:"introduction"},{default:r((()=>[t(V,{modelValue:_.baseInfo.introduction,"onUpdate:modelValue":A[2]||(A[2]=e=>_.baseInfo.introduction=e),type:"textarea",trim:"all",clearable:!1,placeholder:"请输入企业简介"},null,8,["modelValue"])])),_:1}),t(k,{label:"联系电话",required:"",name:"mobile"},{default:r((()=>[t(V,{modelValue:_.baseInfo.mobile,"onUpdate:modelValue":A[3]||(A[3]=e=>_.baseInfo.mobile=e),trim:"all",clearable:!1,placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1}),t(k,{label:"注册地址",required:"",name:"enterpriseAddress"},{default:r((()=>[t(V,{modelValue:_.baseInfo.enterpriseAddress,"onUpdate:modelValue":A[4]||(A[4]=e=>_.baseInfo.enterpriseAddress=e),trim:"all",clearable:!1,placeholder:"请输入注册地址"},null,8,["modelValue"])])),_:1}),t(k,{label:"所属行业",required:"",name:"nationalStandardIndustry"},{default:r((()=>[t(V,{modelValue:_.baseInfo.nationalStandardIndustry,"onUpdate:modelValue":A[5]||(A[5]=e=>_.baseInfo.nationalStandardIndustry=e),trim:"all",clearable:!1,placeholder:"请输入所属行业"},null,8,["modelValue"])])),_:1}),t(k,{label:"经营范围",name:"businessScope"},{default:r((()=>[t(V,{modelValue:_.baseInfo.businessScope,"onUpdate:modelValue":A[6]||(A[6]=e=>_.baseInfo.businessScope=e),trim:"all",clearable:!1,placeholder:"请输入经营范围"},null,8,["modelValue"])])),_:1}),d("div",{style:{"min-height":"88px"}})])),_:1},8,["rules","model-value"]),d("div",{class:"experience",type:"primary",onClick:A[7]||(A[7]=e=>w.submit("valiForm"))}," 确定 ")])),_:1}),t(x,{ref:"alertPop"},null,512)])),_:1})],64)}],["__scopeId","data-v-c53acbbc"]]);export{_ as default};
