import{p as e,o as a,c,w as t,f as i,B as s,a as n,b as l,e as o,g as r,t as h,F as m,d as p,i as d}from"./index-CBCsGYoT.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const u=k({props:{active:{type:Number,default:null},token:{type:String,default:null},cityName:{type:String,default:null},unrestricted:{type:Boolean,default:!1},BottomBlank:{type:Boolean,default:!1},appletTab:{type:Boolean,default:!1}},data:()=>({province:[],citys:[],area:[],type:1,check:{province:{name:""},citys:{name:""},area:{name:""}},param:{}}),methods:{clear(){this.citys=[],this.area=[],this.check={province:{name:""},citys:{name:""},area:{name:""}}},affirm(){if(this.unrestricted)this.$emit("affirm",this.check);else{if(!this.check.citys.code&&this.check.province.code&&82e4!=this.check.province.code&&81e4!=this.check.province.code&&71e4!=this.check.province.code)return e({title:"请选择市级或区级",icon:"none"});this.$emit("affirm",this.check)}},init(){0==this.province.length&&this.mapList(1)},mapList(e,a){let c={};if(this.cityName){let e=this.cityName.split("/");c.province=e[0],e.length>1&&(c.city=e[1]),e.length>2&&(c.area=e[2])}let{province:t,city:i,area:s}=c,n={type:e};a&&(n.parentId=a),this.$api.administrativeDivision({data:n,token:this.token}).then((a=>{1==e?(this.province=a.result,this.province.map((e=>(e.name_new=e.name.replace("自治区",""),e.name_new=e.name_new.replace("壮族",""),e.name_new=e.name_new.replace("回族",""),e.name_new=e.name_new.replace("维吾尔",""),e.name_new=e.name_new.replace("特别行政区",""),t==e.name&&(this.check.province.name=e.name,this.check.province.code=e.code,this.mapList(2,e.id)),e)))):2==e?(a.result.map((e=>{e.name==i&&(this.check.citys.name=e.name,this.check.citys.code=e.code,this.mapList(3,e.id))})),this.citys=a.result):3==e&&(a.result.map((e=>{e.name==s&&(this.check.area.name=e.name,this.check.area.code=e.code)})),this.area=a.result)}))},provinceEvent(e){this.mapList(2,e.id),this.check.province.name=e.name,this.check.province.code=e.code,this.area=[],this.check.citys.name="",this.check.citys.code="",this.check.area.name="",this.check.area.code=""},citysEvent(e){this.mapList(3,e.id),this.check.citys.name=e.name,this.check.citys.code=e.code,this.check.area.name="",this.check.area.code=""},areaEvent(e){this.check.area.name=e.name,this.check.area.code=e.code}},created(){0==this.province.length&&this.init()}},[["render",function(e,k,u,f,v,y){const _=d;return a(),c(_,{class:"region"},{default:t((()=>[i(_,{class:"head"},{default:t((()=>[s("span",{class:"txt"},"请选择")])),_:1}),i(_,{class:"region-con"},{default:t((()=>[i(_,{class:"region-list province"},{default:t((()=>[(a(!0),n(m,null,l(v.province,((e,s)=>(a(),c(_,{class:o(["item",v.check.province.name==e.name?"on":""]),onClick:a=>y.provinceEvent(e),key:s},{default:t((()=>[i(_,{class:"txt"},{default:t((()=>[r(h(e.name_new),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),i(_,{class:o(["region-list citys",v.check.province.name?"on":""])},{default:t((()=>[(a(!0),n(m,null,l(v.citys,((e,s)=>(a(),c(_,{class:o(["item",v.check.citys.name==e.name?"on":""]),onClick:a=>y.citysEvent(e),key:s},{default:t((()=>[i(_,{class:"txt"},{default:t((()=>[r(h(e.name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1},8,["class"]),i(_,{class:o(["region-list area",v.check.province.name?"on":""])},{default:t((()=>[(a(!0),n(m,null,l(v.area,((e,s)=>(a(),c(_,{class:o(["item",v.check.area.name==e.name?"on":""]),onClick:a=>y.areaEvent(e),key:s},{default:t((()=>[i(_,{class:"txt"},{default:t((()=>[r(h(e.name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1},8,["class"])])),_:1}),i(_,{class:"btnbox"},{default:t((()=>[i(_,{onClick:y.clear,class:"clear"},{default:t((()=>[r("重置")])),_:1},8,["onClick"]),i(_,{class:"affirm",onClick:y.affirm},{default:t((()=>[r("确认")])),_:1},8,["onClick"])])),_:1}),u.BottomBlank?(a(),n("div",{key:0,style:{height:"160rpx"}})):p("",!0)])),_:1})}],["__scopeId","data-v-d9dec681"]]);export{u as m};
