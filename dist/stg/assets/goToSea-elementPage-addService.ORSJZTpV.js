import{p as e,z as t,a,f as o,w as s,F as i,i as l,o as r,B as d}from"./index-HcMwrp5e.js";import{_ as n}from"./page-meta.X-Lr6csD.js";import{r as m}from"./uni-app.es.DFp0WTX7.js";import{_ as u}from"./uni-data-select.CGMq-mdz.js";import{_ as p,a as c}from"./uni-forms.i1XM9iHP.js";import{_ as h}from"./uni-easyinput.CAhWtyu2.js";import{t as I}from"./index.B6Rbewql.js";import{m as g}from"./mapList.BhjoS9MT.js";import{m as f}from"./mapList2.B3tGJOdO.js";import{_ as b}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-cloud.es.C7bj1nlK.js";import"./uni-icons.B5Z3RbO2.js";import"./returnFn.BYkANsDr.js";const v=b({components:{tabBar:I,mapList:g,mapList2:f},data:()=>({baseInfo:{catalogId:"",priceQuote:"",itemId:"",zoneCode:"",introduction:""},regionName:"",categoryList:[],serveList:[],provinceList:[],title:"添加服务",showMap:!1,delItem:{},rules:{catalogId:{rules:[{required:!0,errorMessage:"请选择服务类目"}]},itemId:{rules:[{required:!0,errorMessage:"请选择服务"}]},zoneCode:{rules:[{required:!0,errorMessage:"请选择服务区域"}]},priceQuote:{rules:[{required:!0,errorMessage:"请输入价格"},{pattern:new RegExp("^[+]?(0(?!\\d)\\.\\d{1,2}|[1-9]\\d*(\\.\\d{1,2})?)(?<!\\.00)$"),errorMessage:"价格必须大于0且最多保留两位小数"}]},introduction:{rules:[{required:!0,errorMessage:"请输入服务介绍"}]}}}),onLoad(e){this.getCategoryList(),e.item&&(this.delItem=JSON.parse(decodeURIComponent(e.item))),getQueryParam("item")&&(this.delItem=JSON.parse(decodeURIComponent(getQueryParam("item"))))},methods:{Itemecho(){this.baseInfo={catalogId:this.delItem.catalogId,priceQuote:this.delItem.priceQuote,itemId:this.delItem.itemId,zoneCode:this.delItem.zoneCode,introduction:this.delItem.introduction}},changeCatalogId(e){this.serveList=[],this.baseInfo.catalogId=e,this.baseInfo.itemId="",e&&this.$api.serviceItemListAPI({data:{catalogId:e},method:"POST"}).then((t=>{t.result.items.forEach((e=>{this.serveList.push({text:e.itemName,value:e.itemId})})),this.delItem.catalogId&&this.delItem.catalogId==e&&this.Itemecho()}))},getCategoryList(){this.$api.catalogListAPI({token:this.token,method:"POST"}).then((e=>{e.result.forEach((e=>{this.categoryList.push({text:e.catalogName,value:e.catalogId})})),this.echoInfo()}))},mapList(e){let t={country:e};this.$api.getAdminRegionListAPI({data:t}).then((e=>{e.result.forEach((e=>{this.provinceList.push({text:e.name,value:e.regionMd5})})),this.delItem.catalogId&&this.changeCatalogId(this.delItem.catalogId)}))},echoInfo(){this.$api.userMessage().then((e=>{const t=new Set(e.result.userCompletionInfoDO.serviceItemCodes);this.categoryList=this.categoryList.filter((e=>t.has(e.value.toString())||t.has(Number(e.value)))),this.mapList(e.result.userCompletionInfoDO.serviceCountryName)}))},pop(){this.$refs.popup.open("bottom")},pop2(){this.$refs.popup2.open("bottom")},submit(t){let a=this;this.$refs[t].validate().then((t=>{let o=a.baseInfo;a.delItem.catalogId?(o.marketId=a.delItem.marketId,a.$api.marketModifyAPI({data:o,method:"post"}).then((t=>{"SUCCESS"==t.code&&(e({title:"编辑服务成功！",icon:"none"}),setTimeout((()=>{var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{type:1},path:"goBack"}))}),500))}))):a.$api.addMarketAPI({data:o,method:"post"}).then((t=>{"SUCCESS"==t.code&&(e({title:"新增服务成功！",icon:"none"}),setTimeout((()=>{var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{type:1},path:"goBack"}))}),500))}))}))}}},[["render",function(e,I,g,f,b,v){const y=m(t("page-meta"),n),C=m(t("uni-data-select"),u),L=m(t("uni-forms-item"),p),V=m(t("uni-easyinput"),h),_=m(t("uni-forms"),c),w=l;return r(),a(i,null,[o(y,{"page-style":"background-color: transparent"}),o(w,{class:"container"},{default:s((()=>[o(w,{class:"example"},{default:s((()=>[o(_,{ref:"valiForm",rules:b.rules,modelValue:b.baseInfo,"label-position":"top","label-width":"100"},{default:s((()=>[o(L,{label:"服务类目",required:"",name:"catalogId"},{default:s((()=>[o(C,{modelValue:b.baseInfo.catalogId,"onUpdate:modelValue":I[0]||(I[0]=e=>b.baseInfo.catalogId=e),onChange:v.changeCatalogId,localdata:b.categoryList},null,8,["modelValue","onChange","localdata"])])),_:1}),o(L,{label:"服务",required:"",name:"itemId"},{default:s((()=>[o(C,{modelValue:b.baseInfo.itemId,"onUpdate:modelValue":I[1]||(I[1]=e=>b.baseInfo.itemId=e),emptyTips:"暂无数据",localdata:b.serveList},null,8,["modelValue","localdata"])])),_:1}),o(L,{label:"服务区域",required:"",name:"zoneCode"},{default:s((()=>[o(C,{modelValue:b.baseInfo.zoneCode,"onUpdate:modelValue":I[2]||(I[2]=e=>b.baseInfo.zoneCode=e),localdata:b.provinceList},null,8,["modelValue","localdata"])])),_:1}),o(L,{label:"价格",required:"",name:"priceQuote"},{default:s((()=>[o(V,{trim:"all",clearable:!1,modelValue:b.baseInfo.priceQuote,"onUpdate:modelValue":I[3]||(I[3]=e=>b.baseInfo.priceQuote=e),placeholder:"请输入价格（元）"},null,8,["modelValue"])])),_:1}),o(L,{label:"服务介绍",required:"",name:"introduction"},{default:s((()=>[o(V,{type:"textarea",trim:"all",clearable:!1,modelValue:b.baseInfo.introduction,"onUpdate:modelValue":I[4]||(I[4]=e=>b.baseInfo.introduction=e),placeholder:"请输入服务介绍"},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","modelValue"]),d("div",{class:"experience",type:"primary",onClick:I[5]||(I[5]=e=>v.submit("valiForm"))}," 确定 ")])),_:1})])),_:1})],64)}],["__scopeId","data-v-b6cfbc65"]]);export{v as default};
