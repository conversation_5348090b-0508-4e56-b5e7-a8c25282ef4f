import{k as t,l as a,y as s,o as e,c as o,w as i,f as n,n as l,d as r,g as c,t as d,C as p,i as u}from"./index-CBCsGYoT.js";import{_ as f}from"./returnFn.BYkANsDr.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const h=g({props:{title:{type:String,default:""},showicon:{type:Boolean,default:!0},peInfo:{type:Boolean,default:!1}},data:()=>({statusBarHeight:0}),created(){this.statusBarHeight=t().statusBarHeight,this.statusBarHeight=a("top")?a("top"):40},methods:{goBack(){var t;this.peInfo&&s({key:"teamCode"}),null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"goBack"}))}}},[["render",function(t,a,s,g,h,B){const _=p,m=u;return e(),o(m,{class:"bar-con"},{default:i((()=>[n(m,{class:"tabBarCon"},{default:i((()=>[n(m,{id:"tabBar",class:"tabBar"},{default:i((()=>[n(m,{class:"nav",style:l(`\n\t\t\t\t    margin-top: ${h.statusBarHeight}px;\n\t\t\t\t`)},{default:i((()=>[s.showicon?(e(),o(m,{key:0,class:"black",onClick:a[0]||(a[0]=t=>B.goBack())},{default:i((()=>[n(_,{class:"returnFn",src:f})])),_:1})):r("",!0),n(m,{class:"nav-title"},{default:i((()=>[c(d(s.title),1)])),_:1})])),_:1},8,["style"])])),_:1})])),_:1}),n(_,{src:"https://static.idicc.cn/cdn/aiChat/applet/head.png",class:"headimg",mode:""})])),_:1})}],["__scopeId","data-v-3bd12db3"]]);export{h as t};
