import{l as e,U as t,p as a,q as s,z as i,A as o,c as r,w as n,i as l,o as c,f as d,B as u,a as p,b as m,g,t as f,d as h,F as v,C as _,S as y}from"./index-CBCsGYoT.js";import{_ as S}from"./page-meta.BCoSkkXs.js";import{r as w}from"./uni-app.es.CZb2JZWI.js";import{r as C}from"./regionCut.CGH9u9zb.js";import{t as I}from"./index.D5F8338M.js";import{g as k}from"./utils.61Hi-B7M.js";import{_ as L}from"./right.D4iebSn6.js";import{_ as b}from"./fuwuleixing.C6AoCp6M.js";import{_ as j}from"./ic_sea_location.iElRY131.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-load-more.BMOBclrH.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-cloud.es.Dc96wm0-.js";import"./returnFn.BYkANsDr.js";const P=N({components:{tabBar:I,regionCut:C},data:()=>({enterpriseList:[],smartServiceId:"",zoneCode:"",statusBarHeight:0,showNodata:!1}),onLoad(e){this.smartServiceId=e.smartServiceId,k("smartServiceId")&&(this.smartServiceId=k("smartServiceId")),this.getList()},methods:{getList(){if(this.isLoading)return;this.isLoading=!0;let t={itemId:this.smartServiceId,zoneCode:this.zoneCode?this.zoneCode:null,country:e("defaultName")};this.$api.getOverseasServicerListAPI({data:t,method:"POST"}).then((e=>{this.enterpriseList=e.result.records})).finally((()=>{this.isLoading=!1,this.showNodata=!0}))},addPageList(){},updataPage(e){this.zoneCode=e,this.getList()},goBack(){t({delta:1})},goServicerDetail(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{id:e.userId},path:"servicerDetail"}))},goDetails(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{type:e.itemId,name:e.itemName,servicer:e.marketId},path:"serviceInfo"}))},goPay(e){console.log(e),this.$api.overseasSubmitApi({data:{serviceMarketId:e.marketId},method:"POST"}).then((e=>{"SUCCESS"===e.code?this.payment(e.result):a({title:e.msg,icon:"none"})}))},payment(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"toWechatPay",value:{businessCode:"smartService",item:{smartServiceId:e}}}))},paySuccess(){s({url:"/goToSea/mineOrder"})}}},[["render",function(e,t,a,s,C,I){const k=w(i("page-meta"),S),N=o("regionCut"),P=l,x=_,B=y;return c(),r(P,null,{default:n((()=>[d(k,{"page-style":"background-color:transparent"}),u("div",{class:"contentBox"},[d(P,{class:"titleBox"},{default:n((()=>[d(N,{onUpdataPage:I.updataPage,ref:"regionCut",type:1},null,8,["onUpdataPage"])])),_:1}),0!=C.enterpriseList.length?(c(),r(B,{key:0,class:"listBox","scroll-y":"true","enable-flex":"true"},{default:n((()=>[d(P,{class:"list"},{default:n((()=>[(c(!0),p(v,null,m(C.enterpriseList,((e,t)=>(c(),r(P,{class:"card",key:t},{default:n((()=>[d(P,{class:"cardTitle",onClick:t=>I.goServicerDetail(e)},{default:n((()=>[d(P,{class:"title"},{default:n((()=>[g(f(e.userName),1),d(x,{src:L,class:"righticon"})])),_:2},1024)])),_:2},1032,["onClick"]),d(P,{class:"cardHeader"},{default:n((()=>[d(x,{src:b,class:"icon2"}),d(P,{class:"cardContent"},{default:n((()=>[d(P,{class:"title"},{default:n((()=>[g(f(e.itemName),1)])),_:2},1024),d(P,{class:"price"},{default:n((()=>[u("span",{class:"priceLogo"},"￥"),g(f(e.priceQuote),1)])),_:2},1024),e.introduction?(c(),r(P,{key:0,class:"cardBody"},{default:n((()=>[g(f(e.introduction),1)])),_:2},1024)):h("",!0)])),_:2},1024)])),_:2},1024),d(P,{class:"cardFooter"},{default:n((()=>[d(P,{class:"location"},{default:n((()=>[e.province?(c(),r(x,{key:0,src:j,class:"locationIcon"})):h("",!0),u("span",null,f(e.province?e.province:" "),1)])),_:2},1024),d(P,{class:"btns"},{default:n((()=>[d(P,{class:"btn1",onClick:t=>I.goDetails(e)},{default:n((()=>[g("查看详情")])),_:2},1032,["onClick"]),d(P,{class:"btn2",onClick:t=>I.goPay(e)},{default:n((()=>[g("立即购买")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),u("div",{style:{"padding-bottom":"60rpx"}})])),_:1})):h("",!0),0==C.enterpriseList.length&&C.showNodata?(c(),p("div",{key:1,class:"noDataView"},[d(x,{class:"noDataImg",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),u("span",{class:"noDataText"},"暂无内容")])):h("",!0)])])),_:1})}],["__scopeId","data-v-c41b9eec"]]);export{P as default};
