import{_ as a}from"./page-meta.BCoSkkXs.js";import{z as s,A as t,a as e,f as i,B as o,c as p,F as c,o as n,C as r}from"./index-CBCsGYoT.js";import{r as d}from"./uni-app.es.CZb2JZWI.js";import{t as l}from"./index.D5F8338M.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const h=m({components:{tabBar:l},data:()=>({type:1}),onLoad(a){a.type&&(this.type=a.type)},methods:{}},[["render",function(l,m,h,u,g,y){const x=d(s("page-meta"),a),f=t("tabBar"),v=r;return n(),e(c,null,[i(x,{"page-style":"background-color: #FAFCFF"}),o("div",null,[i(f,{title:1==g.type?"加入社群":"联系客服"},null,8,["title"]),o("div",{class:"box"},[o("div",{class:"top"},[1==g.type?(n(),p(v,{key:0,src:"https://static.idicc.cn/cdn/aiChat/applet/xiaoAI1.png",class:"showHeadimg",mode:""})):(n(),p(v,{key:1,src:"https://static.idicc.cn/cdn/aiChat/applet/xiaoAI2.png",class:"showHeadimg",mode:""}))]),o("div",{class:"qtCode"},[o("span",{class:"text"},"—— 扫码添加 ——"),i(v,{"show-menu-by-longpress":"true",src:"https://static.idicc.cn/cdn/aiChat/applet/newServiceCode2.png",class:"qtCodeimg"}),o("div",{class:"explain"},"扫描二维码 添加小艾客服")])])])],64)}],["__scopeId","data-v-302e015d"]]);export{h as default};
