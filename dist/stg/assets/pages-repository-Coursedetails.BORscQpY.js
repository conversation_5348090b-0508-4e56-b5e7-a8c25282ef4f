import{l as s,T as e,p as t,z as i,A as l,a,f as n,B as c,g as o,t as r,n as d,d as h,F as u,b as p,w as f,C as m,o as v,c as g}from"./index-CBCsGYoT.js";import{_ as F}from"./page-meta.BCoSkkXs.js";import{r as y}from"./uni-app.es.CZb2JZWI.js";import{_ as I}from"./uni-popup.BLXBf1r-.js";import{t as S}from"./index.D5F8338M.js";import{_ as x}from"./iconYes.DXJzuKTR.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.Ckb0qY8x.js";import"./returnFn.BYkANsDr.js";const b=k({components:{tabBar:S},data:()=>({otoList:[{id:0,name:"线下"},{id:1,name:"线上"}],title:"课程详情",isBuy:"",del:{},box:[],onlineStatus:1,showOto:!1,whetherIos:!1,enterforInfo:{},selectList:[],selectListId:1,finalPrice:""}),onLoad(e){this.whetherIos=s("whetherIos"),this.getDel(e.id),this.isBuy=e.isBuy,this.onlineStatus=e.onlineStatus,this.selectListId=e.onlineStatus,1==this.onlineStatus?(this.selectListId,this.selectList=[{id:1,text:"线上参与"}]):0==this.onlineStatus?(this.selectListId,this.selectList=[{id:0,text:"线下参与"}]):(this.selectListId,this.selectList=[{id:1,text:"线上参与"},{id:0,text:"线下参与"}]),2==this.onlineStatus&&(this.showOto=!0,this.onlineStatus=1)},methods:{canle(){this.selectListId=1,this.$refs.enterforPopup.close()},changeBuy(){this.isBuy=!0},changeoto(s){this.onlineStatus=s},changeselectWay(s){this.selectListId=s.id,1==this.selectListId?this.finalPrice=this.del.classPriceOnline:this.finalPrice=this.del.classPriceOffline},enterfor(s){this.selectListId=s,1==this.selectListId?this.finalPrice=this.del.classPriceOnline:this.finalPrice=this.del.classPriceOffline,this.$refs.enterforPopup.open("bottom")},purchase(s){let i=this;e({success(s){i.$api.billingSubmit({method:"post",data:{jsCode:s.code,businessCode:"enterpriseClass",item:{onlineStatus:i.selectListId,enterpriseClassesId:i.enterforInfo.classId},method:1}}).then((s=>{wx.requestPayment({timeStamp:s.result.timeStamp,nonceStr:s.result.nonceStr,package:s.result.packageStr,signType:"RSA",paySign:s.result.paySign,success(s){i.canle(),i.changeBuy(),t({title:"支付成功",icon:"none"})},fail(s){t({title:"您已取消支付",icon:"none"})}})}))}})},getDel(s){this.$api.classdetail({data:{classId:s},method:"get"}).then((s=>{this.del=s.result,this.enterforInfo=this.del,this.box=JSON.parse(s.result.classContent)}))}}},[["render",function(s,e,t,S,k,b){const C=y(i("page-meta"),F),L=l("tabBar"),P=m,w=y(i("uni-popup"),I);return v(),a(u,null,[n(C,{"page-style":"background-color: #FAFCFF"}),c("div",null,[n(L,{title:k.title},null,8,["title"]),c("div",{class:"box"},[c("div",{class:"poster"},[n(P,{class:"posterImg",src:k.del.posterUrl},null,8,["src"]),c("div",{style:{"margin-left":"30rpx"}},[c("div",{class:"price"},[c("div",null,[c("span",{class:"currency"},"￥"),o(" "+r(1==k.onlineStatus?k.del.classPriceOnline:k.del.classPriceOffline),1)]),k.showOto?(v(),a("div",{key:0,class:"OTO"},[c("div",{onClick:e[0]||(e[0]=s=>b.changeoto(1)),style:d({color:1==k.onlineStatus?"#FFFFFF":"#86909C",background:1==k.onlineStatus?"linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;":"#EEF1F5"}),class:"tot"},"线上",4),c("div",{onClick:e[1]||(e[1]=s=>b.changeoto(0)),style:d({color:0==k.onlineStatus?"#FFFFFF":"#86909C",background:0==k.onlineStatus?"linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;":"#EEF1F5"}),class:"ontot"},"线下",4)])):h("",!0)]),c("div",{class:"t1"},r(k.del.className),1),c("div",{class:"t2"}," 开课时间："+r(k.del.classBeginDatetime),1),c("div",{class:"t2"}," 讲 师："+r(k.del.lecturer),1),1!=k.onlineStatus?(v(),a("div",{key:0,class:"t2"}," 开课地址："+r(k.del.classAddress),1)):h("",!0)])]),c("div",{class:"content"},[c("div",{class:"head"},[c("div",{class:"title"},"课程内容"),c("div",{class:"shadow"})]),(v(!0),a(u,null,p(k.box,((s,e)=>(v(),a("div",{key:e},[c("div",{class:"theme"},r(s.theme),1),c("div",{class:"particulars"},r(s.particulars),1)])))),128))]),c("div",{style:{height:"150rpx"}}),"false"==k.isBuy?(v(),a("div",{key:0,onClick:e[2]||(e[2]=s=>b.enterfor(k.onlineStatus)),class:"apply"}," 立即报名 ")):h("",!0)]),n(w,{"is-mask-click":!0,"border-radius":"10px 10px 0 0",ref:"enterforPopup","background-color":"#fff"},{default:f((()=>[c("div",{class:"enterforBox"},[c("div",{class:"enterforHead"},[c("div",{onClick:e[3]||(e[3]=(...s)=>b.canle&&b.canle(...s)),class:"canle"},"取消"),c("div",{class:"title"},"课程报名"),c("div",{class:"canle"})]),c("div",{class:"prompt"},[c("span",{class:"text"}," 请注意：请务必在规定的上课时间内参与课程，一旦错过， 将视为自动放弃，费用不予退还。 ")]),c("div",{style:d({" margin-bottom":k.whetherIos?"0rpx":"20rpx"}),class:"enterforContent"},[c("div",{class:"enterforInfo"},[n(P,{src:k.del.posterUrl,class:"enterforImg"},null,8,["src"]),c("div",{class:"enterforclassName"},r(k.del.className),1)]),c("div",{class:"enterforway"},[c("div",{class:"wayTxt"},"参与方式"),c("div",{class:"selectWay"},[(v(!0),a(u,null,p(k.selectList,((s,e)=>(v(),a("div",{class:"selectWayItem",key:s.id},[o(r(s.text)+" ",1),c("div",{onClick:e=>b.changeselectWay(s)},[k.selectListId==s.id?(v(),g(P,{key:0,class:"selectWayItemIcon",src:x})):(v(),a("div",{key:1,class:"circle"}))],8,["onClick"])])))),128))])]),c("div",{class:"btnsbox"},[c("div",{class:"left"},[c("div",{class:"summation"},"合计:"),c("div",{class:"price"},[c("span",{class:"currency"},"￥"),o(r(k.finalPrice),1)])]),c("div",{onClick:e[4]||(e[4]=(...s)=>b.purchase&&b.purchase(...s)),class:"affirm"}," 立即购买 ")])],4)])])),_:1},512)])],64)}],["__scopeId","data-v-eb272c32"]]);export{b as default};
