import{_ as a}from"./uni-popup.BdZPMDVN.js";import{z as i,o as s,c as e,w as n,f as l,B as o,i as t}from"./index-HcMwrp5e.js";import{r as p}from"./uni-app.es.DFp0WTX7.js";import{_ as r}from"./_plugin-vue_export-helper.BCo6x5W8.js";const c=r({data:()=>({porpsData:"",loading:!1}),methods:{opens(){this.$refs.claim.open()},claimCanle(){return this.$refs.claim.close(),this.$emit("noAccessFn",!0)},ConfirmationClaim(){var a;this.claimCanle(),null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{type:2},path:"memberCenter"}))}}},[["render",function(r,c,m,f,d,u){const C=p(i("uni-popup"),a),v=t;return s(),e(v,null,{default:n((()=>[l(C,{ref:"claim","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[o("div",{class:"applyaffirm"},[o("div",{class:"p1"},"提示"),o("div",{class:"p2"},"暂无权限，请到 我的-会员中心 开通【产发臻享版】后体验"),o("div",{class:"popBtn"},[o("div",{onClick:c[0]||(c[0]=(...a)=>u.claimCanle&&u.claimCanle(...a)),class:"canleBt"}," 取消 "),o("div",{onClick:c[1]||(c[1]=(...a)=>u.ConfirmationClaim&&u.ConfirmationClaim(...a)),class:"affirm"}," 开通 ")])])])),_:1},512)])),_:1})}],["__scopeId","data-v-7f6fbf78"]]);export{c as n};
