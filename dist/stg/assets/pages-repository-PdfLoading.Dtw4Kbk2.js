import{Y as s,U as t,Z as a,_ as e,$ as i,c as o,w as d,i as n,o as c,f as l,B as p}from"./index-HcMwrp5e.js";import{_ as r}from"./_plugin-vue_export-helper.BCo6x5W8.js";const h=r({data:()=>({showText:"",isOne:!1}),onLoad(s){this.previewPDF(s.id),this.showText=s.isBuy},onHide(a){s(),t({delta:1})},methods:{previewPDF(s){let t={businessCode:"dataProduct",dataUnionId:s};this.$api.downloadUrl({data:t,method:"get"}).then((s=>{a({title:"加载中"}),e({url:s.result,success(s){if(200===s.statusCode){const t=s.tempFilePath;setTimeout((()=>{i({filePath:t,showMenu:!0,success:function(s){this.isOne=!0}})}),1e3)}}})})).catch((s=>{}))}}},[["render",function(s,t,a,e,i,r){const h=n;return c(),o(h,{class:"page-section page-section-gap"},{default:d((()=>[l(h,{class:"pdgBg"},{default:d((()=>[p("img",{class:"headportrait",src:"https://static.idicc.cn/cdn/aiChat/applet/viewBg.png",alt:""}),p("div",{class:"hint"}," 注：未购买仅展示3页预览 ")])),_:1})])),_:1})}],["__scopeId","data-v-f7bbb297"]]);export{h as default};
