import{l as e,s as t,m as i,z as s,A as n,a,f as o,c as r,w as l,n as h,d as c,F as p,i as d,o as u,B as m,g,t as f,e as y,b as w,C as I,S as L}from"./index-HcMwrp5e.js";import{_ as S}from"./page-meta.X-Lr6csD.js";import{r as k}from"./uni-app.es.DFp0WTX7.js";import{_ as C}from"./uni-popup.BdZPMDVN.js";import{e as v}from"./enterpriseList.DZqkps3N.js";import{t as A,M as b}from"./MultipleChoice.DWrO2ZXK.js";import{t as x}from"./index.B6Rbewql.js";import{m as N}from"./mapList.BhjoS9MT.js";import{m as T}from"./moreScreen.y5u7uXHA.js";import{S as B}from"./SingleLineSelection.BL_T7Qi-.js";import{n as D}from"./noAccess.BxT8m8Qw.js";import{g as E,i as V}from"./utils.61Hi-B7M.js";import{_ as j}from"./up.CByYEzXe.js";import{_ as M}from"./right.D4iebSn6.js";import{_ as $}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.CHOJlBbg.js";import"./uni-tooltip.Bpz4R-Ep.js";import"./returnFn.BYkANsDr.js";const F=$({components:{tabBar:x,enterpriseList:v,treeSelect:A,mapList2:N,moreScreen:T,SingleLineSelection:B,noAccess:D,MultipleChoice:b},data:()=>({bottomBlank:!1,title:"",showChain:"",initShowChain:"",industryChainList:[],nodeName:"",List:[],keyenterprise:[],isshownodeName:"",showPopup:!1,tagList:[],pitchtag:[],pageNum:1,pageSize:10,total:0,size:0,mosj:!1,amount:0,coilHeight:"0rpx",nodeHeight:0,waibianjv:0,popupShow:!1,oletagList:[],statusBarHeight:0,showTree:!1,defaultValue:[],initialValue:[],list:[],selectType:2,knowId:"",baseInfo:{},moreData:{},affinityData:{},sortData:{},Jurisdiction:!1,token:"",moreScreenList:[],moreScreenList2:[],enterpriseTypeList:["扩张意愿指数","快速成长指数"],parameterList:[!0,!0,!0,!0],cityName:"",introductionSelectIndex:0,showPage:!0}),onLoad(i){if(this.statusBarHeight=e("statusBarHeight"),this.token=e("token"),E("bottom")){let e=E("bottom")+"px";t("safeAreaRpx",e)}E("token")&&(this.token=E("token"),t("token",this.token),this.requestOrgCode()),E("identity")&&t("userIdentityType",E("identity")),E("showChain")&&(this.initShowChain=E("showChain"),this.showChain=E("showChain")),E("title")&&(this.title=E("title")),E("knowId")&&(this.knowId=E("knowId")),this.initShowChain?(this.Jurisdiction=!0,this.gettree(this.initShowChain)):(this.bottomBlank=!0,this.getindustryList()),this.getScreen()},mounted(){},methods:{isWithinLastWeek:V,requestOrgCode(){this.$api.ownList({}).then((e=>{var i,s,n;t("orgCode",null==(i=e.result.selected)?void 0:i.orgCode),t("orgName",null==(s=e.result.selected)?void 0:s.orgName),t("isDefault",null==(n=e.result.selected)?void 0:n.isDefault)}))},isGoSea(){let t=e("userIdentityType");3==t||4==t?i({url:"/goToSea/index?pitch=4"}):this.showPage=!0},regionFn(){var e,t;let i="全国";return null!=(null==(e=this.baseInfo)?void 0:e.area)&&0!=(null==(t=this.baseInfo)?void 0:t.area.length)?i=this.baseInfo.area[0]:this.baseInfo.city?i=this.baseInfo.city:this.baseInfo.province&&(i=this.baseInfo.province),i.length>4&&(i=i.substring(0,3)+"..."),i},ClearFilter(){this.moreData={},this.affinityData={},this.sortData={},this.introductionSelectIndex=0,this.baseInfo={},this.$nextTick((()=>{this.$refs.mapList2.clear(),this.$refs.moreScreen.claimFn(),this.$refs.moreScreen2.claimFn(),this.$refs.MultipleChoice.clear()})),this.changeSelectType()},updatamoreScreen(e){this.moreData=e,this.gettree(this.showChain),this.changeSelectType()},updatamoreScreen2(e){this.affinityData=e,this.gettree(this.showChain),this.changeSelectType()},affirm1(e){this.$refs.EnterpriseTypePop.close(),e?(this.sortData.orderByField="扩张意愿指数"==e?"expansionIndex":"growthIndex",this.introductionSelectIndex="扩张意愿指数"==e?1:2):(this.sortData={},this.introductionSelectIndex=0),this.gettree(this.showChain),this.changeSelectType()},affirm2(e){this.$refs.popup2.close(),this.baseInfo.province=e.province.name,this.baseInfo.city=e.citys.name,this.baseInfo.area=[],e.area.name&&(this.baseInfo.area[0]=e.area.name);let t=e.province.name?e.province.name+"/":"",i=e.citys.name?e.citys.name+"/":"",s=e.area.name?e.area.name+"/":"";this.cityName=t+i+s,this.gettree(this.showChain),this.changeSelectType()},iconColour(e){return this.Jurisdiction?e?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png":"/static/AboutAi/grayArrows.png"},isEmptyValue(e){return"string"==typeof e?""===e.trim():"number"==typeof e?0===e:"boolean"==typeof e?!1===e:Array.isArray(e)?e.every(this.isEmptyValue):null!==e&&"object"==typeof e&&Object.values(e).every(this.isEmptyValue)},changeSelectType(){this.parameterList[0]=this.isEmptyValue(this.baseInfo),this.parameterList[1]=this.isEmptyValue(this.affinityData),this.parameterList[2]=this.isEmptyValue(this.moreData),this.parameterList[3]=this.isEmptyValue(this.sortData)},getJurisdiction(){this.token&&this.$api.checkByKnowIdAPI({data:{knowId:this.knowId},method:"GET"}).then((e=>{this.Jurisdiction=e.result}))},getScreen(){this.token&&(this.$api.getSearchParamAPI({method:"GET"}).then((e=>{this.moreScreenList=e.result})),this.$api.getKinshipParamAPI({method:"GET"}).then((e=>{this.moreScreenList2=e.result})))},openJurisdictionPop(){this.$refs.noAccess.opens()},screenFn(e){var t;if(this.token)this.Jurisdiction?1==e?(this.$refs.popup2.open("bottom"),this.$nextTick((()=>{this.$refs.mapList2?this.$refs.mapList2.init():console.warn("mapList2组件未挂载打开时")}))):2==e?this.$refs.moreScreen2.opens():3==e?this.$refs.moreScreen.opens():4==e&&(this.$refs.EnterpriseTypePop.open("bottom"),this.$nextTick((()=>{this.$refs.MultipleChoice?this.$refs.MultipleChoice.init(this.introductionSelectIndex):console.warn("MultipleChoice")}))):this.$refs.noAccess.opens();else{(null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):i({url:"/pages/login/index"})}},goSearch(){var e,t;if(this.token)this.Jurisdiction?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{showChain:this.showChain,knowId:this.knowId},path:"industrySearch"})):this.$refs.noAccess.opens();else{(null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):i({url:"/pages/login/index"})}},init(){var e,i,s;if(navigator.userAgent.indexOf("airApp/1.0.0")>-1){let n={token:"",id:""},a=null==(e=window.location.href.split("?token="))?void 0:e[1],o=null==a?void 0:a.split("&id=");n.token=null==(s=null==(i=null==o?void 0:o[0])?void 0:i.split("&abilityName="))?void 0:s[0],n.id=null==o?void 0:o[1],t("token",n.token)}},onConfirm(e){var i,s,n;t("Selectedindustrychain",e);let{selected:a,values:o}=e;null!=o[1]&&(this.ClearFilter(),this.List=[],this.amount=0,this.showChain=null==(i=a[1])?void 0:i.chainId,this.defaultValue=[o[0],o[1]],this.initialValue=[o[0],o[1]],this.title=null==(s=a[1])?void 0:s.chainName,this.knowId=null==(n=a[1])?void 0:n.knowId,this.gettree(o[1]),this.getJurisdiction())},digitalfiltering(e){if(e.sumEnterpriseCount){e.searchEnterpriseCount||(e.searchEnterpriseCount=0);let t=parseFloat(e.searchEnterpriseCount).toString(),i=parseFloat(e.sumEnterpriseCount).toString();return this.parameterList[0]&&this.parameterList[1]&&this.parameterList[2]&&this.parameterList[3]?`${i}`:`<span style="color: #3370FF">${t}</span>/${i}`}return this.parameterList[0]&&this.parameterList[1]&&this.parameterList[2]&&this.parameterList[3]?"0":'<span style="color: #3370FF;">0</span>/0'},changeState(e){this.popupShow=e.show},changeState2(e){this.popupShow=e},loadMoreData(){if(this.pageNum>this.size)return void(this.mosj=!0);if(this.isLoading)return;this.isLoading=!0;let e={nodeId:this.isshownodeName.nodeId,pageNum:this.pageNum,pageSize:this.pageSize,chainId:this.showChain,enterpriseLabelIds:this.pitchtag,...this.baseInfo,...this.moreData,...this.affinityData,...this.sortData};this.$api.pageByChainNode({data:e,method:"POST"}).then((e=>{this.mosj=!1,this.total=e.result.total,this.size=Math.ceil(this.total/10),this.pageNum++,this.keyenterprise=this.keyenterprise.concat(e.result.records),this.isLoading=!1})).catch((e=>{this.isLoading=!1}))},ecoIndustryChain(e){this.showChain=e.showChain,this.title=e.title,this.knowId=e.knowId,this.Jurisdiction=!0,this.gettree(this.showChain)},getindustryList(){this.$api.industrychainList({data:{capType:this.selectType},method:"get"}).then((t=>{var i,s,n;this.industryChainList=t.result.map((e=>{let t=e.chainList.map((e=>({...e,label:e.chainName,value:e.chainId,categoryBuyStatus:e.buyStatus}))),{chainList:i,...s}=e;return{...s,label:e.categoryName,value:e.categoryId,children:t}}));let a=e("Selectedindustrychain");this.list=this.industryChainList,a?(this.showChain=a.selected[1].chainId,this.title=a.selected[1].chainName,this.knowId=a.selected[1].knowId,this.defaultValue=[a.selected[0].categoryId,this.showChain],this.initialValue=[a.selected[0].categoryId,this.showChain],this.gettree(this.showChain),this.getJurisdiction(),setTimeout((()=>{this.list.map((e=>{e.value==this.initialValue[0]&&this.$refs.treeSelect.echo(this.list,e.children)}))}),1e3)):(this.showChain=null==(i=t.result[0].chainList[0])?void 0:i.chainId,this.title=null==(s=t.result[0].chainList[0])?void 0:s.chainName,this.knowId=null==(n=t.result[0].chainList[0])?void 0:n.knowId,this.defaultValue=[this.industryChainList[0].categoryId,this.showChain],this.initialValue=[this.industryChainList[0].categoryId,this.showChain],this.gettree(this.showChain),this.getJurisdiction())}))},gettree(e){t("chainId",e);let i={chainId:e,...this.baseInfo,...this.moreData,...this.affinityData,...this.sortData};this.showTree=!1,this.$api.nodeTreeListAPI({data:i,method:"POST"}).then((e=>{this.List=e.result,this.List.forEach(((e,t)=>{0==t&&0!=e.children.length?e.show=!0:e.show=!1,e.children.forEach((e=>{e.show=!1,e.children&&e.children.forEach((e=>{e.show=!1}))}))})),this.showTree=!0})).catch((e=>{this.showTree=!0}))},isshow(e,t){null==t?this.List[e].show=!this.List[e].show:this.List[e].children[t].show=!this.List[e].children[t].show},recommendQ(e){var t;if(!this.token){return void((null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):i({url:"/pages/login/index"}))}this.isshownodeName=e,this.pageNum=1,this.mosj=!1,this.keyenterprise=[],this.pitchtag=[];let s={nodeId:e.nodeId,pageNum:this.pageNum,pageSize:this.pageSize,chainId:this.showChain,...this.baseInfo,...this.moreData,...this.affinityData,...this.sortData};this.$api.pageByChainNode({data:s,method:"POST"}).then((e=>{this.total=e.result.total,this.size=Math.ceil(this.total/10),this.keyenterprise=this.keyenterprise.concat(e.result.records),this.pageNum++,this.$refs.popup.open("bottom")}))}},watch:{keyenterprise(e){this.keyenterprise.forEach((e=>{let t=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let i=e.enterpriseLabelNames[0];i?t.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:i.includes("独角兽")?e.enterpriseIconLabelId=2:i.includes("专精特新")?e.enterpriseIconLabelId=3:i.includes("隐形冠军")?e.enterpriseIconLabelId=4:i.includes("瞪羚")?e.enterpriseIconLabelId=5:i.includes("创新")?e.enterpriseIconLabelId=6:i.includes("技术先进")?e.enterpriseIconLabelId=7:i.includes("科技")?e.enterpriseIconLabelId=8:i.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}}},[["render",function(e,t,i,v,A,b){const x=k(s("page-meta"),S),N=d,T=n("tree-select"),B=I,D=L,E=n("enterpriseList"),V=k(s("uni-popup"),C),$=n("mapList2"),F=n("moreScreen"),J=n("noAccess"),P=n("MultipleChoice");return u(),a(p,null,[o(x,{"page-style":"overflow:"+(A.popupShow?"hidden":"visible")},null,8,["page-style"]),A.showPage?(u(),r(N,{key:0,class:"box",style:h({height:A.initShowChain?"calc(100vh - 20rpx)":"calc(100vh - 208rpx)"})},{default:l((()=>[o(N,{class:"titleBox"},{default:l((()=>[A.initShowChain?c("",!0):(u(),r(T,{key:0,selectTile:"切换产业链",optionsKey:"value",value:A.defaultValue,initialValue:A.initialValue,primary:"value",ref:"treeSelect",options:A.list,onConfirm:b.onConfirm,noLimit:!0},{default:l((()=>[o(N,{style:{display:"flex","align-items":"center"}},{default:l((()=>[m("img",{src:"https://static.idicc.cn/cdn/aiChat/applet/cut.png",class:"cutIcon"}),g(" "+f(A.title)+"产业链图谱 ",1)])),_:1})])),_:1},8,["value","initialValue","options","onConfirm"])),A.initShowChain?(u(),r(N,{key:1,style:{display:"flex","align-items":"center"}},{default:l((()=>[g(f(A.title)+"产业链图谱 ",1)])),_:1})):c("",!0),o(B,{onClick:b.goSearch,class:"searchIcon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAMAAADW3miqAAAAQlBMVEUAAABAUFY/Slk/S1lASllASlk/SllASlpASlpAUFg+Slg/SllAS1k/Slk/Slk/SlpAS1lATFhATFxASFg/Slo/Slmrn+u3AAAAFXRSTlMAEN+/cGDvgDAgf4C/r5CfUEBAIM+Hyw0JAAABAUlEQVQ4y5WTWZKDMAxEvcgLCXvo+191RCqDsGUq4f0hP7VUdmHu0tHkmXHuLpXgcTDlppI/iosfrZFGYHp6nyzkdluFPXZlOY1mLWbtpKJih9rq2AmmYuSJ1ghuz1H4ohp4H6OxEVGiNuBlGjxPUZ0EqSh3mhZMk0FGjEBuSyTtHjBtZoAOKV5IC5DuSLyevZRIFl/bUpKTIKEVXmZYYNNCfcv+Yt4EhPN+zjaDXHn9k3Zc+VyWvx+VYzdgKH+VyJWuqHAfxuqV2HLBHjEjGBX/2jvjQGvOM3kw/VOsolmIZIy2OGxy/0pPPFmskjynlGh+G8pS3LfWX6xgvhLI/MYfksYTtEAAxnoAAAAASUVORK5CYII="},null,8,["onClick"])])),_:1}),o(N,{class:"contentBox"},{default:l((()=>[m("div",{class:"optionBox"},[m("span",{class:y([A.Jurisdiction?A.parameterList[0]?"unselected":"pitchOn":"noAccess","optionItem"]),onClick:t[0]||(t[0]=e=>b.screenFn(1))},[g(f(b.regionFn())+" ",1),o(B,{src:b.iconColour(A.parameterList[0]),class:"Arrows"},null,8,["src"])],2),m("span",{class:y([A.Jurisdiction?A.parameterList[1]?"unselected":"pitchOn":"noAccess","optionItem"]),onClick:t[1]||(t[1]=e=>b.screenFn(2))},[g(" 亲缘查询"),o(B,{src:b.iconColour(A.parameterList[1]),class:"Arrows"},null,8,["src"])],2),m("span",{class:y([A.Jurisdiction?A.parameterList[2]?"unselected":"pitchOn":"noAccess","optionItem"]),onClick:t[2]||(t[2]=e=>b.screenFn(3))},[g(" 更多查询 "),o(B,{src:b.iconColour(A.parameterList[2]),class:"Arrows"},null,8,["src"])],2),m("span",{class:y([A.Jurisdiction?A.parameterList[3]?"unselected":"pitchOn":"noAccess","optionItem"]),onClick:t[3]||(t[3]=e=>b.screenFn(4))},[g(" 指数排序 "),o(B,{src:b.iconColour(A.parameterList[3]),class:"Arrows"},null,8,["src"])],2)]),0!=A.List.length?(u(),r(D,{key:0,"scroll-y":"true",class:"nodeBox"},{default:l((()=>[(u(!0),a(p,null,w(A.List,((e,t)=>(u(),a("div",{key:t,class:"nodeItem"},[o(N,{class:"stairNode"},{default:l((()=>[m("span",{class:"stairNodeText",onClick:t=>b.recommendQ(e)},[g(f(e.nodeName)+" (",1),m("span",{style:{"text-align":"center"},innerHTML:b.digitalfiltering(e)},null,8,["innerHTML"]),g(" ) "),b.isWithinLastWeek(e.lastModifyDate)?(u(),r(B,{key:0,class:"upBgc",src:j})):c("",!0)],8,["onClick"]),e.children.length>=1?(u(),a("div",{key:0,onClick:e=>b.isshow(t),class:"OperationExpansion"},[m("img",{src:M,class:y(e.show?"unfoldIcon":"upackIcon")},null,2)],8,["onClick"])):c("",!0)])),_:2},1024),e.children&&e.show?(u(),a("div",{key:0,class:"secondLevelBox"},[(u(!0),a(p,null,w(e.children,((e,i)=>(u(),a("div",{key:i},[m("div",{class:"secondLevel"},[m("span",{class:"secondLevelText",onClick:t=>b.recommendQ(e)},[m("div",{class:"dot"}),g(" "+f(e.nodeName)+" (",1),m("span",{style:{"text-align":"center"},innerHTML:b.digitalfiltering(e)},null,8,["innerHTML"]),g(") "),b.isWithinLastWeek(e.lastModifyDate)?(u(),r(B,{key:0,class:"upBgc",src:j})):c("",!0)],8,["onClick"]),e.children.length>=1?(u(),a("div",{key:0,style:{},onClick:e=>b.isshow(t,i)},[m("img",{src:M,class:y(e.show?"unfoldIcon":"upackIcon")},null,2)],8,["onClick"])):c("",!0)]),e.children&&e.show?(u(),a("div",{key:0,class:"threeLevelBox"},[(u(!0),a(p,null,w(e.children,((e,t)=>(u(),a("div",{key:t,class:"threeLevel"},[m("span",{class:"threeLevelText",onClick:t=>b.recommendQ(e)},[g(f(e.nodeName)+" (",1),m("span",{style:{"text-align":"center"},innerHTML:b.digitalfiltering(e)},null,8,["innerHTML"]),g(") ")],8,["onClick"])])))),128))])):c("",!0)])))),128))])):c("",!0)])))),128)),m("div",{style:{"padding-bottom":"180rpx"}})])),_:1})):c("",!0)])),_:1})])),_:1},8,["style"])):c("",!0),o(V,{ref:"popup","safe-area":!1,"background-color":"rgba(0,0,0,0)",onChange:b.changeState},{default:l((()=>[o(N,{class:"uni-popups"},{default:l((()=>[o(N,{class:"popup-content"},{default:l((()=>[m("span",{class:"nodeName"},f(A.isshownodeName.nodeName)+"("+f(A.total)+")",1)])),_:1}),o(D,{style:{height:"1100rpx"},"scroll-y":"true",onScrolltolower:b.loadMoreData},{default:l((()=>[o(N,{class:"qyList"},{default:l((()=>[o(E,{Jurisdiction:A.Jurisdiction,showChain:A.showChain,knowId:A.knowId,enterpriseList:A.keyenterprise,enterpriseLabelIds:A.pitchtag,onOpenJurisdictionPop:b.openJurisdictionPop},null,8,["Jurisdiction","showChain","knowId","enterpriseList","enterpriseLabelIds","onOpenJurisdictionPop"])])),_:1}),A.total<1?(u(),r(N,{key:0,class:"noemphasis"},{default:l((()=>[m("img",{src:"https://static.idicc.cn/cdn/aiChat/applet/nozdqy.png",alt:""}),m("span",{class:"nozdqy"},"暂无企业")])),_:1})):c("",!0),A.mosj&&0!=A.total?(u(),r(N,{key:1,class:"noemphasis2"},{default:l((()=>[m("span",{class:"total"},"没有更多啦~")])),_:1})):c("",!0)])),_:1},8,["onScrolltolower"])])),_:1})])),_:1},8,["onChange"]),o(V,{"safe-area":!1,ref:"popup2","background-color":"#fff"},{default:l((()=>[o($,{BottomBlank:A.bottomBlank,ref:"mapList2",cityName:A.cityName,unrestricted:!0,onAffirm:b.affirm2},null,8,["BottomBlank","cityName","onAffirm"])])),_:1},512),o(F,{BottomBlank:A.bottomBlank,ref:"moreScreen",title:"更多查询",moreScreenList:A.moreScreenList,onUpdatamoreScreen:b.updatamoreScreen},null,8,["BottomBlank","moreScreenList","onUpdatamoreScreen"]),o(F,{BottomBlank:A.bottomBlank,ref:"moreScreen2",title:"亲缘查询",MultipleChoice:!0,moreScreenList:A.moreScreenList2,onUpdatamoreScreen:b.updatamoreScreen2},null,8,["BottomBlank","moreScreenList","onUpdatamoreScreen"]),o(J,{ref:"noAccess"},null,512),o(V,{"safe-area":!1,ref:"EnterpriseTypePop","background-color":"#fff"},{default:l((()=>[o(P,{introductionSelectIndex:A.introductionSelectIndex,BottomBlank:!1,ref:"MultipleChoice",list:A.enterpriseTypeList,onAffirm:b.affirm1},null,8,["introductionSelectIndex","list","onAffirm"])])),_:1},512)],64)}],["__scopeId","data-v-2a8628f0"]]);export{F as default};
