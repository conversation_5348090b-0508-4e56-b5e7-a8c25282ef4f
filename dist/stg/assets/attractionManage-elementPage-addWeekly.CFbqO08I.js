import{p as e,z as a,a as t,B as s,c as l,t as d,e as r,f as i,w as n,d as o,C as m,o as u}from"./index-HcMwrp5e.js";import{_ as c}from"./uni-easyinput.CAhWtyu2.js";import{r as v}from"./uni-app.es.DFp0WTX7.js";import{_ as h,a as p}from"./uni-forms.i1XM9iHP.js";import{t as y}from"./index.B6Rbewql.js";import{_ as k}from"./userimg.DBGjY0t5.js";import{_ as w}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.B5Z3RbO2.js";import"./returnFn.BYkANsDr.js";const g=w({components:{tabBar:y},data:()=>({title:"写周报",add:!0,userdata:"",presentTime:"",demandForm:{newProject:"",interview:"",other:"",nextWeek:"",assistance:""},weeklyStats:{},rules:{newProject:{rules:[{required:!0,errorMessage:"请输入新增项目信息"}]},interview:{rules:[{required:!0,errorMessage:"请输入采访与考察信息"}]},other:{rules:[{required:!0,errorMessage:"请输入其他工作"}]},nextWeek:{rules:[{required:!0,errorMessage:"请输入下周计划"}]},assistance:{rules:[{required:!0,errorMessage:"请输入需领导协助事项"}]}}}),onLoad(e){e.id?(this.add=!1,this.title="周报详情",this.getDel(e.id)):(this.getStats(),this.day(),this.getUserInfo())},methods:{getStats(){this.$api.weeklyReportWeeklyStatsAPI({method:"get"}).then((e=>{this.weeklyStats=e.result}))},getDel(e){this.$api.weeklyReportDelAPI({data:{reportCode:e},method:"get"}).then((e=>{this.demandForm=e.result}))},addSubmit(){this.$refs.demandvaliForm.validate().then((()=>{let a=this.demandForm;this.$api.addWeeklyReportAPI({data:a,method:"post"}).then((a=>{"SUCCESS"==a.code&&(e({title:"周报提交成功！",icon:"none"}),setTimeout((()=>{var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{type:"refresh"},path:"goBack"}))}),500))}))}))},day(){const e=new Date,a=e.getFullYear(),t=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0");this.presentTime=a+"-"+t+"-"+s},getUserInfo(){this.$api.userMessage().then((e=>{this.userdata=e.result}))}}},[["render",function(e,y,w,g,F,f){var S,b,x,V,j,q,C,I,_,P,U,W;const M=m,E=v(a("uni-easyinput"),c),D=v(a("uni-forms-item"),h),T=v(a("uni-forms"),p);return u(),t("div",null,[s("div",{class:"box33"},[s("div",{class:"information"},[F.demandForm.userhead||F.userdata.headImgUrl?(u(),l(M,{key:0,src:F.demandForm.userhead||F.userdata.headImgUrl,class:"showHeadimg",mode:""},null,8,["src"])):(u(),l(M,{key:1,src:k,class:"showHeadimg",mode:"",onClick:e.goLogin},null,8,["onClick"])),s("div",{class:"right"},[s("div",{class:"name"},d(F.add?F.userdata.realName:F.demandForm.username)+"的周报",1),s("div",{class:"presentTime"},d(F.demandForm.reportDate||F.presentTime),1)])]),s("div",{class:"headline"}," 系统生成内容 "),s("div",{class:"systemContent"},[s("div",{class:"systemContentItem"},[s("div",{class:"key"},"纳入意向企业"),s("div",{class:"value"},d((null==(b=null==(S=F.demandForm)?void 0:S.weeklyStats)?void 0:b.totalEnterprise)||F.weeklyStats.totalEnterprise||0),1)]),s("div",{class:"systemContentItem"},[s("div",{class:"key"},"外部企业添加数"),s("div",{class:"value"},d((null==(V=null==(x=F.demandForm)?void 0:x.weeklyStats)?void 0:V.ownEnterprise)||F.weeklyStats.ownEnterprise||0),1)]),s("div",{class:"systemContentItem"},[s("div",{class:"key"},"企业跟踪数"),s("div",{class:"value"},d((null==(q=null==(j=F.demandForm)?void 0:j.weeklyStats)?void 0:q.withFollowing)||F.weeklyStats.withFollowing||0),1)]),s("div",{class:"systemContentItem"},[s("div",{class:"key"},"企业委托数"),s("div",{class:"value"},d((null==(I=null==(C=F.demandForm)?void 0:C.weeklyStats)?void 0:I.withEntrust)||F.weeklyStats.withEntrust||0),1)]),s("div",{class:"systemContentItem"},[s("div",{class:"key"},"签约成功数"),s("div",{class:"value"},d((null==(P=null==(_=F.demandForm)?void 0:_.weeklyStats)?void 0:P.stateSign)||F.weeklyStats.stateSign||0),1)]),s("div",{class:"systemContentItem"},[s("div",{class:"key"},"签约失败数"),s("div",{class:"value"},d((null==(W=null==(U=F.demandForm)?void 0:U.weeklyStats)?void 0:W.stateFail)||F.weeklyStats.stateFail||0),1)])]),s("div",{class:"form"},[s("div",{class:r(F.add?"weeklyFrom":"weeklyFrom2")},[i(T,{ref:"demandvaliForm",rules:F.rules,"model-value":F.demandForm,"label-position":"top","label-width":"100"},{default:n((()=>[i(D,{required:!!F.add,label:"新增项目信息",name:"newProject"},{default:n((()=>[F.add?(u(),l(E,{key:0,modelValue:F.demandForm.newProject,"onUpdate:modelValue":y[0]||(y[0]=e=>F.demandForm.newProject=e),trim:"all",maxlength:"200",clearable:!1,placeholder:"请输入"},null,8,["modelValue"])):(u(),t("span",{key:1,class:"official"},d(F.demandForm.newProject),1))])),_:1},8,["required"]),i(D,{required:!!F.add,label:"采访与考察",name:"interview"},{default:n((()=>[F.add?(u(),l(E,{key:0,modelValue:F.demandForm.interview,"onUpdate:modelValue":y[1]||(y[1]=e=>F.demandForm.interview=e),trim:"all",maxlength:"200",clearable:!1,placeholder:"请输入"},null,8,["modelValue"])):(u(),t("span",{key:1,class:"official"},d(F.demandForm.interview),1))])),_:1},8,["required"]),i(D,{required:!!F.add,label:"其他工作",name:"other"},{default:n((()=>[F.add?(u(),l(E,{key:0,modelValue:F.demandForm.other,"onUpdate:modelValue":y[2]||(y[2]=e=>F.demandForm.other=e),trim:"all",maxlength:"200",clearable:!1,placeholder:"请输入"},null,8,["modelValue"])):(u(),t("span",{key:1,class:"official"},d(F.demandForm.other),1))])),_:1},8,["required"]),i(D,{required:!!F.add,label:"下周计划",name:"nextWeek"},{default:n((()=>[F.add?(u(),l(E,{key:0,modelValue:F.demandForm.nextWeek,"onUpdate:modelValue":y[3]||(y[3]=e=>F.demandForm.nextWeek=e),trim:"all",maxlength:"200",clearable:!1,placeholder:"请输入"},null,8,["modelValue"])):(u(),t("span",{key:1,class:"official"},d(F.demandForm.nextWeek),1))])),_:1},8,["required"]),i(D,{required:!!F.add,label:"需领导协助事项",name:"assistance"},{default:n((()=>[F.add?(u(),l(E,{key:0,modelValue:F.demandForm.assistance,"onUpdate:modelValue":y[4]||(y[4]=e=>F.demandForm.assistance=e),trim:"all",maxlength:"200",clearable:!1,placeholder:"请输入"},null,8,["modelValue"])):(u(),t("span",{key:1,class:"official"},d(F.demandForm.assistance),1))])),_:1},8,["required"])])),_:1},8,["rules","model-value"])],2)]),F.add?(u(),t("div",{key:0,class:"submit",onClick:y[5]||(y[5]=(...e)=>f.addSubmit&&f.addSubmit(...e))}," 提交周报 ")):o("",!0)])])}],["__scopeId","data-v-18a3e781"]]);export{g as default};
