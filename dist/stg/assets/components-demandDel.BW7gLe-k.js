import{z as s,o as a,c as e,w as t,V as i,f as n,B as l,t as o,g as c,a as d,d as r,i as p,l as m,s as v,p as u,A as f,F as y,b as k,D as h,C as I}from"./index-CBCsGYoT.js";import{t as w}from"./index.D5F8338M.js";import{r as b,f as g}from"./feedback.BbtaHkp2.js";import{_ as D}from"./uni-icons.Dr3tmUrM.js";import{r as C}from"./uni-app.es.CZb2JZWI.js";import{_ as N}from"./uni-popup.BLXBf1r-.js";import{_ as L}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{g as T}from"./utils.61Hi-B7M.js";import{_ as x}from"./right.D4iebSn6.js";import"./returnFn.BYkANsDr.js";import"./uni-easyinput.D_LnJWIZ.js";import"./uni-forms.BsccYHPu.js";import"./uni-transition.Ckb0qY8x.js";const j=L({components:{tabBar:w,revocation:b,processfeedback:L({data:()=>({porpsData:{}}),methods:{opens(s){this.porpsData=s,this.$refs.entrust.open()},entrustCloseFn(){this.$refs.entrust.close()}}},[["render",function(m,v,u,f,y,k){const h=C(s("uni-icons"),D),I=C(s("uni-popup"),N),w=p;return a(),e(w,null,{default:t((()=>[(a(),e(i,{to:"body"},[n(I,{ref:"entrust","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:t((()=>[l("div",{class:"applyaffirm"},[l("div",{class:"p1"},"流程查看"),n(h,{onClick:k.entrustCloseFn,class:"canleIcon",type:"closeempty",color:"#86909C",size:"22"},null,8,["onClick"]),l("div",{class:"contentBox"},[l("div",{class:"contentItemBox"},[l("div",{class:"Time"},[l("div",{class:"circle1"}),l("span",{class:"remindTime"},o(y.porpsData.gmtCreate),1),l("span",{class:"typeText"},[l("span",{class:"dot"}),c("待审核 ")])]),l("div",{class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"反馈人：")]),l("span",{class:"value"},o(y.porpsData.userName),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"推荐企业名称：")]),l("span",{class:"value"},o(y.porpsData.enterpriseName),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"统一社会信用代码：")]),l("span",{class:"value"},o(y.porpsData.enterpriseUniCode),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"推荐理由：")]),l("span",{class:"value"},o(y.porpsData.reason),1)])])]),1==y.porpsData.status||2==y.porpsData.status||3==y.porpsData.status?(a(),d("div",{key:0,class:"contentItemBox"},[l("div",{class:"Time"},[l("div",{class:"circle1"}),l("span",{class:"remindTime"},o(y.porpsData.auditTime),1),l("span",{class:"typeText"},[l("span",{class:"typeText"},[l("span",{class:"dot"}),c(o(1==y.porpsData.status?"审核通过":"审核驳回"),1)])])]),l("div",{class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"审核人：")]),l("span",{class:"value"},o(y.porpsData.auditName),1)]),y.porpsData.auditReason?(a(),d("div",{key:0,class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},o(1==y.porpsData.status?"推荐理由":"驳回理由")+"：",1)]),l("span",{class:"value"},o(y.porpsData.auditReason),1)])):r("",!0),y.porpsData.enterpriseDetail?(a(),d("div",{key:1,class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"企业概述：")]),l("span",{class:"value"},o(y.porpsData.enterpriseDetail),1)])):r("",!0)])])):r("",!0),3==y.porpsData.status?(a(),d("div",{key:1,class:"contentItemBox"},[l("div",{class:"Time"},[l("div",{class:"circle1"}),l("span",{class:"remindTime"},o(y.porpsData.entrustTime),1),l("span",{class:"typeText"},[l("span",{class:"dot"}),c("已委托 ")])]),l("div",{class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"委托人：")]),l("span",{class:"value"},o(y.porpsData.entrustName),1)])])])):r("",!0)])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-85a72b94"]]),feedback:g},data:()=>({title:"哒达助招",dataInfo:{processList:[],reminds:[]},identity:1,state:"",tabIds:1,informationList:[],showenterpriseNote:!1}),onLoad(s){this.identity=m("identity"),T("token")&&v("token",T("token")),T("showenterpriseNote")&&(this.showenterpriseNote=!0),T("id")&&(this.getDel(T("id")),2==this.identity&&this.getTeedbackList(T("id")))},methods:{feedbackFn(s){s.cancel?u({title:"该需求已下架，不能进行反馈",icon:"none"}):this.$refs.feedback.opens(s)},flowDel(s){var a;null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"showAlert",value:{type:"FeedbackFlow",data:s},module:"Investment"}))},updataList(){this.getDel(this.dataInfo.id),2==this.identity&&this.getTeedbackList(this.dataInfo.id)},feedbackList(){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"changePath",value:{id:this.dataInfo.id},path:"feedbackList"}))},getTeedbackList(s){let a={demandId:s,pageSize:1e3,pageNum:1};this.$api.workerrecommendListAPI({data:a,method:"get"}).then((s=>{this.informationList=s.result.records,this.informationList.forEach((s=>{0==s.status?s.showState="待审核":1==s.status?s.showState="审核通过":2==s.status?s.showState="审核驳回":s.showState="已委托"}))})).catch((s=>{}))},getDel(s){if(1==this.identity){let a={demandId:s,pageSize:1e3,pageNum:1};this.$api.demandListAPI({data:a,method:"get"}).then((a=>{a.result.records.map((a=>{a.id==s&&(this.dataInfo=a)}))})).catch((s=>{}))}else{let a={demandId:s,pageSize:1e3,pageNum:1};this.showenterpriseNote?this.$api.demandmineAPI({data:a,method:"get"}).then((a=>{a.result.records.map((a=>{a.id==s&&(this.dataInfo=a)}))})).catch((s=>{})):this.$api.workerdemandListAPI({data:a,method:"get"}).then((a=>{a.result.records.map((a=>{a.id==s&&(this.dataInfo=a)}))})).catch((s=>{}))}},chargebackFn(s){var a;null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"showAlert",value:{RNMethodName:"demandCancelPost",data:s},alertName:"撤销",title:"执行撤销后将不能收到该需求相关推荐企业反馈信息，是否继续撤销"}))}}},[["render",function(s,e,t,i,p,m){var v;const u=I,w=f("revocation"),b=f("processfeedback"),g=f("feedback");return a(),d("div",null,[l("div",{class:"box33"},[l("div",{class:"headText"}," 需求信息 "),l("div",{class:"attDelBox"},[l("div",{class:"row"},[l("span",{class:"key"},"招商需求方："),l("span",{class:"value"},o(p.dataInfo.demander),1)]),l("div",{class:"row"},[l("span",{class:"key"},"招商需求描述："),l("span",{class:"value"},o(p.dataInfo.demandDescribe),1)]),l("div",{class:"row"},[l("span",{class:"key"},"招商需求所需产业链："),p.dataInfo.chainNames?(a(),d("span",{key:0,class:"value"},o(p.dataInfo.chainNames.join("、")),1)):r("",!0)]),l("div",{class:"row"},[l("span",{class:"key"},"招商需求企业所属区域："),p.dataInfo.addressName?(a(),d("span",{key:0,class:"value"},o(p.dataInfo.addressName.join("、")),1)):r("",!0)]),l("div",{class:"row"},[l("span",{class:"key"},"招商需求企业类型："),p.dataInfo.enterpriseType?(a(),d("span",{key:0,class:"value"},o(p.dataInfo.enterpriseType.join("、")),1)):r("",!0)]),l("div",{class:"row"},[l("span",{class:"key"},"招商需求融资阶段："),p.dataInfo.enterpriseFinance?(a(),d("span",{key:0,class:"value"},o(p.dataInfo.enterpriseFinance.join("、")),1)):r("",!0)])]),1==p.identity&&0!=p.dataInfo.recommendCount?(a(),d("div",{key:0,onClick:e[0]||(e[0]=s=>m.feedbackList()),class:"feedback"},"反馈列表")):r("",!0),1==p.identity&&0==p.dataInfo.recommendCount?(a(),d("div",{key:1,style:{opacity:"0.5"},class:"feedback"},"反馈列表")):r("",!0),2==p.identity&&0!=(null==(v=p.informationList)?void 0:v.length)?(a(),d("div",{key:2,class:"headText"},"反馈信息")):r("",!0),(a(!0),d(y,null,k(p.informationList,((s,e)=>(a(),d("div",{key:e,class:"feedbackItem"},[l("div",{class:"stateText"},o(s.showState),1),l("div",{class:"row"},[l("span",{class:"key"},"反馈时间："),l("span",{class:"value"},o(s.gmtCreate),1)]),l("div",{class:"row"},[l("span",{class:"key"},"推荐企业："),l("span",{class:"value"},o(s.enterpriseName),1)]),l("div",{class:"row"},[l("span",{class:"key"},"统一社会信用代码："),l("span",{class:"value"},o(s.enterpriseUniCode),1)]),l("div",{class:"row"},[l("span",{class:"key"},"推荐理由："),l("span",{class:"value"},o(s.reason),1)]),l("div",{onClick:a=>m.flowDel(s),class:"flow"},[c("流程查看"),n(u,{src:x,class:"righticon"})],8,["onClick"])])))),128)),l("div",{style:{height:"150rpx"}})]),1==p.identity?(a(),d("div",{key:0,class:"belowBtns"},[p.dataInfo.cancel?(a(),d("div",{key:1,class:"T4BtnDe",style:{color:"#B7BFC7"}}," 已撤销 ")):(a(),d("div",{key:0,onClick:e[1]||(e[1]=h((s=>m.chargebackFn(p.dataInfo)),["stop"])),class:"T4BtnDe"}," 撤销 "))])):r("",!0),2==p.identity?(a(),d("div",{key:1,class:"belowBtns"},[l("div",{onClick:e[2]||(e[2]=s=>m.feedbackFn(p.dataInfo)),class:"T4BtnDe"},o(p.dataInfo.isRecommend?"继续反馈":"反馈"),1)])):r("",!0),n(w,{onUpdataList:m.updataList,ref:"revocation"},null,8,["onUpdataList"]),n(b,{ref:"processfeedback"},null,512),n(g,{onUpdataList:m.updataList,ref:"feedback"},null,8,["onUpdataList"])])}],["__scopeId","data-v-e4eab704"]]);export{j as default};
