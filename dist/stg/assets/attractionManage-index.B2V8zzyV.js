import{z as t,o as e,c as i,w as a,f as o,B as r,t as n,a as l,F as s,b as c,d as h,e as d,i as p,l as x,p as u,V as g,n as f,s as y,A as m,g as v,D as b,C,S,H as w,I as L,ac as T,aF as A,aL as P,aM as _}from"./index-HcMwrp5e.js";import{_ as k}from"./uni-popup.BdZPMDVN.js";import{r as F}from"./uni-app.es.DFp0WTX7.js";import{_ as M}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as D,a as I}from"./uni-forms.i1XM9iHP.js";import{_ as B}from"./uni-data-select.CGMq-mdz.js";import{_ as z}from"./uni-easyinput.CAhWtyu2.js";import{a as O,_ as N}from"./error.DWcL5XfB.js";import{l as R,g as W}from"./utils.61Hi-B7M.js";import{a as E}from"./alertPop.EfFZ3jW0.js";import{_ as Q}from"./uni-data-checkbox.CvA0WHPC.js";import{_ as Z}from"./enterprise.BFmTa1IP.js";import{_ as V}from"./ic_sea_location.iElRY131.js";import{_ as G}from"./v-tabs.BQ4gzth-.js";import{_ as j}from"./uni-datetime-picker.DDEPfKMC.js";import{t as $}from"./index.B6Rbewql.js";import"./uni-transition.CHOJlBbg.js";import"./uni-cloud.es.C7bj1nlK.js";import"./uni-icons.B5Z3RbO2.js";import"./uni-load-more.Dz9gLz3r.js";import"./returnFn.BYkANsDr.js";const X=M({props:{moreScreenList:{type:Array,default:()=>[]},title:{type:String,default:"筛选"},MultipleChoice:{type:Boolean,default:!1},safearea:{type:Boolean,default:!1},BottomBlank:{type:Boolean,default:!1},appletTab:{type:Boolean,default:!1},exchangeRate:{type:String,default:""}},watch:{moreScreenList:{handler(t){this.form=t.reduce(((t,e)=>(this.MultipleChoice?t[e.paramKey]="":t[e.paramKey]=[],t)),{})},deep:!0}},data:()=>({oleForm:{},form:{},submitLoading:!1}),methods:{changeOption(t,e){if(this.MultipleChoice)this.form[e]==t?this.form[e]="":this.form[e]=t;else if(""==t)this.form[e]=[];else{const i=this.form[e].indexOf(t);i>-1?this.form[e].splice(i,1):this.form[e].push(t)}},opens(){this.$refs.follow.open("bottom")},entrustCloseFn(){this.$refs.follow.close()},claimFn(){this.form=this.moreScreenList.reduce(((t,e)=>(this.MultipleChoice?t[e.paramKey]="":t[e.paramKey]=[],t)),{})},submit(){this.$emit("updatamoreScreen",this.form),this.entrustCloseFn()}}},[["render",function(x,u,g,f,y,m){const v=F(t("uni-popup"),k),b=p;return e(),i(b,null,{default:a((()=>[o(v,{ref:"follow","background-color":"#fff","safe-area":g.safearea,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},n(g.title),1),r("div",{class:"filtrate"},[(e(!0),l(s,null,c(g.moreScreenList,((t,i)=>{var a;return e(),l("div",{key:i},[0!=t.paramValueList.length?(e(),l("div",{key:0,class:"SelectTitle"},n(t.paramName),1)):h("",!0),0!=t.paramValueList.length?(e(),l("div",{key:1,class:"filtrateList"},[(null==t?void 0:t.unlimited)?(e(),l("div",{key:0,onClick:e=>m.changeOption("",t.paramKey),class:d(0==(null==(a=y.form[t.paramKey])?void 0:a.length)?"filtrateItems":"filtrateItem")}," 不限 ",10,["onClick"])):h("",!0),(e(!0),l(s,null,c(t.paramValueList,((a,o)=>{var r;return e(),l("div",{key:i,onClick:e=>m.changeOption(a,t.paramKey),class:d(g.MultipleChoice?y.form[t.paramKey]===a?"filtrateItems":"filtrateItem":(null==(r=y.form[t.paramKey])?void 0:r.includes(a))?"filtrateItems":"filtrateItem")},n(a.key),11,["onClick"])})),128))])):h("",!0)])})),128))]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:u[0]||(u[0]=(...t)=>m.claimFn&&m.claimFn(...t))},"重置"),r("div",{class:"submit",onClick:u[1]||(u[1]=(...t)=>m.submit&&m.submit(...t))},"确认")]),g.BottomBlank?(e(),l("div",{key:0,style:{height:"160rpx"}})):h("",!0)])])),_:1},8,["safe-area"])])),_:1})}],["__scopeId","data-v-0a3ff6f1"]]);const H="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_549_42403'%3e%3crect%20x='0'%20y='0'%20width='20'%20height='20'%20rx='0'/%3e%3c/clipPath%3e%3clinearGradient%20x1='0.3076922595500946'%20y1='-1.399999737739563'%20x2='0.42443391378757683'%20y2='1.4449410628390016'%20id='master_svg1_549_36254'%3e%3cstop%20offset='0%25'%20stop-color='%23CBDAFF'%20stop-opacity='1'/%3e%3cstop%20offset='100%25'%20stop-color='%234D82FF'%20stop-opacity='1'/%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_549_42403)'%3e%3cg%3e%3cpath%20d='M10.30047,17.9999C9.934989999999999,18.0001,9.63861,17.6982,9.63861,17.3258L9.63861,9.688880000000001C9.640329999999999,9.53068,9.69631,9.37774,9.79763,9.25769L12.906,5.3482400000000005L4.13521,5.3482400000000005L7.37,9.24404C7.47372,9.36794,7.5304,9.52613,7.52868,9.688880000000001L7.52868,14.5153L8.29599,15.1285C8.59468,15.3593,8.64565,15.7967,8.4083,16.092399999999998C8.298390000000001,16.2359,8.13628,16.3283,7.95836,16.3482C7.78108,16.371299999999998,7.60218,16.3203,7.46239,16.2068L6.43713,15.3844C6.27327,15.2556,6.17796,15.0562,6.17953,14.8454L6.17953,9.94508L2.157168,5.13264C1.99203056,4.925737,1.9537546,4.642323,2.0579056,4.397653C2.164724,4.156157,2.400688,4.000758675,2.66035,4.0000586892L14.3218,4.0000586892C14.5828,3.99655873,14.8209,4.149857,14.9304,4.3910029999999995C15.0482,4.631533,15.0144,4.920354,14.8446,5.12599L10.9747,9.931429999999999L10.9747,17.3461C10.96385,17.7155,10.6631,18.0071,10.30047,17.9999Z'%20fill='%233F4A59'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M17.39738,17L13.589501,17C13.263928,17,13,16.66015,13,16.24093C13,15.82171,13.263928,15.481860000000001,13.589501,15.481860000000001L17.39707,15.481860000000001C17.72265,15.481860000000001,17.98657,15.82171,17.98657,16.24093C17.98657,16.66015,17.72265,17,17.39707,17L17.39738,17ZM17.39738,13.79701L13.589501,13.79701C13.263928,13.79701,13,13.45716,13,13.037939999999999C13,12.61871,13.263928,12.27887,13.589501,12.27887L17.39707,12.27887C17.72265,12.27887,17.98657,12.61871,17.98657,13.037939999999999C17.98657,13.45716,17.72265,13.79701,17.39707,13.79701L17.39738,13.79701ZM17.39738,10.517949999999999L13.589501,10.517949999999999C13.269243,10.50855,13.0134259,10.17156,13.0134259,9.759075C13.0134259,9.346588,13.269243,9.00959577,13.589501,9.00020115L17.39707,9.00020115C17.72782,8.99049893,18,9.333083,18,9.759075C18,10.18507,17.72782,10.52765,17.39707,10.517949999999999L17.39738,10.517949999999999Z'%20fill='%233370FF'%20fill-opacity='1'/%3e%3cpath%20d='M17.39738,17L13.589501,17C13.263928,17,13,16.66015,13,16.24093C13,15.82171,13.263928,15.481860000000001,13.589501,15.481860000000001L17.39707,15.481860000000001C17.72265,15.481860000000001,17.98657,15.82171,17.98657,16.24093C17.98657,16.66015,17.72265,17,17.39707,17L17.39738,17ZM17.39738,13.79701L13.589501,13.79701C13.263928,13.79701,13,13.45716,13,13.037939999999999C13,12.61871,13.263928,12.27887,13.589501,12.27887L17.39707,12.27887C17.72265,12.27887,17.98657,12.61871,17.98657,13.037939999999999C17.98657,13.45716,17.72265,13.79701,17.39707,13.79701L17.39738,13.79701ZM17.39738,10.517949999999999L13.589501,10.517949999999999C13.269243,10.50855,13.0134259,10.17156,13.0134259,9.759075C13.0134259,9.346588,13.269243,9.00959577,13.589501,9.00020115L17.39707,9.00020115C17.72782,8.99049893,18,9.333083,18,9.759075C18,10.18507,17.72782,10.52765,17.39707,10.517949999999999L17.39738,10.517949999999999Z'%20fill='url(%23master_svg1_549_36254)'%20fill-opacity='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",U="https://staticstg.idicc.cn/static/wechatai/assets/addBySelf-Bw-1MguU.png";const q=M({props:{isZhipai:{type:Boolean,default:!1}},components:{filterView:X,clue:M({data:()=>({porpsData:"",safeArea:x("safeAreaRpx"),form:{note:"",pass:""},name:"",submitLoading:!1,rules:{note:{rules:[{required:!0,errorMessage:"请输入线索流转备注"}]}},personList:[]}),methods:{opens(t){this.porpsData=t,this.name=t.enterpriseName,this.$api.listAllChildUserAPI_investment({method:"GET"}).then((t=>{t.result&&t.result.length?(this.personList=t.result.map((t=>(t.text=t.key,t))),this.form.pass=this.personList[0].value):(u({title:"无可选的招商经理",icon:"none"}),this.claimFn())})),this.$refs.follow.open("bottom")},claimFn(){this.form={note:"",pass:""},this.$refs.follow.close(),this.$emit("updataTab",!0)},submit(){this.submitLoading||(this.submitLoading=!0,this.$refs.valiForm.validate().then((t=>{let e={clueId:this.porpsData.id,assignUserId:this.form.pass,assignUserName:this.personList.find((t=>t.value==this.form.pass)).key,remark:this.form.note};this.$api.changeAssignPersonAPI_investment({method:"post",data:e}).then((t=>{this.submitLoading=!1,this.claimFn(),u({title:"指派成功!",icon:"none"}),setTimeout((()=>{this.$emit("updataList")}),1500)})).catch((t=>{this.submitLoading=!1}))})).catch((t=>{this.submitLoading=!1})))}}},[["render",function(s,c,d,x,u,y){const m=F(t("uni-forms-item"),D),v=F(t("uni-data-select"),B),b=F(t("uni-easyinput"),z),C=F(t("uni-forms"),I),S=F(t("uni-popup"),k),w=p;return e(),i(w,null,{default:a((()=>[(e(),i(g,{to:"body"},[o(S,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},"线索指派"),r("div",{class:"formItem"},[o(C,{ref:"valiForm",rules:u.rules,modelValue:u.form,"label-position":"top","label-width":"100"},{default:a((()=>[o(m,{label:"公司名称",required:!0},{default:a((()=>[r("div",{class:"text"},n(u.name),1)])),_:1}),o(m,{label:"招商经理",required:!0},{default:a((()=>[o(v,{localdata:u.personList,label:"招商经理",modelValue:u.form.pass,"onUpdate:modelValue":c[0]||(c[0]=t=>u.form.pass=t),clear:!1},null,8,["localdata","modelValue"])])),_:1}),o(m,{name:"note",label:"线索流转备注",required:!0},{default:a((()=>[r("div",{class:"summarizeInput"},[o(b,{type:"textarea",trim:"all",clearable:!1,modelValue:u.form.note,"onUpdate:modelValue":c[1]||(c[1]=t=>u.form.note=t),placeholder:"请输入"},null,8,["modelValue"])])])),_:1})])),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:c[2]||(c[2]=(...t)=>y.claimFn&&y.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:c[3]||(c[3]=(...t)=>y.submit&&y.submit(...t))},"提交")]),r("div",{style:f({height:u.safeArea})},null,4),u.porpsData.bottom?(e(),l("div",{key:0,style:{height:"144rpx"}})):h("",!0)])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-9e307e63"]]),addFollow:O,remove:M({data:()=>({porpsData:"",loading:!1}),methods:{opens(t){this.porpsData=t,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){this.loading||(this.loading=!0,this.$api.removeEnterpriseAPI_investment({data:{id:this.porpsData.id},method:"get"}).then((t=>{this.loading=!1,u({title:"删除成功",icon:"none"}),setTimeout((()=>{this.$emit("updataList"),this.claimFn()}),500)})).catch((t=>{this.loading=!1})))}}},[["render",function(n,l,s,c,h,d){const x=F(t("uni-popup"),k),u=p;return e(),i(u,null,{default:a((()=>[(e(),i(g,{to:"body"},[o(x,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"p1"},"温馨提示"),r("div",{class:"p2"},"是否移除该意向企业？ "),r("div",{class:"popBtn"},[r("div",{onClick:l[0]||(l[0]=(...t)=>d.claimFn&&d.claimFn(...t)),class:"canleBt"}," 取消 "),r("div",{onClick:l[1]||(l[1]=(...t)=>d.retreat&&d.retreat(...t)),class:"affirm"}," 删除 ")])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-354dee66"]])},data:()=>({cluePop:!1,userId:"",bottomBlank:!1,moreData:{},moreScreenList:[],enterpriseList:[],listicon:R,token:"",pageSize:1e3,pageNum:1,total:0,status:"more",contentText:{contentdown:"点击或上拉加载更多",contentrefresh:"正在加载...",contentnomore:"没有更多数据了"}}),created(){this.bottomBlank=!0,W("token")&&y("token",W("token")),W("userId")&&y("userId",W("userId")),this.userId=x("userId"),this.token=x("token"),this.$api.getSearchParamAPI_investment({method:"GET"}).then((t=>{this.moreScreenList=t.result})),this.searchList()},methods:{RefreshPage(){this.$nextTick((()=>{this.searchList()}))},remove(t){this.$refs.remove.opens(t)},openAddFollow(t){0==t.clueDealState&&t.beAssignPersonId==this.userId&&this.$refs.addFollow.opens(t)},openClue(t){0==t.clueDealState&&(t.bottom=!0,this.cluePop=!0,this.$refs.clue.opens(t))},updataList(){this.cluePop=!1,this.searchList()},searchList(){let t={};t.clueSource=this.moreData.clueSource?[this.moreData.clueSource.value]:[],t.followers=this.moreData.followers?[this.moreData.followers.value]:[],t.clueDealState=this.moreData.clueDealState?[this.moreData.clueDealState.value]:[],t.entrustOrNot=this.moreData.entrustOrNot?[this.moreData.entrustOrNot.value]:[],t.intentionDate=this.moreData.intentionDate?[this.moreData.intentionDate.value]:[];this.$api.enterpriseListAPI_investment({data:{...t,pageSize:this.pageSize,pageNum:this.pageNum},method:"post"}).then((t=>{"SUCCESS"==t.code&&(this.enterpriseList=t.result.records,this.enterpriseList.forEach((t=>{let e=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===t.enterpriseIconLabelId&&(t.enterpriseIconLabelId=0),t.enterpriseLabelNames){let i=t.enterpriseLabelNames[0];i?e.includes(t.enterpriseLabelNames[0])?t.enterpriseIconLabelId=1:i.includes("独角兽")?t.enterpriseIconLabelId=2:i.includes("专精特新")?t.enterpriseIconLabelId=3:i.includes("隐形冠军")?t.enterpriseIconLabelId=4:i.includes("瞪羚")?t.enterpriseIconLabelId=5:i.includes("创新")?t.enterpriseIconLabelId=6:i.includes("技术先进")?t.enterpriseIconLabelId=7:i.includes("科技")?t.enterpriseIconLabelId=8:i.includes("雏鹰")?t.enterpriseIconLabelId=9:t.enterpriseIconLabelId=0:t.enterpriseIconLabelId=0}else t.enterpriseIconLabelId=0})),this.total=t.result.total)}))},gotoAdd(){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{},path:"addCompany"}))},screenFn(){this.$refs.filterView.opens()},updatamoreScreen(t){this.moreData=t,this.searchList()},getMaxLabelCount(t){let e=0,i=0;for(let a=0;a<t.length&&(e+=t[a].length,!(e>20));a++)i=a+1;return i},itemStyle(t){return!this.token&&t>1?"filter: blur(3px)":""},statementFn(t){var e;0!=t.clueDealState||t.entrustOrNot||null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{enterpriseName:t.enterpriseName,uniCode:t.uniCode,enterpriseId:t.enterpriseId},path:"investment"}))},exportFile(){this.$api.exportEnterpriseListAPI_investment({method:"get"}).then((t=>{t.result?this.downloadFn(t.result):u({icon:"none",mask:!0,title:"导出失败",duration:3e3})}))},downloadFn(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"exportFile",value:{name:"纳入意向企业列表",fileType:"xlsx",url:t}}))},async detailpage(t){var e;this.token&&(null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{item:JSON.stringify(t)},path:"targetDetail"})))},scrolltolower(){this.status}}},[["render",function(t,x,u,g,f,y){const w=p,L=C,T=S,A=m("filterView"),P=m("clue"),_=m("addFollow"),k=m("remove");return e(),l(s,null,[o(w,{class:"pageBox"},{default:a((()=>[o(w,{class:"header"},{default:a((()=>[o(w,{class:"countTxt"},{default:a((()=>[v("共有"),r("span",{class:"countNum"},n(f.total),1),v("家企业")])),_:1}),o(w,{class:"btns"},{default:a((()=>[o(w,{class:"btn"},{default:a((()=>[o(L,{class:"btnIcon",src:"https://staticstg.idicc.cn/static/wechatai/assets/icon_dc-BOB8ufb0.svg"}),o(w,{class:"btnTxt",onClick:y.exportFile},{default:a((()=>[v("导出")])),_:1},8,["onClick"])])),_:1}),o(w,{class:"btn"},{default:a((()=>[o(L,{class:"btnIcon",src:H}),o(w,{class:"btnTxt",onClick:y.screenFn},{default:a((()=>[v("筛选")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),o(T,{class:"historyList box",onScrolltolower:y.scrolltolower,"scroll-y":!f.cluePop},{default:a((()=>[(e(!0),l(s,null,c(f.enterpriseList,((t,p)=>(e(),i(w,{onClick:e=>y.detailpage(t),class:"firm",key:p},{default:a((()=>{var p;return[o(w,{class:"imgNameBox"},{default:a((()=>[r("img",{src:f.listicon[t.enterpriseIconLabelId].icon,alt:"",class:"iconImg"},null,8,["src"]),r("span",{class:"enterpriseName"},n(t.enterpriseName),1),null==t.clueDealState||0!=t.clueDealState||t.isHavingFollow||t.entrustOrNot?h("",!0):(e(),l("img",{key:0,src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_665_29214'%3e%3crect%20x='0'%20y='0'%20width='16'%20height='16'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_665_29214)'%3e%3cg%3e%3cpath%20d='M5.5,8Q5.5,8.09849,5.48079,8.19509Q5.46157,8.29169,5.4238800000000005,8.38268Q5.38619,8.47368,5.3314699999999995,8.55557Q5.27675,8.63746,5.20711,8.70711Q5.13746,8.77675,5.0555699999999995,8.83147Q4.97368,8.88619,4.88268,8.92388Q4.79169,8.96157,4.69509,8.98079Q4.59849,9,4.5,9Q4.401509,9,4.30491,8.98079Q4.208311,8.96157,4.117316,8.92388Q4.026322,8.88619,3.94443,8.83147Q3.862537,8.77675,3.792893,8.70711Q3.723249,8.63746,3.66853,8.55557Q3.613811,8.47368,3.5761205,8.38268Q3.5384294,8.29169,3.5192147,8.19509Q3.5,8.09849,3.5,8Q3.5,7.901509,3.5192147,7.80491Q3.5384294,7.708311,3.5761205,7.617316Q3.613811,7.526322,3.66853,7.44443Q3.723249,7.362537,3.792893,7.292893Q3.862537,7.223249,3.94443,7.16853Q4.026322,7.113811,4.117316,7.0761205Q4.208311,7.0384294,4.30491,7.0192147Q4.401509,7,4.5,7Q4.59849,7,4.69509,7.0192147Q4.79169,7.0384294,4.88268,7.0761205Q4.97368,7.113811,5.0555699999999995,7.16853Q5.13746,7.223249,5.20711,7.292893Q5.27675,7.362537,5.3314699999999995,7.44443Q5.38619,7.526322,5.4238800000000005,7.617316Q5.46157,7.708311,5.48079,7.80491Q5.5,7.901509,5.5,8Z'%20fill='%23999999'%20fill-opacity='1'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M9,8Q9,8.09849,8.98079,8.19509Q8.96157,8.29169,8.92388,8.38268Q8.88619,8.47368,8.83147,8.55557Q8.77675,8.63746,8.70711,8.70711Q8.63746,8.77675,8.55557,8.83147Q8.47368,8.88619,8.38268,8.92388Q8.29169,8.96157,8.19509,8.98079Q8.09849,9,8,9Q7.901509,9,7.80491,8.98079Q7.708311,8.96157,7.617316,8.92388Q7.526322,8.88619,7.44443,8.83147Q7.362537,8.77675,7.292893,8.70711Q7.223249,8.63746,7.16853,8.55557Q7.113811,8.47368,7.0761205,8.38268Q7.0384294,8.29169,7.0192147,8.19509Q7,8.09849,7,8Q7,7.901509,7.0192147,7.80491Q7.0384294,7.708311,7.0761205,7.617316Q7.113811,7.526322,7.16853,7.44443Q7.223249,7.362537,7.292893,7.292893Q7.362537,7.223249,7.44443,7.16853Q7.526322,7.113811,7.617316,7.0761205Q7.708311,7.0384294,7.80491,7.0192147Q7.901509,7,8,7Q8.09849,7,8.19509,7.0192147Q8.29169,7.0384294,8.38268,7.0761205Q8.47368,7.113811,8.55557,7.16853Q8.63746,7.223249,8.70711,7.292893Q8.77675,7.362537,8.83147,7.44443Q8.88619,7.526322,8.92388,7.617316Q8.96157,7.708311,8.98079,7.80491Q9,7.901509,9,8Z'%20fill='%23999999'%20fill-opacity='1'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M12.5,8Q12.5,8.09849,12.48079,8.19509Q12.46157,8.29169,12.42388,8.38268Q12.38619,8.47368,12.33147,8.55557Q12.27675,8.63746,12.20711,8.70711Q12.13746,8.77675,12.05557,8.83147Q11.97368,8.88619,11.88268,8.92388Q11.79169,8.96157,11.69509,8.98079Q11.59849,9,11.5,9Q11.401509,9,11.30491,8.98079Q11.208311,8.96157,11.117316,8.92388Q11.026322,8.88619,10.94443,8.83147Q10.862537,8.77675,10.792893,8.70711Q10.723249,8.63746,10.66853,8.55557Q10.613811,8.47368,10.5761205,8.38268Q10.5384294,8.29169,10.5192147,8.19509Q10.5,8.09849,10.5,8Q10.5,7.901509,10.5192147,7.80491Q10.5384294,7.708311,10.5761205,7.617316Q10.613811,7.526322,10.66853,7.44443Q10.723249,7.362537,10.792893,7.292893Q10.862537,7.223249,10.94443,7.16853Q11.026322,7.113811,11.117316,7.0761205Q11.208311,7.0384294,11.30491,7.0192147Q11.401509,7,11.5,7Q11.59849,7,11.69509,7.0192147Q11.79169,7.0384294,11.88268,7.0761205Q11.97368,7.113811,12.05557,7.16853Q12.13746,7.223249,12.20711,7.292893Q12.27675,7.362537,12.33147,7.44443Q12.38619,7.526322,12.42388,7.617316Q12.46157,7.708311,12.48079,7.80491Q12.5,7.901509,12.5,8Z'%20fill='%23999999'%20fill-opacity='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",alt:"",class:"iconImg",onClick:b((e=>y.remove(t)),["stop"])},null,8,["onClick"]))])),_:2},1024),(null==(p=t.enterpriseLabelNames)?void 0:p.length)?(e(),i(w,{key:0,class:"enterpriseLabeltag"},{default:a((()=>[t.enterpriseLabelNames?(e(),i(w,{key:0,class:"enterpriseLabel"},{default:a((()=>[(e(!0),l(s,null,c(t.enterpriseLabelNames.slice(0,y.getMaxLabelCount(t.enterpriseLabelNames)),((t,o)=>(e(),i(w,{key:o},{default:a((()=>[r("div",{class:"tagBox"},n(t),1)])),_:2},1024)))),128)),t.enterpriseLabelNames.length>y.getMaxLabelCount(t.enterpriseLabelNames)?(e(),l("div",{key:0,class:"tagBox"}," ... ")):h("",!0)])),_:2},1024)):h("",!0)])),_:2},1024)):h("",!0),o(w,{class:"tagView2"},{default:a((()=>[2==t.clueDealState?(e(),l("div",{key:0,class:"enterpriseTag fail"}," 签约失败 ")):h("",!0),1==t.clueDealState?(e(),l("div",{key:1,class:"enterpriseTag success"}," 签约成功 ")):h("",!0),null!=t.clueDealState&&0==t.clueDealState?(e(),l("div",{key:2,class:"enterpriseTag ing"}," 跟进中 ")):h("",!0),t.isNotFollowMonth?(e(),i(w,{key:3,class:"errorView"},{default:a((()=>[o(L,{class:"icon",src:N}),r("span",{class:"txt"},"近一个月未跟进")])),_:1})):h("",!0)])),_:2},1024),o(w,{class:"texts"},{default:a((()=>[o(w,{class:"ListText"},{default:a((()=>[v(" 企业来源："+n(2==t.clueSource?"自行添加":"系统推荐"),1)])),_:2},1024),o(w,{class:"ListText"},{default:a((()=>[v(" 纳入意向日期："+n((null==t?void 0:t.intentionDate)||"--"),1)])),_:2},1024),o(w,{class:"ListText"},{default:a((()=>[v(" 纳入意向人："+n((null==t?void 0:t.intentionPerson)||"--"),1)])),_:2},1024),o(w,{class:"ListText"},{default:a((()=>[v(" 跟进人："+n((null==t?void 0:t.beAssignPerson)||"--"),1)])),_:2},1024)])),_:2},1024),r("div",{class:"btnView"},[u.isZhipai?(e(),l("div",{key:0,onClick:b((e=>y.openClue(t)),["stop"]),class:d(["T4BtnDe",0!=t.clueDealState?"T4BtnDis":""])}," 线索指派 ",10,["onClick"])):h("",!0),r("div",{onClick:b((e=>y.statementFn(t)),["stop"]),class:d(["T4BtnDe",0!=t.clueDealState||t.entrustOrNot?"T4BtnDis":""])},n(t.entrustOrNot?"已委托":"委托招商"),11,["onClick"]),r("div",{onClick:b((e=>y.openAddFollow(t)),["stop"]),class:d(["T4BtnDe",0!=t.clueDealState||t.beAssignPersonId!=f.userId?"T4BtnDis":""])}," 添加跟进记录 ",10,["onClick"])])]})),_:2},1032,["onClick"])))),128)),o(w,{style:{"min-height":"120rpx"}}),0==f.enterpriseList.length?(e(),i(w,{key:0,class:"nodatabox"},{default:a((()=>[o(L,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])),_:1})):h("",!0)])),_:1},8,["onScrolltolower","scroll-y"]),o(A,{BottomBlank:!0,appletTab:!0,ref:"filterView",title:"筛选",moreScreenList:f.moreScreenList,onUpdatamoreScreen:y.updatamoreScreen,MultipleChoice:!0},null,8,["moreScreenList","onUpdatamoreScreen"]),o(P,{onUpdataList:y.updataList,BottomBlank:!0,appletTab:!0,ref:"clue"},null,8,["onUpdataList"]),o(_,{onUpdataList:y.updataList,BottomBlank:!0,appletTab:!0,ref:"addFollow"},null,8,["onUpdataList"]),o(k,{onUpdataList:y.updataList,ref:"remove"},null,8,["onUpdataList"])])),_:1}),r("img",{src:U,alt:"",class:"addButton",onClick:x[0]||(x[0]=(...t)=>y.gotoAdd&&y.gotoAdd(...t))})],64)}],["__scopeId","data-v-816031dd"]]);const Y=M({components:{filterView:X,alertPop:E,chargeBack:M({data:()=>({porpsData:"",safeArea:x("safeAreaRpx"),form:{auditNote:"",status:1},submitLoading:!1,rules:{},personList:[{text:"通过",value:1},{text:"驳回",value:2}]}),methods:{opens(t){this.porpsData=t,this.name=t.enterpriseName,this.$refs.follow.open("bottom")},claimFn(){this.form={auditNote:"",status:1},this.$refs.follow.close()},submit(){this.submitLoading||(this.form.auditNote||2!=this.form.status?(this.submitLoading=!0,this.$refs.valiForm.validate().then((t=>{let e={entrustId:this.porpsData.entrustId,auditNote:this.form.auditNote,status:this.form.status};this.$api.auditApplyAPI_entrust({method:"post",data:e}).then((t=>{this.submitLoading=!1,this.claimFn(),u({title:"审核成功",icon:"none"}),setTimeout((()=>{this.$emit("updataList")}),1500)})).catch((t=>{this.submitLoading=!1}))})).catch((t=>{this.submitLoading=!1}))):u({title:"请输入审批意见",icon:"none"}))}}},[["render",function(n,l,s,c,h,d){const x=F(t("uni-data-checkbox"),Q),u=F(t("uni-forms-item"),D),f=F(t("uni-easyinput"),z),y=F(t("uni-forms"),I),m=F(t("uni-popup"),k),v=p;return e(),i(v,null,{default:a((()=>[(e(),i(g,{to:"body"},[o(m,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},"审批"),r("div",{class:"formItem"},[o(y,{ref:"valiForm",rules:h.rules,modelValue:h.form,"label-position":"top","label-width":"100"},{default:a((()=>[o(u,{label:"是否通过",required:!0},{default:a((()=>[o(x,{modelValue:h.form.status,"onUpdate:modelValue":l[0]||(l[0]=t=>h.form.status=t),localdata:h.personList},null,8,["modelValue","localdata"])])),_:1}),o(u,{name:"auditNote",label:"审批意见",required:2==h.form.status},{default:a((()=>[r("div",{class:"summarizeInput"},[o(f,{type:"textarea",trim:"all",clearable:!1,modelValue:h.form.auditNote,"onUpdate:modelValue":l[1]||(l[1]=t=>h.form.auditNote=t),placeholder:"请输入"},null,8,["modelValue"])])])),_:1},8,["required"])])),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:l[2]||(l[2]=(...t)=>d.claimFn&&d.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:l[3]||(l[3]=(...t)=>d.submit&&d.submit(...t))},"提交")]),r("div",{style:{height:"160rpx"}})])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-0eb9091d"]])},props:{identity:{type:Number,default:1}},data:()=>({bottomBlank:!1,moreData:null,pageSize:1e3,pageNum:1,moreScreenList:[],total:0,enterpriseList:[]}),created(){this.moreScreenList=[],this.token=x("token"),this.bottomBlank=!0,this.searchList(),setTimeout((()=>{this.moreScreenList=[{paramName:"审批状态",paramKey:"status",unlimited:!0,paramValueList:[{key:"待审批",value:0},{key:"审批通过",value:1},{key:"审批驳回",value:2}]}]}),10)},methods:{scrolltolower(){this.status},searchList(){let t={pageSize:this.pageSize,pageNum:this.pageNum};this.moreData&&(this.moreData.status&&0==this.moreData.status.value?t.status=0:t.status=this.moreData.status.value||""),this.$api.listApplyAPI_entrust({data:t,method:"get"}).then((t=>{"SUCCESS"==t.code&&(this.enterpriseList=t.result.records,this.total=t.result.total)}))},updataList(){this.searchList()},chargebackFn(t){this.$api.checkApplyAPI_entrust({data:{entrustId:t.entrustId},method:"get"}).then((e=>{var i,a;"SUCCESS"==e.code&&(e.result?null==(a=null==(i=this.$refs)?void 0:i.chargeBack)||a.opens(t):this.$refs.alertPop.opens({title:"当前您所在机构余额不足，请联系机构负责人充值。"}))}))},screenFn(){this.$refs.filterView.opens()},updatamoreScreen(t){this.moreData=t,this.searchList()},updataList(){this.searchList()},stateShow:t=>({0:"待审批",1:"审批通过",2:"审批驳回"}[t]||"未知状态"),attractDel(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{id:t.entrustId},path:"chargeBackDetail"}))}}},[["render",function(t,x,u,g,f,y){const w=p,L=C,T=S,A=m("filterView"),P=m("alertPop"),_=m("chargeBack");return e(),l("div",{class:"pageBox"},[o(w,{class:"header"},{default:a((()=>[o(w,{class:"countTxt"},{default:a((()=>[v("共有"),r("span",{class:"countNum"},n(f.total),1),v("条申请")])),_:1}),o(w,{class:"btns"},{default:a((()=>[o(w,{class:"btn"},{default:a((()=>[o(L,{class:"btnIcon",src:H}),o(w,{class:"btnTxt",onClick:y.screenFn},{default:a((()=>[v("筛选")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),o(T,{class:"historyList box",onScrolltolower:y.scrolltolower,"scroll-y":"true"},{default:a((()=>[(e(!0),l(s,null,c(f.enterpriseList,((t,i)=>(e(),l("div",{onClick:e=>y.attractDel(t),class:"myentrustListClass",key:t.entrustId},[r("div",{class:"t1"},[r("div",{class:"startTime"},"申请时间："+n(t.applyDatetime),1),r("div",{class:d(["myListstate",2==t.status?"fail":1==t.status?"success":""])},n(y.stateShow(t.status)),3)]),r("div",{class:"t2"},[r("div",{class:"enterprise"},[o(L,{class:"enterpriseIcon",src:Z}),r("span",{class:"enterpriseText"},n(t.enterprise),1)]),r("div",null,[r("span",{class:"currency"},"￥"),r("span",{class:"amount"},n(t.amount),1)])]),r("div",{class:"t3"},[r("div",{class:"line"},"统一社会信用代码："+n(t.enterpriseUniCode),1),r("div",{class:"line"},"期望对接时间："+n(t.exceptedDatetime),1)]),t.isCanAudit?(e(),l("div",{key:0,class:"btnBox"},[r("div",{onClick:b((e=>y.chargebackFn(t)),["stop"]),class:"T4Btn"}," 审批 ",8,["onClick"])])):h("",!0)],8,["onClick"])))),128)),o(w,{style:{"min-height":"120rpx"}}),0==f.enterpriseList.length?(e(),i(w,{key:0,class:"nodatabox"},{default:a((()=>[o(L,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])),_:1})):h("",!0)])),_:1},8,["onScrolltolower"]),o(A,{BottomBlank:!0,appletTab:!0,ref:"filterView",title:"筛选",moreScreenList:f.moreScreenList,onUpdatamoreScreen:y.updatamoreScreen,MultipleChoice:!0},null,8,["moreScreenList","onUpdatamoreScreen"]),o(P,{ref:"alertPop"},null,512),o(_,{BottomBlank:!0,appletTab:!0,onUpdataList:y.updataList,ref:"chargeBack"},null,8,["onUpdataList"])])}],["__scopeId","data-v-1a85965a"]]);const K=M({components:{filterView:X},props:{identity:{type:Number,default:1}},data:()=>({bottomBlank:!1,moreScreenList:null,enterpriseList:[],pageSize:1e3,pageNum:1,total:0,listicon:R,moreData:""}),created(){this.moreScreenList=[],this.token=x("token"),this.bottomBlank=!0,this.searchList(),setTimeout((()=>{this.moreScreenList=[{paramName:"招商意向状态",paramKey:"isClue",unlimited:!0,paramValueList:[{key:"未纳入",value:"0"},{key:"已纳入",value:"1"}]},{paramName:"收藏日期",paramKey:"startTime",unlimited:!0,paramValueList:[{key:"近一周",value:this.getDay(-7)},{key:"近一月",value:this.getDay(-30)},{key:"近半年",value:this.getDay(-180)}]}]}),10)},methods:{RefreshPage(){this.$nextTick((()=>{this.searchList()}))},searchList(){let t={pageSize:this.pageSize,pageNum:this.pageNum};this.moreData&&(t.isClue=this.moreData.isClue.value||"",t.startTime=this.moreData.startTime.value||""),this.$api.listCollectionAPI_investment({data:t,method:"get"}).then((t=>{"SUCCESS"==t.code&&(this.enterpriseList=t.result.records,this.enterpriseList.forEach((t=>{let e=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===t.enterpriseIconLabelId&&(t.enterpriseIconLabelId=0),t.enterpriseLabelNames){let i=t.enterpriseLabelNames[0];i?e.includes(t.enterpriseLabelNames[0])?t.enterpriseIconLabelId=1:i.includes("独角兽")?t.enterpriseIconLabelId=2:i.includes("专精特新")?t.enterpriseIconLabelId=3:i.includes("隐形冠军")?t.enterpriseIconLabelId=4:i.includes("瞪羚")?t.enterpriseIconLabelId=5:i.includes("创新")?t.enterpriseIconLabelId=6:i.includes("技术先进")?t.enterpriseIconLabelId=7:i.includes("科技")?t.enterpriseIconLabelId=8:i.includes("雏鹰")?t.enterpriseIconLabelId=9:t.enterpriseIconLabelId=0:t.enterpriseIconLabelId=0}else t.enterpriseIconLabelId=0})),this.total=t.result.total)}))},screenFn(){this.$refs.filterView.opens()},getDay(t){let e=new Date,i=e.getTime()+864e5*t;e.setTime(i);let a=e.getFullYear(),o=e.getMonth(),r=e.getDate(),n=this.doHandleMonth(e.getHours()),l=this.doHandleMonth(e.getMinutes()),s=this.doHandleMonth(e.getSeconds());return o=this.doHandleMonth(o+1),r=this.doHandleMonth(r),a+"-"+o+"-"+r+" "+n+":"+l+":"+s},doHandleMonth(t){var e=t;return 1==t.toString().length&&(e="0"+t),e},updatamoreScreen(t){this.moreData=t,this.searchList()},getMaxLabelCount(t){let e=0,i=0;for(let a=0;a<t.length&&(e+=t[a].length,!(e>20));a++)i=a+1;return i},updataList(){this.searchList()},stateShow:t=>({0:"待审批",1:"审批通过",2:"审批驳回"}[t]||"未知状态"),isCollectionClick(t){this.$api.collectionAPI_investment({data:{enterpriseUniCode:t.enterpriseUniCode,status:0},method:"post"}).then((t=>{"SUCCESS"==t.code&&(u({title:"已取消收藏",icon:"none"}),this.updataList())}))},attractDel(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{id:t.enterpriseId||t.id,iconTypeid:t.enterpriseIconLabelId,enterpriseName:t.enterpriseName},path:"industryDetail"}))},scrolltolower(){this.status}}},[["render",function(t,d,x,u,g,f){const y=p,w=C,L=S,T=m("filterView");return e(),l("div",{class:"pageBox"},[o(y,{class:"header"},{default:a((()=>[o(y,{class:"countTxt"},{default:a((()=>[v("共有"),r("span",{class:"countNum"},n(g.total),1),v("家企业")])),_:1}),o(y,{class:"btns"},{default:a((()=>[o(y,{class:"btn"},{default:a((()=>[o(w,{class:"btnIcon",src:H}),o(y,{class:"btnTxt",onClick:f.screenFn},{default:a((()=>[v("筛选")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),o(L,{class:"historyList box",onScrolltolower:f.scrolltolower,"scroll-y":"true"},{default:a((()=>[(e(!0),l(s,null,c(g.enterpriseList,((t,d)=>{var p;return e(),l("div",{onClick:e=>f.attractDel(t),class:"cellView",key:t.id},[r("img",{src:g.listicon[t.enterpriseIconLabelId].icon,alt:"",class:"enterpriseIcon"},null,8,["src"]),r("div",{class:"enterprise"},[r("div",null,n(t.enterpriseName),1),(null==(p=t.enterpriseLabelNames)?void 0:p.length)?(e(),i(y,{key:0,class:"enterpriseLabeltag"},{default:a((()=>[t.enterpriseLabelNames?(e(),i(y,{key:0,class:"enterpriseLabel"},{default:a((()=>[(e(!0),l(s,null,c(t.enterpriseLabelNames.slice(0,f.getMaxLabelCount(t.enterpriseLabelNames)),((t,o)=>(e(),i(y,{key:o},{default:a((()=>[r("div",{class:"tagBox"},n(t),1)])),_:2},1024)))),128)),t.enterpriseLabelNames.length>f.getMaxLabelCount(t.enterpriseLabelNames)?(e(),l("div",{key:0,class:"tagBox"}," ... ")):h("",!0)])),_:2},1024)):h("",!0)])),_:2},1024)):h("",!0),r("div",{class:"location"},[o(w,{src:V,class:"locationIcon"}),r("span",null,n(t.province+t.city+t.area),1)])]),r("div",{onClick:b((e=>f.isCollectionClick(t)),["stop"])},[o(w,{class:"collectionIcon",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_523_63016'%3e%3crect%20x='0'%20y='0'%20width='20'%20height='20'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_523_63016)'%3e%3cg%3e%3cpath%20d='M3.19556,17.94514296875C3.23713,17.40184296875,3.35548,16.87534296875,3.44497,16.34414296875C3.63434,15.22414296875,3.83006,14.10584296875,4.02231,12.98694296875C4.04983,12.85044296875,4.00147,12.70984296875,3.89587,12.61914296875C2.82145,11.57594296875,1.75972,10.51994296875,0.67433,9.48710296875C0.181284,9.01599296875,-0.101034,8.48311296875,0.0334853,7.79493296875C0.179552,7.04439296875,0.760353,6.57039296875,1.56458,6.46243296875C2.82549,6.28923296875,4.08293,6.10102296875,5.3421,5.91858296875C5.58401,5.88394296875,5.82533,5.83948296875,6.06839,5.81870296875C6.25603,5.80311296875,6.36283,5.71593296875,6.44424,5.54908296875C7.13704,4.13287296875,7.82985,2.71667296875,8.53766,1.30796596875C9.03129,0.32302786875,10.2778,0.09555696875000003,11.1028,0.81953796875C11.2772,0.98179896875,11.4181,1.17679096875,11.5173,1.39341096875C12.1916,2.76112296875,12.8746,4.12884296875,13.5397,5.50117296875C13.6407,5.70785296875,13.7707,5.78984296875,13.9964,5.82332296875C15.4397,6.02481296875,16.8831,6.24709296875,18.3264,6.45031296875C18.7254,6.50804296875,19.1035,6.58887296875,19.4234,6.85445296875C20.1055,7.42056296875,20.2142,8.42634296875,19.6687,9.12511296875C19.5359,9.29831296875,19.3702,9.43688296875,19.2155,9.58698296875C18.1896,10.58754296875,17.1677,11.59154296875,16.1383,12.58914296875C16.0096,12.69964296875,15.9533,12.87264296875,15.9922,13.03774296875C16.2601,14.57404296875,16.5326,16.10974296875,16.7855,17.64894296875C16.8912,18.29324296875,16.6983,18.85614296875,16.1649,19.25564296875C15.6216,19.66264296875,15.0194,19.71754296875,14.4155,19.40754296875Q12.4099,18.37404296875,10.4221,17.30424296875C10.1219,17.14204296875,9.89614,17.13974296875,9.59246,17.30424296875Q7.59949,18.38504296875,5.57823,19.41444296875C4.62215,19.91334296875,3.45401,19.36094296875,3.23309,18.30534296875C3.21313,18.18614296875,3.2006,18.06584296875,3.19556,17.94514296875Z'%20fill='%23FDCC1F'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})],8,["onClick"])],8,["onClick"])})),128)),o(y,{style:{"min-height":"120rpx"}}),0==g.enterpriseList.length?(e(),i(y,{key:0,class:"nodatabox"},{default:a((()=>[o(w,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])),_:1})):h("",!0)])),_:1},8,["onScrolltolower"]),o(T,{BottomBlank:!0,appletTab:!0,ref:"filterView",title:"筛选",moreScreenList:g.moreScreenList,onUpdatamoreScreen:f.updatamoreScreen,MultipleChoice:!0},null,8,["moreScreenList","onUpdatamoreScreen"])])}],["__scopeId","data-v-868be35c"]]);const J=M({data:()=>({safeArea:x("safeAreaRpx"),range:"",rules:[],form:{}}),methods:{maskClick(t){console.log("maskClick事件:",t)},opens(t){this.$emit("changeBottomTab",!1),this.range=t,this.$refs.follow.open("bottom")},claimFn(){this.range="",this.$refs.follow.close(),this.$emit("changeBottomTab",!0)},submit(){this.$emit("updataList",this.range),this.claimFn(),this.$emit("changeBottomTab",!0)}}},[["render",function(n,l,s,c,h,d){const x=F(t("uni-datetime-picker"),j),u=F(t("uni-forms-item"),D),f=F(t("uni-forms"),I),y=F(t("uni-popup"),k),m=p;return e(),i(m,null,{default:a((()=>[(e(),i(g,{to:"body"},[o(y,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},"日期范围"),r("div",{class:"formItem"},[o(f,{ref:"valiForm",rules:h.rules,modelValue:h.form,"label-position":"top","label-width":"100"},{default:a((()=>[o(u,{label:"日期范围",required:!0},{default:a((()=>[o(x,{modelValue:h.range,"onUpdate:modelValue":l[0]||(l[0]=t=>h.range=t),type:"daterange",onMaskClick:d.maskClick},null,8,["modelValue","onMaskClick"])])),_:1})])),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:l[1]||(l[1]=(...t)=>d.claimFn&&d.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:l[2]||(l[2]=(...t)=>d.submit&&d.submit(...t))},"确定")]),r("div",{style:{height:"22px"}})])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-40075697"]]);const tt=M({components:{filterView:X},data:()=>({indentLsit:[],userId:x("userId"),showNodata:!1,pageNum:1,pages:0,isLoading:!1,openItem:{},optIndex:-1,moreScreenList:[{paramKey:"scope",paramName:"范围",paramValueList:[{key:"我发出的",value:"me"},{key:"我收到的",value:"all"},{key:"我的团队",value:"team"}],unlimited:!1}],moreData:{}}),created(){this.getIndent()},methods:{getWeekday:t=>["星期日","星期一","星期二","星期三","星期四","星期五","星期六"][new Date(t).getDay()],screen(){this.$refs.filterView.opens()},updatamoreScreen(t){console.log(t,"data"),this.moreData=t,this.indentLsit=[],this.pageNum=1,this.RefreshList(!0)},optfor(t){this.optIndex==t?this.optIndex=-1:this.optIndex=t},handlyDel(t,e){let i={reportCode:t.reportCode};this.$api.weeklyReportDeleteAPI({data:i,method:"get"}).then((t=>{this.optIndex=-1,setTimeout((()=>{this.RefreshList()}),500)}))},weeklyDel(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{id:t},path:"addWeekly"}))},RefreshList(t){var e,i;let a={pageSize:10*this.pageNum,pageNum:1,scope:null==(i=null==(e=this.moreData)?void 0:e.scope)?void 0:i.value};this.$api.weeklyReportListAPI({data:a}).then((e=>{t||u({title:"删除周报成功！",icon:"none"}),this.indentLsit=this.classifyByMonth(e.result.records)}))},scrolltolowerFn(){this.pages>this.pageNum&&(this.pageNum++,this.getIndent())},updata(){this.showNodata=!1,this.indentLsit=[],this.pages=0,this.pageNum=1,this.getIndent()},getIndent(){var t,e;if(this.isLoading)return;this.isLoading=!0;let i={pageSize:10,pageNum:this.pageNum,scope:null==(e=null==(t=this.moreData)?void 0:t.scope)?void 0:e.value};this.$api.weeklyReportListAPI({data:i,method:"get"}).then((t=>{let e=this.classifyByMonth(t.result.records);this.indentLsit=this.mergeSameTimeObjects(this.indentLsit.concat(e)),this.pages=t.result.pages})).finally((()=>{this.isLoading=!1,this.showNodata=!0}))},mergeSameTimeObjects(t){const e=new Map,i=[];for(const a of t)if(e.has(a.time)){e.get(a.time).list.push(...a.list)}else{const t={time:a.time,list:[...a.list]};e.set(a.time,t),i.push(t)}return i},classifyByMonth(t){const e=[];return t.forEach((t=>{const i=t.reportDate,a=e.findIndex((t=>t.time===i));-1===a?e.push({time:i,list:[t]}):e[a].list.push(t)})),e}}},[["render",function(t,d,x,u,g,f){const y=C,T=p,A=S,P=m("filterView");return e(),l(s,null,[o(T,{class:"btn",onClick:d[0]||(d[0]=t=>f.screen())},{default:a((()=>[o(y,{class:"btnIcon",src:H}),o(T,{class:"btnTxt"},{default:a((()=>[v("筛选")])),_:1})])),_:1}),0!=g.indentLsit.length?(e(),i(A,{key:0,onClick:d[1]||(d[1]=t=>g.optIndex=-1),"scroll-y":"true",class:"informationBox",onScrolltolower:f.scrolltolowerFn},{default:a((()=>[(e(!0),l(s,null,c(g.indentLsit,((t,i)=>(e(),l("div",{key:i},[r("div",{class:"indentItem"},[r("span",{class:"timeBox"},n(t.time)+" "+n(f.getWeekday(t.time)),1),(e(!0),l(s,null,c(t.list,((t,i)=>(e(),l("div",{key:i,class:"indentItemBox",onClick:e=>f.weeklyDel(t.reportCode)},[o(y,{class:"userhead",src:t.userhead?t.userhead:"../../../static/user/userimg.png"},null,8,["src"]),r("div",{class:"right"},[r("div",{class:"rightTop"},[r("span",{class:"username"},n(t.username)+"的周报",1),r("span",{class:"reportDatetime"},n(null==t?void 0:t.reportDatetime.slice(0,16)),1),g.userId==t.userId?(e(),l("div",{key:0,class:"more",onClick:b((e=>f.optfor(t.reportCode)),["stop"])},[r("span",null,"...")],8,["onClick"])):h("",!0),w(r("div",{class:"popup",onClick:b((e=>f.handlyDel(t)),["stop"])}," 删除 ",8,["onClick"]),[[L,g.optIndex==t.reportCode]])]),r("div",{class:"newProject"},n(t.newProject),1)])],8,["onClick"])))),128))])])))),128)),r("div",{style:{"padding-bottom":"60rpx"}})])),_:1},8,["onScrolltolower"])):h("",!0),0==g.indentLsit.length&&g.showNodata?(e(),l("div",{key:1,class:"nodatabox"},[o(y,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])):h("",!0),o(P,{ref:"filterView",MultipleChoice:!0,BottomBlank:!0,appletTab:!0,title:"筛选","more-screen-list":g.moreScreenList,"multiple-choice":!0,onUpdatamoreScreen:f.updatamoreScreen},null,8,["more-screen-list","onUpdatamoreScreen"])],64)}],["__scopeId","data-v-c1d6e98e"]]);var et={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},it=function(t,...e){if(null==t)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!e||e.length<=0)return t;function i(t,e){for(let a in e)t[a]=t[a]&&"[object Object]"===t[a].toString()?i(t[a],e[a]):t[a]=e[a];return t}return e.forEach((e=>{t=i(t,e)})),t},at={toFixed:function(t,e){return e=e||2,this.isFloat(t)&&(t=t.toFixed(e)),t},isFloat:function(t){return t%1!=0},approximatelyEqual:function(t,e){return Math.abs(t-e)<1e-10},isSameSign:function(t,e){return Math.abs(t)===t&&Math.abs(e)===e||Math.abs(t)!==t&&Math.abs(e)!==e},isSameXCoordinateArea:function(t,e){return this.isSameSign(t.x,e.x)},isCollision:function(t,e){return t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,!(e.start.x>t.end.x||e.end.x<t.start.x||e.end.y>t.start.y||e.start.y<t.end.y)}};function ot(t,e){var i=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,i,a){return e+e+i+i+a+a})),a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i);return"rgba("+parseInt(a[1],16)+","+parseInt(a[2],16)+","+parseInt(a[3],16)+","+e+")"}function rt(t,e,i){if(isNaN(t))throw new Error("[uCharts] series数据需为Number格式");i=i||10,e=e||"upper";for(var a=1;i<1;)i*=10,a*=10;for(t="upper"===e?Math.ceil(t*a):Math.floor(t*a);t%i!=0;)if("upper"===e){if(t==t+1)break;t++}else t--;return t/a}function nt(t,e,i,a,o){var r=o.width-o.area[1]-o.area[3],n=i.eachSpacing*(o.chartData.xAxisData.xAxisPoints.length-1);"mount"==o.type&&o.extra&&o.extra.mount&&o.extra.mount.widthRatio&&o.extra.mount.widthRatio>1&&(o.extra.mount.widthRatio>2&&(o.extra.mount.widthRatio=2),n+=(o.extra.mount.widthRatio-1)*i.eachSpacing);var l=e;return e>=0?(l=0,t.uevent.trigger("scrollLeft"),t.scrollOption.position="left",o.xAxis.scrollPosition="left"):Math.abs(e)>=n-r?(l=r-n,t.uevent.trigger("scrollRight"),t.scrollOption.position="right",o.xAxis.scrollPosition="right"):(t.scrollOption.position=e,o.xAxis.scrollPosition=e),l}function lt(t,e,i){function a(t){for(;t<0;)t+=2*Math.PI;for(;t>2*Math.PI;)t-=2*Math.PI;return t}return t=a(t),(e=a(e))>(i=a(i))&&(i+=2*Math.PI,t<e&&(t+=2*Math.PI)),t>=e&&t<=i}function st(t,e){function i(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}function a(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].x>=Math.max(t[e-1].x,t[e+1].x)||t[e].x<=Math.min(t[e-1].x,t[e+1].x))}var o=.2,r=.2,n=null,l=null,s=null,c=null;if(e<1?(n=t[0].x+(t[1].x-t[0].x)*o,l=t[0].y+(t[1].y-t[0].y)*o):(n=t[e].x+(t[e+1].x-t[e-1].x)*o,l=t[e].y+(t[e+1].y-t[e-1].y)*o),e>t.length-3){var h=t.length-1;s=t[h].x-(t[h].x-t[h-1].x)*r,c=t[h].y-(t[h].y-t[h-1].y)*r}else s=t[e+1].x-(t[e+2].x-t[e].x)*r,c=t[e+1].y-(t[e+2].y-t[e].y)*r;return i(t,e+1)&&(c=t[e+1].y),i(t,e)&&(l=t[e].y),a(t,e+1)&&(s=t[e+1].x),a(t,e)&&(n=t[e].x),(l>=Math.max(t[e].y,t[e+1].y)||l<=Math.min(t[e].y,t[e+1].y))&&(l=t[e].y),(c>=Math.max(t[e].y,t[e+1].y)||c<=Math.min(t[e].y,t[e+1].y))&&(c=t[e+1].y),(n>=Math.max(t[e].x,t[e+1].x)||n<=Math.min(t[e].x,t[e+1].x))&&(n=t[e].x),(s>=Math.max(t[e].x,t[e+1].x)||s<=Math.min(t[e].x,t[e+1].x))&&(s=t[e+1].x),{ctrA:{x:n,y:l},ctrB:{x:s,y:c}}}function ct(t,e,i){return{x:i.x+t,y:i.y-e}}function ht(t,e){if(e)for(;at.isCollision(t,e);)t.start.x>0?t.start.y--:t.start.x<0||t.start.y>0?t.start.y++:t.start.y--;return t}function dt(t,e,i){for(var a=0,o=0;o<t.length;o++){let r=t[o];if(r.color||(r.color=i.color[a],a=(a+1)%i.color.length),r.linearIndex||(r.linearIndex=o),r.index||(r.index=0),r.type||(r.type=e.type),void 0===r.show&&(r.show=!0),r.type||(r.type=e.type),r.pointShape||(r.pointShape="circle"),!r.legendShape)switch(r.type){case"line":r.legendShape="line";break;case"column":case"bar":r.legendShape="rect";break;case"area":case"mount":r.legendShape="triangle";break;default:r.legendShape="circle"}}return t}function pt(t,e,i,a){var o=e||[];if("custom"==t&&0==o.length&&(o=a.linearColor),"custom"==t&&o.length<i.length){let t=i.length-o.length;for(var r=0;r<t;r++)o.push(a.linearColor[(r+1)%a.linearColor.length])}return o}function xt(t,e,i){var a=0;if(t=String(t),!1!==i&&void 0!==i&&i.setFontSize&&i.measureText)return i.setFontSize(e),i.measureText(t).width;t=t.split("");for(let o=0;o<t.length;o++){let e=t[o];/[a-zA-Z]/.test(e)?a+=7:/[0-9]/.test(e)?a+=5.5:/\./.test(e)?a+=2.7:/-/.test(e)?a+=3.25:/:/.test(e)?a+=2.5:/[\u4e00-\u9fa5]/.test(e)?a+=10:/\(|\)/.test(e)?a+=3.73:/\s/.test(e)?a+=2.5:/%/.test(e)?a+=8:a+=10}return a*e/10}function ut(t){return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data)}),[])}function gt(t,e){for(var i=new Array(e),a=0;a<i.length;a++)i[a]=0;for(var o=0;o<t.length;o++)for(a=0;a<i.length;a++)i[a]+=t[o].data[a];return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data).concat(i)}),[])}function ft(t,e,i){let a,o;return t.clientX?e.rotate?(o=e.height-t.clientX*e.pix,a=(t.pageY-i.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):(a=t.clientX*e.pix,o=(t.pageY-i.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):e.rotate?(o=e.height-t.x*e.pix,a=t.y*e.pix):(a=t.x*e.pix,o=t.y*e.pix),{x:a,y:o}}function yt(t,e,i){var a=[],o=[],r=e.constructor.toString().indexOf("Array")>-1;if(r){let e=wt(t);for(var n=0;n<i.length;n++)o.push(e[i[n]])}else o=t;for(let l=0;l<o.length;l++){let t=o[l],i=-1;if(i=r?e[l]:e,null!==t.data[i]&&void 0!==t.data[i]&&t.show){let e={};e.color=t.color,e.type=t.type,e.style=t.style,e.pointShape=t.pointShape,e.disableLegend=t.disableLegend,e.legendShape=t.legendShape,e.name=t.name,e.show=t.show,e.data=t.formatter?t.formatter(t.data[i]):t.data[i],a.push(e)}}return a}function mt(t,e,i){var a=t.map((function(t){return xt(t,e,i)}));return Math.max.apply(null,a)}function vt(t){for(var e=2*Math.PI/t,i=[],a=0;a<t;a++)i.push(e*a);return i.map((function(t){return-1*t+Math.PI/2}))}function bt(t,e,i,a,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},n=e.chartData.calPoints?e.chartData.calPoints:[];let l={};if(a.length>0){let t=[];for(let e=0;e<a.length;e++)t.push(n[a[e]]);l=t[0][i[0]]}else for(let h=0;h<n.length;h++)if(n[h][i]){l=n[h][i];break}var s=t.map((function(t){let a=null;return e.categories&&e.categories.length>0&&(a=o[i]),{text:r.formatter?r.formatter(t,a,i,e):t.name+": "+t.data,color:t.color,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}})),c={x:Math.round(l.x),y:Math.round(l.y)};return{textList:s,offset:c}}function Ct(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=e.chartData.xAxisPoints[i]+e.chartData.eachSpacing/2,n=t.map((function(t){return{text:o.formatter?o.formatter(t,a[i],i,e):t.name+": "+t.data,color:t.color,disableLegend:!!t.disableLegend,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}}));n=n.filter((function(t){if(!0!==t.disableLegend)return t}));var l={x:Math.round(r),y:0};return{textList:n,offset:l}}function St(t,e,i,a,o,r){var n=i.chartData.calPoints;let l=r.color.upFill,s=r.color.downFill,c=[l,l,s,l];var h=[];e.map((function(e){0==a?e.data[1]-e.data[0]<0?c[1]=s:c[1]=l:(e.data[0]<t[a-1][1]&&(c[0]=s),e.data[1]<e.data[0]&&(c[1]=s),e.data[2]>t[a-1][1]&&(c[2]=l),e.data[3]<t[a-1][1]&&(c[3]=s));let o={text:"开盘："+e.data[0],color:c[0],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape},r={text:"收盘："+e.data[1],color:c[1],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape},n={text:"最低："+e.data[2],color:c[2],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape},d={text:"最高："+e.data[3],color:c[3],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape};h.push(o,r,n,d)}));var d=[],p={x:0,y:0};for(let x=0;x<n.length;x++){let t=n[x];void 0!==t[a]&&null!==t[a]&&d.push(t[a])}return p.x=Math.round(d[0][0].x),{textList:h,offset:p}}function wt(t){let e=[];for(let i=0;i<t.length;i++)1==t[i].show&&e.push(t[i]);return e}function Lt(t,e,i){return t.x<=e.width-e.area[1]+10&&t.x>=e.area[3]-10&&t.y>=e.area[0]&&t.y<=e.height-e.area[2]}function Tt(t,e,i){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<=Math.pow(i,2)}function At(t,e){var i=[],a=[];return t.forEach((function(t,o){e.connectNulls?null!==t&&a.push(t):null!==t?a.push(t):(a.length&&i.push(a),a=[])})),a.length&&i.push(a),i}function Pt(t,e,i,a,o){var r={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix},n=e.xAxis.fontSize*e.pix,l=t.map((function(t,i){var a=e.xAxis.formatter?e.xAxis.formatter(t,i,e):t;return xt(String(a),n,o)})),s=Math.max.apply(this,l);if(1==e.xAxis.rotateLabel){r.angle=e.xAxis.rotateAngle*Math.PI/180;let t=e.xAxis.marginTop*e.pix*2+Math.abs(s*Math.sin(r.angle));t=t<n+e.xAxis.marginTop*e.pix*2?t+e.xAxis.marginTop*e.pix*2:t,r.xAxisHeight=t}return e.enableScroll&&e.xAxis.scrollShow&&(r.xAxisHeight+=6*e.pix),e.xAxis.disabled&&(r.xAxisHeight=0),r}function _t(t,e,i,a){var o=it({},{type:""},e.extra.bar),r={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix};r.ranges=function(t,e,i,a){var o,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;o="stack"==a?gt(t,e.categories.length):ut(t);var n=[];(o=o.filter((function(t){return"object"==typeof t&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t}))).map((function(t){"object"==typeof t?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){n.push(t)})):n.push(t[0]):n.push(t.value):n.push(t)}));var l=0,s=0;if(n.length>0&&(l=Math.min.apply(this,n),s=Math.max.apply(this,n)),r>-1?("number"==typeof e.xAxis.data[r].min&&(l=Math.min(e.xAxis.data[r].min,l)),"number"==typeof e.xAxis.data[r].max&&(s=Math.max(e.xAxis.data[r].max,s))):("number"==typeof e.xAxis.min&&(l=Math.min(e.xAxis.min,l)),"number"==typeof e.xAxis.max&&(s=Math.max(e.xAxis.max,s))),l===s){s+=s||10}for(var c=l,h=[],d=(s-c)/e.xAxis.splitNumber,p=0;p<=e.xAxis.splitNumber;p++)h.push(c+d*p);return h}(t,e,i,o.type),r.rangesFormat=r.ranges.map((function(t){return t=at.toFixed(t,2)}));var n=r.ranges.map((function(t){return t=at.toFixed(t,2)}));return(r=Object.assign(r,Zt(n,e))).eachSpacing,n.map((function(t){return xt(t,e.xAxis.fontSize*e.pix,a)})),!0===e.xAxis.disabled&&(r.xAxisHeight=0),r}function kt(t,e,i,a,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=o.extra.radar||{};n.max=n.max||0;var l=Math.max(n.max,Math.max.apply(null,ut(a))),s=[];for(let c=0;c<a.length;c++){let o=a[c],n={};n.color=o.color,n.legendShape=o.legendShape,n.pointShape=o.pointShape,n.data=[],o.data.forEach((function(a,o){let s={};s.angle=t[o],s.proportion=a/l,s.value=a,s.position=ct(i*s.proportion*r*Math.cos(s.angle),i*s.proportion*r*Math.sin(s.angle),e),n.data.push(s)})),s.push(n)}return s}function Ft(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,a=0,o=0;for(let r=0;r<t.length;r++){let e=t[r];e.data=null===e.data?0:e.data,a+=e.data}for(let r=0;r<t.length;r++){let o=t[r];o.data=null===o.data?0:o.data,o._proportion_=0===a?1/t.length*i:o.data/a*i,o._radius_=e}for(let r=0;r<t.length;r++){let e=t[r];e._start_=o,o+=2*e._proportion_*Math.PI}return t}function Mt(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;for(let r=0;r<t.length;r++)"funnel"==i.type?t[r].radius=t[r].data/t[0].data*e*o:t[r].radius=a*(t.length-r)/(a*t.length)*e*o,t[r]._proportion_=t[r].data/t[0].data;return t}function Dt(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0,n=0,l=[];for(let d=0;d<t.length;d++){let e=t[d];e.data=null===e.data?0:e.data,r+=e.data,l.push(e.data)}var s=Math.min.apply(null,l),c=Math.max.apply(null,l),h=a-i;for(let d=0;d<t.length;d++){let n=t[d];n.data=null===n.data?0:n.data,0===r?(n._proportion_=1/t.length*o,n._rose_proportion_=1/t.length*o):(n._proportion_=n.data/r*o,n._rose_proportion_="area"==e?1/t.length*o:n.data/r*o),n._radius_=i+h*((n.data-s)/(c-s))||a}for(let d=0;d<t.length;d++){let e=t[d];e._start_=n,n+=2*e._rose_proportion_*Math.PI}return t}function It(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(let a=0;a<t.length;a++){let o,r=t[a];r.data=null===r.data?0:r.data,o="circle"==e.type?2:"ccw"==e.direction?e.startAngle<e.endAngle?2+e.startAngle-e.endAngle:e.startAngle-e.endAngle:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,r._proportion_=o*r.data*i+e.startAngle,"ccw"==e.direction&&(r._proportion_=e.startAngle-o*r.data*i),r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return t}function Bt(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(let a=0;a<t.length;a++){let o,r=t[a];r.data=null===r.data?0:r.data,o="circle"==e.type?2:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,r._proportion_=o*r.data*i+e.startAngle,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return t}function zt(t,e,i){let a;a=i<e?2+i-e:e-i;let o=e;for(let r=0;r<t.length;r++)t[r].value=null===t[r].value?0:t[r].value,t[r]._startAngle_=o,t[r]._endAngle_=a*t[r].value+e,t[r]._endAngle_>=2&&(t[r]._endAngle_=t[r]._endAngle_%2),o=t[r]._endAngle_;return t}function Ot(t,e,i){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;for(let o=0;o<t.length;o++){let r,n=t[o];if(n.data=null===n.data?0:n.data,"auto"==i.pointer.color){for(let t=0;t<e.length;t++)if(n.data<=e[t].value){n.color=e[t].color;break}}else n.color=i.pointer.color;r=i.endAngle<i.startAngle?2+i.endAngle-i.startAngle:i.startAngle-i.endAngle,n._endAngle_=r*n.data+i.startAngle,n._oldAngle_=i.oldAngle,i.oldAngle<i.endAngle&&(n._oldAngle_+=2),n.data>=i.oldData?n._proportion_=(n._endAngle_-n._oldAngle_)*a+i.oldAngle:n._proportion_=n._oldAngle_-(n._oldAngle_-n._endAngle_)*a,n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return t}function Nt(t,e,i,a,o,r){return t.map((function(t){if(null===t)return null;var o=0,n=0;return"mix"==r.type?(o=r.extra.mix.column.seriesGap*r.pix||0,n=r.extra.mix.column.categoryGap*r.pix||0):(o=r.extra.column.seriesGap*r.pix||0,n=r.extra.column.categoryGap*r.pix||0),o=Math.min(o,e/i),n=Math.min(n,e/i),t.width=Math.ceil((e-2*n-o*(i-1))/i),r.extra.mix&&r.extra.mix.column.width&&+r.extra.mix.column.width>0&&(t.width=Math.min(t.width,+r.extra.mix.column.width*r.pix)),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t.x+=(a+.5-i/2)*(t.width+o),t}))}function Rt(t,e,i,a,o,r){return t.map((function(t){if(null===t)return null;var o=0,n=0;return o=r.extra.bar.seriesGap*r.pix||0,n=r.extra.bar.categoryGap*r.pix||0,o=Math.min(o,e/i),n=Math.min(n,e/i),t.width=Math.ceil((e-2*n-o*(i-1))/i),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(t.width=Math.min(t.width,+r.extra.bar.width*r.pix)),t.width<=0&&(t.width=1),t.y+=(a+.5-i/2)*(t.width+o),t}))}function Wt(t,e,i,a,o,r,n){var l=r.extra.column.categoryGap*r.pix||0;return t.map((function(t){return null===t?null:(t.width=e-2*l,r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),a>0&&(t.width-=n),t)}))}function Et(t,e,i,a,o,r,n){var l=r.extra.column.categoryGap*r.pix||0;return t.map((function(t,i){return null===t?null:(t.width=Math.ceil(e-2*l),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t)}))}function Qt(t,e,i,a,o,r,n){var l=r.extra.bar.categoryGap*r.pix||0;return t.map((function(t,i){return null===t?null:(t.width=Math.ceil(e-2*l),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(t.width=Math.min(t.width,+r.extra.bar.width*r.pix)),t.width<=0&&(t.width=1),t)}))}function Zt(t,e,i){var a=e.width-e.area[1]-e.area[3],o=e.enableScroll?Math.min(e.xAxis.itemCount,t.length):t.length;("line"==e.type||"area"==e.type||"scatter"==e.type||"bubble"==e.type||"bar"==e.type)&&o>1&&"justify"==e.xAxis.boundaryGap&&(o-=1);var r=0;"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),o+=r=e.extra.mount.widthRatio-1);var n=a/o,l=[],s=e.area[3],c=e.width-e.area[1];return t.forEach((function(t,e){l.push(s+r/2*n+e*n)})),"justify"!==e.xAxis.boundaryGap&&(!0===e.enableScroll?l.push(s+r*n+t.length*n):l.push(c)),{xAxisPoints:l,startX:s,endX:c,eachSpacing:n}}function Vt(t,e,i,a,o,r,n){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,s=[],c=r.height-r.area[0]-r.area[2];return t.forEach((function(t,n){if(null===t)s.push(null);else{var h=[];t.forEach((function(t,s){var d={};d.x=a[n]+Math.round(o/2);var p=t.value||t,x=c*(p-e)/(i-e);x*=l,d.y=r.height-Math.round(x)-r.area[2],h.push(d)})),s.push(h)}})),s}function Gt(t,e,i,a,o,r,n){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,s="center";"line"!=r.type&&"area"!=r.type&&"scatter"!=r.type&&"bubble"!=r.type||(s=r.xAxis.boundaryGap);var c=[],h=r.height-r.area[0]-r.area[2],d=r.width-r.area[1]-r.area[3];return t.forEach((function(t,n){if(null===t)c.push(null);else{var p={};p.color=t.color,p.x=a[n];var x=t;if("object"==typeof t&&null!==t)if(t.constructor.toString().indexOf("Array")>-1){let e,i,a;e=[].concat(r.chartData.xAxisData.ranges),i=e.shift(),a=e.pop(),x=t[1],p.x=r.area[3]+d*(t[0]-i)/(a-i),"bubble"==r.type&&(p.r=t[2],p.t=t[3])}else x=t.value;"center"==s&&(p.x+=o/2);var u=h*(x-e)/(i-e);u*=l,p.y=r.height-u-r.area[2],c.push(p)}})),c}function jt(t,e,i,a,o,r,n,l,s){s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=r.xAxis.boundaryGap,h=[],d=r.height-r.area[0]-r.area[2],p=r.width-r.area[1]-r.area[3];return t.forEach((function(t,n){if(null===t)h.push(null);else{var x={};if(x.color=t.color,"vertical"==l.animation){x.x=a[n];var u=t;if("object"==typeof t&&null!==t)if(t.constructor.toString().indexOf("Array")>-1){let e,i,a;e=[].concat(r.chartData.xAxisData.ranges),i=e.shift(),a=e.pop(),u=t[1],x.x=r.area[3]+p*(t[0]-i)/(a-i)}else u=t.value;"center"==c&&(x.x+=o/2);var g=d*(u-e)/(i-e);g*=s,x.y=r.height-g-r.area[2],h.push(x)}else{x.x=a[0]+o*n*s;u=t;"center"==c&&(x.x+=o/2);g=d*(u-e)/(i-e);x.y=r.height-g-r.area[2],h.push(x)}}})),h}function $t(t,e,i,a,o,r,n,l,s){s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=[],h=r.height-r.area[0]-r.area[2],d=r.width-r.area[1]-r.area[3];return t.forEach((function(t,n){if(null===t)c.push(null);else{var l={};l.color=t.color,l.x=a[n];var p=t;if("object"==typeof t&&null!==t)if(t.constructor.toString().indexOf("Array")>-1){let e,i,a;e=[].concat(r.chartData.xAxisData.ranges),i=e.shift(),a=e.pop(),p=t[1],l.x=r.area[3]+d*(t[0]-i)/(a-i)}else p=t.value;l.x+=o/2;var x=h*(p*s-e)/(i-e);l.y=r.height-x-r.area[2],c.push(l)}})),c}function Xt(t,e,i,a,o,r,n,l){var s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1,c=[],h=r.height-r.area[0]-r.area[2];r.width,r.area[1],r.area[3];var d=o*n.widthRatio;return t.forEach((function(t,n){if(null===t)c.push(null);else{var l={};l.color=t.color,l.x=a[n],l.x+=o/2;var p=t.data,x=h*(p*s-e)/(i-e);l.y=r.height-x-r.area[2],l.value=p,l.width=d,c.push(l)}})),c}function Ht(t,e,i,a,o,r,n){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,s=[];r.height,r.area[0],r.area[2];var c=r.width-r.area[1]-r.area[3];return t.forEach((function(t,o){if(null===t)s.push(null);else{var n={};n.color=t.color,n.y=a[o];var h=t;"object"==typeof t&&null!==t&&(h=t.value);var d=c*(h-e)/(i-e);d*=l,n.height=d,n.value=h,n.x=d+r.area[3],s.push(n)}})),s}function Ut(t,e,i,a,o,r,n,l,s){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],d=r.height-r.area[0]-r.area[2];return t.forEach((function(t,n){if(null===t)h.push(null);else{var p={};if(p.color=t.color,p.x=a[n]+Math.round(o/2),l>0){var x=0;for(let t=0;t<=l;t++)x+=s[t].data[n];var u=d*(x-e)/(i-e),g=d*(x-t-e)/(i-e)}else{x=t;"object"==typeof t&&null!==t&&(x=t.value);u=d*(x-e)/(i-e),g=0}var f=g;u*=c,f*=c,p.y=r.height-Math.round(u)-r.area[2],p.y0=r.height-Math.round(f)-r.area[2],h.push(p)}})),h}function qt(t,e,i,a,o,r,n,l,s){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],d=r.width-r.area[1]-r.area[3];return t.forEach((function(t,o){if(null===t||0===t)h.push(null);else{var n={};if(n.color=t.color,n.y=a[o],l>0){var p=0;for(let t=0;t<=l;t++)p+=s[t].data[o];var x=d*(p-e)/(i-e),u=d*(p-t-e)/(i-e)}else{p=t;"object"==typeof t&&null!==t&&(p=t.value);x=d*(p-e)/(i-e),u=0}var g=u;x*=c,g*=c,n.height=x-g,n.x=r.area[3]+x,n.x0=r.area[3]+g,h.push(n)}})),h}function Yt(t,e,i,a,o){var r;r="stack"==a?gt(t,e.categories.length):ut(t);var n=[];(r=r.filter((function(t){return"object"==typeof t&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t}))).map((function(t){"object"==typeof t?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){n.push(t)})):n.push(t[1]):n.push(t.value):n.push(t)}));var l=o.min||0,s=o.max||0;n.length>0&&(l=Math.min.apply(this,n),s=Math.max.apply(this,n)),l===s&&(0==s?s=10:l=0);for(var c=function(t,e){var i=0,a=e-t;return{minRange:rt(t,"lower",i=a>=1e4?1e3:a>=1e3?100:a>=100?10:a>=10?5:a>=1?1:a>=.1?.1:a>=.01?.01:a>=.001?.001:a>=1e-4?1e-4:a>=1e-5?1e-5:1e-6),maxRange:rt(e,"upper",i)}}(l,s),h=void 0===o.min||null===o.min?c.minRange:o.min,d=((void 0===o.max||null===o.max?c.maxRange:o.max)-h)/e.yAxis.splitNumber,p=[],x=0;x<=e.yAxis.splitNumber;x++)p.push(h+d*x);return p.reverse()}function Kt(t,e,i,a){var o=it({},{type:""},e.extra.column),r=e.yAxis.data.length,n=new Array(r);if(r>0){for(let e=0;e<r;e++){n[e]=[];for(let i=0;i<t.length;i++)t[i].index==e&&n[e].push(t[i])}var l=new Array(r),s=new Array(r),c=new Array(r);for(let t=0;t<r;t++){let r=e.yAxis.data[t];1==e.yAxis.disabled&&(r.disabled=!0),"categories"===r.type?(r.formatter||(r.formatter=(t,e,i)=>t+(r.unit||"")),r.categories=r.categories||e.categories,l[t]=r.categories):(r.formatter||(r.formatter=(t,e,i)=>at.toFixed(t,r.tofix||0)+(r.unit||"")),l[t]=Yt(n[t],e,0,o.type,r));let h=r.fontSize*e.pix||i.fontSize;c[t]={position:r.position?r.position:"left",width:0},s[t]=l[t].map((function(i,o){return i=r.formatter(i,o,e),c[t].width=Math.max(c[t].width,xt(i,h,a)+5),i}));let d=r.calibration?4*e.pix:0;c[t].width+=d+3*e.pix,!0===r.disabled&&(c[t].width=0)}}else{l=new Array(1),s=new Array(1),c=new Array(1);"bar"===e.type?(l[0]=e.categories,e.yAxis.formatter||(e.yAxis.formatter=(t,e,i)=>t+(i.yAxis.unit||""))):(e.yAxis.formatter||(e.yAxis.formatter=(t,e,i)=>t.toFixed(i.yAxis.tofix)+(i.yAxis.unit||"")),l[0]=Yt(t,e,0,o.type,{})),c[0]={position:"left",width:0};var h=e.yAxis.fontSize*e.pix||i.fontSize;s[0]=l[0].map((function(t,i){return t=e.yAxis.formatter(t,i,e),c[0].width=Math.max(c[0].width,xt(t,h,a)+5),t})),c[0].width+=3*e.pix,!0===e.yAxis.disabled?(c[0]={position:"left",width:0},e.yAxis.data[0]={disabled:!0}):(e.yAxis.data[0]={disabled:!1,position:"left",max:e.yAxis.max,min:e.yAxis.min,formatter:e.yAxis.formatter},"bar"===e.type&&(e.yAxis.data[0].categories=e.categories,e.yAxis.data[0].type="categories"))}return{rangesFormat:s,ranges:l,yAxisWidth:c}}function Jt(t,e){!0!==e.rotateLock?(t.translate(e.height,0),t.rotate(90*Math.PI/180)):!0!==e._rotate_&&(t.translate(e.height,0),t.rotate(90*Math.PI/180),e._rotate_=!0)}function te(t,e,i,a,o){if(a.beginPath(),"hollow"==o.dataPointShapeType?(a.setStrokeStyle(e),a.setFillStyle(o.background),a.setLineWidth(2*o.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(e),a.setLineWidth(1*o.pix)),"diamond"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))}));else if("circle"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x*****o.pix,t.y),a.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))}));else if("none"===i)return;a.closePath(),a.fill(),a.stroke()}function ee(t,e,i,a,o,r,n){if(o.tooltip&&!(o.tooltip.group.length>0&&0==o.tooltip.group.includes(n))){var l="number"==typeof o.tooltip.index?o.tooltip.index:o.tooltip.index[o.tooltip.group.indexOf(n)];if(a.beginPath(),"hollow"==r.activeType?(a.setStrokeStyle(e),a.setFillStyle(o.background),a.setLineWidth(2*o.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(e),a.setLineWidth(1*o.pix)),"diamond"===i)t.forEach((function(t,e){null!==t&&l==e&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))}));else if("circle"===i)t.forEach((function(t,e){null!==t&&l==e&&(a.moveTo(t.x*****o.pix,t.y),a.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===i)t.forEach((function(t,e){null!==t&&l==e&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===i)t.forEach((function(t,e){null!==t&&l==e&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))}));else if("none"===i)return;a.closePath(),a.fill(),a.stroke()}}function ie(t,e,i,a){var o=t.title.fontSize||e.titleFontSize,r=t.subtitle.fontSize||e.subtitleFontSize,n=t.title.name||"",l=t.subtitle.name||"",s=t.title.color||t.fontColor,c=t.subtitle.color||t.fontColor,h=n?o:0,d=l?r:0;if(l){var p=xt(l,r*t.pix,i),x=a.x-p/2+(t.subtitle.offsetX||0)*t.pix,u=a.y+r*t.pix/2+(t.subtitle.offsetY||0)*t.pix;n&&(u+=(h*t.pix+5)/2),i.beginPath(),i.setFontSize(r*t.pix),i.setFillStyle(c),i.fillText(l,x,u),i.closePath(),i.stroke()}if(n){var g=xt(n,o*t.pix,i),f=a.x-g/2+(t.title.offsetX||0),y=a.y+o*t.pix/2+(t.title.offsetY||0)*t.pix;l&&(y-=(d*t.pix+5)/2),i.beginPath(),i.setFontSize(o*t.pix),i.setFillStyle(s),i.fillText(n,f,y),i.closePath(),i.stroke()}}function ae(t,e,i,a,o){var r=e.data,n=e.textOffset?e.textOffset:0;t.forEach((function(t,l){if(null!==t){a.beginPath();var s=e.textSize?e.textSize*o.pix:i.fontSize;a.setFontSize(s),a.setFillStyle(e.textColor||o.fontColor);var c=r[l];"object"==typeof r[l]&&null!==r[l]&&(c=r[l].constructor.toString().indexOf("Array")>-1?r[l][1]:r[l].value);var h=e.formatter?e.formatter(c,l,e,o):c;a.setTextAlign("center"),a.fillText(String(h),t.x,t.y-4+n*o.pix),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function oe(t,e,i,a,o){var r=e.data,n=e.textOffset?e.textOffset:0,l=o.extra.column.labelPosition;t.forEach((function(t,s){if(null!==t){a.beginPath();var c=e.textSize?e.textSize*o.pix:i.fontSize;a.setFontSize(c),a.setFillStyle(e.textColor||o.fontColor);var h=r[s];"object"==typeof r[s]&&null!==r[s]&&(h=r[s].constructor.toString().indexOf("Array")>-1?r[s][1]:r[s].value);var d=e.formatter?e.formatter(h,s,e,o):h;a.setTextAlign("center");var p=t.y-4*o.pix+n*o.pix;t.y>e.zeroPoints&&(p=t.y+n*o.pix+c),"insideTop"==l&&(p=t.y+c+n*o.pix,t.y>e.zeroPoints&&(p=t.y-n*o.pix-4*o.pix)),"center"==l&&(p=t.y+n*o.pix+(o.height-o.area[2]-t.y+c)/2,e.zeroPoints<o.height-o.area[2]&&(p=t.y+n*o.pix+(e.zeroPoints-t.y+c)/2),t.y>e.zeroPoints&&(p=t.y-n*o.pix-(t.y-e.zeroPoints-c)/2),"stack"==o.extra.column.type&&(p=t.y+n*o.pix+(t.y0-t.y+c)/2)),"bottom"==l&&(p=o.height-o.area[2]+n*o.pix-4*o.pix,e.zeroPoints<o.height-o.area[2]&&(p=e.zeroPoints+n*o.pix-4*o.pix),t.y>e.zeroPoints&&(p=e.zeroPoints-n*o.pix+c+2*o.pix),"stack"==o.extra.column.type&&(p=t.y0+n*o.pix-4*o.pix)),a.fillText(String(d),t.x,p),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function re(t,e,i,a,o,r){e.data;var n=e.textOffset?e.textOffset:0;o.extra.mount.labelPosition,t.forEach((function(t,l){if(null!==t){a.beginPath();var s=e[l].textSize?e[l].textSize*o.pix:i.fontSize;a.setFontSize(s),a.setFillStyle(e[l].textColor||o.fontColor);var c=t.value,h=e[l].formatter?e[l].formatter(c,l,e,o):c;a.setTextAlign("center");var d=t.y-4*o.pix+n*o.pix;t.y>r&&(d=t.y+n*o.pix+s),a.fillText(String(h),t.x,d),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function ne(t,e,i,a,o){var r=e.data;e.textOffset&&e.textOffset,t.forEach((function(t,n){if(null!==t){a.beginPath();var l=e.textSize?e.textSize*o.pix:i.fontSize;a.setFontSize(l),a.setFillStyle(e.textColor||o.fontColor);var s=r[n];"object"==typeof r[n]&&null!==r[n]&&(s=r[n].value);var c=e.formatter?e.formatter(s,n,e,o):s;a.setTextAlign("left"),a.fillText(String(c),t.x+4*o.pix,t.y+l/2-3),a.closePath(),a.stroke()}}))}function le(t,e,i,a,o,r){let n;e=(e-=t.width/2+t.labelOffset*a.pix)<10?10:e,n=t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle;let l=n/t.splitLine.splitNumber,s=(t.endNumber-t.startNumber)/t.splitLine.splitNumber,c=t.startAngle,h=t.startNumber;for(let g=0;g<t.splitLine.splitNumber+1;g++){var d={x:e*Math.cos(c*Math.PI),y:e*Math.sin(c*Math.PI)},p=t.formatter?t.formatter(h,g,a):h;d.x+=i.x-xt(p,o.fontSize,r)/2,d.y+=i.y;var x=d.x,u=d.y;r.beginPath(),r.setFontSize(o.fontSize),r.setFillStyle(t.labelColor||a.fontColor),r.fillText(p,x,u+o.fontSize/2),r.closePath(),r.stroke(),c+=l,c>=2&&(c%=2),h+=s}}function se(t,e,i,a,o,r){var n=a.extra.radar||{};t.forEach((function(t,l){if(!0===n.labelPointShow&&""!==a.categories[l]){var s={x:e*Math.cos(t),y:e*Math.sin(t)},c=ct(s.x,s.y,i);r.setFillStyle(n.labelPointColor),r.beginPath(),r.arc(c.x,c.y,n.labelPointRadius*a.pix,0,2*Math.PI,!1),r.closePath(),r.fill()}if(!0===n.labelShow){var h={x:(e+o.radarLabelTextMargin*a.pix)*Math.cos(t),y:(e+o.radarLabelTextMargin*a.pix)*Math.sin(t)},d=ct(h.x,h.y,i),p=d.x,x=d.y;at.approximatelyEqual(h.x,0)?p-=xt(a.categories[l]||"",o.fontSize,r)/2:h.x<0&&(p-=xt(a.categories[l]||"",o.fontSize,r)),r.beginPath(),r.setFontSize(o.fontSize),r.setFillStyle(n.labelColor||a.fontColor),r.fillText(a.categories[l]||"",p,x+o.fontSize/2),r.closePath(),r.stroke()}}))}function ce(t,e,i,a,o,r){var n=i.pieChartLinePadding,l=[],s=null,c=t.map((function(i,a){var o=i.formatter?i.formatter(i,a,t,e):at.toFixed(100*i._proportion_.toFixed(4))+"%";o=i.labelText?i.labelText:o;var r=2*Math.PI-(i._start_+2*Math.PI*i._proportion_/2);return i._rose_proportion_&&(r=2*Math.PI-(i._start_+2*Math.PI*i._rose_proportion_/2)),{arc:r,text:o,color:i.color,radius:i._radius_,textColor:i.textColor,textSize:i.textSize,labelShow:i.labelShow}}));for(let h=0;h<c.length;h++){let t=c[h],o=Math.cos(t.arc)*(t.radius+n),r=Math.sin(t.arc)*(t.radius+n),d=Math.cos(t.arc)*t.radius,p=Math.sin(t.arc)*t.radius,x=o>=0?o+i.pieChartTextPadding:o-i.pieChartTextPadding,u=r,g=xt(t.text,t.textSize*e.pix||i.fontSize,a),f=u;s&&at.isSameXCoordinateArea(s.start,{x:x})&&(f=x>0?Math.min(u,s.start.y):o<0||u>0?Math.max(u,s.start.y):Math.min(u,s.start.y)),x<0&&(x-=g),s=ht({lineStart:{x:d,y:p},lineEnd:{x:o,y:r},start:{x:x,y:f},width:g,height:i.fontSize,text:t.text,color:t.color,textColor:t.textColor,textSize:t.textSize},s),l.push(s)}for(let h=0;h<l.length;h++){if(!1===c[h].labelShow)continue;let t=l[h],o=ct(t.lineStart.x,t.lineStart.y,r),n=ct(t.lineEnd.x,t.lineEnd.y,r),s=ct(t.start.x,t.start.y,r);a.setLineWidth(1*e.pix),a.setFontSize(t.textSize*e.pix||i.fontSize),a.beginPath(),a.setStrokeStyle(t.color),a.setFillStyle(t.color),a.moveTo(o.x,o.y);let d=t.start.x<0?s.x+t.width:s.x,p=t.start.x<0?s.x-5:s.x+5;a.quadraticCurveTo(n.x,n.y,d,s.y),a.moveTo(o.x,o.y),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(s.x+t.width,s.y),a.arc(d,s.y,2*e.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(t.textSize*e.pix||i.fontSize),a.setFillStyle(t.textColor||e.fontColor),a.fillText(t.text,p,s.y+3),a.closePath(),a.stroke(),a.closePath()}}function he(t,e,i){let a=it({},{type:"solid",dashLength:4,data:[]},t.extra.markLine),o=t.area[3],r=t.width-t.area[1],n=function(t,e){let i,a,o=e.height-e.area[0]-e.area[2];for(let r=0;r<t.length;r++){t[r].yAxisIndex=t[r].yAxisIndex?t[r].yAxisIndex:0;let n=[].concat(e.chartData.yAxisData.ranges[t[r].yAxisIndex]);i=n.pop(),a=n.shift();let l=o*(t[r].value-i)/(a-i);t[r].y=e.height-Math.round(l)-e.area[2]}return t}(a.data,t);for(let l=0;l<n.length;l++){let e=it({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},n[l]);if("dash"==a.type&&i.setLineDash([a.dashLength,a.dashLength]),i.setStrokeStyle(e.lineColor),i.setLineWidth(1*t.pix),i.beginPath(),i.moveTo(o,e.y),i.lineTo(r,e.y),i.stroke(),i.setLineDash([]),e.showLabel){let a=e.labelFontSize*t.pix,o=e.labelText?e.labelText:e.value;i.setFontSize(a);let r=xt(o,a,i)+e.labelPadding*t.pix*2,n="left"==e.labelAlign?t.area[3]-r:t.width-t.area[1];n+=e.labelOffsetX;let l=e.y-.5*a-e.labelPadding*t.pix;l+=e.labelOffsetY;let s=n+e.labelPadding*t.pix;e.y,i.setFillStyle(ot(e.labelBgColor,e.labelBgOpacity)),i.setStrokeStyle(e.labelBgColor),i.setLineWidth(1*t.pix),i.beginPath(),i.rect(n,l,r,a+2*e.labelPadding*t.pix),i.closePath(),i.stroke(),i.fill(),i.setFontSize(a),i.setTextAlign("left"),i.setFillStyle(e.labelFontColor),i.fillText(String(o),s,l+a+e.labelPadding*t.pix/2),i.stroke(),i.setTextAlign("left")}}}function de(t,e,i,a,o){var r=it({},{gridType:"solid",dashLength:4},t.extra.tooltip),n=t.area[3],l=t.width-t.area[1];if("dash"==r.gridType&&i.setLineDash([r.dashLength,r.dashLength]),i.setStrokeStyle(r.gridColor||"#cccccc"),i.setLineWidth(1*t.pix),i.beginPath(),i.moveTo(n,t.tooltip.offset.y),i.lineTo(l,t.tooltip.offset.y),i.stroke(),i.setLineDash([]),r.yAxisLabel){let a=r.boxPadding*t.pix,o=function(t,e,i,a,o){let r=[].concat(i.chartData.yAxisData.ranges),n=i.height-i.area[0]-i.area[2],l=i.area[0],s=[];for(let c=0;c<r.length;c++){let e=Math.max.apply(this,r[c]),a=e-(e-Math.min.apply(this,r[c]))*(t-l)/n;a=i.yAxis.data&&i.yAxis.data[c].formatter?i.yAxis.data[c].formatter(a,c,i):a.toFixed(0),s.push(String(a))}return s}(t.tooltip.offset.y,t.series,t),n=t.chartData.yAxisData.yAxisWidth,l=t.area[3],s=t.width-t.area[1];for(let c=0;c<o.length;c++){i.setFontSize(r.fontSize*t.pix);let h,d,p,x=xt(o[c],r.fontSize*t.pix,i);"left"==n[c].position?(h=l-(x+2*a)-2*t.pix,d=Math.max(h,h+x+2*a)):(h=s+2*t.pix,d=Math.max(h+n[c].width,h+x+2*a)),p=d-h;let u=h+(p-x)/2,g=t.tooltip.offset.y;i.beginPath(),i.setFillStyle(ot(r.labelBgColor||e.toolTipBackground,r.labelBgOpacity||e.toolTipOpacity)),i.setStrokeStyle(r.labelBgColor||e.toolTipBackground),i.setLineWidth(1*t.pix),i.rect(h,g-.5*e.fontSize-a,p,e.fontSize+2*a),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(e.fontSize),i.setFillStyle(r.labelFontColor||t.fontColor),i.fillText(o[c],u,g+.5*e.fontSize),i.closePath(),i.stroke(),"left"==n[c].position?l-=n[c].width+t.yAxis.padding*t.pix:s+=n[c].width+t.yAxis.padding*t.pix}}}function pe(t,e,i,a,o){var r=it({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:o},e.extra.column);r.activeWidth=r.activeWidth>o?o:r.activeWidth;var n=e.area[0],l=e.height-e.area[2];a.beginPath(),a.setFillStyle(ot(r.activeBgColor,r.activeBgOpacity)),a.rect(t-r.activeWidth/2,n,r.activeWidth,l-n),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function xe(t,e,i,a,o){var r=it({},{activeBgColor:"#000000",activeBgOpacity:.08},e.extra.bar),n=e.area[3],l=e.width-e.area[1];a.beginPath(),a.setFillStyle(ot(r.activeBgColor,r.activeBgOpacity)),a.rect(n,t-o/2,l-n,o),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function ue(t,e,i,a,o,r,n){var l=it({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},i.extra.tooltip);1==l.showCategory&&i.categories&&t.unshift({text:i.categories[i.tooltip.index],color:null});var s=l.fontSize*i.pix,c=l.lineHeight*i.pix,h=l.boxPadding*i.pix,d=s,p=5*i.pix;0==l.legendShow&&(d=0,p=0);var x=l.showArrow?8*i.pix:0,u=!1;"line"!=i.type&&"mount"!=i.type&&"area"!=i.type&&"candle"!=i.type&&"mix"!=i.type||1==l.splitLine&&function(t,e,i,a){var o=e.extra.tooltip||{};o.gridType=null==o.gridType?"solid":o.gridType,o.dashLength=null==o.dashLength?4:o.dashLength;var r=e.area[0],n=e.height-e.area[2];if("dash"==o.gridType&&a.setLineDash([o.dashLength,o.dashLength]),a.setStrokeStyle(o.gridColor||"#cccccc"),a.setLineWidth(1*e.pix),a.beginPath(),a.moveTo(t,r),a.lineTo(t,n),a.stroke(),a.setLineDash([]),o.xAxisLabel){let r=e.categories[e.tooltip.index];a.setFontSize(i.fontSize);let l=xt(r,i.fontSize,a),s=t-.5*l,c=n+2*e.pix;a.beginPath(),a.setFillStyle(ot(o.labelBgColor||i.toolTipBackground,o.labelBgOpacity||i.toolTipOpacity)),a.setStrokeStyle(o.labelBgColor||i.toolTipBackground),a.setLineWidth(1*e.pix),a.rect(s-o.boxPadding*e.pix,c,l+2*o.boxPadding*e.pix,i.fontSize+2*o.boxPadding*e.pix),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(o.labelFontColor||e.fontColor),a.fillText(String(r),s,c+o.boxPadding*e.pix+i.fontSize),a.closePath(),a.stroke()}}(i.tooltip.offset.x,i,a,o),(e=it({x:0,y:0},e)).y-=8*i.pix;var g=t.map((function(t){return xt(t.text,s,o)})),f=d+p+4*h+Math.max.apply(null,g),y=2*h+t.length*c;if(0!=l.showBox){e.x-Math.abs(i._scrollDistance_||0)+x+f>i.width&&(u=!0),y+e.y>i.height&&(e.y=i.height-y),o.beginPath(),o.setFillStyle(ot(l.bgColor,l.bgOpacity)),o.setLineWidth(l.borderWidth*i.pix),o.setStrokeStyle(ot(l.borderColor,l.borderOpacity));var m=l.borderRadius;u?(f+x>i.width&&(e.x=i.width+Math.abs(i._scrollDistance_||0)+x+(f-i.width)),f>e.x&&(e.x=i.width+Math.abs(i._scrollDistance_||0)+x+(f-i.width)),l.showArrow&&(o.moveTo(e.x,e.y+10*i.pix),o.lineTo(e.x-x,e.y+10*i.pix+5*i.pix)),o.arc(e.x-x-m,e.y+y-m,m,0,Math.PI/2,!1),o.arc(e.x-x-Math.round(f)+m,e.y+y-m,m,Math.PI/2,Math.PI,!1),o.arc(e.x-x-Math.round(f)+m,e.y+m,m,-Math.PI,-Math.PI/2,!1),o.arc(e.x-x-m,e.y+m,m,-Math.PI/2,0,!1),l.showArrow&&(o.lineTo(e.x-x,e.y+10*i.pix-5*i.pix),o.lineTo(e.x,e.y+10*i.pix))):(l.showArrow&&(o.moveTo(e.x,e.y+10*i.pix),o.lineTo(e.x+x,e.y+10*i.pix-5*i.pix)),o.arc(e.x+x+m,e.y+m,m,-Math.PI,-Math.PI/2,!1),o.arc(e.x+x+Math.round(f)-m,e.y+m,m,-Math.PI/2,0,!1),o.arc(e.x+x+Math.round(f)-m,e.y+y-m,m,0,Math.PI/2,!1),o.arc(e.x+x+m,e.y+y-m,m,Math.PI/2,Math.PI,!1),l.showArrow&&(o.lineTo(e.x+x,e.y+10*i.pix+5*i.pix),o.lineTo(e.x,e.y+10*i.pix))),o.closePath(),o.fill(),l.borderWidth>0&&o.stroke(),l.legendShow&&t.forEach((function(t,a){if(null!==t.color){o.beginPath(),o.setFillStyle(t.color);var r=e.x+x+2*h,n=e.y+(c-s)/2+c*a+h+1;switch(u&&(r=e.x-f-x+2*h),t.legendShape){case"line":o.moveTo(r,n+.5*d-2*i.pix),o.fillRect(r,n+.5*d-2*i.pix,d,4*i.pix);break;case"triangle":o.moveTo(r+7.5*i.pix,n+.5*d-5*i.pix),o.lineTo(r*****i.pix,n+.5*d+5*i.pix),o.lineTo(r+12.5*i.pix,n+.5*d+5*i.pix),o.lineTo(r+7.5*i.pix,n+.5*d-5*i.pix);break;case"diamond":o.moveTo(r+7.5*i.pix,n+.5*d-5*i.pix),o.lineTo(r*****i.pix,n+.5*d),o.lineTo(r+7.5*i.pix,n+.5*d+5*i.pix),o.lineTo(r+12.5*i.pix,n+.5*d),o.lineTo(r+7.5*i.pix,n+.5*d-5*i.pix);break;case"circle":o.moveTo(r+7.5*i.pix,n+.5*d),o.arc(r+7.5*i.pix,n+.5*d,5*i.pix,0,2*Math.PI);break;case"rect":default:o.moveTo(r,n+.5*d-5*i.pix),o.fillRect(r,n+.5*d-5*i.pix,15*i.pix,10*i.pix);break;case"square":o.moveTo(r+2*i.pix,n+.5*d-5*i.pix),o.fillRect(r+2*i.pix,n+.5*d-5*i.pix,10*i.pix,10*i.pix)}o.closePath(),o.fill()}})),t.forEach((function(t,i){var a=e.x+x+2*h+d+p;u&&(a=e.x-f-x+2*h+d+p);var r=e.y+c*i+(c-s)/2-1+h+s;o.beginPath(),o.setFontSize(s),o.setTextBaseline("normal"),o.setFillStyle(l.fontColor),o.fillText(t.text,a,r),o.closePath(),o.stroke()}))}}function ge(t,e,i,a,o,r){(t.extra.tooltip||{}).horizentalLine&&t.tooltip&&1===a&&("line"==t.type||"area"==t.type||"column"==t.type||"mount"==t.type||"candle"==t.type||"mix"==t.type)&&de(t,e,i),i.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&i.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===a&&ue(t.tooltip.textList,t.tooltip.offset,t,e,i),i.restore()}function fe(t,e,i,a){let o=e.chartData.xAxisData,r=o.xAxisPoints,n=o.startX,l=o.endX,s=o.eachSpacing;var c="center";"bar"!=e.type&&"line"!=e.type&&"area"!=e.type&&"scatter"!=e.type&&"bubble"!=e.type||(c=e.xAxis.boundaryGap);var h=e.height-e.area[2],d=e.area[0];if(e.enableScroll&&e.xAxis.scrollShow){var p=e.height-e.area[2]+i.xAxisHeight,x=l-n,u=s*(r.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),u+=(e.extra.mount.widthRatio-1)*s);var g=x*x/u,f=0;e._scrollDistance_&&(f=-e._scrollDistance_*x/u),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pix),a.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),a.moveTo(n,p),a.lineTo(l,p),a.stroke(),a.closePath(),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pix),a.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),a.moveTo(n+f,p),a.lineTo(n+f+g,p),a.stroke(),a.closePath(),a.setLineCap("butt")}if(a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&a.translate(e._scrollDistance_,0),!0===e.xAxis.calibration&&(a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pix),r.forEach((function(t,i){i>0&&(a.beginPath(),a.moveTo(t-s/2,h),a.lineTo(t-s/2,h+3*e.pix),a.closePath(),a.stroke())}))),!0!==e.xAxis.disableGrid&&(a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pix),"dash"==e.xAxis.gridType&&a.setLineDash([e.xAxis.dashLength*e.pix,e.xAxis.dashLength*e.pix]),e.xAxis.gridEval=e.xAxis.gridEval||1,r.forEach((function(t,i){i%e.xAxis.gridEval==0&&(a.beginPath(),a.moveTo(t,h),a.lineTo(t,d),a.stroke())})),a.setLineDash([])),!0!==e.xAxis.disabled){let o=t.length;e.xAxis.labelCount&&(o=e.xAxis.itemCount?Math.ceil(t.length/e.xAxis.itemCount*e.xAxis.labelCount):e.xAxis.labelCount,o-=1);let n=Math.ceil(t.length/o),l=[],d=t.length;for(let e=0;e<d;e++)e%n!=0?l.push(""):l.push(t[e]);l[d-1]=t[d-1];var y=e.xAxis.fontSize*e.pix||i.fontSize;0===i._xAxisTextAngle_?l.forEach((function(t,i){var o=e.xAxis.formatter?e.xAxis.formatter(t,i,e):t,n=-xt(String(o),y,a)/2;"center"==c&&(n+=s/2),e.xAxis.scrollShow&&e.pix;var l=e._scrollDistance_||0,d="center"==c?r[i]+s/2:r[i];d-Math.abs(l)>=e.area[3]-1&&d-Math.abs(l)<=e.width-e.area[1]+1&&(a.beginPath(),a.setFontSize(y),a.setFillStyle(e.xAxis.fontColor||e.fontColor),a.fillText(String(o),r[i]+n,h+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.fontSize)*e.pix/2+e.xAxis.fontSize*e.pix),a.closePath(),a.stroke())})):l.forEach((function(t,o){var n=e.xAxis.formatter?e.xAxis.formatter(t):t,l=e._scrollDistance_||0,d="center"==c?r[o]+s/2:r[o];if(d-Math.abs(l)>=e.area[3]-1&&d-Math.abs(l)<=e.width-e.area[1]+1){a.save(),a.beginPath(),a.setFontSize(y),a.setFillStyle(e.xAxis.fontColor||e.fontColor);var p=xt(String(n),y,a),x=r[o];"center"==c&&(x=r[o]+s/2),e.xAxis.scrollShow&&e.pix;var u=h+e.xAxis.marginTop*e.pix+y-y*Math.abs(Math.sin(i._xAxisTextAngle_));e.xAxis.rotateAngle<0?(x-=y/2,p=0):(x+=y/2,p=-p),a.translate(x,u),a.rotate(-1*i._xAxisTextAngle_),a.fillText(String(n),p,0),a.closePath(),a.stroke(),a.restore()}}))}a.restore(),e.xAxis.title&&(a.beginPath(),a.setFontSize(e.xAxis.titleFontSize*e.pix),a.setFillStyle(e.xAxis.titleFontColor),a.fillText(String(e.xAxis.title),e.width-e.area[1]+e.xAxis.titleOffsetX*e.pix,e.height-e.area[2]+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.titleFontSize)*e.pix/2+(e.xAxis.titleFontSize+e.xAxis.titleOffsetY)*e.pix),a.closePath(),a.stroke()),e.xAxis.axisLine&&(a.beginPath(),a.setStrokeStyle(e.xAxis.axisLineColor),a.setLineWidth(1*e.pix),a.moveTo(n,e.height-e.area[2]),a.lineTo(l,e.height-e.area[2]),a.stroke())}function ye(t,e,i,a){if(!0===e.yAxis.disableGrid)return;let o=(e.height-e.area[0]-e.area[2])/e.yAxis.splitNumber,r=e.area[3],n=e.chartData.xAxisData.xAxisPoints,l=e.chartData.xAxisData.eachSpacing,s=l*(n.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),s+=(e.extra.mount.widthRatio-1)*l);let c=r+s,h=[],d=1;!1===e.xAxis.axisLine&&(d=0);for(let p=d;p<e.yAxis.splitNumber+1;p++)h.push(e.height-e.area[2]-o*p);a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&a.translate(e._scrollDistance_,0),"dash"==e.yAxis.gridType&&a.setLineDash([e.yAxis.dashLength*e.pix,e.yAxis.dashLength*e.pix]),a.setStrokeStyle(e.yAxis.gridColor),a.setLineWidth(1*e.pix),h.forEach((function(t,e){a.beginPath(),a.moveTo(r,t),a.lineTo(c,t),a.stroke()})),a.setLineDash([]),a.restore()}function me(t,e,i,a){if(!0===e.yAxis.disabled)return;var o=e.height-e.area[0]-e.area[2],r=o/e.yAxis.splitNumber,n=e.area[3],l=e.width-e.area[1],s=e.height-e.area[2];a.beginPath(),a.setFillStyle(e.background),1==e.enableScroll&&e.xAxis.scrollPosition&&"left"!==e.xAxis.scrollPosition&&a.fillRect(0,0,n,s+2*e.pix),1==e.enableScroll&&e.xAxis.scrollPosition&&"right"!==e.xAxis.scrollPosition&&a.fillRect(l,0,e.width,s+2*e.pix),a.closePath(),a.stroke();let c=e.area[3],h=e.width-e.area[1],d=e.area[3]+(e.width-e.area[1]-e.area[3])/2;if(e.yAxis.data)for(let x=0;x<e.yAxis.data.length;x++){let t=e.yAxis.data[x];var p=[];if("categories"===t.type)for(let i=0;i<=t.categories.length;i++)p.push(e.area[0]+o/t.categories.length/2+o/t.categories.length*i);else for(let i=0;i<=e.yAxis.splitNumber;i++)p.push(e.area[0]+r*i);if(!0!==t.disabled){let o=e.chartData.yAxisData.rangesFormat[x],r=t.fontSize?t.fontSize*e.pix:i.fontSize,n=e.chartData.yAxisData.yAxisWidth[x],l=t.textAlign||"right";if(o.forEach((function(i,o){var s=p[o];a.beginPath(),a.setFontSize(r),a.setLineWidth(1*e.pix),a.setStrokeStyle(t.axisLineColor||"#cccccc"),a.setFillStyle(t.fontColor||e.fontColor);let x=0,u=4*e.pix;if("left"==n.position){switch(1==t.calibration&&(a.moveTo(c,s),a.lineTo(c-3*e.pix,s),u+=3*e.pix),l){case"left":a.setTextAlign("left"),x=c-n.width;break;case"right":a.setTextAlign("right"),x=c-u;break;default:a.setTextAlign("center"),x=c-n.width/2}a.fillText(String(i),x,s+r/2-3*e.pix)}else if("right"==n.position){switch(1==t.calibration&&(a.moveTo(h,s),a.lineTo(h+3*e.pix,s),u+=3*e.pix),l){case"left":a.setTextAlign("left"),x=h+u;break;case"right":a.setTextAlign("right"),x=h+n.width;break;default:a.setTextAlign("center"),x=h+n.width/2}a.fillText(String(i),x,s+r/2-3*e.pix)}else if("center"==n.position){switch(1==t.calibration&&(a.moveTo(d,s),a.lineTo(d-3*e.pix,s),u+=3*e.pix),l){case"left":a.setTextAlign("left"),x=d-n.width;break;case"right":a.setTextAlign("right"),x=d-u;break;default:a.setTextAlign("center"),x=d-n.width/2}a.fillText(String(i),x,s+r/2-3*e.pix)}a.closePath(),a.stroke(),a.setTextAlign("left")})),!1!==t.axisLine&&(a.beginPath(),a.setStrokeStyle(t.axisLineColor||"#cccccc"),a.setLineWidth(1*e.pix),"left"==n.position?(a.moveTo(c,e.height-e.area[2]),a.lineTo(c,e.area[0])):"right"==n.position?(a.moveTo(h,e.height-e.area[2]),a.lineTo(h,e.area[0])):"center"==n.position&&(a.moveTo(d,e.height-e.area[2]),a.lineTo(d,e.area[0])),a.stroke()),e.yAxis.showTitle){let o=t.titleFontSize*e.pix||i.fontSize,r=t.title;a.beginPath(),a.setFontSize(o),a.setFillStyle(t.titleFontColor||e.fontColor),"left"==n.position?a.fillText(r,c-xt(r,o,a)/2+(t.titleOffsetX||0),e.area[0]-(10-(t.titleOffsetY||0))*e.pix):"right"==n.position?a.fillText(r,h-xt(r,o,a)/2+(t.titleOffsetX||0),e.area[0]-(10-(t.titleOffsetY||0))*e.pix):"center"==n.position&&a.fillText(r,d-xt(r,o,a)/2+(t.titleOffsetX||0),e.area[0]-(10-(t.titleOffsetY||0))*e.pix),a.closePath(),a.stroke()}"left"==n.position?c-=n.width+e.yAxis.padding*e.pix:h+=n.width+e.yAxis.padding*e.pix}}}function ve(t,e,i,a,o){if(!1===e.legend.show)return;let r=o.legendData,n=r.points,l=r.area,s=e.legend.padding*e.pix,c=e.legend.fontSize*e.pix,h=15*e.pix,d=5*e.pix,p=e.legend.itemGap*e.pix,x=Math.max(e.legend.lineHeight*e.pix,c);a.beginPath(),a.setLineWidth(e.legend.borderWidth*e.pix),a.setStrokeStyle(e.legend.borderColor),a.setFillStyle(e.legend.backgroundColor),a.moveTo(l.start.x,l.start.y),a.rect(l.start.x,l.start.y,l.width,l.height),a.closePath(),a.fill(),a.stroke(),n.forEach((function(t,o){let n=0,u=0;n=r.widthArr[o],u=r.heightArr[o];let g=0,f=0;if("top"==e.legend.position||"bottom"==e.legend.position){switch(e.legend.float){case"left":g=l.start.x+s;break;case"right":g=l.start.x+l.width-n;break;default:g=l.start.x+(l.width-n)/2}f=l.start.y+s+o*x}else n=0==o?0:r.widthArr[o-1],g=l.start.x+s+n,f=l.start.y+s+(l.height-u)/2;a.setFontSize(i.fontSize);for(let i=0;i<t.length;i++){let o=t[i];switch(o.area=[0,0,0,0],o.area[0]=g,o.area[1]=f,o.area[3]=f+x,a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(o.show?o.color:e.legend.hiddenColor),a.setFillStyle(o.show?o.color:e.legend.hiddenColor),o.legendShape){case"line":a.moveTo(g,f+.5*x-2*e.pix),a.fillRect(g,f+.5*x-2*e.pix,15*e.pix,4*e.pix);break;case"triangle":a.moveTo(g+7.5*e.pix,f+.5*x-5*e.pix),a.lineTo(g*****e.pix,f+.5*x+5*e.pix),a.lineTo(g+12.5*e.pix,f+.5*x+5*e.pix),a.lineTo(g+7.5*e.pix,f+.5*x-5*e.pix);break;case"diamond":a.moveTo(g+7.5*e.pix,f+.5*x-5*e.pix),a.lineTo(g*****e.pix,f+.5*x),a.lineTo(g+7.5*e.pix,f+.5*x+5*e.pix),a.lineTo(g+12.5*e.pix,f+.5*x),a.lineTo(g+7.5*e.pix,f+.5*x-5*e.pix);break;case"circle":a.moveTo(g+7.5*e.pix,f+.5*x),a.arc(g+7.5*e.pix,f+.5*x,5*e.pix,0,2*Math.PI);break;case"rect":default:a.moveTo(g,f+.5*x-5*e.pix),a.fillRect(g,f+.5*x-5*e.pix,15*e.pix,10*e.pix);break;case"square":a.moveTo(g+5*e.pix,f+.5*x-5*e.pix),a.fillRect(g+5*e.pix,f+.5*x-5*e.pix,10*e.pix,10*e.pix);case"none":}a.closePath(),a.fill(),a.stroke(),g+=h+d;let r=.5*x+.5*c-2;const n=o.legendText?o.legendText:o.name;a.beginPath(),a.setFontSize(c),a.setFillStyle(o.show?e.legend.fontColor:e.legend.hiddenColor),a.fillText(n,g,f+r),a.closePath(),a.stroke(),"top"==e.legend.position||"bottom"==e.legend.position?(g+=xt(n,c,a)+p,o.area[2]=g):(o.area[2]=g+xt(n,c,a)+p,g-=h+d,f+=x)}}))}function be(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==e.type?e.extra.pie:e.extra.ring),n={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2};0==i.pieChartLinePadding&&(i.pieChartLinePadding=r.activeRadius*e.pix);var l=Math.min((e.width-e.area[1]-e.area[3])/2-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-i.pieChartLinePadding-i.pieChartTextPadding);l=l<10?10:l,r.customRadius>0&&(l=r.customRadius*e.pix),t=Ft(t,l,o);var s=r.activeRadius*e.pix;if(r.customColor=pt(r.linearType,r.customColor,t,i),(t=t.map((function(t){return t._start_+=r.offsetAngle*Math.PI/180,t}))).forEach((function(t,i){e.tooltip&&e.tooltip.index==i&&(a.beginPath(),a.setFillStyle(ot(t.color,r.activeOpacity||.5)),a.moveTo(n.x,n.y),a.arc(n.x,n.y,t._radius_+s,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.lineJoin="round",a.setStrokeStyle(r.borderColor);var o,l=t.color;"custom"==r.linearType&&((o=a.createCircularGradient?a.createCircularGradient(n.x,n.y,t._radius_):a.createRadialGradient(n.x,n.y,0,n.x,n.y,t._radius_)).addColorStop(0,ot(r.customColor[t.linearIndex],1)),o.addColorStop(1,ot(t.color,1)),l=o);a.setFillStyle(l),a.moveTo(n.x,n.y),a.arc(n.x,n.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill(),1==r.border&&a.stroke()})),"ring"===e.type){var c=.6*l;"number"==typeof r.ringWidth&&r.ringWidth>0&&(c=Math.max(0,l-r.ringWidth*e.pix)),a.beginPath(),a.setFillStyle(r.centerColor),a.moveTo(n.x,n.y),a.arc(n.x,n.y,c,0,2*Math.PI),a.closePath(),a.fill()}return!1!==e.dataLabel&&1===o&&ce(t,e,i,a,0,n),1===o&&"ring"===e.type&&ie(e,i,a,n),{center:n,radius:l,series:t}}function Ce(t,e){var i=Array(2),a=20037508.34*t/180,o=Math.log(Math.tan((90+e)*Math.PI/360))/(Math.PI/180);return o=20037508.34*o/180,i[0]=a,i[1]=o,i}function Se(t,e,i,a,o,r){return{x:(e-i.xMin)*a+o,y:(i.yMax-t)*a+r}}function we(t,e,i){if(e[1]==i[1])return!1;if(e[1]>t[1]&&i[1]>t[1])return!1;if(e[1]<t[1]&&i[1]<t[1])return!1;if(e[1]==t[1]&&i[1]>t[1])return!1;if(i[1]==t[1]&&e[1]>t[1])return!1;if(e[0]<t[0]&&i[1]<t[1])return!1;return!(i[0]-(i[0]-e[0])*(i[1]-t[1])/(i[1]-e[1])<t[0])}function Le(t,e,i){let a=0;for(let o=0;o<e.length;o++){let r=e[o][0];1==e.length&&(r=e[o][0]);for(let e=0;e<r.length-1;e++){let o=r[e],n=r[e+1];i&&(o=Ce(r[e][0],r[e][1]),n=Ce(r[e+1][0],r[e+1][1])),we(t,o,n)&&(a+=1)}}return a%2==1}function Te(t,e,i){i=0==i?1:i;for(var a=[],o=0;o<i;o++)a[o]=Math.random();return Math.floor(a.reduce((function(t,e){return t+e}))/i*(e-t))+t}function Ae(t,e,i,a){var o=!1;for(let r=0;r<e.length;r++)if(e[r].area){if(!(t[3]<e[r].area[1]||t[0]>e[r].area[2]||t[1]>e[r].area[3]||t[2]<e[r].area[0])){o=!0;break}if(t[0]<0||t[1]<0||t[2]>i||t[3]>a){o=!0;break}o=!1}return o}function Pe(t,e,i){let a=t.series;switch(e){case"normal":for(let o=0;o<a.length;o++){let e,r,n,l=a[o].name,s=a[o].textSize*t.pix,c=xt(l,s,i),h=0;for(;;){if(h++,e=Te(-t.width/2,t.width/2,5)-c/2,r=Te(-t.height/2,t.height/2,5)+s/2,n=[e-5+t.width/2,r-5-s+t.height/2,e+c+5+t.width/2,r+5+t.height/2],!Ae(n,a,t.width,t.height))break;if(1e3==h){n=[-100,-100,-100,-100];break}}a[o].area=n}break;case"vertical":let e=function(){return Math.random()>.7};for(let o=0;o<a.length;o++){let r,n,l,s,c=a[o].name,h=a[o].textSize*t.pix,d=xt(c,h,i),p=e(),x=0;for(;;){let e;if(x++,p?(r=Te(-t.width/2,t.width/2,5)-d/2,n=Te(-t.height/2,t.height/2,5)+h/2,l=[n-5-d+t.width/2,-r-5+t.height/2,n+5+t.width/2,-r+h+5+t.height/2],s=[t.width-(t.width/2-t.height/2)-(-r+h+5+t.height/2)-5,t.height/2-t.width/2+(n-5-d+t.width/2)-5,t.width-(t.width/2-t.height/2)-(-r+h+5+t.height/2)+h,t.height/2-t.width/2+(n-5-d+t.width/2)+d+5],e=Ae(s,a,t.height,t.width)):(r=Te(-t.width/2,t.width/2,5)-d/2,n=Te(-t.height/2,t.height/2,5)+h/2,l=[r-5+t.width/2,n-5-h+t.height/2,r+d+5+t.width/2,n+5+t.height/2],e=Ae(l,a,t.width,t.height)),!e)break;if(1e3==x){l=[-1e3,-1e3,-1e3,-1e3];break}}p?(a[o].area=s,a[o].areav=l):a[o].area=l,a[o].rotate=p}}return a}function _e(t,e,i,a,o,r,n){for(let l=0;l<t.length;l++){let s,c,h,d,p=t[l];if(!1===p.labelShow)continue;let x=p.formatter?p.formatter(p,l,t,e):at.toFixed(100*p._proportion_)+"%";x=p.labelText?p.labelText:x,"right"==o&&(s=l==t.length-1?(p.funnelArea[2]+n.x)/2:(p.funnelArea[2]+t[l+1].funnelArea[2])/2,c=s+2*r,h=p.funnelArea[1]+a/2,d=p.textSize*e.pix||e.fontSize*e.pix,i.setLineWidth(1*e.pix),i.setStrokeStyle(p.color),i.setFillStyle(p.color),i.beginPath(),i.moveTo(s,h),i.lineTo(c,h),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(c,h),i.arc(c,h,2*e.pix,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(d),i.setFillStyle(p.textColor||e.fontColor),i.fillText(x,c+5,h+d/2-2),i.closePath(),i.stroke(),i.closePath()),"left"==o&&(s=l==t.length-1?(p.funnelArea[0]+n.x)/2:(p.funnelArea[0]+t[l+1].funnelArea[0])/2,c=s-2*r,h=p.funnelArea[1]+a/2,d=p.textSize*e.pix||e.fontSize*e.pix,i.setLineWidth(1*e.pix),i.setStrokeStyle(p.color),i.setFillStyle(p.color),i.beginPath(),i.moveTo(s,h),i.lineTo(c,h),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(c,h),i.arc(c,h,2,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(d),i.setFillStyle(p.textColor||e.fontColor),i.fillText(x,c-5-xt(x,d,i),h+d/2-2),i.closePath(),i.stroke(),i.closePath())}}function ke(t,e,i,a,o,r,n){for(let l=0;l<t.length;l++){let o,r,s=t[l];s.centerText&&(o=s.funnelArea[1]+a/2,r=s.centerTextSize*e.pix||e.fontSize*e.pix,i.beginPath(),i.setFontSize(r),i.setFillStyle(s.centerTextColor||"#FFFFFF"),i.fillText(s.centerText,n.x-xt(s.centerText,r,i)/2,o+r/2-2),i.closePath(),i.stroke(),i.closePath())}}function Fe(t,e){e.save(),e.translate(0,.5),e.restore(),e.draw()}var Me={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};function De(t){this.isStop=!1,t.duration=void 0===t.duration?1e3:t.duration,t.timing=t.timing||"easeInOut";var e="undefined"!=typeof setTimeout?function(t,e){setTimeout((function(){t(+new Date)}),e)}:"undefined"!=typeof requestAnimationFrame?requestAnimationFrame:function(t){t(null)},i=null,a=function(o){if(null===o||!0===this.isStop)return t.onProcess&&t.onProcess(1),void(t.onAnimationFinish&&t.onAnimationFinish());if(null===i&&(i=o),o-i<t.duration){var r=(o-i)/t.duration;r=(0,Me[t.timing])(r),t.onProcess&&t.onProcess(r),e(a,17)}else t.onProcess&&t.onProcess(1),t.onAnimationFinish&&t.onAnimationFinish()};a=a.bind(this),e(a,17)}function Ie(t,e,i,a){var o=this,r=e.series;"pie"!==t&&"ring"!==t&&"mount"!==t&&"rose"!==t&&"funnel"!==t||(r=function(t,e,i){let a=[];if(t.length>0&&t[0].data.constructor.toString().indexOf("Array")>-1){e._pieSeries_=t;let i=t[0].data;for(var o=0;o<i.length;o++)i[o].formatter=t[0].formatter,i[o].data=i[o].value,a.push(i[o]);e.series=a}else a=t;return a}(r,e));var n=e.categories;if("mount"===t){n=[];for(let t=0;t<r.length;t++)!1!==r[t].show&&n.push(r[t].name);e.categories=n}r=dt(r,e,i);var l=e.animation?e.duration:0;o.animationInstance&&o.animationInstance.stop();var s=null;if("candle"==t){let t=it({},e.extra.candle.average);t.show?(s=dt(s=function(t,e,i,a){let o=[];for(let r=0;r<t.length;r++){let n={data:[],name:e[r],color:i[r]};for(let e=0,i=a.length;e<i;e++){if(e<t[r]){n.data.push(null);continue}let i=0;for(let o=0;o<t[r];o++)i+=a[e-o][1];n.data.push(+(i/t[r]).toFixed(3))}o.push(n)}return o}(t.day,t.name,t.color,r[0].data),e,i),e.seriesMA=s):s=e.seriesMA?e.seriesMA=dt(e.seriesMA,e,i):r}else s=r;e._series_=r=wt(r),e.area=new Array(4);for(let u=0;u<4;u++)e.area[u]=e.padding[u]*e.pix;var c=function(t,e,i,a,o){let r={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===e.legend.show)return a.legendData=r,r;let n=e.legend.padding*e.pix,l=e.legend.margin*e.pix,s=e.legend.fontSize?e.legend.fontSize*e.pix:i.fontSize,c=15*e.pix,h=5*e.pix,d=Math.max(e.legend.lineHeight*e.pix,s);if("top"==e.legend.position||"bottom"==e.legend.position){let i=[],a=0,p=[],x=[];for(let r=0;r<t.length;r++){let n=t[r],l=c+h+xt((n.legendText?n.legendText:n.name)||"undefined",s,o)+e.legend.itemGap*e.pix;a+l>e.width-e.area[1]-e.area[3]?(i.push(x),p.push(a-e.legend.itemGap*e.pix),a=l,x=[n]):(a+=l,x.push(n))}if(x.length){i.push(x),p.push(a-e.legend.itemGap*e.pix),r.widthArr=p;let t=Math.max.apply(null,p);switch(e.legend.float){case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+t+2*n;break;case"right":r.area.start.x=e.width-e.area[1]-t-2*n,r.area.end.x=e.width-e.area[1];break;default:r.area.start.x=(e.width-t)/2-n,r.area.end.x=(e.width+t)/2+n}r.area.width=t+2*n,r.area.wholeWidth=t+2*n,r.area.height=i.length*d+2*n,r.area.wholeHeight=i.length*d+2*n+2*l,r.points=i}}else{let i=t.length,a=e.height-e.area[0]-e.area[2]-2*l-2*n,p=Math.min(Math.floor(a/d),i);switch(r.area.height=p*d+2*n,r.area.wholeHeight=p*d+2*n,e.legend.float){case"top":r.area.start.y=e.area[0]+l,r.area.end.y=e.area[0]+l+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-l-r.area.height,r.area.end.y=e.height-e.area[2]-l;break;default:r.area.start.y=(e.height-r.area.height)/2,r.area.end.y=(e.height+r.area.height)/2}let x=i%p==0?i/p:Math.floor(i/p+1),u=[];for(let e=0;e<x;e++){let i=t.slice(e*p,e*p+p);u.push(i)}if(r.points=u,u.length){for(let i=0;i<u.length;i++){let t=u[i],a=0;for(let i=0;i<t.length;i++){let r=c+h+xt(t[i].name||"undefined",s,o)+e.legend.itemGap*e.pix;r>a&&(a=r)}r.widthArr.push(a),r.heightArr.push(t.length*d+2*n)}let t=0;for(let e=0;e<r.widthArr.length;e++)t+=r.widthArr[e];r.area.width=t-e.legend.itemGap*e.pix+2*n,r.area.wholeWidth=r.area.width+n}}switch(e.legend.position){case"top":r.area.start.y=e.area[0]+l,r.area.end.y=e.area[0]+l+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-r.area.height-l,r.area.end.y=e.height-e.area[2]-l;break;case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+r.area.width;break;case"right":r.area.start.x=e.width-e.area[1]-r.area.width,r.area.end.x=e.width-e.area[1]}return a.legendData=r,r}(s,e,i,e.chartData,a),h=c.area.wholeHeight,d=c.area.wholeWidth;switch(e.legend.position){case"top":e.area[0]+=h;break;case"bottom":e.area[2]+=h;break;case"left":e.area[3]+=d;break;case"right":e.area[1]+=d}let p={},x=0;if("line"===e.type||"column"===e.type||"mount"===e.type||"area"===e.type||"mix"===e.type||"candle"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){if(p=Kt(r,e,i,a),x=p.yAxisWidth,e.yAxis.showTitle){let t=0;for(let a=0;a<e.yAxis.data.length;a++)t=Math.max(t,e.yAxis.data[a].titleFontSize?e.yAxis.data[a].titleFontSize*e.pix:i.fontSize);e.area[0]+=t}let t=0,o=0;for(let i=0;i<x.length;i++)"left"==x[i].position?(e.area[3]+=o>0?x[i].width+e.yAxis.padding*e.pix:x[i].width,o+=1):"right"==x[i].position&&(e.area[1]+=t>0?x[i].width+e.yAxis.padding*e.pix:x[i].width,t+=1)}else i.yAxisWidth=x;if(e.chartData.yAxisData=p,e.categories&&e.categories.length&&"radar"!==e.type&&"gauge"!==e.type&&"bar"!==e.type){e.chartData.xAxisData=Zt(e.categories,e);let t=Pt(e.categories,e,0,e.chartData.xAxisData.eachSpacing,a),o=t.xAxisHeight,r=t.angle;i.xAxisHeight=o,i._xAxisTextAngle_=r,e.area[2]+=o,e.chartData.categoriesData=t}else if("line"===e.type||"area"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){e.chartData.xAxisData=_t(r,e,i,a);let t=Pt(n=e.chartData.xAxisData.rangesFormat,e,0,e.chartData.xAxisData.eachSpacing,a),o=t.xAxisHeight,l=t.angle;i.xAxisHeight=o,i._xAxisTextAngle_=l,e.area[2]+=o,e.chartData.categoriesData=t}else e.chartData.xAxisData={xAxisPoints:[]};if(e.enableScroll&&"right"==e.xAxis.scrollAlign&&void 0===e._scrollDistance_){let t=0,i=e.chartData.xAxisData.xAxisPoints,a=e.chartData.xAxisData.startX;t=e.chartData.xAxisData.endX-a-e.chartData.xAxisData.eachSpacing*(i.length-1),o.scrollOption.currentOffset=t,o.scrollOption.startTouchX=t,o.scrollOption.distance=0,o.scrollOption.lastMoveTime=0,e._scrollDistance_=t}switch("pie"!==t&&"ring"!==t&&"rose"!==t||(i._pieTextMaxLength_=!1===e.dataLabel?0:function(t,e,i,a){t=Ft(t);let o=0;for(let r=0;r<t.length;r++){let n=t[r],l=n.formatter?n.formatter(+n._proportion_.toFixed(2)):at.toFixed(100*n._proportion_)+"%";o=Math.max(o,xt(l,n.textSize*a.pix||e.fontSize,i))}return o}(s,i,a,e)),t){case"word":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{type:"normal",autoColors:!0},e.extra.word);e.chartData.wordCloudData||(e.chartData.wordCloudData=Pe(e,r.type,a)),a.beginPath(),a.setFillStyle(e.background),a.rect(0,0,e.width,e.height),a.fill(),a.save();let n=e.chartData.wordCloudData;a.translate(e.width/2,e.height/2);for(let l=0;l<n.length;l++){a.save(),n[l].rotate&&a.rotate(90*Math.PI/180);let t=n[l].name,i=n[l].textSize*e.pix,r=xt(t,i,a);a.beginPath(),a.setStrokeStyle(n[l].color),a.setFillStyle(n[l].color),a.setFontSize(i),n[l].rotate?n[l].areav[0]>0&&(e.tooltip&&e.tooltip.index==l?a.strokeText(t,(n[l].areav[0]+5-e.width/2)*o-r*(1-o)/2,(n[l].areav[1]+5+i-e.height/2)*o):a.fillText(t,(n[l].areav[0]+5-e.width/2)*o-r*(1-o)/2,(n[l].areav[1]+5+i-e.height/2)*o)):n[l].area[0]>0&&(e.tooltip&&e.tooltip.index==l?a.strokeText(t,(n[l].area[0]+5-e.width/2)*o-r*(1-o)/2,(n[l].area[1]+5+i-e.height/2)*o):a.fillText(t,(n[l].area[0]+5-e.width/2)*o-r*(1-o)/2,(n[l].area[1]+5+i-e.height/2)*o)),a.stroke(),a.restore()}a.restore()}(r,e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"map":a.clearRect(0,0,e.width,e.height),function(t,e,i,a){var o,r,n,l,s,c,h,d,p,x,u=it({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},e.extra.map),g=t,f=function(t){for(var e,i={xMin:180,xMax:0,yMin:90,yMax:0},a=0;a<t.length;a++)for(var o=t[a].geometry.coordinates,r=0;r<o.length;r++){1==(e=o[r]).length&&(e=e[0]);for(var n=0;n<e.length;n++){var l={x:e[n][0],y:e[n][1]};i.xMin=i.xMin<l.x?i.xMin:l.x,i.xMax=i.xMax>l.x?i.xMax:l.x,i.yMin=i.yMin<l.y?i.yMin:l.y,i.yMax=i.yMax>l.y?i.yMax:l.y}}return i}(g);if(u.mercator){var y=Ce(f.xMax,f.yMax),m=Ce(f.xMin,f.yMin);f.xMax=y[0],f.yMax=y[1],f.xMin=m[0],f.yMin=m[1]}for(var v=e.width/Math.abs(f.xMax-f.xMin),b=e.height/Math.abs(f.yMax-f.yMin),C=v<b?v:b,S=e.width/2-Math.abs(f.xMax-f.xMin)/2*C,w=e.height/2-Math.abs(f.yMax-f.yMin)/2*C,L=0;L<g.length;L++){a.beginPath(),a.setLineWidth(u.borderWidth*e.pix),a.setStrokeStyle(u.borderColor),a.setFillStyle(ot(t[L].color,t[L].fillOpacity||u.fillOpacity)),1==u.active&&e.tooltip&&e.tooltip.index==L&&(a.setStrokeStyle(u.activeBorderColor),a.setFillStyle(ot(u.activeFillColor,u.activeFillOpacity)));for(var T=g[L].geometry.coordinates,A=0;A<T.length;A++){1==(p=T[A]).length&&(p=p[0]);for(var P=0;P<p.length;P++){var _=Array(2);x=Se((_=u.mercator?Ce(p[P][0],p[P][1]):p[P])[1],_[0],f,C,S,w),0===P?(a.beginPath(),a.moveTo(x.x,x.y)):a.lineTo(x.x,x.y)}a.fill(),1==u.border&&a.stroke()}}if(1==e.dataLabel)for(L=0;L<g.length;L++){var k=null==(r=null==(o=null==g?void 0:g[L])?void 0:o.properties)?void 0:r.centroid;if(k){u.mercator&&(k=Ce(null==(l=null==(n=null==g?void 0:g[L])?void 0:n.properties)?void 0:l.centroid[0],null==(c=null==(s=null==g?void 0:g[L])?void 0:s.properties)?void 0:c.centroid[1])),x=Se(k[1],k[0],f,C,S,w);let t=g[L].textSize*e.pix||i.fontSize,o=g[L].textColor||e.fontColor;u.active&&u.activeTextColor&&e.tooltip&&e.tooltip.index==L&&(o=u.activeTextColor);let r=null==(d=null==(h=null==g?void 0:g[L])?void 0:h.properties)?void 0:d.name;a.beginPath(),a.setFontSize(t),a.setFillStyle(o),a.fillText(r,x.x-xt(r,t,a)/2,x.y+t/2),a.closePath(),a.stroke()}}e.chartData.mapData={bounds:f,scale:C,xoffset:S,yoffset:w,mercator:u.mercator},ge(e,i,a,1),a.draw()}(r,e,i,a),setTimeout((()=>{this.uevent.trigger("renderComplete")}),50);break;case"funnel":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),e.chartData.funnelData=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},e.extra.funnel),n=(e.height-e.area[0]-e.area[2])/t.length,l={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.height-e.area[2]},s=r.activeWidth*e.pix,c=Math.min((e.width-e.area[1]-e.area[3])/2-s,(e.height-e.area[0]-e.area[2])/2-s),h=Mt(t,c,r,n,o);if(a.save(),a.translate(l.x,l.y),r.customColor=pt(r.linearType,r.customColor,t,i),"pyramid"==r.type)for(let x=0;x<h.length;x++){if(x==h.length-1){e.tooltip&&e.tooltip.index==x&&(a.beginPath(),a.setFillStyle(ot(h[x].color,r.activeOpacity)),a.moveTo(-s,-n),a.lineTo(-h[x].radius-s,0),a.lineTo(h[x].radius+s,0),a.lineTo(s,-n),a.lineTo(-s,-n),a.closePath(),a.fill()),h[x].funnelArea=[l.x-h[x].radius,l.y-n*(x+1),l.x+h[x].radius,l.y-n*x],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor);var d=ot(h[x].color,r.fillOpacity);"custom"==r.linearType&&((p=a.createLinearGradient(h[x].radius,-n,-h[x].radius,-n)).addColorStop(0,ot(h[x].color,r.fillOpacity)),p.addColorStop(.5,ot(r.customColor[h[x].linearIndex],r.fillOpacity)),p.addColorStop(1,ot(h[x].color,r.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,-n),a.lineTo(-h[x].radius,0),a.lineTo(h[x].radius,0),a.lineTo(0,-n),a.closePath(),a.fill(),1==r.border&&a.stroke()}else e.tooltip&&e.tooltip.index==x&&(a.beginPath(),a.setFillStyle(ot(h[x].color,r.activeOpacity)),a.moveTo(0,0),a.lineTo(-h[x].radius-s,0),a.lineTo(-h[x+1].radius-s,-n),a.lineTo(h[x+1].radius+s,-n),a.lineTo(h[x].radius+s,0),a.lineTo(0,0),a.closePath(),a.fill()),h[x].funnelArea=[l.x-h[x].radius,l.y-n*(x+1),l.x+h[x].radius,l.y-n*x],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor),d=ot(h[x].color,r.fillOpacity),"custom"==r.linearType&&((p=a.createLinearGradient(h[x].radius,-n,-h[x].radius,-n)).addColorStop(0,ot(h[x].color,r.fillOpacity)),p.addColorStop(.5,ot(r.customColor[h[x].linearIndex],r.fillOpacity)),p.addColorStop(1,ot(h[x].color,r.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-h[x].radius,0),a.lineTo(-h[x+1].radius,-n),a.lineTo(h[x+1].radius,-n),a.lineTo(h[x].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),1==r.border&&a.stroke();a.translate(0,-n)}else{a.translate(0,-(h.length-1)*n);for(let t=0;t<h.length;t++){var p;if(t==h.length-1)e.tooltip&&e.tooltip.index==t&&(a.beginPath(),a.setFillStyle(ot(h[t].color,r.activeOpacity)),a.moveTo(-s-r.minSize/2,0),a.lineTo(-h[t].radius-s,-n),a.lineTo(h[t].radius+s,-n),a.lineTo(s+r.minSize/2,0),a.lineTo(-s-r.minSize/2,0),a.closePath(),a.fill()),h[t].funnelArea=[l.x-h[t].radius,l.y-n,l.x+h[t].radius,l.y],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor),d=ot(h[t].color,r.fillOpacity),"custom"==r.linearType&&((p=a.createLinearGradient(h[t].radius,-n,-h[t].radius,-n)).addColorStop(0,ot(h[t].color,r.fillOpacity)),p.addColorStop(.5,ot(r.customColor[h[t].linearIndex],r.fillOpacity)),p.addColorStop(1,ot(h[t].color,r.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-r.minSize/2,0),a.lineTo(-h[t].radius,-n),a.lineTo(h[t].radius,-n),a.lineTo(r.minSize/2,0),a.lineTo(0,0),a.closePath(),a.fill(),1==r.border&&a.stroke();else e.tooltip&&e.tooltip.index==t&&(a.beginPath(),a.setFillStyle(ot(h[t].color,r.activeOpacity)),a.moveTo(0,0),a.lineTo(-h[t+1].radius-s,0),a.lineTo(-h[t].radius-s,-n),a.lineTo(h[t].radius+s,-n),a.lineTo(h[t+1].radius+s,0),a.lineTo(0,0),a.closePath(),a.fill()),h[t].funnelArea=[l.x-h[t].radius,l.y-n*(h.length-t),l.x+h[t].radius,l.y-n*(h.length-t-1)],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor),d=ot(h[t].color,r.fillOpacity),"custom"==r.linearType&&((p=a.createLinearGradient(h[t].radius,-n,-h[t].radius,-n)).addColorStop(0,ot(h[t].color,r.fillOpacity)),p.addColorStop(.5,ot(r.customColor[h[t].linearIndex],r.fillOpacity)),p.addColorStop(1,ot(h[t].color,r.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-h[t+1].radius,0),a.lineTo(-h[t].radius,-n),a.lineTo(h[t].radius,-n),a.lineTo(h[t+1].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),1==r.border&&a.stroke();a.translate(0,n)}}return a.restore(),!1!==e.dataLabel&&1===o&&_e(h,e,a,n,r.labelAlign,s,l),1===o&&ke(h,e,a,n,r.labelAlign,0,l),{center:l,radius:c,series:h}}(r,e,i,a,t),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},e.extra.line);r.width*=e.pix;let n=e.chartData.xAxisData,l=n.xAxisPoints,s=n.eachSpacing;var c=[];a.save();let h=0,d=e.width+s;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),h=-e._scrollDistance_-2*s+e.area[3],d=h+(e.xAxis.itemCount+4)*s),t.forEach((function(t,n){let p,x,u;a.beginPath(),a.setStrokeStyle(t.color),a.moveTo(-1e4,-1e4),a.lineTo(-10001,-10001),a.stroke(),p=[].concat(e.chartData.yAxisData.ranges[t.index]),x=p.pop(),u=p.shift();var g=jt(t.data,x,u,l,s,e,i,r,o);c.push(g);var f=At(g,t);if("dash"==t.lineType){let i=t.dashLength?t.dashLength:8;i*=e.pix,a.setLineDash([i,i])}a.beginPath();var y=t.color;if("none"!==r.linearType&&t.linearColor&&t.linearColor.length>0){for(var m=a.createLinearGradient(e.chartData.xAxisData.startX,e.height/2,e.chartData.xAxisData.endX,e.height/2),v=0;v<t.linearColor.length;v++)m.addColorStop(t.linearColor[v][0],ot(t.linearColor[v][1],1));y=m}a.setStrokeStyle(y),1==r.onShadow&&t.setShadow&&t.setShadow.length>0?a.setShadow(t.setShadow[0],t.setShadow[1],t.setShadow[2],t.setShadow[3]):a.setShadow(0,0,0,"rgba(0,0,0,0)"),a.setLineWidth(r.width),f.forEach((function(t,e){if(1===t.length)a.moveTo(t[0].x,t[0].y);else{a.moveTo(t[0].x,t[0].y);let e=0;if("curve"===r.type)for(let o=0;o<t.length;o++){let r=t[o];if(0==e&&r.x>h&&(a.moveTo(r.x,r.y),e=1),o>0&&r.x>h&&r.x<d){var i=st(t,o-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,r.x,r.y)}}if("straight"===r.type)for(let i=0;i<t.length;i++){let o=t[i];0==e&&o.x>h&&(a.moveTo(o.x,o.y),e=1),i>0&&o.x>h&&o.x<d&&a.lineTo(o.x,o.y)}if("step"===r.type)for(let i=0;i<t.length;i++){let o=t[i];0==e&&o.x>h&&(a.moveTo(o.x,o.y),e=1),i>0&&o.x>h&&o.x<d&&(a.lineTo(o.x,t[i-1].y),a.lineTo(o.x,o.y))}a.moveTo(t[0].x,t[0].y)}})),a.stroke(),a.setLineDash([]),!1!==e.dataPointShape&&te(g,t.color,t.pointShape,a,e),ee(g,t.color,t.pointShape,a,e,r)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){let n,c,h;n=[].concat(e.chartData.yAxisData.ranges[t.index]),c=n.pop(),h=n.shift(),ae(Gt(t.data,c,h,l,s,e,i,o),t,i,a,e)})),a.restore(),{xAxisPoints:l,calPoints:c,eachSpacing:s}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;it({},{type:"circle"},e.extra.scatter);let r=e.chartData.xAxisData,n=r.xAxisPoints,l=r.eachSpacing;var s=[];a.save();let c=0;return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),c=-e._scrollDistance_-2*l+e.area[3],e.xAxis.itemCount),t.forEach((function(t,r){let s,c,h;s=[].concat(e.chartData.yAxisData.ranges[t.index]),c=s.pop(),h=s.shift();var d=Gt(t.data,c,h,n,l,e,i,o);a.beginPath(),a.setStrokeStyle(t.color),a.setFillStyle(t.color),a.setLineWidth(1*e.pix);var p=t.pointShape;if("diamond"===p)d.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))}));else if("circle"===p)d.forEach((function(t,i){null!==t&&(a.moveTo(t.x*****e.pix,t.y),a.arc(t.x,t.y,3*e.pix,0,2*Math.PI,!1))}));else if("square"===p)d.forEach((function(t,e){null!==t&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===p)d.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))}));else if("triangle"===p)return;a.closePath(),a.fill(),a.stroke()})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){let s,c,h;s=[].concat(e.chartData.yAxisData.ranges[t.index]),c=s.pop(),h=s.shift(),ae(Gt(t.data,c,h,n,l,e,i,o),t,i,a,e)})),a.restore(),{xAxisPoints:n,calPoints:s,eachSpacing:l}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{opacity:1,border:2},e.extra.bubble);let n=e.chartData.xAxisData,l=n.xAxisPoints,s=n.eachSpacing;var c=[];a.save();let h=0;return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),h=-e._scrollDistance_-2*s+e.area[3],e.xAxis.itemCount),t.forEach((function(t,n){let c,h,d;c=[].concat(e.chartData.yAxisData.ranges[t.index]),h=c.pop(),d=c.shift();var p=Gt(t.data,h,d,l,s,e,i,o);a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(r.border*e.pix),a.setFillStyle(ot(t.color,r.opacity)),p.forEach((function(t,i){a.moveTo(t.x+t.r,t.y),a.arc(t.x,t.y,t.r*e.pix,0,2*Math.PI,!1)})),a.closePath(),a.fill(),a.stroke(),!1!==e.dataLabel&&1===o&&p.forEach((function(o,r){a.beginPath();var n=t.textSize*e.pix||i.fontSize;a.setFontSize(n),a.setFillStyle(t.textColor||"#FFFFFF"),a.setTextAlign("center"),a.fillText(String(o.t),o.x,o.y+n/2),a.closePath(),a.stroke(),a.setTextAlign("left")}))})),a.restore(),{xAxisPoints:l,calPoints:c,eachSpacing:s}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=e.chartData.xAxisData,n=r.xAxisPoints,l=r.eachSpacing,s=it({},{width:l/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mix.column),c=it({},{opacity:.2,gradient:!1},e.extra.mix.area),h=it({},{width:2},e.extra.mix.line),d=e.height-e.area[2],p=[];var x=0,u=0;t.forEach((function(t,e){"column"==t.type&&(u+=1)})),a.save();let g=-2,f=n.length+2,y=0,m=e.width+l;e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),g=Math.floor(-e._scrollDistance_/l)-2,f=g+e.xAxis.itemCount+4,y=-e._scrollDistance_-2*l+e.area[3],m=y+(e.xAxis.itemCount+4)*l),s.customColor=pt(s.linearType,s.customColor,t,i),t.forEach((function(t,r){let v,b,C;v=[].concat(e.chartData.yAxisData.ranges[t.index]),b=v.pop(),C=v.shift();var S=Gt(t.data,b,C,n,l,e,i,o);if(p.push(S),"column"==t.type){S=Nt(S,l,u,x,0,e);for(let i=0;i<S.length;i++){let o=S[i];if(null!==o&&i>g&&i<f){var w=o.x-o.width/2;e.height,o.y,e.area[2],a.beginPath();var L=o.color||t.color,T=o.color||t.color;if("none"!==s.linearType){var A=a.createLinearGradient(w,o.y,w,e.height-e.area[2]);"opacity"==s.linearType?(A.addColorStop(0,ot(L,s.linearOpacity)),A.addColorStop(1,ot(L,1))):(A.addColorStop(0,ot(s.customColor[t.linearIndex],s.linearOpacity)),A.addColorStop(s.colorStop,ot(s.customColor[t.linearIndex],s.linearOpacity)),A.addColorStop(1,ot(L,1))),L=A}if(s.barBorderRadius&&4===s.barBorderRadius.length||s.barBorderCircle){const t=w,i=o.y,r=o.width,n=e.height-e.area[2]-o.y;s.barBorderCircle&&(s.barBorderRadius=[r/2,r/2,0,0]);let[l,c,h,d]=s.barBorderRadius,p=Math.min(r/2,n/2);l=l>p?p:l,c=c>p?p:c,h=h>p?p:h,d=d>p?p:d,l=l<0?0:l,c=c<0?0:c,h=h<0?0:h,d=d<0?0:d,a.arc(t+l,i+l,l,-Math.PI,-Math.PI/2),a.arc(t+r-c,i+c,c,-Math.PI/2,0),a.arc(t+r-h,i+n-h,h,0,Math.PI/2),a.arc(t+d,i+n-d,d,Math.PI/2,Math.PI)}else a.moveTo(w,o.y),a.lineTo(w+o.width,o.y),a.lineTo(w+o.width,e.height-e.area[2]),a.lineTo(w,e.height-e.area[2]),a.lineTo(w,o.y),a.setLineWidth(1),a.setStrokeStyle(T);a.setFillStyle(L),a.closePath(),a.fill()}}x+=1}if("area"==t.type){let i=At(S,t);for(let o=0;o<i.length;o++){let r=i[o];if(a.beginPath(),a.setStrokeStyle(t.color),a.setStrokeStyle(ot(t.color,c.opacity)),c.gradient){let i=a.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);i.addColorStop("0",ot(t.color,c.opacity)),i.addColorStop("1.0",ot("#FFFFFF",.1)),a.setFillStyle(i)}else a.setFillStyle(ot(t.color,c.opacity));if(a.setLineWidth(2*e.pix),r.length>1){var P=r[0];let e=r[r.length-1];a.moveTo(P.x,P.y);let i=0;if("curve"===t.style)for(let t=0;t<r.length;t++){let e=r[t];if(0==i&&e.x>y&&(a.moveTo(e.x,e.y),i=1),t>0&&e.x>y&&e.x<m){var _=st(r,t-1);a.bezierCurveTo(_.ctrA.x,_.ctrA.y,_.ctrB.x,_.ctrB.y,e.x,e.y)}}else for(let t=0;t<r.length;t++){let e=r[t];0==i&&e.x>y&&(a.moveTo(e.x,e.y),i=1),t>0&&e.x>y&&e.x<m&&a.lineTo(e.x,e.y)}a.lineTo(e.x,d),a.lineTo(P.x,d),a.lineTo(P.x,P.y)}else{let t=r[0];a.moveTo(t.x-l/2,t.y)}a.closePath(),a.fill()}}"line"==t.type&&At(S,t).forEach((function(i,o){if("dash"==t.lineType){let i=t.dashLength?t.dashLength:8;i*=e.pix,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(h.width*e.pix),1===i.length)a.moveTo(i[0].x,i[0].y);else{a.moveTo(i[0].x,i[0].y);let e=0;if("curve"==t.style)for(let t=0;t<i.length;t++){let o=i[t];if(0==e&&o.x>y&&(a.moveTo(o.x,o.y),e=1),t>0&&o.x>y&&o.x<m){var r=st(i,t-1);a.bezierCurveTo(r.ctrA.x,r.ctrA.y,r.ctrB.x,r.ctrB.y,o.x,o.y)}}else for(let t=0;t<i.length;t++){let o=i[t];0==e&&o.x>y&&(a.moveTo(o.x,o.y),e=1),t>0&&o.x>y&&o.x<m&&a.lineTo(o.x,o.y)}a.moveTo(i[0].x,i[0].y)}a.stroke(),a.setLineDash([])})),"point"==t.type&&(t.addPoint=!0),1==t.addPoint&&"column"!==t.type&&te(S,t.color,t.pointShape,a,e)})),!1!==e.dataLabel&&1===o&&(x=0,t.forEach((function(t,r){let s,c,h;s=[].concat(e.chartData.yAxisData.ranges[t.index]),c=s.pop(),h=s.shift();var d=Gt(t.data,c,h,n,l,e,i,o);"column"!==t.type?ae(d,t,i,a,e):(ae(d=Nt(d,l,u,x,0,e),t,i,a,e),x+=1)})));return a.restore(),{xAxisPoints:n,calPoints:p,eachSpacing:l}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=e.chartData.xAxisData,n=r.xAxisPoints,l=r.eachSpacing,s=it({},{type:"group",width:l/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},e.extra.column),c=[];a.save();let h=-2,d=n.length+2;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),h=Math.floor(-e._scrollDistance_/l)-2,d=h+e.xAxis.itemCount+4),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&pe(e.tooltip.offset.x,e,0,a,l),s.customColor=pt(s.linearType,s.customColor,t,i),t.forEach((function(r,p){let x,u,g;x=[].concat(e.chartData.yAxisData.ranges[r.index]),u=x.pop(),g=x.shift();let f=(e.height-e.area[0]-e.area[2])*(0-u)/(g-u),y=e.height-Math.round(f)-e.area[2];r.zeroPoints=y;var m=r.data;switch(s.type){case"group":var v=$t(m,u,g,n,l,e,i,y,o),b=Ut(m,u,g,n,l,e,i,p,t,o);c.push(b),v=Nt(v,l,t.length,p,0,e);for(let t=0;t<v.length;t++){let i=v[t];if(null!==i&&t>h&&t<d){var C=i.x-i.width/2,S=e.height-i.y-e.area[2];a.beginPath();var w=i.color||r.color,L=i.color||r.color;if("none"!==s.linearType){var T=a.createLinearGradient(C,i.y,C,y);"opacity"==s.linearType?(T.addColorStop(0,ot(w,s.linearOpacity)),T.addColorStop(1,ot(w,1))):(T.addColorStop(0,ot(s.customColor[r.linearIndex],s.linearOpacity)),T.addColorStop(s.colorStop,ot(s.customColor[r.linearIndex],s.linearOpacity)),T.addColorStop(1,ot(w,1))),w=T}if(s.barBorderRadius&&4===s.barBorderRadius.length||!0===s.barBorderCircle){const t=C,e=i.y>y?y:i.y,o=i.width,r=Math.abs(y-i.y);s.barBorderCircle&&(s.barBorderRadius=[o/2,o/2,0,0]),i.y>y&&(s.barBorderRadius=[0,0,o/2,o/2]);let[n,l,c,h]=s.barBorderRadius,d=Math.min(o/2,r/2);n=n>d?d:n,l=l>d?d:l,c=c>d?d:c,h=h>d?d:h,n=n<0?0:n,l=l<0?0:l,c=c<0?0:c,h=h<0?0:h,a.arc(t+n,e+n,n,-Math.PI,-Math.PI/2),a.arc(t+o-l,e+l,l,-Math.PI/2,0),a.arc(t+o-c,e+r-c,c,0,Math.PI/2),a.arc(t+h,e+r-h,h,Math.PI/2,Math.PI)}else a.moveTo(C,i.y),a.lineTo(C+i.width,i.y),a.lineTo(C+i.width,y),a.lineTo(C,y),a.lineTo(C,i.y),a.setLineWidth(1),a.setStrokeStyle(L);a.setFillStyle(w),a.closePath(),a.fill()}}break;case"stack":v=Ut(m,u,g,n,l,e,i,p,t,o),c.push(v),v=Et(v,l,t.length,0,0,e);for(let t=0;t<v.length;t++){let i=v[t];if(null!==i&&t>h&&t<d){a.beginPath(),w=i.color||r.color,C=i.x-i.width/2+1,S=e.height-i.y-e.area[2];var A=e.height-i.y0-e.area[2];p>0&&(S-=A),a.setFillStyle(w),a.moveTo(C,i.y),a.fillRect(C,i.y,i.width,S),a.closePath(),a.fill()}}break;case"meter":v=Gt(m,u,g,n,l,e,i,o),c.push(v),v=Wt(v,l,t.length,p,0,e,s.meterBorder);for(let t=0;t<v.length;t++){let i=v[t];if(null!==i&&t>h&&t<d){if(a.beginPath(),0==p&&s.meterBorder>0&&(a.setStrokeStyle(r.color),a.setLineWidth(s.meterBorder*e.pix)),0==p?a.setFillStyle(s.meterFillColor):a.setFillStyle(i.color||r.color),C=i.x-i.width/2,S=e.height-i.y-e.area[2],s.barBorderRadius&&4===s.barBorderRadius.length||!0===s.barBorderCircle){const t=C,e=i.y,o=i.width,r=y-i.y;s.barBorderCircle&&(s.barBorderRadius=[o/2,o/2,0,0]);let[n,l,c,h]=s.barBorderRadius,d=Math.min(o/2,r/2);n=n>d?d:n,l=l>d?d:l,c=c>d?d:c,h=h>d?d:h,n=n<0?0:n,l=l<0?0:l,c=c<0?0:c,h=h<0?0:h,a.arc(t+n,e+n,n,-Math.PI,-Math.PI/2),a.arc(t+o-l,e+l,l,-Math.PI/2,0),a.arc(t+o-c,e+r-c,c,0,Math.PI/2),a.arc(t+h,e+r-h,h,Math.PI/2,Math.PI),a.fill()}else a.moveTo(C,i.y),a.lineTo(C+i.width,i.y),a.lineTo(C+i.width,y),a.lineTo(C,y),a.lineTo(C,i.y),a.fill();0==p&&s.meterBorder>0&&(a.closePath(),a.stroke())}}}})),!1!==e.dataLabel&&1===o&&t.forEach((function(r,c){let h,d,p;h=[].concat(e.chartData.yAxisData.ranges[r.index]),d=h.pop(),p=h.shift();var x=r.data;switch(s.type){case"group":oe(Nt($t(x,d,p,n,l,e,i,o),l,t.length,c,0,e),r,i,a,e);break;case"stack":oe(Ut(x,d,p,n,l,e,i,c,t,o),r,i,a,e);break;case"meter":oe(Gt(x,d,p,n,l,e,i,o),r,i,a,e)}})),a.restore(),{xAxisPoints:n,calPoints:c,eachSpacing:l}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=e.chartData.xAxisData,n=r.xAxisPoints,l=r.eachSpacing,s=it({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mount);s.widthRatio=s.widthRatio<=0?0:s.widthRatio,s.widthRatio=s.widthRatio>=2?2:s.widthRatio,a.save();let c,h,d,p=-2,x=n.length+2;e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),p=Math.floor(-e._scrollDistance_/l)-2,x=p+e.xAxis.itemCount+4),s.customColor=pt(s.linearType,s.customColor,t,i),c=[].concat(e.chartData.yAxisData.ranges[0]),h=c.pop(),d=c.shift();let u=(e.height-e.area[0]-e.area[2])*(0-h)/(d-h),g=e.height-Math.round(u)-e.area[2];var f=Xt(t,h,d,n,l,e,s,g,o);switch(s.type){case"bar":for(let i=0;i<f.length;i++){let o=f[i];if(null!==o&&i>p&&i<x){var y=o.x-l*s.widthRatio/2,m=e.height-o.y-e.area[2];a.beginPath();var v=o.color||t[i].color,b=o.color||t[i].color;if("none"!==s.linearType){var C=a.createLinearGradient(y,o.y,y,g);"opacity"==s.linearType?(C.addColorStop(0,ot(v,s.linearOpacity)),C.addColorStop(1,ot(v,1))):(C.addColorStop(0,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(s.colorStop,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(1,ot(v,1))),v=C}if(s.barBorderRadius&&4===s.barBorderRadius.length||!0===s.barBorderCircle){const t=y,e=o.y>g?g:o.y,i=o.width,r=Math.abs(g-o.y);s.barBorderCircle&&(s.barBorderRadius=[i/2,i/2,0,0]),o.y>g&&(s.barBorderRadius=[0,0,i/2,i/2]);let[n,l,c,h]=s.barBorderRadius,d=Math.min(i/2,r/2);n=n>d?d:n,l=l>d?d:l,c=c>d?d:c,h=h>d?d:h,n=n<0?0:n,l=l<0?0:l,c=c<0?0:c,h=h<0?0:h,a.arc(t+n,e+n,n,-Math.PI,-Math.PI/2),a.arc(t+i-l,e+l,l,-Math.PI/2,0),a.arc(t+i-c,e+r-c,c,0,Math.PI/2),a.arc(t+h,e+r-h,h,Math.PI/2,Math.PI)}else a.moveTo(y,o.y),a.lineTo(y+o.width,o.y),a.lineTo(y+o.width,g),a.lineTo(y,g),a.lineTo(y,o.y);a.setStrokeStyle(b),a.setFillStyle(v),s.borderWidth>0&&(a.setLineWidth(s.borderWidth*e.pix),a.closePath(),a.stroke()),a.fill()}}break;case"triangle":for(let i=0;i<f.length;i++){let o=f[i];null!==o&&i>p&&i<x&&(y=o.x-l*s.widthRatio/2,m=e.height-o.y-e.area[2],a.beginPath(),v=o.color||t[i].color,b=o.color||t[i].color,"none"!==s.linearType&&(C=a.createLinearGradient(y,o.y,y,g),"opacity"==s.linearType?(C.addColorStop(0,ot(v,s.linearOpacity)),C.addColorStop(1,ot(v,1))):(C.addColorStop(0,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(s.colorStop,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(1,ot(v,1))),v=C),a.moveTo(y,g),a.lineTo(o.x,o.y),a.lineTo(y+o.width,g),a.setStrokeStyle(b),a.setFillStyle(v),s.borderWidth>0&&(a.setLineWidth(s.borderWidth*e.pix),a.stroke()),a.fill())}break;case"mount":for(let i=0;i<f.length;i++){let o=f[i];null!==o&&i>p&&i<x&&(y=o.x-l*s.widthRatio/2,m=e.height-o.y-e.area[2],a.beginPath(),v=o.color||t[i].color,b=o.color||t[i].color,"none"!==s.linearType&&(C=a.createLinearGradient(y,o.y,y,g),"opacity"==s.linearType?(C.addColorStop(0,ot(v,s.linearOpacity)),C.addColorStop(1,ot(v,1))):(C.addColorStop(0,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(s.colorStop,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(1,ot(v,1))),v=C),a.moveTo(y,g),a.bezierCurveTo(o.x-o.width/4,g,o.x-o.width/4,o.y,o.x,o.y),a.bezierCurveTo(o.x+o.width/4,o.y,o.x+o.width/4,g,y+o.width,g),a.setStrokeStyle(b),a.setFillStyle(v),s.borderWidth>0&&(a.setLineWidth(s.borderWidth*e.pix),a.stroke()),a.fill())}break;case"sharp":for(let i=0;i<f.length;i++){let o=f[i];null!==o&&i>p&&i<x&&(y=o.x-l*s.widthRatio/2,m=e.height-o.y-e.area[2],a.beginPath(),v=o.color||t[i].color,b=o.color||t[i].color,"none"!==s.linearType&&(C=a.createLinearGradient(y,o.y,y,g),"opacity"==s.linearType?(C.addColorStop(0,ot(v,s.linearOpacity)),C.addColorStop(1,ot(v,1))):(C.addColorStop(0,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(s.colorStop,ot(s.customColor[t[i].linearIndex],s.linearOpacity)),C.addColorStop(1,ot(v,1))),v=C),a.moveTo(y,g),a.quadraticCurveTo(o.x-0,g-m/4,o.x,o.y),a.quadraticCurveTo(o.x+0,g-m/4,y+o.width,g),a.setStrokeStyle(b),a.setFillStyle(v),s.borderWidth>0&&(a.setLineWidth(s.borderWidth*e.pix),a.stroke()),a.fill())}}if(!1!==e.dataLabel&&1===o){let r,c,h;r=[].concat(e.chartData.yAxisData.ranges[0]),c=r.pop(),h=r.shift(),re(f=Xt(t,c,h,n,l,e,s,g,o),t,i,a,e,g)}return a.restore(),{xAxisPoints:n,calPoints:f,eachSpacing:l}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),fe(n,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=[],n=(e.height-e.area[0]-e.area[2])/e.categories.length;for(let d=0;d<e.categories.length;d++)r.push(e.area[0]+n/2+n*d);let l=it({},{type:"group",width:n/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.bar),s=[];a.save();let c=-2,h=r.length+2;return e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&xe(e.tooltip.offset.y,e,0,a,n),l.customColor=pt(l.linearType,l.customColor,t,i),t.forEach((function(d,p){let x,u,g;x=[].concat(e.chartData.xAxisData.ranges),g=x.pop(),u=x.shift();var f=d.data;switch(l.type){case"group":var y=Ht(f,u,g,r,n,e,i,o),m=qt(f,u,g,r,n,e,i,p,t,o);s.push(m),y=Rt(y,n,t.length,p,0,e);for(let t=0;t<y.length;t++){let i=y[t];if(null!==i&&t>c&&t<h){var v=e.area[3],b=i.y-i.width/2;i.height,a.beginPath();var C=i.color||d.color,S=i.color||d.color;if("none"!==l.linearType){var w=a.createLinearGradient(v,i.y,i.x,i.y);"opacity"==l.linearType?(w.addColorStop(0,ot(C,l.linearOpacity)),w.addColorStop(1,ot(C,1))):(w.addColorStop(0,ot(l.customColor[d.linearIndex],l.linearOpacity)),w.addColorStop(l.colorStop,ot(l.customColor[d.linearIndex],l.linearOpacity)),w.addColorStop(1,ot(C,1))),C=w}if(l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const t=v,e=i.width,o=i.y-i.width/2,r=i.height;l.barBorderCircle&&(l.barBorderRadius=[e/2,e/2,0,0]);let[n,s,c,h]=l.barBorderRadius,d=Math.min(e/2,r/2);n=n>d?d:n,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,n=n<0?0:n,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(t+h,o+h,h,-Math.PI,-Math.PI/2),a.arc(i.x-n,o+n,n,-Math.PI/2,0),a.arc(i.x-s,o+e-s,s,0,Math.PI/2),a.arc(t+c,o+e-c,c,Math.PI/2,Math.PI)}else a.moveTo(v,b),a.lineTo(i.x,b),a.lineTo(i.x,b+i.width),a.lineTo(v,b+i.width),a.lineTo(v,b),a.setLineWidth(1),a.setStrokeStyle(S);a.setFillStyle(C),a.closePath(),a.fill()}}break;case"stack":y=qt(f,u,g,r,n,e,i,p,t,o),s.push(y),y=Qt(y,n,t.length,0,0,e);for(let t=0;t<y.length;t++){let e=y[t];null!==e&&t>c&&t<h&&(a.beginPath(),C=e.color||d.color,v=e.x0,a.setFillStyle(C),a.moveTo(v,e.y-e.width/2),a.fillRect(v,e.y-e.width/2,e.height,e.width),a.closePath(),a.fill())}}})),!1!==e.dataLabel&&1===o&&t.forEach((function(s,c){let h,d,p;h=[].concat(e.chartData.xAxisData.ranges),p=h.pop(),d=h.shift();var x=s.data;switch(l.type){case"group":ne(Rt(Ht(x,d,p,r,n,e,i,o),n,t.length,c,0,e),s,i,a,e);break;case"stack":ne(qt(x,d,p,r,n,e,i,c,t,o),s,i,a,e)}})),{yAxisPoints:r,calPoints:s,eachSpacing:n}}(r,e,i,a,t),l=o.yAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.yAxisPoints=l,e.chartData.xAxisPoints=e.chartData.xAxisData.xAxisPoints,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},e.extra.area);let n=e.chartData.xAxisData,l=n.xAxisPoints,s=n.eachSpacing,c=e.height-e.area[2],h=[];a.save();let d=0,p=e.width+s;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),d=-e._scrollDistance_-2*s+e.area[3],p=d+(e.xAxis.itemCount+4)*s),t.forEach((function(t,n){let x,u,g;x=[].concat(e.chartData.yAxisData.ranges[t.index]),u=x.pop(),g=x.shift();let f=Gt(t.data,u,g,l,s,e,i,o);h.push(f);let y=At(f,t);for(let i=0;i<y.length;i++){let o=y[i];if(a.beginPath(),a.setStrokeStyle(ot(t.color,r.opacity)),r.gradient){let i=a.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);i.addColorStop("0",ot(t.color,r.opacity)),i.addColorStop("1.0",ot("#FFFFFF",.1)),a.setFillStyle(i)}else a.setFillStyle(ot(t.color,r.opacity));if(a.setLineWidth(r.width*e.pix),o.length>1){let t=o[0],e=o[o.length-1];a.moveTo(t.x,t.y);let i=0;if("curve"===r.type)for(let r=0;r<o.length;r++){let t=o[r];if(0==i&&t.x>d&&(a.moveTo(t.x,t.y),i=1),r>0&&t.x>d&&t.x<p){let e=st(o,r-1);a.bezierCurveTo(e.ctrA.x,e.ctrA.y,e.ctrB.x,e.ctrB.y,t.x,t.y)}}if("straight"===r.type)for(let r=0;r<o.length;r++){let t=o[r];0==i&&t.x>d&&(a.moveTo(t.x,t.y),i=1),r>0&&t.x>d&&t.x<p&&a.lineTo(t.x,t.y)}if("step"===r.type)for(let r=0;r<o.length;r++){let t=o[r];0==i&&t.x>d&&(a.moveTo(t.x,t.y),i=1),r>0&&t.x>d&&t.x<p&&(a.lineTo(t.x,o[r-1].y),a.lineTo(t.x,t.y))}a.lineTo(e.x,c),a.lineTo(t.x,c),a.lineTo(t.x,t.y)}else{let t=o[0];a.moveTo(t.x-s/2,t.y)}if(a.closePath(),a.fill(),r.addLine){if("dash"==t.lineType){let i=t.dashLength?t.dashLength:8;i*=e.pix,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(r.width*e.pix),1===o.length)a.moveTo(o[0].x,o[0].y);else{a.moveTo(o[0].x,o[0].y);let t=0;if("curve"===r.type)for(let e=0;e<o.length;e++){let i=o[e];if(0==t&&i.x>d&&(a.moveTo(i.x,i.y),t=1),e>0&&i.x>d&&i.x<p){let t=st(o,e-1);a.bezierCurveTo(t.ctrA.x,t.ctrA.y,t.ctrB.x,t.ctrB.y,i.x,i.y)}}if("straight"===r.type)for(let e=0;e<o.length;e++){let i=o[e];0==t&&i.x>d&&(a.moveTo(i.x,i.y),t=1),e>0&&i.x>d&&i.x<p&&a.lineTo(i.x,i.y)}if("step"===r.type)for(let e=0;e<o.length;e++){let i=o[e];0==t&&i.x>d&&(a.moveTo(i.x,i.y),t=1),e>0&&i.x>d&&i.x<p&&(a.lineTo(i.x,o[e-1].y),a.lineTo(i.x,i.y))}a.moveTo(o[0].x,o[0].y)}a.stroke(),a.setLineDash([])}}!1!==e.dataPointShape&&te(f,t.color,t.pointShape,a,e),ee(f,t.color,t.pointShape,a,e,r,n)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,r){let n,c,h;n=[].concat(e.chartData.yAxisData.ranges[t.index]),c=n.pop(),h=n.shift(),ae(Gt(t.data,c,h,l,s,e,i,o),t,i,a,e)})),a.restore(),{xAxisPoints:l,calPoints:h,eachSpacing:s}}(r,e,i,a,t),l=o.xAxisPoints,s=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=s,e.chartData.eachSpacing=c,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"ring":case"pie":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),e.chartData.pieData=be(r,e,i,a,t),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),e.chartData.pieData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},e.extra.rose);0==i.pieChartLinePadding&&(i.pieChartLinePadding=r.activeRadius*e.pix);var n={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},l=Math.min((e.width-e.area[1]-e.area[3])/2-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-i.pieChartLinePadding-i.pieChartTextPadding);l=l<10?10:l;var s=r.minRadius||.5*l;l<s&&(l=s+10),t=Dt(t,r.type,s,l,o);var c=r.activeRadius*e.pix;return r.customColor=pt(r.linearType,r.customColor,t,i),(t=t.map((function(t){return t._start_+=(r.offsetAngle||0)*Math.PI/180,t}))).forEach((function(t,i){e.tooltip&&e.tooltip.index==i&&(a.beginPath(),a.setFillStyle(ot(t.color,r.activeOpacity||.5)),a.moveTo(n.x,n.y),a.arc(n.x,n.y,c+t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.lineJoin="round",a.setStrokeStyle(r.borderColor);var o,l=t.color;"custom"==r.linearType&&((o=a.createCircularGradient?a.createCircularGradient(n.x,n.y,t._radius_):a.createRadialGradient(n.x,n.y,0,n.x,n.y,t._radius_)).addColorStop(0,ot(r.customColor[t.linearIndex],1)),o.addColorStop(1,ot(t.color,1)),l=o),a.setFillStyle(l),a.moveTo(n.x,n.y),a.arc(n.x,n.y,t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),a.closePath(),a.fill(),1==r.border&&a.stroke()})),!1!==e.dataLabel&&1===o&&ce(t,e,i,a,0,n),{center:n,radius:l,series:t}}(r,e,i,a,t),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),e.chartData.radarData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=it({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},e.extra.radar),n=vt(e.categories.length),l={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},s=(e.width-e.area[1]-e.area[3])/2,c=(e.height-e.area[0]-e.area[2])/2,h=Math.min(s-(mt(e.categories,i.fontSize,a)+i.radarLabelTextMargin),c-i.radarLabelTextMargin);h=(h-=i.radarLabelTextMargin*e.pix)<10?10:h,h=r.radius?r.radius:h,a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(r.gridColor),n.forEach((function(t,e){var i=ct(h*Math.cos(t),h*Math.sin(t),l);a.moveTo(l.x,l.y),e%r.gridEval==0&&a.lineTo(i.x,i.y)})),a.stroke(),a.closePath();for(var d=function(t){var i={};if(a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(r.gridColor),"radar"==r.gridType)n.forEach((function(e,o){var n=ct(h/r.gridCount*t*Math.cos(e),h/r.gridCount*t*Math.sin(e),l);0===o?(i=n,a.moveTo(n.x,n.y)):a.lineTo(n.x,n.y)})),a.lineTo(i.x,i.y);else{var o=ct(h/r.gridCount*t*Math.cos(1.5),h/r.gridCount*t*Math.sin(1.5),l);a.arc(l.x,l.y,l.y-o.y,0,2*Math.PI,!1)}a.stroke(),a.closePath()},p=1;p<=r.gridCount;p++)d(p);r.customColor=pt(r.linearType,r.customColor,t,i);var x=kt(n,l,h,t,e,o);if(x.forEach((function(i,o){a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(i.color);var n,s=ot(i.color,r.opacity);"custom"==r.linearType&&((n=a.createCircularGradient?a.createCircularGradient(l.x,l.y,h):a.createRadialGradient(l.x,l.y,0,l.x,l.y,h)).addColorStop(0,ot(r.customColor[t[o].linearIndex],r.opacity)),n.addColorStop(1,ot(i.color,r.opacity)),s=n),a.setFillStyle(s),i.data.forEach((function(t,e){0===e?a.moveTo(t.position.x,t.position.y):a.lineTo(t.position.x,t.position.y)})),a.closePath(),a.fill(),!0===r.border&&a.stroke(),a.closePath(),!1!==e.dataPointShape&&te(i.data.map((function(t){return t.position})),i.color,i.pointShape,a,e)})),!0===r.axisLabel){const i=Math.max(r.max,Math.max.apply(null,ut(t))),o=h/r.gridCount,n=e.fontSize*e.pix;for(a.setFontSize(n),a.setFillStyle(e.fontColor),a.setTextAlign("left"),p=0;p<r.gridCount+1;p++){let t=p*i/r.gridCount;t=t.toFixed(r.axisLabelTofix),a.fillText(String(t),l.x+3*e.pix,l.y-p*o+n/2)}}return se(n,h,l,e,i,a),!1!==e.dataLabel&&1===o&&(x.forEach((function(t,o){a.beginPath();var r=t.textSize*e.pix||i.fontSize;a.setFontSize(r),a.setFillStyle(t.textColor||e.fontColor),t.data.forEach((function(t,e){Math.abs(t.position.x-l.x)<2?t.position.y<l.y?(a.setTextAlign("center"),a.fillText(t.value,t.position.x,t.position.y-4)):(a.setTextAlign("center"),a.fillText(t.value,t.position.x,t.position.y+r+2)):t.position.x<l.x?(a.setTextAlign("right"),a.fillText(t.value,t.position.x-4,t.position.y+r/2-2)):(a.setTextAlign("left"),a.fillText(t.value,t.position.x+4,t.position.y+r/2-2))})),a.closePath(),a.stroke()})),a.setTextAlign("left")),{center:l,radius:h,angleList:n}}(r,e,i,a,t),ve(e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),e.chartData.arcbarData=function(t,e,i,a){var o,r,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,l=it({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},e.extra.arcbar);t=It(t,l,n),o=l.centerX||l.centerY?{x:l.centerX?l.centerX:e.width/2,y:l.centerY?l.centerY:e.height/2}:{x:e.width/2,y:e.height/2},l.radius?r=l.radius:(r=Math.min(o.x,o.y),r-=5*e.pix,r-=l.width/2),r=r<10?10:r,l.customColor=pt(l.linearType,l.customColor,t,i);for(let h=0;h<t.length;h++){let i=t[h];a.setLineWidth(l.width*e.pix),a.setStrokeStyle(l.backgroundColor||"#E9E9E9"),a.setLineCap(l.lineCap),a.beginPath(),"default"==l.type?a.arc(o.x,o.y,r-(l.width*e.pix+l.gap*e.pix)*h,l.startAngle*Math.PI,l.endAngle*Math.PI,"ccw"==l.direction):a.arc(o.x,o.y,r-(l.width*e.pix+l.gap*e.pix)*h,0,2*Math.PI,"ccw"==l.direction),a.stroke();var s=i.color;if("custom"==l.linearType){var c=a.createLinearGradient(o.x-r,o.y,o.x+r,o.y);c.addColorStop(1,ot(l.customColor[i.linearIndex],1)),c.addColorStop(0,ot(i.color,1)),s=c}a.setLineWidth(l.width*e.pix),a.setStrokeStyle(s),a.setLineCap(l.lineCap),a.beginPath(),a.arc(o.x,o.y,r-(l.width*e.pix+l.gap*e.pix)*h,l.startAngle*Math.PI,i._proportion_*Math.PI,"ccw"==l.direction),a.stroke()}return ie(e,i,a,o),{center:o,radius:r,series:t}}(r,e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),e.chartData.gaugeData=function(t,e,i,a,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=it({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},i.extra.gauge);null==n.oldAngle&&(n.oldAngle=n.startAngle),null==n.oldData&&(n.oldData=0),t=zt(t,n.startAngle,n.endAngle);var l={x:i.width/2,y:i.height/2},s=Math.min(l.x,l.y);s-=5*i.pix;var c=(s=(s-=n.width/2)<10?10:s)-n.width,h=0;if("progress"==n.type){var d=s-3*n.width;o.beginPath();let t=o.createLinearGradient(l.x,l.y-d,l.x,l.y+d);t.addColorStop("0",ot(e[0].color,.3)),t.addColorStop("1.0",ot("#FFFFFF",.1)),o.setFillStyle(t),o.arc(l.x,l.y,d,0,2*Math.PI,!1),o.fill(),o.setLineWidth(n.width),o.setStrokeStyle(ot(e[0].color,.3)),o.setLineCap("round"),o.beginPath(),o.arc(l.x,l.y,c,n.startAngle*Math.PI,n.endAngle*Math.PI,!1),o.stroke(),h=n.endAngle<n.startAngle?2+n.endAngle-n.startAngle:n.startAngle-n.endAngle,n.splitLine.splitNumber;let a=h/n.splitLine.splitNumber/n.splitLine.childNumber,p=-s-.5*n.width-n.splitLine.fixRadius,x=-s-n.width-n.splitLine.fixRadius+n.splitLine.width;o.save(),o.translate(l.x,l.y),o.rotate((n.startAngle-1)*Math.PI);let u=n.splitLine.splitNumber*n.splitLine.childNumber+1,g=e[0].data*r;for(let r=0;r<u;r++)o.beginPath(),g>r/u?o.setStrokeStyle(ot(e[0].color,1)):o.setStrokeStyle(ot(e[0].color,.3)),o.setLineWidth(3*i.pix),o.moveTo(p,0),o.lineTo(x,0),o.stroke(),o.rotate(a*Math.PI);o.restore(),e=Bt(e,n,r),o.setLineWidth(n.width),o.setStrokeStyle(e[0].color),o.setLineCap("round"),o.beginPath(),o.arc(l.x,l.y,c,n.startAngle*Math.PI,e[0]._proportion_*Math.PI,!1),o.stroke();let f=s-2.5*n.width;o.save(),o.translate(l.x,l.y),o.rotate((e[0]._proportion_-1)*Math.PI),o.beginPath(),o.setLineWidth(n.width/3);let y=o.createLinearGradient(0,.6*-f,0,.6*f);y.addColorStop("0",ot("#FFFFFF",0)),y.addColorStop("0.5",ot(e[0].color,1)),y.addColorStop("1.0",ot("#FFFFFF",0)),o.setStrokeStyle(y),o.arc(0,0,f,.85*Math.PI,1.15*Math.PI,!1),o.stroke(),o.beginPath(),o.setLineWidth(1),o.setStrokeStyle(e[0].color),o.setFillStyle(e[0].color),o.moveTo(-f-n.width/3/2,-4),o.lineTo(-f-n.width/3/2-4,0),o.lineTo(-f-n.width/3/2,4),o.lineTo(-f-n.width/3/2,-4),o.stroke(),o.fill(),o.restore()}else{o.setLineWidth(n.width),o.setLineCap("butt");for(let e=0;e<t.length;e++){let i=t[e];o.beginPath(),o.setStrokeStyle(i.color),o.arc(l.x,l.y,s,i._startAngle_*Math.PI,i._endAngle_*Math.PI,!1),o.stroke()}o.save();let d=(h=n.endAngle<n.startAngle?2+n.endAngle-n.startAngle:n.startAngle-n.endAngle)/n.splitLine.splitNumber,p=h/n.splitLine.splitNumber/n.splitLine.childNumber,x=-s-.5*n.width-n.splitLine.fixRadius,u=-s-.5*n.width-n.splitLine.fixRadius+n.splitLine.width,g=-s-.5*n.width-n.splitLine.fixRadius+n.splitLine.childWidth;o.translate(l.x,l.y),o.rotate((n.startAngle-1)*Math.PI);for(let t=0;t<n.splitLine.splitNumber+1;t++)o.beginPath(),o.setStrokeStyle(n.splitLine.color),o.setLineWidth(2*i.pix),o.moveTo(x,0),o.lineTo(u,0),o.stroke(),o.rotate(d*Math.PI);o.restore(),o.save(),o.translate(l.x,l.y),o.rotate((n.startAngle-1)*Math.PI);for(let t=0;t<n.splitLine.splitNumber*n.splitLine.childNumber+1;t++)o.beginPath(),o.setStrokeStyle(n.splitLine.color),o.setLineWidth(1*i.pix),o.moveTo(x,0),o.lineTo(g,0),o.stroke(),o.rotate(p*Math.PI);o.restore(),e=Ot(e,t,n,r);for(let t=0;t<e.length;t++){let i=e[t];o.save(),o.translate(l.x,l.y),o.rotate((i._proportion_-1)*Math.PI),o.beginPath(),o.setFillStyle(i.color),o.moveTo(n.pointer.width,0),o.lineTo(0,-n.pointer.width/2),o.lineTo(-c,0),o.lineTo(0,n.pointer.width/2),o.lineTo(n.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,n.pointer.width/6,0,2*Math.PI,!1),o.fill(),o.restore()}!1!==i.dataLabel&&le(n,s,l,i,a,o)}return ie(i,a,o,l),1===r&&"gauge"===i.type&&(i.extra.gauge.oldAngle=e[0]._proportion_,i.extra.gauge.oldData=e[0].data),{center:l,radius:s,innerRadius:c,categories:t,totalAngle:h}}(n,r,e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new De({timing:e.timing,duration:l,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Jt(a,e),ye(0,e,0,a),fe(n,e,i,a);var o=function(t,e,i,a,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,n=it({},{color:{},average:{}},i.extra.candle);n.color=it({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},n.color),n.average=it({},{show:!1,name:[],day:[],color:a.color},n.average),i.extra.candle=n;let l=i.chartData.xAxisData,s=l.xAxisPoints,c=l.eachSpacing,h=[];o.save();let d=-2,p=s.length+2,x=0,u=i.width+c;return i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&(o.translate(i._scrollDistance_,0),d=Math.floor(-i._scrollDistance_/c)-2,p=d+i.xAxis.itemCount+4,x=-i._scrollDistance_-2*c+i.area[3],u=x+(i.xAxis.itemCount+4)*c),(n.average.show||e)&&e.forEach((function(t,e){let n,l,h;n=[].concat(i.chartData.yAxisData.ranges[t.index]),l=n.pop(),h=n.shift();var d=At(Gt(t.data,l,h,s,c,i,a,r),t);for(let i=0;i<d.length;i++){let e=d[i];if(o.beginPath(),o.setStrokeStyle(t.color),o.setLineWidth(1),1===e.length)o.moveTo(e[0].x,e[0].y),o.arc(e[0].x,e[0].y,1,0,2*Math.PI);else{o.moveTo(e[0].x,e[0].y);let t=0;for(let i=0;i<e.length;i++){let a=e[i];if(0==t&&a.x>x&&(o.moveTo(a.x,a.y),t=1),i>0&&a.x>x&&a.x<u){var p=st(e,i-1);o.bezierCurveTo(p.ctrA.x,p.ctrA.y,p.ctrB.x,p.ctrB.y,a.x,a.y)}}o.moveTo(e[0].x,e[0].y)}o.closePath(),o.stroke()}})),t.forEach((function(t,e){let l,x,u;l=[].concat(i.chartData.yAxisData.ranges[t.index]),x=l.pop(),u=l.shift();var g=t.data,f=Vt(g,x,u,s,c,i,a,r);h.push(f);var y=At(f,t);for(let a=0;a<y[0].length;a++)if(a>d&&a<p){let t=y[0][a];o.beginPath(),g[a][1]-g[a][0]>0?(o.setStrokeStyle(n.color.upLine),o.setFillStyle(n.color.upFill),o.setLineWidth(1*i.pix),o.moveTo(t[3].x,t[3].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[1].x-c/4,t[1].y),o.lineTo(t[0].x-c/4,t[0].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[2].x,t[2].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[0].x+c/4,t[0].y),o.lineTo(t[1].x+c/4,t[1].y),o.lineTo(t[1].x,t[1].y),o.moveTo(t[3].x,t[3].y)):(o.setStrokeStyle(n.color.downLine),o.setFillStyle(n.color.downFill),o.setLineWidth(1*i.pix),o.moveTo(t[3].x,t[3].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[0].x-c/4,t[0].y),o.lineTo(t[1].x-c/4,t[1].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[2].x,t[2].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[1].x+c/4,t[1].y),o.lineTo(t[0].x+c/4,t[0].y),o.lineTo(t[0].x,t[0].y),o.moveTo(t[3].x,t[3].y)),o.closePath(),o.fill(),o.stroke()}})),o.restore(),{xAxisPoints:s,calPoints:h,eachSpacing:c}}(r,s,e,i,a,t),l=o.xAxisPoints,c=o.calPoints,h=o.eachSpacing;e.chartData.xAxisPoints=l,e.chartData.calPoints=c,e.chartData.eachSpacing=h,me(0,e,i,a),!1!==e.enableMarkLine&&1===t&&he(e,0,a),ve(s?0:e.series,e,i,a,e.chartData),ge(e,i,a,t),Fe(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}})}}function Be(){this.events={}}De.prototype.stop=function(){this.isStop=!0},Be.prototype.addEventListener=function(t,e){this.events[t]=this.events[t]||[],this.events[t].push(e)},Be.prototype.delEventListener=function(t){this.events[t]=[]},Be.prototype.trigger=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];var a=e[0],o=e.slice(1);this.events[a]&&this.events[a].forEach((function(t){try{t.apply(null,o)}catch(e){}}))};var ze=function(t){t.pix=t.pixelRatio?t.pixelRatio:1,t.fontSize=t.fontSize?t.fontSize:13,t.fontColor=t.fontColor?t.fontColor:et.fontColor,""!=t.background&&"none"!=t.background||(t.background="#FFFFFF"),t.title=it({},t.title),t.subtitle=it({},t.subtitle),t.duration=t.duration?t.duration:1e3,t.yAxis=it({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*t.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},t.yAxis),t.xAxis=it({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},t.xAxis),t.xAxis.scrollPosition=t.xAxis.scrollAlign,t.legend=it({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:t.fontSize,lineHeight:t.fontSize,fontColor:t.fontColor,formatter:{},hiddenColor:"#CECECE"},t.legend),t.extra=it({tooltip:{legendShape:"auto"}},t.extra),t.rotate=!!t.rotate,t.animation=!!t.animation,t.rotate=!!t.rotate,t.canvas2d=!!t.canvas2d;let e=it({},et);if(e.color=t.color?t.color:e.color,"pie"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.pie.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"ring"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.ring.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"rose"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.rose.labelWidth*t.pix||e.pieChartLinePadding*t.pix),e.pieChartTextPadding=!1===t.dataLabel?0:e.pieChartTextPadding*t.pix,e.rotate=t.rotate,t.rotate){let e=t.width,i=t.height;t.width=i,t.height=e}if(t.padding=t.padding?t.padding:e.padding,e.yAxisWidth=et.yAxisWidth*t.pix,e.fontSize=t.fontSize*t.pix,e.titleFontSize=et.titleFontSize*t.pix,e.subtitleFontSize=et.subtitleFontSize*t.pix,!t.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=t.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(t){return this.strokeStyle=t},this.context.setLineWidth=function(t){return this.lineWidth=t},this.context.setLineCap=function(t){return this.lineCap=t},this.context.setFontSize=function(t){return this.font=t+"px sans-serif"},this.context.setFillStyle=function(t){return this.fillStyle=t},this.context.setTextAlign=function(t){return this.textAlign=t},this.context.setTextBaseline=function(t){return this.textBaseline=t},this.context.setShadow=function(t,e,i,a){this.shadowColor=a,this.shadowOffsetX=t,this.shadowOffsetY=e,this.shadowBlur=i},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(t){}),t.chartData={},this.uevent=new Be,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=t,this.config=e,Ie.call(this,t.type,t,e,this.context)};ze.prototype.updateData=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=it({},this.opts,t),this.opts.updateData=!0;let e=t.scrollPosition||"current";switch(e){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":let t=Kt(this.opts.series,this.opts,this.config,this.context).yAxisWidth;this.config.yAxisWidth=t;let e=0,i=Zt(this.opts.categories,this.opts,this.config),a=i.xAxisPoints,o=i.startX;e=i.endX-o-i.eachSpacing*(a.length-1),this.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0},this.opts._scrollDistance_=e}Ie.call(this,this.opts.type,this.opts,this.config,this.context)},ze.prototype.zoom=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0!==this.opts.enableScroll)return void console.log("[uCharts] 请启用滚动条后使用");let e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;let i=Kt(this.opts.series,this.opts,this.config,this.context),a=i.yAxisWidth;this.config.yAxisWidth=a;let o=0,r=Zt(this.opts.categories,this.opts,this.config),n=r.xAxisPoints,l=r.startX,s=r.endX,c=r.eachSpacing,h=c*e,d=s-l,p=d-c*(n.length-1);o=d/2-h,o>0&&(o=0),o<p&&(o=p),this.scrollOption={currentOffset:o,startTouchX:0,distance:0,lastMoveTime:0},nt(this,o,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=o,Ie.call(this,this.opts.type,this.opts,this.config,this.context)},ze.prototype.dobuleZoom=function(t){if(!0!==this.opts.enableScroll)return void console.log("[uCharts] 请启用滚动条后使用");const e=t.changedTouches;if(e.length<2)return;for(var i=0;i<e.length;i++)e[i].x=e[i].x?e[i].x:e[i].clientX,e[i].y=e[i].y?e[i].y:e[i].clientY;const a=[ft(e[0],this.opts,t),ft(e[1],this.opts,t)],o=Math.abs(a[0].x-a[1].x);if(!this.scrollOption.moveCount){let t={changedTouches:[{x:e[0].x,y:this.opts.area[0]/this.opts.pix+2}]},i={changedTouches:[{x:e[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(t={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[0].y}]},i={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[1].y}]});const a=this.getCurrentDataIndex(t).index,o=this.getCurrentDataIndex(i).index,r=Math.abs(a-o);return this.scrollOption.moveCount=r,this.scrollOption.moveCurrent1=Math.min(a,o),void(this.scrollOption.moveCurrent2=Math.max(a,o))}let r=o/this.scrollOption.moveCount,n=(this.opts.width-this.opts.area[1]-this.opts.area[3])/r;n=n<=2?2:n,n=n>=this.opts.categories.length?this.opts.categories.length:n,this.opts.animation=!1,this.opts.xAxis.itemCount=n;let l=0,s=Zt(this.opts.categories,this.opts,this.config),c=s.xAxisPoints,h=s.startX,d=s.endX,p=s.eachSpacing,x=p*this.scrollOption.moveCurrent1,u=d-h-p*(c.length-1);l=-x+Math.min(a[0].x,a[1].x)-this.opts.area[3]-p,l>0&&(l=0),l<u&&(l=u),this.scrollOption.currentOffset=l,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,nt(this,l,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=l,Ie.call(this,this.opts.type,this.opts,this.config,this.context)},ze.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},ze.prototype.addEventListener=function(t,e){this.uevent.addEventListener(t,e)},ze.prototype.delEventListener=function(t){this.uevent.delEventListener(t)},ze.prototype.getCurrentDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0]){let i=ft(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type?function(t,e,i){var a=-1,o=Ft(e.series);if(e&&e.center&&Tt(t,e.center,e.radius)){var r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r=-r,i.extra.pie&&i.extra.pie.offsetAngle&&(r-=i.extra.pie.offsetAngle*Math.PI/180),i.extra.ring&&i.extra.ring.offsetAngle&&(r-=i.extra.ring.offsetAngle*Math.PI/180);for(var n=0,l=o.length;n<l;n++)if(lt(r,o[n]._start_,o[n]._start_+2*o[n]._proportion_*Math.PI)){a=n;break}}return a}({x:i.x,y:i.y},this.opts.chartData.pieData,this.opts):"rose"===this.opts.type?function(t,e,i){var a=-1,o=Dt(i._series_,i.extra.rose.type,e.radius,e.radius);if(e&&e.center&&Tt(t,e.center,e.radius)){var r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r=-r,i.extra.rose&&i.extra.rose.offsetAngle&&(r-=i.extra.rose.offsetAngle*Math.PI/180);for(var n=0,l=o.length;n<l;n++)if(lt(r,o[n]._start_,o[n]._start_+2*o[n]._rose_proportion_*Math.PI)){a=n;break}}return a}({x:i.x,y:i.y},this.opts.chartData.pieData,this.opts):"radar"===this.opts.type?function(t,e,i){var a=2*Math.PI/i,o=-1;if(Tt(t,e.center,e.radius)){var r=function(t){return t<0&&(t+=2*Math.PI),t>2*Math.PI&&(t-=2*Math.PI),t},n=Math.atan2(e.center.y-t.y,t.x-e.center.x);(n*=-1)<0&&(n+=2*Math.PI),e.angleList.map((function(t){return r(-1*t)})).forEach((function(t,e){var i=r(t-a/2),l=r(t+a/2);l<i&&(l+=2*Math.PI),(n>=i&&n<=l||n+2*Math.PI>=i&&n+2*Math.PI<=l)&&(o=e)}))}return o}({x:i.x,y:i.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(t,e){for(var i=-1,a=0,o=e.series.length;a<o;a++){var r=e.series[a];if(t.x>r.funnelArea[0]&&t.x<r.funnelArea[2]&&t.y>r.funnelArea[1]&&t.y<r.funnelArea[3]){i=a;break}}return i}({x:i.x,y:i.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(t,e){for(var i,a,o,r,n,l,s=-1,c=e.chartData.mapData,h=e.series,d=(i=t.y,a=t.x,o=c.bounds,r=c.scale,n=c.xoffset,l=c.yoffset,{x:(a-n)/r+o.xMin,y:o.yMax-(i-l)/r}),p=[d.x,d.y],x=0,u=h.length;x<u;x++)if(Le(p,h[x].geometry.coordinates,e.chartData.mapData.mercator)){s=x;break}return s}({x:i.x,y:i.y},this.opts):"word"===this.opts.type?function(t,e){for(var i=-1,a=0,o=e.length;a<o;a++){var r=e[a];if(t.x>r.area[0]&&t.x<r.area[2]&&t.y>r.area[1]&&t.y<r.area[3]){i=a;break}}return i}({x:i.x,y:i.y},this.opts.chartData.wordCloudData):"bar"===this.opts.type?function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},n=i.chartData.eachSpacing/2;let l=i.chartData.yAxisPoints;return e&&e.length>0&&Lt(t,i)&&l.forEach((function(e,i){t.y+o+n>e&&(r.index=i)})),r}({x:i.x,y:i.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},n=i.chartData.eachSpacing/2;let l=[];if(e&&e.length>0){if(i.categories){for(let t=1;t<i.chartData.xAxisPoints.length;t++)l.push(i.chartData.xAxisPoints[t]-n);"line"!=i.type&&"area"!=i.type||"justify"!=i.xAxis.boundaryGap||(l=i.chartData.xAxisPoints)}else n=0;if(Lt(t,i))if(i.categories)l.forEach((function(e,i){t.x+o+n>e&&(r.index=i)}));else{let i=Array(e.length);for(let r=0;r<e.length;r++){i[r]=Array(e[r].length);for(let a=0;a<e[r].length;a++)i[r][a]=Math.abs(e[r][a].x-t.x)}let a=Array(i.length),o=Array(i.length);for(let t=0;t<i.length;t++)a[t]=Math.min.apply(null,i[t]),o[t]=i[t].indexOf(a[t]);let n=Math.min.apply(null,a);r.index=[];for(let t=0;t<a.length;t++)a[t]==n&&(r.group.push(t),r.index.push(o[t]))}}return r}({x:i.x,y:i.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},ze.prototype.getLegendDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0]){let i=ft(e,this.opts,t);return function(t,e,i){let a=-1;if(function(t,e){return t.x>e.start.x&&t.x<e.end.x&&t.y>e.start.y&&t.y<e.end.y}(t,e.area)){let i=e.points,o=-1;for(let e=0,r=i.length;e<r;e++){let r=i[e];for(let e=0;e<r.length;e++){o+=1;let i=r[e].area;if(i&&t.x>i[0]-0&&t.x<i[2]+0&&t.y>i[1]-0&&t.y<i[3]+0){a=o;break}}}return a}return a}({x:i.x,y:i.y},this.opts.chartData.legendData)}return-1},ze.prototype.touchLegend=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;if(i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0]){ft(i,this.opts,t);var a=this.getLegendDataIndex(t);a>=0&&("candle"==this.opts.type?this.opts.seriesMA[a].show=!this.opts.seriesMA[a].show:this.opts.series[a].show=!this.opts.series[a].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,Ie.call(this,this.opts.type,this.opts,this.config,this.context))}},ze.prototype.showToolTip=function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null;(a=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0])||console.log("[uCharts] 未获取到event坐标信息");var o=ft(a,this.opts,t),r=this.scrollOption.currentOffset,n=it({},this.opts,{_scrollDistance_:r,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type||"scatter"===this.opts.type||"bubble"===this.opts.type){var l=this.getCurrentDataIndex(t);if((x=null==i.index?l.index:i.index)>-1||x.length>0)if(0!==(c=yt(this.opts.series,x,l.group)).length){var s=(p=bt(c,this.opts,x,l.group,this.opts.categories,i)).textList;(h=p.offset).y=o.y,n.tooltip={textList:void 0!==i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x,group:l.group}}Ie.call(this,n.type,n,this.config,this.context)}if("mount"===this.opts.type){if((x=null==i.index?this.getCurrentDataIndex(t).index:i.index)>-1){n=it({},this.opts,{animation:!1});var c=it({},n._series_[x]),h=(s=[{text:i.formatter?i.formatter(c,void 0,x,n):c.name+": "+c.data,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],{x:n.chartData.calPoints[x].x,y:o.y});n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}Ie.call(this,n.type,n,this.config,this.context)}if("bar"===this.opts.type){l=this.getCurrentDataIndex(t);if((x=null==i.index?l.index:i.index)>-1||x.length>0)if(0!==(c=yt(this.opts.series,x,l.group)).length){s=(p=bt(c,this.opts,x,l.group,this.opts.categories,i)).textList;(h=p.offset).x=o.x,n.tooltip={textList:void 0!==i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}Ie.call(this,n.type,n,this.config,this.context)}if("mix"===this.opts.type){l=this.getCurrentDataIndex(t);if((x=null==i.index?l.index:i.index)>-1){r=this.scrollOption.currentOffset,n=it({},this.opts,{_scrollDistance_:r,animation:!1});if(0!==(c=yt(this.opts.series,x)).length){var d=Ct(c,this.opts,x,this.opts.categories,i);s=d.textList;(h=d.offset).y=o.y,n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}}Ie.call(this,n.type,n,this.config,this.context)}if("candle"===this.opts.type){l=this.getCurrentDataIndex(t);if((x=null==i.index?l.index:i.index)>-1){r=this.scrollOption.currentOffset,n=it({},this.opts,{_scrollDistance_:r,animation:!1});if(0!==(c=yt(this.opts.series,x)).length){var p;s=(p=St(this.opts.series[0].data,c,this.opts,x,this.opts.categories,this.opts.extra.candle)).textList;(h=p.offset).y=o.y,n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}}Ie.call(this,n.type,n,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){if((x=null==i.index?this.getCurrentDataIndex(t):i.index)>-1){n=it({},this.opts,{animation:!1}),c=it({},n._series_[x]),s=[{text:i.formatter?i.formatter(c,void 0,x,n):c.name+": "+c.data,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],h={x:o.x,y:o.y};n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}Ie.call(this,n.type,n,this.config,this.context)}if("map"===this.opts.type){if((x=null==i.index?this.getCurrentDataIndex(t):i.index)>-1){n=it({},this.opts,{animation:!1});(c=it({},this.opts.series[x])).name=null==(e=null==c?void 0:c.properties)?void 0:e.name;s=[{text:i.formatter?i.formatter(c,void 0,x,this.opts):c.name,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],h={x:o.x,y:o.y};n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}n.updateData=!1,Ie.call(this,n.type,n,this.config,this.context)}if("word"===this.opts.type){if((x=null==i.index?this.getCurrentDataIndex(t):i.index)>-1){n=it({},this.opts,{animation:!1}),c=it({},this.opts.series[x]),s=[{text:i.formatter?i.formatter(c,void 0,x,this.opts):c.name,color:c.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?c.legendShape:this.opts.extra.tooltip.legendShape}],h={x:o.x,y:o.y};n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}n.updateData=!1,Ie.call(this,n.type,n,this.config,this.context)}if("radar"===this.opts.type){var x;if((x=null==i.index?this.getCurrentDataIndex(t):i.index)>-1){n=it({},this.opts,{animation:!1});if(0!==(c=yt(this.opts.series,x)).length){s=c.map((t=>({text:i.formatter?i.formatter(t,this.opts.categories[x],x,this.opts):t.name+": "+t.data,color:t.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?t.legendShape:this.opts.extra.tooltip.legendShape}))),h={x:o.x,y:o.y};n.tooltip={textList:i.textList?i.textList:s,offset:void 0!==i.offset?i.offset:h,option:i,index:x}}}Ie.call(this,n.type,n,this.config,this.context)}},ze.prototype.translate=function(t){this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0};let e=it({},this.opts,{_scrollDistance_:t,animation:!1});Ie.call(this,this.opts.type,e,this.config,this.context)},ze.prototype.scrollStart=function(t){var e=null,i=ft(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],this.opts,t);e&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=i.x)},ze.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());let e=this.opts.touchMoveLimit||60,i=Date.now();if(!(i-this.scrollOption.lastMoveTime<Math.floor(1e3/e))&&0!=this.scrollOption.startTouchX){this.scrollOption.lastMoveTime=i;var a=null;if((a=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0])&&!0===this.opts.enableScroll){var o;o=ft(a,this.opts,t).x-this.scrollOption.startTouchX;var r=this.scrollOption.currentOffset,n=nt(this,r+o,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=o=n-r;var l=it({},this.opts,{_scrollDistance_:r+o,animation:!1});return this.opts=l,Ie.call(this,l.type,l,this.config,this.context),r+o}}},ze.prototype.scrollEnd=function(t){if(!0===this.opts.enableScroll){var e=this.scrollOption,i=e.currentOffset,a=e.distance;this.scrollOption.currentOffset=i+a,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};var Oe={};const Ne={components:{dateRange:J,weeklyList:tt},props:{identity:{type:Number,default:1},isZhoubao:{type:Boolean,default:!1},isXiezhoubao:{type:Boolean,default:!1},isFenxi:{type:Boolean,default:!1},tabNames:{type:String,default:""}},data:()=>({cWidth:320,cHeight:300,tabs:["统计分析","周报"],tabCur:0,tabName:"统计分析",briefing:{},chartData_bar:{},barPath:"",chartData_ring:{},ringPath:"",range:[]}),watch:{tabNames:{handler(t,e){t&&null!=t&&(this.isFenxi&&this.isZhoubao&&(this.tabCur=1),this.tabName=t)},immediate:!0}},onReady(){this.cWidth=T(300),this.cHeight=T(300),this.getServerData()},created(){this.updataList([])},methods:{tabsFn(){let t=[];return this.isFenxi||this.isZhoubao||(this.tabName=""),this.isFenxi&&t.push("统计分析"),this.isZhoubao&&(this.isFenxi||(this.tabName="周报"),t.push("周报")),t},RefreshPage(){this.$nextTick((()=>{"周报"==this.tabName&&this.$refs.weeklyList.RefreshList(!0)}))},gotoAdd(){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{},path:"addWeekly"}))},updataList(t){let e={};t&&2==t.length&&(e.timeStart=t[0]+" 00:00:00",e.timeEnd=t[1]+" 23:59:59"),this.range=t,this.$api.staticInfoByPeriodAPI_investment({data:e,method:"post"}).then((t=>{var e;this.briefing=(null==(e=t.result)?void 0:e.briefing)||{},this.barPath="",this.ringPath="",Oe={},this.getBarData(t.result),this.getRingData(t.result)}))},changeBottomTab(t){this.$emit("changeBottomTab",t)},openRange(){this.$refs.dateRange.opens(this.range)},getBarData(t){let e=[],i=[],a=[],o=[];t.personCount&&t.personCount.length&&t.personCount.map((t=>(o.push(t.name),Number(t.stateSign)?e.push(Number(t.stateSign)):e.push(0),Number(t.stateFollow)?i.push(Number(t.stateFollow)):i.push(0),Number(t.stateFail)?a.push(Number(t.stateFail)):a.push(0),t)));let r={categories:o,series:[{name:"签约成功",textColor:"#4CC169",data:e,barMinHeight:5},{name:"跟进中",textColor:"#3370FF",barMinHeight:5,data:i},{name:"签约失败",textColor:"#E04848",barMinHeight:5,data:a}]};this.drawCharts1("barId",r)},drawCharts1(t,e){const i=A(t,this);Oe[t]=new ze({type:"bar",color:["#4CC169","#3370FF","#E04848"],padding:[15,25,15,15],enableScroll:!1,legend:{position:"top",float:"center"},context:i,width:this.cWidth,height:this.cHeight,categories:e.categories,series:e.series,xAxis:{disableGrid:!0,splitNumber:1,data:[{min:1}]},yAxis:{},extra:{showCategory:!0,bar:{type:"stack",width:12,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",barBorderCircle:!0,categoryGap:2}}}),setTimeout((()=>{P({canvasId:t,fileType:"png",quality:1,success:t=>{this.barPath=t.tempFilePath},fail:t=>{console.log(t,"1111111**")}},this)}),300)},getRingData(t){let e=[];t.chainGroupCount&&t.chainGroupCount.length&&t.chainGroupCount.map((t=>(e.push({name:t.name,value:Number(t.total)}),t)));let i={series:[{data:e}]};this.drawCharts2("ringId",i)},drawCharts2(t,e){const i=A(t,this);Oe[t]=new ze({type:"ring",context:i,width:this.cWidth,height:this.cHeight,categories:e.categories,series:e.series,xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},rotate:!1,rotateLock:!1,color:["#F0C92E","#3370FF","#6CCE26","#987EFB","#F0722E","#6CCBFF","#31A9FF","#EE97FD","#ACE2DD","#E04848","#5A39D8","#E9F148"],padding:[5,5,5,5],dataLabel:!0,enableScroll:!1,legend:{show:!0,position:"top",lineHeight:25,float:"left",itemGap:20},title:{name:"总数",fontSize:12,color:"#666666"},subtitle:{name:this.briefing.totalEnterprise||0,fontSize:28,color:"#000000"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:5,border:!1,borderColor:"#FFFFFF"}}}),setTimeout((()=>{P({canvasId:t,fileType:"png",quality:1,success:t=>{this.ringPath=t.tempFilePath},fail:t=>{console.log(t,"1111111**")}},this)}),300)},tap(t){Oe[t.target.id].touchLegend(t),Oe[t.target.id].showToolTip(t)},tabChange(t,e){this.tabName=e,"统计分析"==e&&this.updataList()},attractDel(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{id:t.id,state:t.state,type:"1"},path:"attractDetail"}))}}};const Re=M({components:{targetCompany:q,myApprovals:Y,myCollection:K,dataDashboard:M(Ne,[["render",function(s,c,d,x,u,g){var f;const y=F(t("v-tabs"),G),b=p,S=C,T=_,A=m("weeklyList"),P=m("dateRange");return e(),l("div",{class:"pageBox"},[w(o(b,{class:"policyTabs"},{default:a((()=>[o(y,{modelValue:u.tabCur,"onUpdate:modelValue":c[0]||(c[0]=t=>u.tabCur=t),tabs:g.tabsFn(),"bg-color":"transparent",color:"#727983","active-color":"#1D2129","line-color":"#3370FF","font-size":"30rpx","font-weight":500,scroll:!1,height:"80rpx","line-height":"6rpx","padding-item":"0 50rpx","line-scale":.2,onChange:g.tabChange},null,8,["modelValue","tabs","onChange"])])),_:1},512),[[L,d.isFenxi&&d.isZhoubao]]),"统计分析"==u.tabName?(e(),i(b,{key:0,class:"header"},{default:a((()=>[o(b,{class:"countTxt"}),o(b,{class:"btns"},{default:a((()=>[o(b,{class:"btn",onClick:g.openRange},{default:a((()=>[o(S,{class:"btnIcon",src:"https://staticstg.idicc.cn/static/wechatai/assets/icon_rq-Ctx0X2yg.svg"}),o(b,{class:"btnTxt"},{default:a((()=>[v("日期")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})):h("",!0),"统计分析"==u.tabName?(e(),l("div",{key:1,class:"contentView"},[r("div",{class:"simpleView"},[r("div",{class:"tit"},"招商简报"),r("div",{class:"card1"},[o(S,{class:"icon",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='45'%20height='45'%20viewBox='0%200%2045%2045'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_670_29131'%3e%3crect%20x='8'%20y='7'%20width='30'%20height='30'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%3e%3cg%3e%3crect%20x='0'%20y='0'%20width='45'%20height='45'%20rx='8'%20fill='%233370FF'%20fill-opacity='0.20000000298023224'/%3e%3c/g%3e%3cg%20clip-path='url(%23master_svg0_670_29131)'%3e%3cg%3e%3cpath%20d='M26.75,10.39998759765625L26.75,34.15003759765625L13,34.15003759765625C12.25,34.15003759765625,11.75,33.65003759765625,11.75,32.90003759765625L11.75,13.89998759765625C11.75,13.27498759765625,12.125,12.77498759765625,12.75,12.64998759765625L25.25,9.52498779765625C25.875,9.39998759765625,26.5,9.77498759765625,26.75,10.39998759765625ZM28,21.649937597656248L33.5,23.774937597656248C34,24.024937597656248,34.25,24.399937597656248,34.25,24.899937597656248L34.25,32.89993759765625C34.25,33.64993759765625,33.75,34.14993759765625,33,34.14993759765625L28,34.14993759765625L28,21.649937597656248ZM21.75,22.899937597656248L16.75,22.899937597656248C16,22.899937597656248,15.5,23.399937597656248,15.5,24.149937597656248C15.5,24.77503759765625,16,25.274937597656248,16.625,25.399937597656248L21.75,25.399937597656248C22.5,25.399937597656248,23,24.899937597656248,23,24.149937597656248C23,23.399937597656248,22.5,22.899937597656248,21.75,22.899937597656248ZM21.75,16.64998759765625L16.75,16.64998759765625C16,16.64998759765625,15.5,17.14998759765625,15.5,17.89998759765625C15.5,18.52498759765625,16,19.02498759765625,16.625,19.14998759765625L21.75,19.14998759765625C22.5,19.14998759765625,23,18.64998759765625,23,17.89998759765625C23,17.14998759765625,22.5,16.64998759765625,21.75,16.64998759765625Z'%20fill='%233370FF'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e"}),r("div",{class:"dataView"},[r("div",{class:"data1"},[v("纳入意向企业："),r("span",{class:"data1_num"},n((null==(f=u.briefing)?void 0:f.totalEnterprise)||0),1)]),r("div",{class:"data2"},[r("div",{class:"dataTxt"},"系统推荐："+n(u.briefing.systemEnterprise||0),1),r("div",{class:"dataTxt"},"自行添加："+n(u.briefing.ownEnterprise||0),1)])])]),r("div",{class:"cardView"},[r("div",{class:"card2"},[r("div",{class:"dataT"},"自行跟进"),r("div",{class:"dataN"},n(u.briefing.withFollowing||0),1)]),r("div",{class:"card2"},[r("div",{class:"dataT"},"委托招商"),r("div",{class:"dataN"},n(u.briefing.withEntrust||0),1)])]),r("div",{class:"cardView"},[r("div",{class:"card2"},[r("div",{class:"dataT"},"未跟踪企业"),r("div",{class:"dataN"},n(u.briefing.withNotFollow||0),1)])]),r("div",{class:"card1",style:{"justify-content":"space-between","align-items":"center"}},[r("div",{class:"dataCard"},[r("div",{class:"dataTView"},[o(S,{class:"dataIcon",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%3e%3cg%3e%3cpath%20d='M8,0C12.4182,0,16,3.5818,16,8C16,12.4182,12.4182,16,8,16C3.5818,16,0,12.4182,0,8C0,3.5818,3.5818,0,8,0ZM11.424,5.5754C11.1895,5.3412,10.8096,5.34147,10.5754,5.576L7.0048,9.152L5.4234,7.5772C5.18809,7.34654,4.8108,7.34879,4.57827,7.58226C4.34574,7.81572,4.34499,8.19302,4.5766,8.4274L6.5826,10.4252C6.81718,10.6588,7.19668,10.6583,7.4306,10.424L11.4246,6.424C11.6588,6.18951,11.6585,5.80956,11.424,5.5754Z'%20fill='%236CCE26'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/svg%3e"}),r("div",{class:"dataT"},"签约成功")]),r("div",{class:"dataN dataNC"},n(u.briefing.stateSign||0),1)]),r("div",{class:"line"}),r("div",{class:"dataCard"},[r("div",{class:"dataTView"},[o(S,{class:"dataIcon",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%3e%3cg%3e%3cpath%20d='M8,0C12.4183,0,16,3.58172,16,8C16,12.4183,12.4183,16,8,16C3.58172,16,0,12.4183,0,8C0,3.58172,3.58172,0,8,0ZM8.424,3.922L7.578,3.922C7.50091,3.92255,7.43855,3.98491,7.438,4.062L7.438,8.904C7.43783,8.94913,7.45942,8.99157,7.496,9.018L10.403,11.138C10.4658,11.1835,10.5536,11.1701,10.6,11.108L11.103,10.422C11.1492,10.3589,11.1348,10.2702,11.071,10.225L8.564,8.413L8.564,4.063C8.564,3.98552,8.50148,3.92255,8.424,3.922Z'%20fill='%231C7EF3'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/svg%3e"}),r("div",{class:"dataT"},"跟进中")]),r("div",{class:"dataN dataNC"},n(u.briefing.stateFollow||0),1)]),r("div",{class:"line"}),r("div",{class:"dataCard"},[r("div",{class:"dataTView"},[o(S,{class:"dataIcon",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%3e%3cg%3e%3cpath%20d='M8,0C12.4182,0,16,3.5818,16,8C16,12.4182,12.4182,16,8,16C3.5818,16,0,12.4182,0,8C0,3.5818,3.5818,0,8,0ZM5.48958,4.67912C5.26629,4.45212,4.9005,4.4506,4.67535,4.67575C4.45019,4.90091,4.45182,5.26641,4.67882,5.48971L7.1894,8L4.67882,10.5103C4.45182,10.7336,4.45019,11.0991,4.67535,11.3242C4.9005,11.5494,5.26629,11.5479,5.48958,11.3209L8,8.8106L10.5104,11.3209C10.7337,11.5479,11.0995,11.5494,11.3247,11.3242C11.5498,11.0991,11.5482,10.7336,11.3212,10.5103L8.8106,8L11.3212,5.48971C11.5482,5.26641,11.5498,4.90091,11.3247,4.67575C11.0995,4.4506,10.7337,4.45212,10.5104,4.67912L8,7.1894L5.48958,4.67912Z'%20fill='%23E04848'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/svg%3e"}),r("div",{class:"dataT"},"签约失败")]),r("div",{class:"dataN dataNC"},n(u.briefing.stateFail||0),1)])])]),r("div",{class:"zhuView"},[r("div",{class:"tit"},"跟进人员排行榜"),r("div",{class:"chartsBox"},[u.barPath?(e(),i(S,{key:0,class:"chartsImg",src:u.barPath},null,8,["src"])):(e(),i(T,{key:1,id:"barId","canvas-id":"barId",class:"charts",onClick:g.tap},null,8,["onClick"]))])]),r("div",{class:"ringView"},[r("div",{class:"tit"},"意向企业产业链分布"),r("div",{class:"chartsBox"},[u.ringPath?(e(),i(S,{key:0,class:"chartsImg",src:u.ringPath},null,8,["src"])):(e(),i(T,{key:1,id:"ringId","canvas-id":"ringId",class:"charts",onClick:g.tap},null,8,["onClick"]))])]),r("div",{style:{height:"120px"}})])):h("",!0),"周报"==u.tabName?(e(),l("div",{key:2},[o(A,{ref:"weeklyList"},null,512)])):h("",!0),o(P,{ref:"dateRange","bottom-blank":!1,"applet-tab":!1,onUpdataList:g.updataList,onChangeBottomTab:g.changeBottomTab},null,8,["onUpdataList","onChangeBottomTab"]),"周报"==u.tabName&&d.isXiezhoubao?(e(),i(S,{key:3,src:U,class:"addButton",onClick:g.gotoAdd},null,8,["onClick"])):h("",!0)])}],["__scopeId","data-v-cb2c04fd"]]),tabBar:$},data:()=>({showBtns:!0,pitch:1,tabNames:"",token:"",IdentityType:1,jurisdiction:{isZhipai:!1,isWodeshenpi:!1,isShenpi:!1,isKanban:!1,isZhoubao:!1,isXiezhoubao:!1,isFenxi:!1}}),onLoad(t){let e="";this.token=x("token"),e=t.pitch,W("pitch")&&(e=W("pitch")),W("token")&&(this.token=W("token"),y("token",this.token)),W("identity")&&(this.IdentityType=W("identity"),y("userIdentityType",W("identity"))),this.getJurisdictionFn(e)},onShow(){this.$nextTick((()=>{4==this.pitch?this.$refs.dataDashboard.RefreshPage(!0):1==this.pitch?this.$refs.targetCompany.RefreshPage(!0):3==this.pitch&&this.$refs.myCollection.RefreshPage(!0)}))},methods:{changeBottomTab(t){this.showBtns=t},getJurisdictionFn(t){this.$api.queryResourceByType({data:{type:3}}).then((e=>{console.log(e),e.result.map((t=>{"ClueAssignmentButton"==t.resourceCode&&(console.log(t.resourceName),this.jurisdiction.isZhipai=!0),"Myapproval"==t.resourceCode&&(console.log(t.resourceName),this.jurisdiction.isWodeshenpi=!0),"DataBoard"==t.resourceCode&&(console.log(t.resourceName),this.jurisdiction.isKanban=!0),"weeklyNewspaper"==t.resourceCode&&(console.log(t.resourceName),this.jurisdiction.isZhoubao=!0),"WriteWeeklyReportButton"==t.resourceCode&&(console.log(t.resourceName),this.jurisdiction.isXiezhoubao=!0),"statisticAnalysis"==t.resourceCode&&(console.log(t.resourceName),this.jurisdiction.isFenxi=!0)})),!t||this.jurisdiction.isZhoubao&&this.jurisdiction.isKanban?t&&this.jurisdiction.isZhoubao&&this.jurisdiction.isKanban&&(this.pitch=4,this.tabNames="周报"):u({title:"暂无周报查看权限",icon:"none",duration:2e3})}))},changtab(t){this.pitch=t}}},[["render",function(t,r,n,l,s,c){const d=m("targetCompany"),x=m("myApprovals"),u=m("myCollection"),g=m("dataDashboard"),y=C,b=p;return e(),i(b,{class:"contentView",style:f(s.showBtns?"height:calc(100vh - 144rpx)":"height:100vh")},{default:a((()=>[1==s.pitch?(e(),i(d,{key:0,ref:"targetCompany",isZhipai:s.jurisdiction.isZhipai},null,8,["isZhipai"])):h("",!0),2==s.pitch?(e(),i(x,{key:1,ref:"handleStrategyRef"},null,512)):h("",!0),3==s.pitch?(e(),i(u,{key:2,ref:"myCollection"},null,512)):h("",!0),4==s.pitch?(e(),i(g,{key:3,tabNames:s.tabNames,isZhoubao:s.jurisdiction.isZhoubao,isXiezhoubao:s.jurisdiction.isXiezhoubao,isFenxi:s.jurisdiction.isFenxi,ref:"dataDashboard",onChangeBottomTab:c.changeBottomTab},null,8,["tabNames","isZhoubao","isXiezhoubao","isFenxi","onChangeBottomTab"])):h("",!0),s.showBtns?(e(),i(b,{key:4,class:"bottomTab"},{default:a((()=>[o(b,{onClick:r[0]||(r[0]=t=>c.changtab(1)),class:"singleTabs",style:f({color:1==s.pitch?"#3370FF":"#86909C"})},{default:a((()=>[1==s.pitch?(e(),i(y,{key:0,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_597_34149'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_597_34149)'%3e%3cg%3e%3cpath%20d='M3.24023125,8.09400533203125L3.24023125,20.89801533203125L1.77078125,20.89801533203125C1.37478125,20.89801533203125,1.05078125,21.22201533203125,1.05078125,21.61801533203125C1.05078125,22.01401533203125,1.37478125,22.33801533203125,1.77078125,22.33801533203125L22.06878125,22.33801533203125C22.47078125,22.33801533203125,22.78878125,22.01401533203125,22.78878125,21.61801533203125C22.78878125,21.22201533203125,22.46478125,20.89801533203125,22.06878125,20.89801533203125L20.96448125,20.89801533203125L20.96448125,10.55399533203125C20.96448125,9.74999533203125,20.53248125,8.99399533203125,19.84248125,8.579995332031249L12.68428125,4.32374533203125L12.68428125,2.99400533203125C12.68428125,2.58600433203125,12.46228125,2.20200433203125,12.10228125,2.00400433203125C11.74228125,1.80000453203125,11.29828125,1.81200443203125,10.95023125,2.02800433203125L4.33823125,6.12000533203125C3.64823125,6.54600533203125,3.24023125,7.28400533203125,3.24023125,8.09400533203125ZM12.68428125,5.99929533203125L12.68428125,20.89801533203125L19.52448125,20.89801533203125L19.52448125,10.55999533203125C19.52448125,10.25999533203125,19.36248125,9.97799533203125,19.10448125,9.82199533203125L12.68428125,5.99929533203125ZM11.24428125,3.54000533203125L11.24428125,20.89801533203125L4.68023125,20.89801533203125L4.68023125,8.10000533203125C4.68023125,7.79400533203125,4.83623125,7.51200533203125,5.10023125,7.35000533203125L11.24428125,3.54000533203125ZM6.45047125,11.24399533203125L9.54047125,11.24399533203125C9.93647125,11.24399533203125,10.26047125,10.91999533203125,10.26047125,10.52399533203125C10.26047125,10.12799533203125,9.93647125,9.80399533203125,9.54047125,9.80399533203125L6.45047125,9.80399533203125C6.05447125,9.80399533203125,5.73047125,10.12799533203125,5.73047125,10.52399533203125C5.73047125,10.91999533203125,6.05447125,11.24399533203125,6.45047125,11.24399533203125ZM17.35878125,13.54201533203125L14.26878125,13.54201533203125C13.87278125,13.54201533203125,13.54878125,13.21801533203125,13.54878125,12.82201533203125C13.54878125,12.42601533203125,13.87278125,12.10201533203125,14.26878125,12.10201533203125L17.35878125,12.10201533203125C17.75478125,12.10201533203125,18.07878125,12.42601533203125,18.07878125,12.82201533203125C18.07878125,13.21801533203125,17.76078125,13.54201533203125,17.35878125,13.54201533203125ZM6.45047125,14.82001533203125L9.54047125,14.82001533203125C9.93647125,14.82001533203125,10.26047125,14.49601533203125,10.26047125,14.10001533203125C10.26047125,13.70401533203125,9.93647125,13.38001533203125,9.54047125,13.38001533203125L6.45047125,13.38001533203125C6.05447125,13.38001533203125,5.73047125,13.70401533203125,5.73047125,14.10001533203125C5.73047125,14.49601533203125,6.05447125,14.82001533203125,6.45047125,14.82001533203125ZM17.35878125,17.41801533203125L14.26878125,17.41801533203125C13.87278125,17.41801533203125,13.54878125,17.094015332031248,13.54878125,16.69801533203125C13.54878125,16.30201533203125,13.87278125,15.97801533203125,14.26878125,15.97801533203125L17.35878125,15.97801533203125C17.75478125,15.97801533203125,18.07878125,16.30201533203125,18.07878125,16.69801533203125C18.07878125,17.094015332031248,17.76078125,17.41801533203125,17.35878125,17.41801533203125ZM6.45047125,18.39601533203125L9.54047125,18.39601533203125C9.93647125,18.39601533203125,10.26047125,18.07201533203125,10.26047125,17.67601533203125C10.26047125,17.280015332031248,9.93647125,16.95601533203125,9.54047125,16.95601533203125L6.45047125,16.95601533203125C6.05447125,16.95601533203125,5.73047125,17.280015332031248,5.73047125,17.67601533203125C5.73047125,18.07201533203125,6.05447125,18.39601533203125,6.45047125,18.39601533203125Z'%20fill-rule='evenodd'%20fill='%233370FF'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})):(e(),i(y,{key:1,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_597_34149'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_597_34149)'%3e%3cg%3e%3cpath%20d='M3.24023125,8.09400533203125L3.24023125,20.89801533203125L1.77078125,20.89801533203125C1.37478125,20.89801533203125,1.05078125,21.22201533203125,1.05078125,21.61801533203125C1.05078125,22.01401533203125,1.37478125,22.33801533203125,1.77078125,22.33801533203125L22.06878125,22.33801533203125C22.47078125,22.33801533203125,22.78878125,22.01401533203125,22.78878125,21.61801533203125C22.78878125,21.22201533203125,22.46478125,20.89801533203125,22.06878125,20.89801533203125L20.96448125,20.89801533203125L20.96448125,10.55399533203125C20.96448125,9.74999533203125,20.53248125,8.99399533203125,19.84248125,8.579995332031249L12.68428125,4.32374533203125L12.68428125,2.99400533203125C12.68428125,2.58600433203125,12.46228125,2.20200433203125,12.10228125,2.00400433203125C11.74228125,1.80000453203125,11.29828125,1.81200443203125,10.95023125,2.02800433203125L4.33823125,6.12000533203125C3.64823125,6.54600533203125,3.24023125,7.28400533203125,3.24023125,8.09400533203125ZM12.68428125,5.99929533203125L12.68428125,20.89801533203125L19.52448125,20.89801533203125L19.52448125,10.55999533203125C19.52448125,10.25999533203125,19.36248125,9.97799533203125,19.10448125,9.82199533203125L12.68428125,5.99929533203125ZM11.24428125,3.54000533203125L11.24428125,20.89801533203125L4.68023125,20.89801533203125L4.68023125,8.10000533203125C4.68023125,7.79400533203125,4.83623125,7.51200533203125,5.10023125,7.35000533203125L11.24428125,3.54000533203125ZM6.45047125,11.24399533203125L9.54047125,11.24399533203125C9.93647125,11.24399533203125,10.26047125,10.91999533203125,10.26047125,10.52399533203125C10.26047125,10.12799533203125,9.93647125,9.80399533203125,9.54047125,9.80399533203125L6.45047125,9.80399533203125C6.05447125,9.80399533203125,5.73047125,10.12799533203125,5.73047125,10.52399533203125C5.73047125,10.91999533203125,6.05447125,11.24399533203125,6.45047125,11.24399533203125ZM17.35878125,13.54201533203125L14.26878125,13.54201533203125C13.87278125,13.54201533203125,13.54878125,13.21801533203125,13.54878125,12.82201533203125C13.54878125,12.42601533203125,13.87278125,12.10201533203125,14.26878125,12.10201533203125L17.35878125,12.10201533203125C17.75478125,12.10201533203125,18.07878125,12.42601533203125,18.07878125,12.82201533203125C18.07878125,13.21801533203125,17.76078125,13.54201533203125,17.35878125,13.54201533203125ZM6.45047125,14.82001533203125L9.54047125,14.82001533203125C9.93647125,14.82001533203125,10.26047125,14.49601533203125,10.26047125,14.10001533203125C10.26047125,13.70401533203125,9.93647125,13.38001533203125,9.54047125,13.38001533203125L6.45047125,13.38001533203125C6.05447125,13.38001533203125,5.73047125,13.70401533203125,5.73047125,14.10001533203125C5.73047125,14.49601533203125,6.05447125,14.82001533203125,6.45047125,14.82001533203125ZM17.35878125,17.41801533203125L14.26878125,17.41801533203125C13.87278125,17.41801533203125,13.54878125,17.094015332031248,13.54878125,16.69801533203125C13.54878125,16.30201533203125,13.87278125,15.97801533203125,14.26878125,15.97801533203125L17.35878125,15.97801533203125C17.75478125,15.97801533203125,18.07878125,16.30201533203125,18.07878125,16.69801533203125C18.07878125,17.094015332031248,17.76078125,17.41801533203125,17.35878125,17.41801533203125ZM6.45047125,18.39601533203125L9.54047125,18.39601533203125C9.93647125,18.39601533203125,10.26047125,18.07201533203125,10.26047125,17.67601533203125C10.26047125,17.280015332031248,9.93647125,16.95601533203125,9.54047125,16.95601533203125L6.45047125,16.95601533203125C6.05447125,16.95601533203125,5.73047125,17.280015332031248,5.73047125,17.67601533203125C5.73047125,18.07201533203125,6.05447125,18.39601533203125,6.45047125,18.39601533203125Z'%20fill-rule='evenodd'%20fill='%233F4A59'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})),v(" 意向企业 ")])),_:1},8,["style"]),s.jurisdiction.isWodeshenpi?(e(),i(b,{key:0,onClick:r[1]||(r[1]=t=>c.changtab(2)),class:"singleTabs",style:f({color:2==s.pitch?"#3370FF":"#86909C"})},{default:a((()=>[2==s.pitch?(e(),i(y,{key:0,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_597_34160'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_597_34160)'%3e%3cg%3e%3cpath%20d='M20.814421875,22.535629630088806L3.2753908750000003,22.535629630088806C2.873370875,22.535629630088806,2.544921875,22.198229630088807,2.544921875,21.785629630088806C2.544921875,21.373129630088805,2.873370875,21.035729630088806,3.2753908750000003,21.035729630088806L20.814421875,21.035729630088806C21.216321875,21.035729630088806,21.544921875,21.373129630088805,21.544921875,21.785629630088806C21.544921875,22.198229630088807,21.216321875,22.535629630088806,20.814421875,22.535629630088806ZM20.814421875,19.536029630088805L3.2753908750000003,19.536029630088805C2.873370875,19.536029630088805,2.544921875,19.198529630088807,2.544921875,18.786029630088805L2.544921875,15.036429630088806C2.544921875,13.799129630088807,3.532421875,12.786729630088805,4.738281875,12.786729630088805L9.414061875,12.786729630088805C9.267781875,11.886629630088807,8.830121875,11.024339630088805,8.208981875,10.274369630088806C7.368721875,9.299339630088806,6.929691875,8.062049630088806,6.929691875,6.787189630088806C6.929691875,5.362219630088806,7.477041875,4.012409630088806,8.500001874999999,2.999999630088806C9.522961875,1.9875926300888063,10.838691875,1.5000664300888062,12.263671875,1.5376467700888061C14.894521875,1.650182630088806,17.123721875,3.974819630088806,17.160121875,6.674659630088806C17.196821874999998,8.024469630088806,16.685521875,9.336909630088806,15.808621875,10.349319630088806C15.187421875,11.024329630088806,14.821821875,11.849229630088805,14.675821875,12.786729630088805L19.351521875,12.786729630088805C20.557221875,12.786729630088805,21.544921875,13.799129630088807,21.544921875,15.036429630088806L21.544921875,18.786029630088805C21.544921875,19.198529630088807,21.216221875,19.536029630088805,20.814421875,19.536029630088805ZM4.005861875,18.036029630088805L20.084021875,18.036029630088805L20.084021875,15.036429630088806C20.084021875,14.624029630088806,19.753421875,14.286429630088806,19.351521875,14.286429630088806L13.871121875,14.286429630088806C13.469121875,14.286429630088806,13.140621875,13.949029630088805,13.140621875,13.536429630088806C13.140621875,11.961829630088806,13.689921875,10.461659630088807,14.712921875,9.336919630088806C15.370621875,8.624529630088805,15.699221875,7.687079630088806,15.699221875,6.712039630088806C15.662621875,4.762169630088806,14.091421875,3.112329630088806,12.191401875,3.037369630088806C11.168241875,2.999789630088806,10.217821875,3.374769630088806,9.523441875,4.087359630088806C8.792641875000001,4.799739630088807,8.390621875,5.774589630088806,8.390621875,6.787199630088806C8.390621875,7.687079630088806,8.719971874999999,8.586939630088807,9.304691875,9.261949630088807C10.364271875,10.499229630088806,10.949221875,11.999129630088806,10.949221875,13.536429630088806C10.949221875,13.949029630088805,10.618621875,14.286429630088806,10.216801875,14.286429630088806L4.736331874999999,14.286429630088806C4.334311875,14.286429630088806,4.005861875,14.623829630088807,4.005861875,15.036429630088806L4.005861875,18.036029630088805Z'%20fill='%233370FF'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})):(e(),i(y,{key:1,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_597_34160'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_597_34160)'%3e%3cg%3e%3cpath%20d='M20.814421875,22.535629630088806L3.2753908750000003,22.535629630088806C2.873370875,22.535629630088806,2.544921875,22.198229630088807,2.544921875,21.785629630088806C2.544921875,21.373129630088805,2.873370875,21.035729630088806,3.2753908750000003,21.035729630088806L20.814421875,21.035729630088806C21.216321875,21.035729630088806,21.544921875,21.373129630088805,21.544921875,21.785629630088806C21.544921875,22.198229630088807,21.216321875,22.535629630088806,20.814421875,22.535629630088806ZM20.814421875,19.536029630088805L3.2753908750000003,19.536029630088805C2.873370875,19.536029630088805,2.544921875,19.198529630088807,2.544921875,18.786029630088805L2.544921875,15.036429630088806C2.544921875,13.799129630088807,3.532421875,12.786729630088805,4.738281875,12.786729630088805L9.414061875,12.786729630088805C9.267781875,11.886629630088807,8.830121875,11.024339630088805,8.208981875,10.274369630088806C7.368721875,9.299339630088806,6.929691875,8.062049630088806,6.929691875,6.787189630088806C6.929691875,5.362219630088806,7.477041875,4.012409630088806,8.500001874999999,2.999999630088806C9.522961875,1.9875926300888063,10.838691875,1.5000664300888062,12.263671875,1.5376467700888061C14.894521875,1.650182630088806,17.123721875,3.974819630088806,17.160121875,6.674659630088806C17.196821874999998,8.024469630088806,16.685521875,9.336909630088806,15.808621875,10.349319630088806C15.187421875,11.024329630088806,14.821821875,11.849229630088805,14.675821875,12.786729630088805L19.351521875,12.786729630088805C20.557221875,12.786729630088805,21.544921875,13.799129630088807,21.544921875,15.036429630088806L21.544921875,18.786029630088805C21.544921875,19.198529630088807,21.216221875,19.536029630088805,20.814421875,19.536029630088805ZM4.005861875,18.036029630088805L20.084021875,18.036029630088805L20.084021875,15.036429630088806C20.084021875,14.624029630088806,19.753421875,14.286429630088806,19.351521875,14.286429630088806L13.871121875,14.286429630088806C13.469121875,14.286429630088806,13.140621875,13.949029630088805,13.140621875,13.536429630088806C13.140621875,11.961829630088806,13.689921875,10.461659630088807,14.712921875,9.336919630088806C15.370621875,8.624529630088805,15.699221875,7.687079630088806,15.699221875,6.712039630088806C15.662621875,4.762169630088806,14.091421875,3.112329630088806,12.191401875,3.037369630088806C11.168241875,2.999789630088806,10.217821875,3.374769630088806,9.523441875,4.087359630088806C8.792641875000001,4.799739630088807,8.390621875,5.774589630088806,8.390621875,6.787199630088806C8.390621875,7.687079630088806,8.719971874999999,8.586939630088807,9.304691875,9.261949630088807C10.364271875,10.499229630088806,10.949221875,11.999129630088806,10.949221875,13.536429630088806C10.949221875,13.949029630088805,10.618621875,14.286429630088806,10.216801875,14.286429630088806L4.736331874999999,14.286429630088806C4.334311875,14.286429630088806,4.005861875,14.623829630088807,4.005861875,15.036429630088806L4.005861875,18.036029630088805Z'%20fill='%233F4A59'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})),v(" 我的审批 ")])),_:1},8,["style"])):h("",!0),o(b,{onClick:r[2]||(r[2]=t=>c.changtab(3)),class:"singleTabs",style:f({color:3==s.pitch?"#3370FF":"#86909C"})},{default:a((()=>[3==s.pitch?(e(),i(y,{key:0,class:"my",src:"https://staticstg.idicc.cn/static/wechatai/assets/wdsc1-CDETSkHt.svg"})):(e(),i(y,{key:1,class:"my",src:"https://staticstg.idicc.cn/static/wechatai/assets/wdsc0-xCClYxjY.svg"})),v(" 我的收藏 ")])),_:1},8,["style"]),s.jurisdiction.isKanban?(e(),i(b,{key:1,onClick:r[3]||(r[3]=t=>c.changtab(4)),class:"singleTabs",style:f({color:4==s.pitch?"#3370FF":"#86909C"})},{default:a((()=>[4==s.pitch?(e(),i(y,{key:0,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_597_34175'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_597_34175)'%3e%3cg%3e%3cpath%20d='M4.5,18.6667L19.5,18.6667C20.8807,18.6667,22,17.5474,22,16.1667L22,4.5C22,3.11929,20.8807,2,19.5,2L4.5,2C3.11929,2,2,3.11929,2,4.5L2,16.1667C2,17.5474,3.11929,18.6667,4.5,18.6667ZM3.66667,4.5C3.66667,4.039759999999999,4.039759999999999,3.66667,4.5,3.66667L19.5,3.66667C19.9602,3.66667,20.3333,4.039759999999999,20.3333,4.5L20.3333,16.1667C20.3333,16.6269,19.9602,17,19.5,17L4.5,17C4.039759999999999,17,3.66667,16.6269,3.66667,16.1667L3.66667,4.5ZM11.16615,13.6667C11.16615,14.1269,11.53924,14.5,11.99948,14.5C12.4597,14.5,12.8328,14.1269,12.8328,13.6667L12.8328,6.16668C12.8328,5.706440000000001,12.4597,5.33334,11.99948,5.33334C11.53924,5.33334,11.16615,5.706440000000001,11.16615,6.16668L11.16615,13.6667ZM14.8828,13.6667C14.8828,14.1269,15.2559,14.5,15.7161,14.5C16.1764,14.5,16.549500000000002,14.1269,16.549500000000002,13.6667L16.549500000000002,7.83334C16.549,7.37342,16.176099999999998,7.00082,15.7161,7.00082C15.2562,7.00082,14.8833,7.37342,14.8828,7.83334L14.8828,13.6667ZM7.38281,13.6667C7.38281,14.1269,7.75591,14.5,8.216149999999999,14.5C8.67638,14.5,9.049479999999999,14.1269,9.049479999999999,13.6667L9.049479999999999,9.50001C9.049479999999999,9.03977,8.67638,8.66668,8.216149999999999,8.66668C7.75591,8.66668,7.38281,9.03977,7.38281,9.50001L7.38281,13.6667ZM5.33333,22L18.6667,22C19.1269,22,19.5,21.6269,19.5,21.1667C19.5,20.7064,19.1269,20.3333,18.6667,20.3333L5.33333,20.3333C4.8731,20.3333,4.5,20.7064,4.5,21.1667C4.5,21.6269,4.8731,22,5.33333,22Z'%20fill-rule='evenodd'%20fill='%233370FF'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})):(e(),i(y,{key:1,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_597_34175'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_597_34175)'%3e%3cg%3e%3cpath%20d='M4.5,18.6667L19.5,18.6667C20.8807,18.6667,22,17.5474,22,16.1667L22,4.5C22,3.11929,20.8807,2,19.5,2L4.5,2C3.11929,2,2,3.11929,2,4.5L2,16.1667C2,17.5474,3.11929,18.6667,4.5,18.6667ZM3.66667,4.5C3.66667,4.039759999999999,4.039759999999999,3.66667,4.5,3.66667L19.5,3.66667C19.9602,3.66667,20.3333,4.039759999999999,20.3333,4.5L20.3333,16.1667C20.3333,16.6269,19.9602,17,19.5,17L4.5,17C4.039759999999999,17,3.66667,16.6269,3.66667,16.1667L3.66667,4.5ZM11.16615,13.6667C11.16615,14.1269,11.53924,14.5,11.99948,14.5C12.4597,14.5,12.8328,14.1269,12.8328,13.6667L12.8328,6.16668C12.8328,5.706440000000001,12.4597,5.33334,11.99948,5.33334C11.53924,5.33334,11.16615,5.706440000000001,11.16615,6.16668L11.16615,13.6667ZM14.8828,13.6667C14.8828,14.1269,15.2559,14.5,15.7161,14.5C16.1764,14.5,16.549500000000002,14.1269,16.549500000000002,13.6667L16.549500000000002,7.83334C16.549,7.37342,16.176099999999998,7.00082,15.7161,7.00082C15.2562,7.00082,14.8833,7.37342,14.8828,7.83334L14.8828,13.6667ZM7.38281,13.6667C7.38281,14.1269,7.75591,14.5,8.216149999999999,14.5C8.67638,14.5,9.049479999999999,14.1269,9.049479999999999,13.6667L9.049479999999999,9.50001C9.049479999999999,9.03977,8.67638,8.66668,8.216149999999999,8.66668C7.75591,8.66668,7.38281,9.03977,7.38281,9.50001L7.38281,13.6667ZM5.33333,22L18.6667,22C19.1269,22,19.5,21.6269,19.5,21.1667C19.5,20.7064,19.1269,20.3333,18.6667,20.3333L5.33333,20.3333C4.8731,20.3333,4.5,20.7064,4.5,21.1667C4.5,21.6269,4.8731,22,5.33333,22Z'%20fill-rule='evenodd'%20fill='%233F4A59'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})),v(" 数据看板 ")])),_:1},8,["style"])):h("",!0)])),_:1})):h("",!0)])),_:1},8,["style"])}],["__scopeId","data-v-7a1e2bca"]]);export{Re as default};
