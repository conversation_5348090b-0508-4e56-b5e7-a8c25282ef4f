import{l as e,p as t,U as a,z as o,A as i,a as l,f as n,w as s,F as r,i as u,o as d,B as m,t as f,c as p,e as c,d as h}from"./index-CBCsGYoT.js";import{_ as b}from"./page-meta.BCoSkkXs.js";import{r as I}from"./uni-app.es.CZb2JZWI.js";import{_ as y}from"./uni-easyinput.D_LnJWIZ.js";import{_ as v,a as g}from"./uni-forms.BsccYHPu.js";import{_ as C}from"./uni-icons.Dr3tmUrM.js";import{_ as k}from"./uni-popup.BLXBf1r-.js";import{t as _}from"./index.D5F8338M.js";import{m as V}from"./mapList.8RZSfjp3.js";import{m as j}from"./mapList2.BCv64X7E.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.Ckb0qY8x.js";import"./returnFn.BYkANsDr.js";const N=x({components:{tabBar:_,mapList:V,mapList2:j},data:()=>({baseInfo:{userIdentity:5,focusIndustryIds:[],focusDirection:[],gender:"",realName:"",code:"",email:"",company:"",cityCode:"",cityName:"",advantageCity:"",resume:"",referrer:"",InvitationCode:""},regionName:"",sex:[{text:"男",value:1},{text:"女",value:0}],identityList:[{value:1,text:"政府机构工作人员"},{value:2,text:"企业工作人员"},{value:5,text:"产业顾问"}],industryList:[],directionList:[{value:1,text:"产业研究"},{value:2,text:"园区管理"},{value:3,text:"企业招商"},{value:4,text:"政府决策"}],DivisionList:[],title:"信息完善",showMap:!1,rules:{},phone:"",refresh:!1,gotoIdentity:5}),onLoad(e){"yes"==(null==e?void 0:e.refresh)&&(this.refresh=!0),e.identity&&(this.gotoIdentity=e.identity),this.echoInfo()},created(){this.token=e("token"),this.userId=e("userId"),this.phone=e("phone"),this.showMap=!0},methods:{echoInfo(){this.$api.userMessage().then((e=>{var t,a,o,i;this.baseInfo.realName=e.result.realName,5==this.gotoIdentity&&(this.baseInfo.gender=Number(null==(t=e.result.userCompletionInfoDO)?void 0:t.gender),this.baseInfo.email=null==(a=e.result.userCompletionInfoDO)?void 0:a.email,this.baseInfo.company=null==(o=e.result.userCompletionInfoDO)?void 0:o.company,this.baseInfo.jobTitle=null==(i=e.result.userCompletionInfoDO)?void 0:i.jobTitle)}))},affirm(e){var t,a,o;this.$refs.popup.close(),this.baseInfo.region=[],this.regionName=(null==(t=e.province)?void 0:t.name)+((null==(a=e.citys)?void 0:a.name)?"/"+e.citys.name:"")+((null==(o=e.area)?void 0:o.name)?"/"+e.area.name:"");let i=Object.keys(e).map((t=>e[t]&&e[t].code&&""!==e[t].code?e[t].code:null));this.baseInfo.region=i.filter((e=>null!==e))},affirm2(e){var t,a,o;this.$refs.popup2.close(),this.baseInfo.cityCode=[],this.baseInfo.cityName=(null==(t=e.province)?void 0:t.name)+((null==(a=e.citys)?void 0:a.name)?"/"+e.citys.name:"")+((null==(o=e.area)?void 0:o.name)?"/"+e.area.name:"");let i=Object.keys(e).map((t=>e[t]&&e[t].code&&""!==e[t].code?e[t].code:null));this.baseInfo.cityCode=i.filter((e=>null!==e))},pop(){this.$refs.popup.open("bottom")},pop2(){this.$refs.popup2.open("bottom")},presentFn(e){this.$api.updateHeadImg({data:e,method:"POST"}).then((e=>{"SUCCESS"==e.code&&(5==this.gotoIdentity&&this.baseInfo.InvitationCode?this.$api.workerapplyAPI({data:{invitationCode:this.baseInfo.InvitationCode},method:"post",error:"no"}).then((e=>{t({title:"切换身份成功，加入团队成功",icon:"none"}),setTimeout((()=>{this.refresh?a({delta:2}):a({delta:1})}),1e3)})).catch((e=>{t({title:"切换身份成功,"+(null==e?void 0:e.msg),icon:"none"}),setTimeout((()=>{this.refresh?a({delta:2}):a({delta:1})}),1e3)})):(t({title:"切换身份成功",icon:"none"}),setTimeout((()=>{this.refresh?a({delta:2}):a({delta:1})}),1e3)))}))},submit(e){this.$refs[e].validate().then((e=>{if(!this.baseInfo.realName)return t({title:"请输入姓名",icon:"none"});if(!this.baseInfo.company)return t({title:"请输入所在单位",icon:"none"});if(!this.baseInfo.jobTitle)return t({title:"请输入所在单位职务",icon:"none"});if(0==this.baseInfo.cityCode.length)return t({title:"请输入所在城市",icon:"none"});if(!this.baseInfo.advantageCity)return t({title:"请输入优势资源城市",icon:"none"});if(!this.baseInfo.resume)return t({title:"请输入优势资源简述",icon:"none"});let a={userId:this.userId,userIdentity:5,realName:this.baseInfo.realName,gender:this.baseInfo.gender,email:this.baseInfo.email,jobTitle:this.baseInfo.jobTitle,company:this.baseInfo.company,cityCode:this.baseInfo.cityCode[this.baseInfo.cityCode.length-1],cityName:this.baseInfo.cityName,advantageCity:this.baseInfo.advantageCity,resume:this.baseInfo.resume,referrer:this.baseInfo.referrer};this.presentFn(a)}))}}},[["render",function(e,t,a,_,V,j){const x=I(o("page-meta"),b),N=i("tabBar"),T=I(o("uni-easyinput"),y),L=I(o("uni-forms-item"),v),F=I(o("uni-icons"),C),U=I(o("uni-forms"),g),w=u,$=i("mapList"),q=I(o("uni-popup"),k),A=i("mapList2");return d(),l(r,null,[n(x,{"page-style":"background-color: #FAFCFF"}),n(w,{class:"container"},{default:s((()=>[n(N,{title:V.title,peInfo:!0},null,8,["title"]),n(w,{class:"example"},{default:s((()=>[5==V.baseInfo.userIdentity?(d(),l("div",{key:0,class:"ConsultantPerfection"},[m("span",{class:"TitleName"},"更多信息"),m("div",{class:"perfectBox"},[n(U,{ref:"valiForm",rules:V.rules,modelValue:V.baseInfo,"label-align":"left","label-width":"100"},{default:s((()=>[m("div",{class:"customizationInput"},[n(L,{label:"您的姓名",required:""},{default:s((()=>[n(T,{type:"nickname",trim:"all",clearable:!1,modelValue:V.baseInfo.realName,"onUpdate:modelValue":t[0]||(t[0]=e=>V.baseInfo.realName=e),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),n(L,{label:"联系电话",required:""},{default:s((()=>[m("span",{class:"phone"},f(V.phone),1)])),_:1}),n(L,{label:"所在单位",required:""},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:V.baseInfo.company,"onUpdate:modelValue":t[1]||(t[1]=e=>V.baseInfo.company=e),placeholder:"请输入所在单位"},null,8,["modelValue"])])),_:1}),n(L,{label:"所在单位职务",required:""},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:V.baseInfo.jobTitle,"onUpdate:modelValue":t[2]||(t[2]=e=>V.baseInfo.jobTitle=e),placeholder:"请输入所在单位职务"},null,8,["modelValue"])])),_:1}),5==V.gotoIdentity?(d(),p(L,{key:0,label:"所在城市",required:""},{default:s((()=>{var e,a;return[m("div",{onClick:t[3]||(t[3]=e=>j.pop2()),class:"citySelect"},[m("span",{class:c(""==(null==(e=V.baseInfo)?void 0:e.cityName)?"":"citySelectText")},f(""==(null==(a=V.baseInfo)?void 0:a.cityName)?"请选择所在城市":this.baseInfo.cityName),3),n(F,{color:"#999",type:"down",size:"14"})])]})),_:1})):h("",!0),5==V.gotoIdentity?(d(),p(L,{key:1,label:"优势资源城市",required:""},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:V.baseInfo.advantageCity,"onUpdate:modelValue":t[4]||(t[4]=e=>V.baseInfo.advantageCity=e),placeholder:"请输入优势资源城市"},null,8,["modelValue"])])),_:1})):h("",!0)]),m("div",{class:"resume"},[5==V.gotoIdentity?(d(),p(L,{key:0,label:"优势资源简述",required:""},{default:s((()=>[n(T,{type:"textarea",trim:"all",clearable:!1,modelValue:V.baseInfo.resume,"onUpdate:modelValue":t[5]||(t[5]=e=>V.baseInfo.resume=e),placeholder:"请输入优势资源，包括但不限于 行业、地域等资源"},null,8,["modelValue"])])),_:1})):h("",!0)]),m("div",{class:"customizationInput"},[n(L,{label:"您的邮箱"},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:V.baseInfo.email,"onUpdate:modelValue":t[6]||(t[6]=e=>V.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})]),5==V.gotoIdentity?(d(),l("div",{key:0,class:"customizationInput"},[n(L,{label:"加入团队"},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:V.baseInfo.InvitationCode,"onUpdate:modelValue":t[7]||(t[7]=e=>V.baseInfo.InvitationCode=e),placeholder:"请输入团队邀请码"},null,8,["modelValue"])])),_:1})])):h("",!0)])),_:1},8,["rules","modelValue"])])])):h("",!0),m("div",{class:"experience",type:"primary",onClick:t[8]||(t[8]=e=>j.submit("valiForm"))}," 确定 ")])),_:1}),n(q,{ref:"popup","background-color":"#fff"},{default:s((()=>[V.showMap?(d(),p($,{key:0,token:e.token,onAffirm:j.affirm},null,8,["token","onAffirm"])):h("",!0)])),_:1},512),n(q,{ref:"popup2","background-color":"#fff"},{default:s((()=>[V.showMap?(d(),p(A,{key:0,token:e.token,onAffirm:j.affirm2},null,8,["token","onAffirm"])):h("",!0)])),_:1},512)])),_:1})],64)}],["__scopeId","data-v-322fac2a"]]);export{N as default};
