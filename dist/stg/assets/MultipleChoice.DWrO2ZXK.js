import{p as t,z as e,o as i,c as s,w as l,f as a,r as o,D as n,n as r,a as c,g as d,t as h,F as u,d as f,b as p,B as y,e as m,i as g,h as v,C as b,S as C,ai as _,aj as k}from"./index-HcMwrp5e.js";import{_ as x}from"./uni-transition.CHOJlBbg.js";import{r as S}from"./uni-app.es.DFp0WTX7.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";const L=I({name:"FyTreeSelect",props:{noLimit:{type:Boolean,default:!1},initialValue:{type:Array,default:()=>[]},value:{type:Array,default:()=>[]},columns:{type:Number,default:2},closeOnClickMask:{type:Boolean,default:!0},showBar:{type:<PERSON>olean,default:!0},customBarStyle:{type:Object,default:()=>({})},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确定"},cancelColor:{type:String,default:"#666666"},confirmColor:{type:String,default:"#3370FF"},options:{type:Array,default:()=>[]},height:{type:Number,default:400},itemHeight:{type:Number,default:43},duration:{type:Number,default:400},children:{type:String,default:"children"},label:{type:String,default:"label"},primary:{type:String,default:"id"},optionsKey:{type:String,default:"value"},selectTile:{type:String,default:"请选择"},customChild:{type:Boolean,default:!1},fontSize:{type:Number,default:30},color:{type:String,default:"rgb(158, 160, 162)"},activeColor:{type:String,default:"#3370FF"}},data:()=>({maskClass:{position:"fixed",bottom:0,top:0,left:0,right:0,backgroundColor:"rgba(0, 0, 0, 0.6)","z-index":"999999"},transClass:{position:"fixed",left:0,right:0,bottom:0,"z-index":"999999"},pitchKnowId:"",vipList:[],oneEcho:"",visible:!1,treeList:[],treePosition:[],currentSelectedId:[],initialData:[],pitchData:{}}),watch:{visible:{immediate:!0,handler:function(t){t&&(this.currentSelectedId=this.initialValue,this.initialData=JSON.parse(JSON.stringify(this.initialValue))),t&&0===this.treeList.length&&this.updateData(this.options)}}},created(){this.getViP(),this.init()},methods:{changePitchKnowId(t){this.pitchKnowId=t.knowId,this.pitchData=t},getViP(){this.$api.getVipList().then((t=>{this.vipList=Object.values(t.result.investStatus),console.log(this.vipList,"this.vipList ")}))},echo(t,e){this.treeList=[t,e]},init(){try{const t=this.columns;let e=0;for(;e<t;)e===t-1?this.$set(this.treePosition,e,{index:-1}):this.$set(this.treePosition,e,{index:0}),e+=1}catch(t){console.log(t)}},updateData(t){try{const e=[];let i=this.toJSON(t);if(0===i.length)return this.treeList=[];for(let t=0;t<this.columns;t++)0===t?e[0]=i:this.treePosition[t-1]&&(e[t]=this.getColumnData({data:e[t-1],index:this.treePosition[t-1].index}));this.treeList=this.toJSON(e),Array.isArray(this.value)&&this.value.length>0&&this.setDefaultData()}catch(e){console.log(e)}},setDefaultData(){try{const{treeList:t,value:e,columns:i,optionsKey:s}=this;this.oneEcho=e[0];for(let l=0;l<i;l++){const i=t[l];if(!i||!Array.isArray(i)||0===i.length)break;const a=i.length;if(0!==e[l]&&!e[l])break;for(let t=0;t<a;t++){if(i[t][s]==e[l]){this.$set(this.treePosition,l,{index:t});break}}}}catch(t){console.log(t)}},onChange(e,i,s){var l,a,o,n,r,c,d,h,u,f;if("-1"==s.categoryBuyStatus&&this.noLimit)t({title:"敬请期待",icon:"none",duration:2e3});else try{const p=this.treeList[e],y=p[this.treePosition[e].index];this.treeList.length;this.setPosAttr(e,"index",i);const m=p[i],g={current:this.toJSON(m),prev:this.toJSON(y),column:e,index:i},v=this.getColumnSelectData()||{};if(this.$emit("change",Object.assign({},g,v)),this.currentSelectedId[0]=null==(a=null==(l=v.selected)?void 0:l[0])?void 0:a.value,this.oneEcho=s.categoryId,1===e)if("1"===(null==(n=null==(o=v.selected)?void 0:o[1])?void 0:n.categoryBuyStatus)||this.noLimit){if(this.noLimit&&"-1"===(null==(c=null==(r=v.selected)?void 0:r[1])?void 0:c.categoryBuyStatus))return void t({title:"敬请期待",icon:"none",duration:2e3});this.currentSelectedId[1]=1===e?null==(h=null==(d=v.selected)?void 0:d[1])?void 0:h.value:this.currentSelectedId[1]}else{if("0"!==(null==(f=null==(u=v.selected)?void 0:u[1])?void 0:f.categoryBuyStatus))return void t({title:"敬请期待",icon:"none",duration:2e3});t({title:"该产业链未开通会员，请在我的-会员中心中开通",icon:"none",duration:2e3})}if(!0===this.customChild)return;for(let t=e+1;t<this.columns;t++)this.resetColumn(t,m,v),this.$set(this.treeList,t,this.getColumnData({data:this.treeList[t-1],index:this.treePosition[t-1].index}))}catch(p){console.log(p)}},getColumnSelectData(){try{const t=this.treeList,e=t.length,i=this.treePosition,s=[];let l=0;for(;l<e;){if(i[l].index>-1){const e=t[l][i[l].index];s.push(e)}else s.push(null);l+=1}return{selected:JSON.parse(JSON.stringify(s))}}catch(t){console.log(t)}},getColumnData({data:t,index:e}){try{return t[e]&&t[e][this.children]||[]}catch(i){console.log(i)}},resetColumn(t,e,i){this.$set(this.treePosition,t,{index:t==this.columns-1?-1:0})},toJSON:t=>"object"==typeof t?JSON.parse(JSON.stringify(t)):{},setPosAttr(t,e,i){try{this.$set(this.treePosition[t],e,i)}catch(s){console.log(s)}},handlerConfirm(){this.pitchKnowId&&(this.$emit("confirm",this.pitchData,!0),this.handlerCloseModal());try{const t=this.treeList,e=this.columns,i=this.treePosition,s=[];let l=0,a=[],o=[];for(;l<e;){const e=i[l],n=t[l][e.index]||{};a.push(n[this.optionsKey]),o.push(n[this.primary]),s.push(n||null),l+=1}this.$emit("confirm",{primary:o,values:a,selected:this.toJSON(s)}),this.$emit("input",a),this.handlerCloseModal()}catch(t){console.log(t)}},handlerMaskClick(){this.closeOnClickMask&&this.handlerCloseModal()},handlerCloseModal(){this.visible=!1,this.currentSelectedId=this.initialData,this.$emit("close",{visible:!1})},handlerOpenModal(){this.visible=!0,this.$emit("open",{visible:!0})}}},[["render",function(t,_,k,I,L,w){const $=g,B=S(e("uni-transition"),x),N=v,O=b,P=C;return i(),s($,{class:"fy-tree-select",onTouchmove:n((()=>{}),["stop","prevent"])},{default:l((()=>[a($,{onClick:n(w.handlerOpenModal,["stop","prevent"])},{default:l((()=>[o(t.$slots,"default",{},void 0,!0)])),_:3},8,["onClick"]),a(B,{"mode-class":["fade"],styles:L.maskClass,duration:k.duration,show:L.visible,onClick:w.handlerMaskClick},null,8,["styles","duration","show","onClick"]),a(B,{"mode-class":["slide-bottom"],styles:L.transClass,duration:k.duration,show:L.visible},{default:l((()=>[a($,{class:"fy-tree-select_wrapper",onTouchmove:n((()=>{}),["stop","prevent"])},{default:l((()=>[k.showBar?(i(),s($,{key:0,class:"fy-tree-select_bar",style:r(k.customBarStyle)},{default:l((()=>[t.$slots.bar?o(t.$slots,"bar",{key:0},void 0,!0):(i(),c(u,{key:1},[a(N,{class:"fy-tree-select_bar__btn",style:r({color:k.cancelColor}),onClick:n(w.handlerCloseModal,["stop","prevent"])},{default:l((()=>[d(h(k.cancelText),1)])),_:1},8,["style","onClick"]),a(N,{class:"fy-tree-select_bar__title"},{default:l((()=>[d(h(k.selectTile),1)])),_:1}),a(N,{class:"fy-tree-select_bar__btn",style:r({color:k.confirmColor}),onClick:n(w.handlerConfirm,["stop","prevent"])},{default:l((()=>[d(h(k.confirmText),1)])),_:1},8,["style","onClick"])],64))])),_:3},8,["style"])):f("",!0),0!=L.vipList.length?(i(),c("div",{key:1,class:"headText"}," 会员开通情况 ")):f("",!0),0!=L.vipList.length?(i(),s($,{key:2,class:"vipListBox"},{default:l((()=>[(i(!0),c(u,null,p(L.vipList,((t,e)=>(i(),s($,{onClick:e=>w.changePitchKnowId(t),class:"vipListItem",key:e},{default:l((()=>[a(O,{src:L.pitchKnowId==t.knowId?"https://static.idicc.cn/cdn/aiChat/applet/newHome/chainIdsBox.png":"https://static.idicc.cn/cdn/aiChat/applet/newHome/chainIdBox.png"},null,8,["src"]),y("span",{class:m(L.pitchKnowId==t.knowId?"knowledgeNames":"knowledgeName")},h(t.knowledgeName),3)])),_:2},1032,["onClick"])))),128))])),_:1})):f("",!0),a($,{class:"fy-tree-select_container",style:r({height:`${k.height}px`})},{default:l((()=>[(i(!0),c(u,null,p(L.treeList,((t,e)=>(i(),s($,{key:e,class:m("treeItems"+e)},{default:l((()=>[a(P,{"scroll-y":!0,ref_for:!0,ref:`fy-tree-select_column_${e}`,class:m(["fy-tree-select_column",`fy-tree-select_column_${e}`]),style:r({height:`${k.height}px`})},{default:l((()=>[(i(!0),c(u,null,p(t,((t,o)=>(i(),c(u,{key:o},[L.treePosition[e]?(i(),s($,{class:m(["fy-tree-select_item",t.categoryId==L.oneEcho&&0===e?"actived":""]),key:o,onClick:n((i=>w.onChange(e,o,t)),["stop","prevent"])},{default:l((()=>[a(N,{style:r({"line-height":`${k.itemHeight}px`,"font-size":`${k.fontSize}rpx`,color:L.currentSelectedId[e]===t.value?k.activeColor:"-1"===t.categoryBuyStatus?k.color:"black"}),class:"fy-tree-select_item_text"},{default:l((()=>[d(h(t[k.label]),1)])),_:2},1032,["style"]),(null==t?void 0:t.buyVersion)&&!k.noLimit?(i(),s(N,{key:0,class:"buyVersion"},{default:l((()=>[d(h(null==t?void 0:t.buyVersion),1)])),_:2},1024)):f("",!0),"臻享版"==(null==t?void 0:t.buyVersion)&&k.noLimit?(i(),s(N,{key:1,class:"buyVersion"},{default:l((()=>[d(" 产发臻享版")])),_:1})):f("",!0)])),_:2},1032,["class","onClick"])):f("",!0)],64)))),128)),a($,{class:"fy-tree-select_item",style:r({height:`${k.itemHeight}px`,"border-top-width":"0px"})},null,8,["style"])])),_:2},1032,["class","style"])])),_:2},1032,["class"])))),128))])),_:1},8,["style"])])),_:3})])),_:3},8,["styles","duration","show"])])),_:3})}],["__scopeId","data-v-4a8e2303"]]);const w=I({props:{list:{type:Array,default:()=>[]},single:{type:Boolean,default:!1},BottomBlank:{type:Boolean,default:!1},introductionSelectIndex:{type:Number,default:1},appletTab:{type:Boolean,default:!1}},data:()=>({value:[0],checkNames:[],selectIndex:0,indicatorStyle:"height: 50px;",visible:!0}),created(){this.selectIndex=this.introductionSelectIndex,this.value[0]=this.introductionSelectIndex},methods:{init(t){this.selectIndex=t,this.value[0]=t},clear(){this.selectIndex,this.visible=!1,setTimeout((()=>{this.visible=!0}),10)},bindChange(t){this.selectIndex=t.detail.value[0]},affirm(){0==this.selectIndex?this.$emit("affirm",""):this.$emit("affirm",this.list[this.selectIndex-1])}}},[["render",function(t,e,o,n,r,m){const v=g,b=_,C=k;return i(),s(v,{class:"region"},{default:l((()=>[a(v,{class:"head"},{default:l((()=>[y("span",{class:"txt"},"请选择"),a(v,{class:"affirm2",onClick:m.affirm},{default:l((()=>[d("确认")])),_:1},8,["onClick"])])),_:1}),r.visible?(i(),s(C,{key:0,"indicator-style":r.indicatorStyle,value:r.value,onChange:m.bindChange,class:"picker-view","immediate-change":!0},{default:l((()=>[a(b,null,{default:l((()=>[a(v,{class:"item"},{default:l((()=>[d("不限")])),_:1}),(i(!0),c(u,null,p(o.list,((t,e)=>(i(),s(v,{class:"item",key:e},{default:l((()=>[d(h(t),1)])),_:2},1024)))),128))])),_:1})])),_:1},8,["indicator-style","value","onChange"])):f("",!0),o.BottomBlank?(i(),c("div",{key:1,style:{height:"160rpx"}})):f("",!0)])),_:1})}],["__scopeId","data-v-b6953c01"]]);export{w as M,L as t};
