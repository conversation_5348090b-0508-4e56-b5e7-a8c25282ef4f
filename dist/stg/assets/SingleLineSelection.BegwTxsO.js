import{p as s,o as e,c as a,w as t,f as c,B as l,a as i,b as n,e as o,g as A,t as m,d as h,F as r,i as u}from"./index-CBCsGYoT.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAo5JREFUSEu9lktIVGEUx3/HRxQEtZSQtKdJDyh66giGLgoMdBEVutCcoHJTJElUNGK0ElpFgWMaKNSmFAoMXbhw7EFB9ECFoJeilbQwQ2nueOK7M2PONNdxpujs7j3n+//O9zrnE+axvFZdk+KnFNiHsAElAyENGEN5DfRaFu2PT8h7JxmJ5cjzapaAR5SKkOB8eRhfxwyc6nfLh+jAPwD5zVouM9xAWBpPNco/rUql75jcmfs/AuBq1ksongSFI8MFT1+11Id/zgLym/SQCLf/Sjw0WIUKX7W0m08bYNY8BQaBxf8CgDJpWWw2m28DXF69B/ZpScpWLoMl6TA0/nu4QqvPLVWy+7pmp6XzLillYFcmXCmGa0/g7sAcFcUKCLniatLzCJeTARRkgWcvPBuBuu4YCkqtAXQjFCcKOLgRanbCmy9wugt+BmICesTl1VEgIxFA1VY4ug1Gv0PNA/j6w2G0MmwAGu3evw62r4BGH0xZkd6TO+DIFpjyQ1UHjEzMk5pimSXyR5eDwmxoKILhCah9GBRJFbhYCEWrg8txrhuejsSft5nBJyAzOvSsCw7kBMUaeqEkJ3hijJmZdZpbE9/GHDd5USrcLIWs5ZEqt16A93l8ZTtC6TGAMwiNsYaYC9RSBgZmrP+jw3F04ikXZI9X16YqA05luWQ91BXA4DjU3Hc4jg4Ay88qu1Tke7VFoNIpEbPpLz/Dt6kFLk0wrKPPLWU2wC4XabxKogc4EactP7mzxc6ehWk0SltCOToEq3I43Hj+X8MJJxNqPK0J9wZlUlM4Hm40Yb2YTT9Uwq8uqEcolgptAT/1sV4XMQFhemjzy4FChE12UVQsxH62mLvcFRA6H7nlrdPe/QJVGebZn23NLgAAAABJRU5ErkJggg==";const d=g({props:{list:{type:Array,default:()=>[]},single:{type:Boolean,default:!1},BottomBlank:{type:Boolean,default:!1}},data:()=>({checkNames:[]}),methods:{clear(){this.checkNames=[]},affirm(){this.$emit("affirm",this.checkNames)},citysEvent(e){if(this.single)this.checkNames[0]=e;else{const a=this.checkNames.indexOf(e);if(a>-1)this.checkNames.splice(a,1);else{if(3==this.checkNames.length)return s({title:"最多只能选择三种选项",icon:"none"});this.checkNames.push(e)}}}}},[["render",function(s,g,d,f,p,E){const B=u;return e(),a(B,{class:"region"},{default:t((()=>[c(B,{class:"head"},{default:t((()=>[l("span",{class:"txt"},"请选择")])),_:1}),c(B,{class:"region-con"},{default:t((()=>[c(B,{class:"region-list citys"},{default:t((()=>[(e(!0),i(r,null,n(d.list,((s,l)=>(e(),a(B,{class:o(["item",p.checkNames.includes(s)?"on":""]),onClick:e=>E.citysEvent(s),key:l},{default:t((()=>[c(B,{class:"txt"},{default:t((()=>[A(m(s),1)])),_:2},1024),p.checkNames.includes(s)?(e(),i("img",{key:0,class:"icon04",src:k})):h("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),c(B,{class:"btnbox"},{default:t((()=>[c(B,{onClick:E.clear,class:"clear"},{default:t((()=>[A("重置")])),_:1},8,["onClick"]),c(B,{class:"affirm",onClick:E.affirm},{default:t((()=>[A("确认")])),_:1},8,["onClick"])])),_:1}),d.BottomBlank?(e(),i("div",{key:0,style:{height:"160rpx"}})):h("",!0)])),_:1})}],["__scopeId","data-v-7b91e415"]]);export{d as S,k as _};
