import{l as e,s,U as t,p as a,q as i,z as l,A as r,a as n,f as c,w as o,F as d,i as p,o as u,c as h,B as m,g,t as A,b as f,e as I,d as v,C}from"./index-CBCsGYoT.js";import{_ as S}from"./page-meta.BCoSkkXs.js";import{r as _}from"./uni-app.es.CZb2JZWI.js";import{t as w}from"./index.D5F8338M.js";import{i as k}from"./industryChain.DNIq-Ht6.js";import{g as y}from"./utils.61Hi-B7M.js";import{_ as N}from"./fuwuleixing.C6AoCp6M.js";import{_ as P}from"./ic_sea_location.iElRY131.js";import{_ as L}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";import"./uni-popup.BLXBf1r-.js";import"./uni-transition.Ckb0qY8x.js";const b=L({components:{tabBar:w,industryChain:k},data:()=>({id:"",token:"",show:!1,selectClassId:"",selectClassName:"",serviceList:[],classList:[],serviceListAll:[]}),onLoad(t){this.token=e("token"),this.id=t.id,y("token")&&(this.token=y("token"),s("token",this.token)),y("id")&&(this.id=y("id")),this.getDetail(this.id)},computed:{currentProductNames(){var e,s,t;return(null==(t=null==(s=null==(e=this.enterpriseInfo)?void 0:e.chainDTOS[this.selectedTagIndex])?void 0:s.chainNodeList[this.selectedTagIndex2])?void 0:t.productNames)||[]}},watch:{},methods:{goDetails(e){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"changePath",value:{type:e.itemId,name:e.itemName,servicer:e.marketId},path:"serviceInfo"}))},screenFn(){this.$refs.industryChain.opens()},affirm(e){this.selectClassId=e.chainId,this.selectClassName=e.chainName,console.log(this.selectClassId,"==="),this.$api.providerDetailAPI({data:{userId:this.id,catalogName:this.selectClassName,pageSize:1e3,pageNum:1},method:"POST"}).then((e=>{this.enterpriseInfo=e.result,this.show=!0,this.serviceList=e.result.serviceList}))},goBack(){t({delta:1})},getTotalStringLength(e){let s=0;return e.forEach((e=>{s+=e.length})),s},pitchsecond(e){this.selectedTagIndex2=e},async getDetail(e){let s={userId:e,pageSize:1e3,pageNum:1};this.$api.providerDetailAPI({data:s,method:"POST"}).then((s=>{this.enterpriseInfo=s.result,this.show=!0,this.serviceListAll=s.result.serviceList,this.$api.userCatalogListAPI({data:{userId:e},method:"POST"}).then((e=>{this.classList=e.result.map((e=>({chainName:e.catalogName,chainId:e.catalogId}))),0!=this.classList.length&&(this.selectClassId=this.classList[0].chainId,this.selectClassName=this.classList[0].chainName,this.serviceList=this.serviceListAll.filter((e=>e.catalogId==this.selectClassId)))}))}))},goPay(e){console.log(e),this.$api.overseasSubmitApi({data:{serviceMarketId:e.marketId},method:"POST"}).then((e=>{"SUCCESS"===e.code?this.payment(e.result):a({title:e.msg,icon:"none"})}))},payment(e){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"toWechatPay",value:{businessCode:"smartService",item:{smartServiceId:e}}}))},paySuccess(){i({url:"/goToSea/mineOrder"})}}},[["render",function(e,s,t,a,i,w){const k=_(l("page-meta"),S),y=p,L=C,b=r("industryChain");return u(),n(d,null,[c(k,{"page-style":"background-color: transparent"}),c(y,null,{default:o((()=>[i.show?(u(),h(y,{key:0,class:"enterpriceDetail"},{default:o((()=>[c(y,{class:"infoBox"},{default:o((()=>[m("img",{src:e.enterpriseInfo.userImageUrl?e.enterpriseInfo.userImageUrl:"https://static.idicc.cn/cdn/aiChat/0.png",alt:"",class:"iconImg"},null,8,["src"]),c(y,{class:"enterpriseName"},{default:o((()=>[g(A(e.enterpriseInfo.userName),1)])),_:1}),c(y,{class:"enterpriseDescription"},{default:o((()=>[g(A(e.enterpriseInfo.enterpriseProfile),1)])),_:1})])),_:1}),c(y,{class:"businessScopeBox"},{default:o((()=>[c(y,{class:"businessScopetitle"},{default:o((()=>[g("服务类目")])),_:1}),c(y,{class:"classSelect"},{default:o((()=>[m("span",{class:"optionItem pitchOn",onClick:s[0]||(s[0]=e=>w.screenFn())},[g(A(i.selectClassName),1),c(L,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAJFBMVEUAAAAzcP8xcP8zcP8wcP8zcP80cP8zcP8zcP8zcP80cP8zcP/pckwDAAAAC3RSTlMA3yAwEO+AcK+fQDAKS2oAAAA7SURBVAjXYyANqO4GAmEgoxrE2AZkKIIYQkAGF4hhwACR2wZWzbF79wQwg0V6owLEAMMgqEksDlgtAACHJBPzpSpLBwAAAABJRU5ErkJggg==",class:"Arrows"})])])),_:1}),(u(!0),n(d,null,f(i.serviceList,((e,s)=>(u(),h(y,{class:I(["card",s===i.serviceList.length-1?"noBorder":""]),key:s},{default:o((()=>[c(y,{class:"cardHeader"},{default:o((()=>[c(L,{src:N,class:"icon2"}),c(y,{class:"cardContent"},{default:o((()=>[c(y,{class:"title"},{default:o((()=>[g(A(e.itemName),1)])),_:2},1024),c(y,{class:"price"},{default:o((()=>[m("span",{class:"priceLogo"},"￥"),g(A(e.priceQuote),1)])),_:2},1024),c(y,{class:"content"},{default:o((()=>[g(A(e.introduction),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(y,{class:"cardFooter"},{default:o((()=>[c(y,{class:"location"},{default:o((()=>[e.province?(u(),h(L,{key:0,src:P,class:"locationIcon"})):v("",!0),m("span",null,A(e.province?e.province:" "),1)])),_:2},1024),c(y,{class:"btns"},{default:o((()=>[c(y,{class:"btn1",onClick:s=>w.goDetails(e)},{default:o((()=>[g("查看详情")])),_:2},1032,["onClick"]),c(y,{class:"btn2",onClick:s=>w.goPay(e)},{default:o((()=>[g("立即购买")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1}),c(y,{class:"placeholder"})])),_:1})):v("",!0),c(b,{list:i.classList,ref:"industryChain",BottomBlank:!0,appletTab:!0,onAffirm:w.affirm,CurrentlySelected:i.selectClassId},null,8,["list","onAffirm","CurrentlySelected"])])),_:1})],64)}],["__scopeId","data-v-e192375a"]]);export{b as default};
