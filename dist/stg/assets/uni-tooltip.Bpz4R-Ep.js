import{o as t,c as e,w as a,r as o,n,g as s,t as r,d as l,i as p}from"./index-HcMwrp5e.js";import{_ as i}from"./_plugin-vue_export-helper.BCo6x5W8.js";const c=i({name:"uni-tooltip",data:()=>({}),methods:{},computed:{initPlacement(){let t={};switch(this.placement){case"left":t={top:"50%",transform:"translateY(-50%)",right:"100%","margin-right":"10rpx"};break;case"right":t={top:"50%",transform:"translateY(-50%)",left:"100%","margin-left":"10rpx"};break;case"top":t={bottom:"100%",transform:"translateX(-50%)",left:"50%","margin-bottom":"10rpx"};break;case"bottom":t={top:"100%",transform:"translateX(-50%)",left:"50%","margin-top":"10rpx"}}return t}},props:{content:{type:String,default:""},placement:{type:String,default:"left"}}},[["render",function(i,c,m,f,u,d){const g=p;return t(),e(g,{class:"uni-tooltip"},{default:a((()=>[o(i.$slots,"default",{},void 0,!0),m.content||i.$slots.content?(t(),e(g,{key:0,class:"uni-tooltip-popup",style:n(d.initPlacement)},{default:a((()=>[o(i.$slots,"content",{},(()=>[s(r(m.content),1)]),!0)])),_:3},8,["style"])):l("",!0)])),_:3})}],["__scopeId","data-v-4a0c04ba"]]);export{c as _};
