import{l as t,q as e,z as s,A as a,a as i,f as d,B as n,F as o,b as r,o as l,t as m,g as p,d as c,D as u,C as h}from"./index-CBCsGYoT.js";import{_ as v}from"./page-meta.BCoSkkXs.js";import{r as D}from"./uni-app.es.CZb2JZWI.js";import{t as f}from"./index3.FbzqC-zk.js";import{d as g,A as k}from"./Attentrust.C6w8lxzZ.js";import{_ as j}from"./enterprise.BFmTa1IP.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";import"./uni-popup.BLXBf1r-.js";import"./uni-transition.Ckb0qY8x.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-forms.BsccYHPu.js";import"./uni-datetime-picker.DkX7SHFR.js";import"./uni-easyinput.D_LnJWIZ.js";import"./entrust.CU6YFENy.js";const I=y({components:{tabBar:f,delDemand:g,Attentrust:k},data:()=>({title:"哒达助招",identity:1,myentrustList:[],delDemandId:""}),onLoad(e){this.identity=t("identity"),this.delDemandId=e.id,this.getDel(this.delDemandId)},onShow(){this.getDel(this.delDemandId)},methods:{updataList(){this.getDel(this.delDemandId)},feedbackDel(t){e({url:`/components/feedbackDel?demandId=${t.demandId}&id=${t.id}`})},stateShow:t=>3==t?"已委托":"",getDel(t){let e={demandId:t,pageSize:1e3,pageNum:1};this.$api.recommendlistAPI({data:e,method:"get"}).then((t=>{this.myentrustList=t.result.records})).catch((t=>{}))},deleteFeedback(t){this.$refs.delDemand.opens(t)},todosthFn(t){this.$refs.Attentrust.opens(t)}}},[["render",function(t,e,f,g,k,y){const I=D(s("page-meta"),v),L=a("tabBar"),b=h,F=a("delDemand"),A=a("Attentrust");return l(),i(o,null,[d(I,{"page-style":"background-color: #FAFCFF"}),n("div",null,[d(L,{title:k.title},null,8,["title"]),n("div",{class:"box33"},[n("div",{class:"headText"}," 反馈企业列表 "),(l(!0),i(o,null,r(k.myentrustList,((t,e)=>(l(),i("div",{onClick:e=>y.feedbackDel(t),class:"myentrustListClass",key:t.entrustId},[n("div",{class:"t1"},[n("div",{class:"startTime"}," 反馈时间："+m(t.auditTime),1),n("div",{class:"myListstate"},m(y.stateShow(t.status)),1)]),n("div",{class:"t2"},[n("div",{class:"enterprise"},[d(b,{class:"enterpriseIcon",src:j}),p(" "+m(t.enterpriseName)+" ",1),t.chainNames?(l(),i("div",{key:0,class:"tagItem"},m(null==t?void 0:t.chainNames[0]),1)):c("",!0)])]),n("div",{class:"t3"},[n("div",{class:"line"},"企业概况："+m(t.enterpriseDetail),1)]),n("div",{class:"btnBox"},[n("div",{onClick:u((e=>y.deleteFeedback(t)),["stop"]),class:"T4BtnDe"}," 删除 ",8,["onClick"]),3!=t.status?(l(),i("div",{key:0,onClick:u((e=>y.todosthFn(t)),["stop"]),class:"T4Btn"}," 一键委托 ",8,["onClick"])):c("",!0)])],8,["onClick"])))),128)),n("div",{style:{height:"150rpx"}})]),d(F,{onUpdataList:y.updataList,ref:"delDemand"},null,8,["onUpdataList"]),d(A,{onUpdataList:y.updataList,ref:"Attentrust"},null,8,["onUpdataList"])])],64)}],["__scopeId","data-v-d0da0132"]]);export{I as default};
