import{l as s,s as a,a as t,B as e,t as n,F as i,b as l,o,e as d,g as c,n as r,d as p}from"./index-CBCsGYoT.js";import{t as u}from"./index.D5F8338M.js";import{g as m}from"./utils.61Hi-B7M.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const y=v({components:{tabBar:u},data:()=>({title:"哒达助招",dataInfo:{processList:[],reminds:[]},identity:1,state:"",tabIds:1,tabList:[{id:1,name:"进度信息"},{id:2,name:"催办信息"}]}),onLoad(t){this.identity=s("identity"),m("token")&&a("token",m("token")),m("id")&&this.getDel(m("id"))},methods:{previewImg(s,a){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"showAlert",value:{type:"ImageViewer",data:{images:s,imageIndex:a}},module:"Investment"}))},updataList(){this.getDel(this.dataInfo.id)},getDel(a){this.$api.paymentdetailAPI({method:"get",data:{entrustId:a}}).then((a=>{this.dataInfo=a.result;let t=s("userId");this.dataInfo.paymentApplyFlowList.push({gmtModify:this.dataInfo.applyDatetime,who:"发起人",userName:t==this.dataInfo.userId?"我":this.dataInfo.userName,instruction:this.dataInfo.reason}),this.state=a.result.state,this.dataInfo.processList||(this.dataInfo.processList=[])}))}}},[["render",function(s,a,u,m,v,y){return o(),t("div",null,[e("div",{class:"box33"},[e("div",{class:"headText"}," 委托信息 "),e("div",{class:"attDelBox"},[e("div",{class:"row"},[e("span",{class:"key"},"委托单申请时间："),e("span",{class:"value"},n(v.dataInfo.applyDatetime),1)]),e("div",{class:"row"},[e("span",{class:"key"},"委托人："),e("span",{class:"value"},n(v.dataInfo.userName),1)]),e("div",{class:"row"},[e("span",{class:"key"},"申请理由："),e("span",{class:"value"},n(v.dataInfo.reason),1)]),e("div",{class:"row"},[e("span",{class:"key"},"意向企业："),e("span",{class:"value"},n(v.dataInfo.enterprise),1)]),e("div",{class:"row"},[e("span",{class:"key"},"企业社会信用代码："),e("span",{class:"value"},n(v.dataInfo.enterpriseUniCode),1)]),e("div",{class:"row"},[e("span",{class:"key"},"期望对接时间："),e("span",{class:"value"},n(v.dataInfo.exceptedDatetime),1)]),e("div",{class:"row"},[e("span",{class:"key"},"招商对接人："),e("span",{class:"value"},n(v.dataInfo.contact),1)]),e("div",{class:"row"},[e("span",{class:"key"},"联系方式："),e("span",{class:"value"},n(v.dataInfo.contactPhone),1)]),e("div",{class:"row"},[e("span",{class:"key"},"招商要求："),e("span",{class:"value"},n(v.dataInfo.note),1)]),e("div",{class:"row"},[e("span",{class:"key"},"金额："),e("span",{class:"value"},n(1==v.identity?v.dataInfo.amount:v.dataInfo.receiveAmount),1)])]),e("div",{style:{"margin-bottom":"26rpx"},class:"headText"}," 流程 "),e("div",{class:"contentBox"},[(o(!0),t(i,null,l(v.dataInfo.paymentApplyFlowList,((s,a)=>(o(),t("div",{key:a,class:"contentItemBox"},[e("div",{class:"Time"},[e("div",{class:"remindTime"},[e("div",{class:d(2==s.status?"circle2":"circle1")},null,2),e("span",null,n(s.who?s.who:"审批人"),1)]),e("span",{class:"typeText"},n(s.gmtModify),1)]),e("div",{class:"userName"},[c(n(s.userName)+" ",1),s.userId&&a!=v.dataInfo.paymentApplyFlowList.length-1?(o(),t("span",{key:0,class:"status",style:r({color:2==s.status?"#E04848":"#3370FF"})},"("+n(0==s.status?"审批中":1==s.status?"审批通过":"审批驳回")+")",5)):p("",!0)]),s.instruction?(o(),t("div",{key:0,class:"instruction"},n(s.instruction),1)):p("",!0)])))),128))]),e("div",{style:{height:"150rpx"}})])])}],["__scopeId","data-v-6bc69abe"]]);export{y as default};
