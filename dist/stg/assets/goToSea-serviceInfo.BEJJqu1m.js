import{l as e,U as t,p as s,q as n,z as i,c as a,w as o,S as r,o as l,n as c,g as p,f as u,B as d,t as v,d as y,H as m,I as g,a as h,e as f,i as k,C as L}from"./index-HcMwrp5e.js";import{_ as w}from"./uni-popup.BdZPMDVN.js";import{r as S}from"./uni-app.es.DFp0WTX7.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.CHOJlBbg.js";const b=C({data:()=>({statusBarHeight:e("statusBarHeight"),key:"",name:"",content:{},servicer:null,enterpriseList:[]}),onLoad(e){this.key=e.type,this.name=e.name,this.servicer=e.servicer,this.getData()},methods:{getData(){this.$api.getTextContent({data:{itemId:this.key,marketId:this.servicer},method:"post"}).then((e=>{"SUCCESS"==e.code&&(this.content=e.result)})),this.$api.getOverseasServicerListAPI({data:{itemId:this.key,zoneCode:null,country:e("defaultName")},method:"POST"}).then((e=>{var t;this.enterpriseList=(null==(t=e.result)?void 0:t.records)||[]}))},goBack(){t({delta:1})},limit(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"saveImage",value:"https://pangu.idicc.cn/ai/upload/getFileStream?ossPath=source/serviceCode.jpg"}))},toKf(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"toWechatService"}))},open(){this.$refs.popup.open()},close(){this.$refs.popup.close()},goServiceList(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{smartServiceId:this.key},path:"serviceList"}))},goPay(){this.$api.overseasSubmitApi({data:{serviceMarketId:this.servicer},method:"POST"}).then((e=>{"SUCCESS"===e.code?this.payment(e.result):s({title:e.msg,icon:"none"})}))},payment(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"toWechatPay",value:{businessCode:"smartService",item:{smartServiceId:e}}}))},paySuccess(){n({url:"/goToSea/mineOrder"})}}},[["render",function(e,t,s,n,C,b){const _=k,x=L,M=S(i("uni-popup"),w),I=r;return l(),a(I,{style:{position:"absolute"},class:"mainView","scroll-y":"true"},{default:o((()=>[C.content.itemName?(l(),a(_,{key:0,class:"box",style:c("overflow: auto;;top: 10px; height: calc( 100vh - 10px ) ")},{default:o((()=>[C.content.priceQuote?(l(),a(_,{key:0,class:"fetchItem"},{default:o((()=>[p(" 服务价格 "),u(_,{class:"price"},{default:o((()=>[d("span",{class:"priceLogo"},"￥"),p(v(C.content.priceQuote),1)])),_:1})])),_:1})):y("",!0),m(u(_,{class:"fetchItem",style:c(C.content.priceQuote?"margin-top: 24rpx;":"margin-top: 0")},{default:o((()=>{var e,t;return[d("p",null,"基本信息"),C.content.infoDesc?(l(),h("p",{key:0,class:"font2",innerHTML:`${null==(e=C.content)?void 0:e.infoDesc}`.replace(/\n/g,"<br/>")},null,8,["innerHTML"])):y("",!0),C.content.fitDuration?(l(),h("p",{key:1,style:{"margin-top":"42rpx"}},"办理时间")):y("",!0),C.content.fitDuration?(l(),h("p",{key:2,class:"font2",innerHTML:null==(t=C.content)?void 0:t.fitDuration},null,8,["innerHTML"])):y("",!0),C.content.applyList?(l(),h("p",{key:3,style:{"margin-top":"42rpx"}},"所需材料")):y("",!0),C.content.applyList?(l(),h("p",{key:4,class:"font2",innerHTML:`${C.content.applyList}`.replace(/\n/g,"<br/>")},null,8,["innerHTML"])):y("",!0),C.content.deliveryList?(l(),h("p",{key:5,style:{"margin-top":"42rpx"}},"交付材料")):y("",!0),C.content.deliveryList?(l(),h("p",{key:6,class:"font2",innerHTML:`${C.content.deliveryList}`.replace(/\n/g,"<br/>")},null,8,["innerHTML"])):y("",!0)]})),_:1},8,["style"]),[[g,C.content]]),C.servicer?(l(),a(_,{key:1,class:"fetchItem",style:{"margin-top":"24rpx"}},{default:o((()=>{var e,t;return[d("p",null,"服务介绍"),(null==(e=C.content)?void 0:e.introduction)?(l(),h("p",{key:0,class:"font2",innerHTML:`${null==(t=C.content)?void 0:t.introduction}`.replace(/\n/g,"<br/>")},null,8,["innerHTML"])):y("",!0)]})),_:1})):y("",!0),u(_,{class:f(["btns",C.servicer||C.enterpriseList.length?"":"centerFlex"]),style:{"margin-top":"60rpx"}},{default:o((()=>[u(_,{class:"btn1",onClick:b.toKf},{default:o((()=>[p("客服咨询")])),_:1},8,["onClick"]),C.servicer?(l(),a(_,{key:0,class:"btn2",onClick:t[0]||(t[0]=e=>b.goPay())},{default:o((()=>[p("立即购买")])),_:1})):y("",!0),!C.servicer&&C.enterpriseList&&C.enterpriseList.length?(l(),a(_,{key:1,class:"btn2",onClick:b.goServiceList},{default:o((()=>[p("服务列表 ")])),_:1},8,["onClick"])):y("",!0)])),_:1},8,["class"]),u(M,{ref:"popup",type:"center","border-radius":"10px 10px 0 0"},{default:o((()=>[u(_,{class:"pop"},{default:o((()=>[u(x,{src:"https://static.idicc.cn/cdn/aiChat/applet/serviceCode3.png","show-menu-by-longpress":"true",class:"popImg",onClick:b.limit},null,8,["onClick"]),u(_,{class:"close",onClick:b.close},null,8,["onClick"])])),_:1})])),_:1},512)])),_:1},8,["style"])):y("",!0)])),_:1})}],["__scopeId","data-v-a81dde7e"]]);export{b as default};
