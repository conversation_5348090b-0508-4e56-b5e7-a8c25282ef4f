import{l as e,p as t,q as a,m as o,T as i,s,z as l,A as r,a as n,f as u,w as c,F as d,i as m,o as p,B as g,t as h,d as f,g as v,c as y,a5 as k}from"./index-HcMwrp5e.js";import{_ as C}from"./page-meta.X-Lr6csD.js";import{r as b}from"./uni-app.es.DFp0WTX7.js";import{_ as I}from"./uni-easyinput.CAhWtyu2.js";import{_ as x,a as F}from"./uni-forms.i1XM9iHP.js";import{_ as D}from"./uni-data-checkbox.CvA0WHPC.js";import"./JSEncrypt.BbYaXJGF.js";import{q as _}from"./quicklogin.BIvdG-8C.js";import{_ as j}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.B5Z3RbO2.js";import"./uni-cloud.es.C7bj1nlK.js";import"./uni-load-more.Dz9gLz3r.js";import"./uni-popup.BdZPMDVN.js";import"./uni-transition.CHOJlBbg.js";const V=j({components:{quickLogin:_},data:()=>({consent:!1,whetherClick:!1,publicKey:"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB",valiFormData:{name:"",age:""},checkbox:[],hobby:[{text:"",value:0}],verify:!0,i:60,Haveyousent:!1,rules:{name:{rules:[{required:!0,errorMessage:"手机号不能为空"},{minLength:11,maxLength:11,errorMessage:"请输入11位合法手机号"}]},age:{rules:[{required:!0,errorMessage:"验证码不能为空"}]}}}),created(){},computed:{showget(){return 11===this.valiFormData.name.length}},onLoad(){e("consent")&&(this.checkbox=[0])},onShow(){},methods:{Unchecked(){t({title:"请勾选用户协议",icon:"error"})},go(e){"agreement"==e?a({url:`/pages/login/components/${e}`}):o({url:`/pages/login/components/${e}`})},bindingId(e){let t=this;i({success(a){t.$api.syncUnion({data:{code:a.code},token:e,method:"post"}).then((e=>{})).catch((e=>{}))}})},getorgCode(e){this.$api.ownList({token:e}).then((e=>{var t,a,o;s("orgCode",null==(t=e.result.selected)?void 0:t.orgCode),s("orgName",null==(a=e.result.selected)?void 0:a.orgName),s("isDefault",null==(o=e.result.selected)?void 0:o.isDefault)}))},getverification(){if(this.showget&&this.verify){let e={mobile:this.valiFormData.name};this.$api.getVerifyCode({data:e,method:"get"}).then((e=>{if(0==e.result.status){t({title:"验证码发送成功",icon:"none"}),this.verify=!1;var a=setInterval((()=>{this.i--,this.i<1&&(this.verify=!0,this.i=60,clearInterval(a),a=null)}),1e3)}else t({title:e.result.msg||"验证码发送失败",icon:"none"})}))}},whereTogo(){e("teamCode"),this.$api.userMessage().then((e=>{var t,a;s("userIdentityType",null==(t=e.result.userCompletionInfoDO)?void 0:t.userIdentity),5==(null==(a=e.result.userCompletionInfoDO)?void 0:a.userIdentity)?s("identity",2):s("identity",1),o({url:"/pages/repository/capacity"})}))},submit(a){if(0==this.checkbox.length)return t({title:"请勾选用户协议",icon:"error"});this.$refs[a].validate().then((a=>{let i=this,l={username:i.valiFormData.name,verifyCode:i.valiFormData.age,loginType:"XA_VERIFY_CODE",invitationCode:e("invitationCode")||""};i.$api.login({data:l,method:"post"}).then((e=>{"SUCCESS"==e.code?(s("token",e.result.token),s("consent",!0),s("phone",e.result.username),s("userId",e.result.userId),i.getorgCode(e.result.token),i.bindingId(e.result.token),i.$api.isPopUserInfoCompletion({method:"GET"}).then((e=>{!0===e.result?o({url:"/pages/user/perfectionInfo"}):i.whereTogo()}))):t({title:e.msg,icon:"none"})}))})).catch((e=>{}))}}},[["render",function(e,t,a,o,i,s){const _=b(l("page-meta"),C),j=b(l("uni-easyinput"),I),V=b(l("uni-forms-item"),x),q=b(l("uni-forms"),F),A=b(l("uni-data-checkbox"),D),w=k,B=r("quickLogin"),S=m;return p(),n(d,null,[u(_,{"page-style":"background-color: #FAFCFF"}),u(S,{class:"login"},{default:c((()=>[g("div",{class:"t1"},"验证码登录/注册"),g("div",{class:"t2"},"未注册的手机号将自动注册并登录"),u(S,{class:"example"},{default:c((()=>[u(q,{ref:"valiForm",rules:i.rules,modelValue:i.valiFormData},{default:c((()=>[u(V,{class:"forms-item",required:"",name:"name"},{default:c((()=>[u(j,{clearable:!1,modelValue:i.valiFormData.name,"onUpdate:modelValue":t[0]||(t[0]=e=>i.valiFormData.name=e),placeholder:"请输入手机号",placeholderStyle:"font-size:26rpx",trim:!0},null,8,["modelValue"])])),_:1}),u(V,{class:"forms-item",required:"",name:"age"},{default:c((()=>[u(j,{clearable:!1,modelValue:i.valiFormData.age,"onUpdate:modelValue":t[2]||(t[2]=e=>i.valiFormData.age=e),placeholder:"请输入验证码",placeholderStyle:"font-size:26rpx",maxlength:"10",trim:!0},{right:c((()=>[g("div",{onClick:t[1]||(t[1]=(...e)=>s.getverification&&s.getverification(...e)),class:"getverification"},[!s.showget&&i.verify?(p(),n("div",{key:0,class:"getcode"},h(i.Haveyousent?"重新获取":"获取验证码"),1)):f("",!0),s.showget&&i.verify?(p(),n("div",{key:1,class:"getcodes"},h(i.Haveyousent?"重新获取":"获取验证码"),1)):f("",!0),i.verify?f("",!0):(p(),n("div",{key:2,class:"getcode"},h(i.i)+"秒后重试",1))])])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["rules","modelValue"]),g("div",{class:"t3"},[u(A,{multiple:"",modelValue:i.checkbox,"onUpdate:modelValue":t[3]||(t[3]=e=>i.checkbox=e),localdata:i.hobby},null,8,["modelValue","localdata"]),g("div",{class:"agreement"},[v("阅读并同意 "),g("span",{onClick:t[4]||(t[4]=e=>s.go("agreement"))},"《用户协议》")])]),u(w,{class:"loginButton",onClick:t[5]||(t[5]=e=>s.submit("valiForm")),type:"primary"},{default:c((()=>[v("登 录")])),_:1}),0==i.checkbox.length?(p(),y(w,{key:0,class:"ClickLogin",type:"primary",onClick:s.Unchecked},{default:c((()=>[v("本机一键登录")])),_:1},8,["onClick"])):(p(),y(B,{key:1})),g("span",{onClick:t[6]||(t[6]=e=>s.go("passwordLogin")),class:"gocode"},"使用账号密码登录")])),_:1})])),_:1})],64)}],["__scopeId","data-v-d6b3b2f6"]]);export{V as default};
