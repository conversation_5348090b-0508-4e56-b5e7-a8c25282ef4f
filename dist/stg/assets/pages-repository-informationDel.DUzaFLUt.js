import{_ as e}from"./page-meta.X-Lr6csD.js";import{z as s,A as a,a as t,f as r,B as o,t as i,F as l,C as c,o as d}from"./index-HcMwrp5e.js";import{r as n}from"./uni-app.es.DFp0WTX7.js";import{t as m}from"./index.B6Rbewql.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const u=p({components:{tabBar:m},data:()=>({title:"产品详情",del:{}}),onLoad(e){e.imageUrl=decodeURIComponent(e.imageUrl),this.del=e},methods:{customerService(){wx.openCustomerServiceChat({extInfo:{url:"https://work.weixin.qq.com/kfid/kfc1de27190dee490b8"},corpId:"wwc94172e0ae82b937",success(e){}})}}},[["render",function(m,p,u,f,x,v){const g=n(s("page-meta"),e),h=a("tabBar"),b=c;return d(),t(l,null,[r(g,{"page-style":"background-color: #FAFCFF"}),o("div",null,[r(h,{title:x.title},null,8,["title"]),o("div",{class:"boxs"},[o("div",{class:"box"},[r(b,{src:x.del.imageUrl,class:"imageUrl",mode:"widthFix"},null,8,["src"]),o("span",{class:"detail"},i(x.del.detail),1)]),o("div",{onClick:p[0]||(p[0]=(...e)=>v.customerService&&v.customerService(...e)),class:"experience"}," 联系客服 "),o("div",{style:{height:"180rpx"}})])])],64)}],["__scopeId","data-v-f84378da"]]);export{u as default};
