import{Z as s,l as e,Y as t,A as a,c as o,w as l,o as i,B as n,d,a1 as c}from"./index-CBCsGYoT.js";import{v as r}from"./webviewUrl.D8mk1f89.js";import{_ as u}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=u({data:()=>({tokenData:"",url:"",isFirstLogin:!0,webV:null}),onLoad(t){s({title:"加载中"});let a=`${r}InformationDetails`;this.tokenData=e("token"),this.url=`${a}?token=${this.tokenData}&type=${t.type}&id=${t.id}&newData=${(new Date).getTime()}`,console.log("identity",this.url)},onShow(){},mounted(){},methods:{loadSuccess(){t()}}},[["render",function(s,e,t,r,u,g){const k=c,v=a("View");return i(),o(v,null,{default:l((()=>[n("div",{class:"skeleton"},[n("div",{class:"chatBg"}),n("div",{class:"skeletonlist"},[n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"})])]),u.url?(i(),o(k,{key:0,src:u.url,onMessage:s.handlePostMessage,title:"资讯详情",onLoad:g.loadSuccess},null,8,["src","onMessage","onLoad"])):d("",!0)])),_:1})}],["__scopeId","data-v-be3b6367"]]);export{g as default};
