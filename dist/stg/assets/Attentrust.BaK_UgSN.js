import{p as e,z as t,o as a,c as i,w as s,V as o,f as r,B as n,i as l,l as c,q as p,A as m,t as d,g as u,a as h,d as f,n as g}from"./index-HcMwrp5e.js";import{_ as b}from"./uni-popup.BdZPMDVN.js";import{r as y}from"./uni-app.es.DFp0WTX7.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as x}from"./uni-icons.B5Z3RbO2.js";import{_,a as D}from"./uni-forms.i1XM9iHP.js";import{_ as k}from"./uni-datetime-picker.DDEPfKMC.js";import{_ as V}from"./uni-easyinput.CAhWtyu2.js";import{e as C}from"./entrust.BGv2GPvP.js";const F=v({data:()=>({porpsData:"",loading:!1}),methods:{opens(e){this.porpsData=e,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){if(this.loading)return;this.loading=!0;let t={recommendId:this.porpsData.recommendId};this.$api.recommenddeleteAPI({data:t,method:"post"}).then((t=>{e({title:"删除成功！",icon:"none"}),setTimeout((()=>{this.claimFn(),this.loading=!1,this.$emit("updataList")}),500)})).catch((e=>{this.loading=!1}))}}},[["render",function(e,c,p,m,d,u){const h=y(t("uni-popup"),b),f=l;return a(),i(f,null,{default:s((()=>[(a(),i(o,{to:"body"},[r(h,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:s((()=>[n("div",{class:"applyaffirm"},[n("div",{class:"p1"},"删除"),n("div",{class:"p2"},"确认删除该反馈？ "),n("div",{class:"popBtn"},[n("div",{onClick:c[0]||(c[0]=(...e)=>u.claimFn&&u.claimFn(...e)),class:"affirm"}," 取消 "),n("div",{onClick:c[1]||(c[1]=(...e)=>u.retreat&&u.retreat(...e)),class:"canleBt"}," 确认 ")])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-e80b0105"]]);const w=v({components:{entrust:C},data:()=>({clientVersion:0,porpsData:"",safeArea:c("safeAreaRpx"),form:{enterprise:"",exceptedDatetime:"",contact:"",contactPhone:"",note:""},startTime:"",endTime:"",submitLoading:!1,uploadFileList:[],priceText:{amount:"--",type:"",Text:""},enterpriseAmountTypeList:[],rules:{exceptedDatetime:{rules:[{required:!0,errorMessage:"请选择期望日期"}]},contact:{rules:[{required:!0,errorMessage:"请输入对接人姓名"},{maxLength:10,errorMessage:"对接人姓名最多10个字"}]},contactPhone:{rules:[{required:!0,errorMessage:"请输入联系方式"},{pattern:/^1[3-9]\d{9}$/,errorMessage:"请输入合法手机号"}]}}}),methods:{opens(e){this.timeData=(new Date).getTime()+6048e5,this.porpsData=e,this.form.enterprise=this.porpsData.enterpriseName,this.$refs.follow.open("bottom"),this.getmoney(this.porpsData.enterpriseUniCode)},applySucceedFn(){e({title:"发起委托申请已提交成功!",icon:"none",duration:2e3})},timeChange(e){this.form.exceptedDatetime=e},BillingRuleFn(){var e;this.clientVersion=c("clientVersion")?Number(c("clientVersion")):0,this.clientVersion>=270?null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{},path:"billingRule2"})):p({url:"/pages/repository/BillingRule"})},getmoney(e){let t={enterpriseUniCode:e||""};this.$api.getEnterpriseLabelAPI({data:t}).then((e=>{this.$api.enumListAPI({}).then((t=>{this.enterpriseAmountTypeList=t.result.enterpriseAmountTypeList,this.priceText=e.result}))}))},claimFn(){this.form={enterprise:"",exceptedDatetime:"",contact:"",contactPhone:"",note:""},this.$refs.atetimepicker.clear(),this.$refs.follow.close()},ClearOrder(){this.claimFn(),this.$emit("updataList")},submit(){let t=(new Date).getTime()+5184e5;if(this.form.exceptedDatetime<t)return e({title:"期望对接时间须为委托日期七日后",icon:"none",duration:2e3});if(!this.submitLoading)try{this.submitLoading=!0,this.$refs.valiForm.validate().then((e=>{let t={enterprise:this.form.enterprise,exceptedDatetime:this.form.exceptedDatetime,contact:this.form.contact,contactPhone:this.form.contactPhone,note:this.form.note,recommendId:this.porpsData.recommendId};this.$refs.entrust.opens(t,this.enterpriseAmountTypeList),this.submitLoading=!1})).catch((e=>{this.submitLoading=!1}))}catch(a){this.submitLoading=!1}}}},[["render",function(e,c,p,v,C,F){const w=y(t("uni-icons"),x),L=y(t("uni-forms-item"),_),A=y(t("uni-datetime-picker"),k),I=y(t("uni-easyinput"),V),T=y(t("uni-forms"),D),$=y(t("uni-popup"),b),P=m("entrust"),B=l;return a(),i(B,null,{default:s((()=>[(a(),i(o,{to:"body"},[r($,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:s((()=>[n("div",{class:"applyaffirm"},[r(w,{onClick:F.claimFn,class:"canleIcon",type:"closeempty",color:"#86909C",size:"22"},null,8,["onClick"]),n("div",{class:"head"},"一键委托"),n("div",{class:"followForm"},[r(T,{ref:"valiForm",rules:C.rules,modelValue:C.form,"label-position":"top","label-width":"100"},{default:s((()=>[r(L,{label:"意向企业",required:""},{default:s((()=>[n("div",{class:"AnalogInputBox"},d(C.form.enterprise),1)])),_:1}),r(L,{label:"期望对接时间",name:"exceptedDatetime",required:""},{default:s((()=>[n("div",{class:"followInput"},[r(A,{ref:"atetimepicker",type:"date","return-type":"timestamp",value:C.form.exceptedDatetime,start:e.timeData,onChange:F.timeChange},null,8,["value","start","onChange"])])])),_:1}),r(L,{name:"contact",label:"招商对接人",required:""},{default:s((()=>[n("div",{class:"followInput"},[r(I,{trim:"all",clearable:!1,modelValue:C.form.contact,"onUpdate:modelValue":c[0]||(c[0]=e=>C.form.contact=e),placeholder:"请输入"},null,8,["modelValue"])])])),_:1}),r(L,{name:"contactPhone",label:"联系方式",required:""},{default:s((()=>[n("div",{class:"followInput"},[r(I,{trim:"all",clearable:!1,modelValue:C.form.contactPhone,"onUpdate:modelValue":c[1]||(c[1]=e=>C.form.contactPhone=e),placeholder:"请输入"},null,8,["modelValue"])])])),_:1}),r(L,{label:"招商要求"},{default:s((()=>[n("div",{class:"summarizeInput"},[r(I,{type:"textarea",trim:"all",clearable:!1,modelValue:C.form.note,"onUpdate:modelValue":c[2]||(c[2]=e=>C.form.note=e),placeholder:"请输入"},null,8,["modelValue"])])])),_:1})])),_:1},8,["rules","modelValue"])]),n("div",{class:"btnBox"},[n("div",{class:"priceBox"},[n("div",{class:"price"},[n("span",{class:"symbol"},"￥"),u(d(C.priceText.amount),1),C.priceText.name?(a(),h("span",{key:0,class:"type"},"| "+d(C.priceText.name),1)):f("",!0)]),n("div",{onClick:c[3]||(c[3]=e=>F.BillingRuleFn()),class:"BillingRule"}," 计费规则 > ")]),n("div",{class:"submit",onClick:c[4]||(c[4]=(...e)=>F.submit&&F.submit(...e))},"一键委托")]),n("div",{style:g({height:C.safeArea})},null,4)])])),_:1},512)])),r(P,{onClearOrder:F.ClearOrder,onApplySucceedFn:F.applySucceedFn,ref:"entrust"},null,8,["onClearOrder","onApplySucceedFn"])])),_:1})}],["__scopeId","data-v-6a86b196"]]);export{w as A,F as d};
