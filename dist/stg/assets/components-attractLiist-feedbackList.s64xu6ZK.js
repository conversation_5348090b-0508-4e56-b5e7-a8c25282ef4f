import{l as t,q as e,z as s,A as a,a as i,f as d,B as n,F as o,b as r,o as l,t as m,g as p,d as c,D as u,C as h}from"./index-HcMwrp5e.js";import{_ as f}from"./page-meta.X-Lr6csD.js";import{r as v}from"./uni-app.es.DFp0WTX7.js";import{t as D}from"./index3.6XIegSX5.js";import{d as g,A as k}from"./Attentrust.BaK_UgSN.js";import{_ as j}from"./enterprise.BFmTa1IP.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";import"./uni-popup.BdZPMDVN.js";import"./uni-transition.CHOJlBbg.js";import"./uni-icons.B5Z3RbO2.js";import"./uni-forms.i1XM9iHP.js";import"./uni-datetime-picker.DDEPfKMC.js";import"./uni-easyinput.CAhWtyu2.js";import"./entrust.BGv2GPvP.js";const I=y({components:{tabBar:D,delDemand:g,Attentrust:k},data:()=>({title:"哒达助招",identity:1,myentrustList:[],delDemandId:""}),onLoad(e){this.identity=t("identity"),this.delDemandId=e.id,this.getDel(this.delDemandId)},onShow(){this.getDel(this.delDemandId)},methods:{updataList(){this.getDel(this.delDemandId)},feedbackDel(t){e({url:`/components/feedbackDel?demandId=${t.demandId}&id=${t.id}`})},stateShow:t=>3==t?"已委托":"",getDel(t){let e={demandId:t,pageSize:1e3,pageNum:1};this.$api.recommendlistAPI({data:e,method:"get"}).then((t=>{this.myentrustList=t.result.records})).catch((t=>{}))},deleteFeedback(t){this.$refs.delDemand.opens(t)},todosthFn(t){this.$refs.Attentrust.opens(t)}}},[["render",function(t,e,D,g,k,y){const I=v(s("page-meta"),f),b=a("tabBar"),L=h,F=a("delDemand"),A=a("Attentrust");return l(),i(o,null,[d(I,{"page-style":"background-color: #FAFCFF"}),n("div",null,[d(b,{title:k.title},null,8,["title"]),n("div",{class:"box33"},[n("div",{class:"headText"}," 反馈企业列表 "),(l(!0),i(o,null,r(k.myentrustList,((t,e)=>(l(),i("div",{onClick:e=>y.feedbackDel(t),class:"myentrustListClass",key:t.entrustId},[n("div",{class:"t1"},[n("div",{class:"startTime"}," 反馈时间："+m(t.auditTime),1),n("div",{class:"myListstate"},m(y.stateShow(t.status)),1)]),n("div",{class:"t2"},[n("div",{class:"enterprise"},[d(L,{class:"enterpriseIcon",src:j}),p(" "+m(t.enterpriseName)+" ",1),t.chainNames?(l(),i("div",{key:0,class:"tagItem"},m(null==t?void 0:t.chainNames[0]),1)):c("",!0)])]),n("div",{class:"t3"},[n("div",{class:"line"},"企业概况："+m(t.enterpriseDetail),1)]),n("div",{class:"btnBox"},[n("div",{onClick:u((e=>y.deleteFeedback(t)),["stop"]),class:"T4BtnDe"}," 删除 ",8,["onClick"]),3!=t.status?(l(),i("div",{key:0,onClick:u((e=>y.todosthFn(t)),["stop"]),class:"T4Btn"}," 一键委托 ",8,["onClick"])):c("",!0)])],8,["onClick"])))),128)),n("div",{style:{height:"150rpx"}})]),d(F,{onUpdataList:y.updataList,ref:"delDemand"},null,8,["onUpdataList"]),d(A,{onUpdataList:y.updataList,ref:"Attentrust"},null,8,["onUpdataList"])])],64)}],["__scopeId","data-v-376bf17e"]]);export{I as default};
