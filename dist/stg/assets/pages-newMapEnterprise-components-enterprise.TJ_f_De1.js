import{P as e,z as t,o as s,c as i,w as n,f as a,r as l,e as r,d as o,n as c,i as d,g as u,t as h,C as p,h as m,q as g,p as f,a as v,b as y,B as L,F as _,l as C,O as b,s as I,a0 as w,U as S,A as k,D as x}from"./index-HcMwrp5e.js";import{_ as T}from"./page-meta.X-Lr6csD.js";import{r as N}from"./uni-app.es.DFp0WTX7.js";import{_ as A}from"./uni-icons.B5Z3RbO2.js";import{_ as D}from"./uni-tag.nXvhaS4z.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as O}from"./uni-tooltip.Bpz4R-Ep.js";import{t as F}from"./index.B6Rbewql.js";import{_ as B}from"./uni-popup.BdZPMDVN.js";import{l as z,g as E}from"./utils.61Hi-B7M.js";import{_ as $}from"./right.D4iebSn6.js";import"./returnFn.BYkANsDr.js";import"./uni-transition.CHOJlBbg.js";const V=P({name:"uniCollapseItem",props:{title:{type:String,default:""},name:{type:[Number,String],default:""},disabled:{type:Boolean,default:!1},showAnimation:{type:Boolean,default:!0},open:{type:Boolean,default:!1},thumb:{type:String,default:""},titleBorder:{type:String,default:"auto"},border:{type:Boolean,default:!0},showArrow:{type:Boolean,default:!0}},data:()=>({isOpen:!1,isheight:null,height:0,elId:`Uni_${Math.ceil(1e6*Math.random()).toString(36)}`,nameSync:0}),watch:{open(e){this.isOpen=e,this.onClick(e,"init")}},updated(e){this.$nextTick((()=>{this.init(!0)}))},created(){this.collapse=this.getCollapse(),this.oldHeight=0,this.onClick(this.open,"init")},unmounted(){this.__isUnmounted=!0,this.uninstall()},mounted(){this.collapse&&(""!==this.name?this.nameSync=this.name:this.nameSync=this.collapse.childrens.length+"",-1===this.collapse.names.indexOf(this.nameSync)?this.collapse.names.push(this.nameSync):console.warn(`name 值 ${this.nameSync} 重复`),-1===this.collapse.childrens.indexOf(this)&&this.collapse.childrens.push(this),this.init())},methods:{init(e){this.getCollapseHeight(e)},uninstall(){this.collapse&&(this.collapse.childrens.forEach(((e,t)=>{e===this&&this.collapse.childrens.splice(t,1)})),this.collapse.names.forEach(((e,t)=>{e===this.nameSync&&this.collapse.names.splice(t,1)})))},onClick(e,t){this.disabled||(this.isOpen=e,this.isOpen&&this.collapse&&this.collapse.setAccordion(this),"init"!==t&&this.collapse.onChange(e,this))},getCollapseHeight(t,s=0){e().in(this).select(`#${this.elId}`).fields({size:!0},(e=>{if(!(s>=10)){if(!e)return s++,void this.getCollapseHeight(!1,s);this.height=e.height,this.isheight=!0,t||this.onClick(this.isOpen,"init")}})).exec()},getNvueHwight(e){dom.getComponentRect(this.$refs["collapse--hook"],(t=>{if(t&&t.result&&t.size){if(this.height=t.size.height,this.isheight=!0,e)return;this.onClick(this.open,"init")}}))},getCollapse(e="uniCollapse"){let t=this.$parent,s=t.$options.name;for(;s!==e;){if(t=t.$parent,!t)return!1;s=t.$options.name}return t}}},[["render",function(e,g,f,v,y,L){const _=p,C=m,b=d,I=N(t("uni-icons"),A);return s(),i(b,{class:"uni-collapse-item"},{default:n((()=>[a(b,{onClick:g[0]||(g[0]=e=>L.onClick(!y.isOpen)),class:r(["uni-collapse-item__title",{"is-open":y.isOpen&&"auto"===f.titleBorder,"uni-collapse-item-border":"none"!==f.titleBorder}])},{default:n((()=>[a(b,{class:"uni-collapse-item__title-wrap"},{default:n((()=>[l(e.$slots,"title",{},(()=>[a(b,{class:r(["uni-collapse-item__title-box",{"is-disabled":f.disabled}])},{default:n((()=>[f.thumb?(s(),i(_,{key:0,src:f.thumb,class:"uni-collapse-item__title-img"},null,8,["src"])):o("",!0),a(C,{class:"uni-collapse-item__title-text"},{default:n((()=>[u(h(f.title),1)])),_:1})])),_:1},8,["class"])]),!0)])),_:3}),f.showArrow?(s(),i(b,{key:0,class:r([{"uni-collapse-item__title-arrow-active":y.isOpen,"uni-collapse-item--animation":!0===f.showAnimation},"uni-collapse-item__title-arrow"])},{default:n((()=>[a(I,{color:f.disabled?"#ddd":"#bbb",size:"14",type:"bottom"},null,8,["color"])])),_:1},8,["class"])):o("",!0)])),_:3},8,["class"]),a(b,{class:r(["uni-collapse-item__wrap",{"is--transition":f.showAnimation}]),style:c({height:(y.isOpen?y.height:0)+"px"})},{default:n((()=>[a(b,{id:y.elId,ref:"collapse--hook",class:r(["uni-collapse-item__wrap-content",{open:y.isheight,"uni-collapse-item--border":f.border&&y.isOpen}])},{default:n((()=>[l(e.$slots,"default",{},void 0,!0)])),_:3},8,["id","class"])])),_:3},8,["class","style"])])),_:3})}],["__scopeId","data-v-d9eb4150"]]);const R=P({name:"uniCollapse",emits:["change","activeItem","input","update:modelValue"],props:{value:{type:[String,Array],default:""},modelValue:{type:[String,Array],default:""},accordion:{type:[Boolean,String],default:!1}},data:()=>({}),computed:{dataValue(){let e="string"==typeof this.value&&""===this.value||Array.isArray(this.value)&&0===this.value.length;"string"==typeof this.modelValue&&""===this.modelValue||Array.isArray(this.modelValue)&&this.modelValue.length;return e?this.modelValue:this.value}},watch:{dataValue(e){this.setOpen(e)}},created(){this.childrens=[],this.names=[]},mounted(){this.$nextTick((()=>{this.setOpen(this.dataValue)}))},methods:{setOpen(e){let t="string"==typeof e,s=Array.isArray(e);this.childrens.forEach(((i,n)=>{if(t&&e===i.nameSync){if(!this.accordion)return void console.warn("accordion 属性为 false ,v-model 类型应该为 array");i.isOpen=!0}s&&e.forEach((e=>{if(e===i.nameSync){if(this.accordion)return void console.warn("accordion 属性为 true ,v-model 类型应该为 string");i.isOpen=!0}}))})),this.emit(e)},setAccordion(e){this.accordion&&this.childrens.forEach(((t,s)=>{e!==t&&(t.isOpen=!1)}))},resize(){this.childrens.forEach(((e,t)=>{e.getCollapseHeight()}))},onChange(e,t){let s=[];this.accordion?s=e?t.nameSync:"":this.childrens.forEach(((e,t)=>{e.isOpen&&s.push(e.nameSync)})),this.$emit("change",s),this.emit(s)},emit(e){this.$emit("input",e),this.$emit("update:modelValue",e)}}},[["render",function(e,t,a,r,o,c){const u=d;return s(),i(u,{class:"uni-collapse"},{default:n((()=>[l(e.$slots,"default",{},void 0,!0)])),_:3})}],["__scopeId","data-v-3aec18a2"]]),j={props:{informationList:{type:Array,default:()=>[]},noschedule:{type:Boolean,default:!1},type:{type:String,default:""}},data:()=>({showDelete:!1,currentIndex:null,start:{},current:{}}),methods:{information(e){g({url:`/components/particulars/information?id=${e.id}`})},deleteEven(e){let t={informationId:e.id};this.$api.cancelCollect({data:t}).then((e=>{this.$emit("getservice"),this.showDelete=!1,f({title:"删除成功！",icon:"success",mask:!0})}))},listTouchstart(e){"collect"===this.type&&(this.start.x=e.touches[0].clientX,this.start.y=e.touches[0].clientY)},listTouchmove(e,t){if("collect"===this.type){this.currentIndex=t,this.current.x=e.touches[0].clientX,this.current.y=e.touches[0].clientY;const s=Math.round(this.start.x),i=Math.round(this.current.x);s-i>20?this.showDelete=!0:s-i<-20&&(this.showDelete=!1)}}}};const M=P({components:{industryinformation:P(j,[["render",function(e,l,p,m,g,f){const C=d,b=N(t("uni-tag"),D);return s(),i(C,{class:"box"},{default:n((()=>[a(C,{class:r(p.noschedule?"conston":"const")},{default:n((()=>[(s(!0),v(_,null,y(p.informationList,((e,t)=>(s(),i(C,{class:r(p.noschedule?"contentson":"contents"),key:t},{default:n((()=>[a(C,{class:r(["list","collect"===p.type?"on":""]),onTouchstart:f.listTouchstart,onTouchmove:e=>f.listTouchmove(e,t)},{default:n((()=>[a(C,{onClick:t=>f.information(e),class:"list-con",style:c({left:g.showDelete&&g.currentIndex==t?"-110rpx":"0rpx"})},{default:n((()=>[a(C,{class:"text-box"},{default:n((()=>[L("span",{class:"titletext"},h(e.title),1)])),_:2},1024),a(C,{class:"tag"},{default:n((()=>[a(C,{class:"tag-s"},{default:n((()=>[(s(!0),v(_,null,y(e.industryChainNameList,((e,t)=>(s(),i(C,{class:"tags",key:t},{default:n((()=>[a(b,{text:e.slice(5),"custom-style":"background-color: #fff2e6; border-color: #fff2e6; color: #FF7D00;font-weight: 700;"},null,8,["text"])])),_:2},1024)))),128)),(s(!0),v(_,null,y(e.themes,((e,t)=>(s(),i(C,{class:"tags",key:t},{default:n((()=>[a(b,{text:e,"custom-style":"background-color: #eff4ff; border-color: #eff4ff; color: #417FFF ;font-weight: 700;"},null,8,["text"])])),_:2},1024)))),128))])),_:2},1024),L("span",{class:"time"},h(e.publishingDate),1)])),_:2},1024)])),_:2},1032,["onClick","style"]),"collect"===p.type?(s(),i(C,{key:0,onClick:t=>f.deleteEven(e),class:"delete-btn",style:c({right:g.showDelete&&g.currentIndex==t?"0rpx":"-110rpx"})},{default:n((()=>[a(C,{class:"delete-txt"},{default:n((()=>[u("删除")])),_:1})])),_:2},1032,["onClick","style"])):o("",!0)])),_:2},1032,["class","onTouchstart","onTouchmove"])])),_:2},1032,["class"])))),128))])),_:1},8,["class"])])),_:1})}],["__scopeId","data-v-4f73de7c"]]),tabBar:F,bringInto:P({data:()=>({porpsData:"",loading:!1}),methods:{opens(){this.$refs.claim.open()},claimCanle(){return this.$refs.claim.close(),this.$emit("operatePopup",!1)},ConfirmationClaim(){var e;this.claimCanle(),null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",path:"attractionManage"}))}}},[["render",function(e,l,r,o,c,u){const h=N(t("uni-icons"),A),p=N(t("uni-popup"),B),m=d;return s(),i(m,null,{default:n((()=>[a(p,{ref:"claim","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[L("div",{class:"applyaffirm"},[a(h,{onClick:u.claimCanle,class:"canleIcon",type:"closeempty",color:"#86909C",size:"22"},null,8,["onClick"]),L("div",{class:"p1"},"纳入招商意向成功！"),L("div",{class:"p2"},"企业已纳入您的招商意向名单，您可以在'招商管理''意向企业'中查看并处理。"),L("div",{onClick:l[0]||(l[0]=(...e)=>u.ConfirmationClaim&&u.ConfirmationClaim(...e)),class:"affirm"}," 去看看 ")])])),_:1},512)])),_:1})}],["__scopeId","data-v-a3725688"]]),UniCollapseItem:V},data:()=>({showString:!1,showHeader:!1,listicon:z,enterpriseList:{chainDTOS:[]},informationList:[],pageNum:1,pageSize:10,pages:0,total:0,total2:0,showTag:[],isshow:!1,selectedTagIndex:0,selectedTagIndex2:0,messageText:"",Successmessage:{},companyID:"",Intentionornot:!1,IntenID:"",name:"",assign:!1,IdentityType:C("userIdentityType")||1,orgName:C("orgName")||"",isDefault:C("isDefault")||!1,tabChack:1,tabIsShow:!1,tabList:[],time:null,isScrollByTab:!1,bottomHeight:0,enterpriseLabelIds:[],popupShow:!1,names:"",iconTypeid:0,showChain:"",clientVersion:0,show:!1,productNamesLength:0,StringTotal:0,clueSource:"",clueId:"",isShowMore:!0,businessDataList:[]}),onLoad(e){this.requestOrgCode(),E("clientVersion")&&(this.clientVersion=Number(E("clientVersion"))),E("enterpriseLabelIds")&&(this.enterpriseLabelIds=JSON.parse(decodeURIComponent(E("enterpriseLabelIds")))),E("showChain")&&(this.showChain=E("showChain")),E("id")&&(this.companyID=E("id")),E("iconTypeid")&&(this.iconTypeid=E("iconTypeid")),E("enterpriseName")&&(this.name=E("enterpriseName")),E("clueSource")&&(this.clueSource=E("clueSource")),E("clueId")&&(this.clueId=E("clueId")),2==this.clueSource?this.getDetailClue(this.clueId):this.getdetail(this.companyID),this.name&&b({title:this.name})},computed:{currentProductNames(){var e,t,s;return(null==(s=null==(t=null==(e=this.enterpriseList)?void 0:e.chainDTOS[this.selectedTagIndex])?void 0:t.chainNodeList[this.selectedTagIndex2])?void 0:s.productNames)||[]}},watch:{selectedTagIndex(){this.updateProductNamesLength()},selectedTagIndex2(){this.updateProductNamesLength()},currentProductNames(e){this.productNamesLength=e.length}},methods:{requestOrgCode(){this.$api.ownList({}).then((e=>{var t,s,i,n,a;this.orgName=null==(t=e.result.selected)?void 0:t.orgName,this.isDefault=null==(s=e.result.selected)?void 0:s.isDefault,I("orgCode",null==(i=e.result.selected)?void 0:i.orgCode),I("orgName",null==(n=e.result.selected)?void 0:n.orgName),I("isDefault",null==(a=e.result.selected)?void 0:a.isDefault)}))},getWidthClass(){return this.enterpriseList.historyRecommend&&1==this.IdentityType&&!this.isDefault&&this.orgName?"narrow-width":"wide-width"},Intention(e){if(e)return;let t={uniCode:this.enterpriseList.unifiedSocialCreditCode,clueSource:1};this.$api.addEnterpriseAPI({data:t,method:"post"}).then((e=>{"SUCCESS"==e.code?(f({title:"操作成功",icon:"none"}),this.operatePopup(!0),this.$refs.bringInto.opens(),this.getdetail(this.enterpriseList.id)):f({title:e.msg,icon:"none"})}))},changAttention(e){let t={enterpriseUniCode:this.enterpriseList.unifiedSocialCreditCode,status:!e};this.$api.collectionAPI_investment({data:t,method:"post"}).then((t=>{f(e?{title:"取消收藏成功！",icon:"none"}:{title:"收藏成功！已添加至'招商管理-我的收藏'",icon:"none"}),this.enterpriseList.isCollect=!this.enterpriseList.isCollect}))},handleLongPress(e){w({data:e,success:function(){console.log("success")}})},goDetail(e){let t={enterpriseId:this.enterpriseList.id,enterpriseName:this.enterpriseList.enterpriseName,uniCode:this.enterpriseList.unifiedSocialCreditCode,recommendRegionCode:this.enterpriseList.recommendRegionCode||""};this.$api.reportGenerateAPI({data:t,method:"post"}).then((e=>{var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{url:"previewPdf",name:"报告预览",params:{reportId:e.result,type:"pdf",enterpriseName:this.enterpriseList.enterpriseName}},path:"webViewPage"}))}))},RetractionExpansion(e){this.businessDataList[e].Expand=!this.businessDataList[e].Expand},goBack(){S({delta:1})},operatePopup(e){this.popupShow=e},getTotalStringLength(e){let t=0;return e.forEach((e=>{t+=e.length})),t},updateProductNamesLength(){this.$nextTick((()=>{this.showString=!1;const e=this.currentProductNames;this.productNamesLength=(null==e?void 0:e.length)||0;let t=this.getTotalStringLength(e);this.StringTotal=t}))},more(){this.isshow?(this.showTag=this.enterpriseList.enterpriseLabelNames.slice(0,4),this.isshow=!this.isshow):(this.showTag=this.enterpriseList.enterpriseLabelNames,this.isshow=!this.isshow)},selectTag(e){this.selectedTagIndex=e},pitchsecond(e){this.selectedTagIndex2=e},scrolltolowerFn(){this.pages>this.pageNum&&(this.pageNum++,this.getBusinessDataList())},getBusinessDataList(e){this.isLoading||(this.isLoading=!0,this.$api.enterpriseEventListAPI({data:{uniCode:e,pageNum:this.pageNum,pageSize:this.pageSize},method:"get"}).then((e=>{e.result&&(this.businessDataList=this.businessDataList.concat(e.result.records.map((e=>{var t;return{eventContent:e.eventContent,eventTitle:e.eventTitle,publishDate:e.publishDate,Expand:!1,showEventContent:(null==(t=null==e?void 0:e.eventContent)?void 0:t.slice(0,38))||""}}))),this.pages=e.result.pages>10?10:e.result.pages)})).finally((()=>{this.isLoading=!1})))},async getdetail(e){let t={enterpriseId:e};this.showChain&&"undefined"!=this.showChain?t.chainId=this.showChain:await this.$api.choosechainAPI({method:"get"}).then((e=>{t.chainId=e.result.chain2Id,this.showChain=e.result.chain2Id})),this.$api.enterprisedetail({data:t,method:"GET"}).then((e=>{this.enterpriseList=e.result,this.getBusinessDataList(this.enterpriseList.unifiedSocialCreditCode),this.enterpriseList.chainDTOS?this.enterpriseList.chainDTOS=this.enterpriseList.chainDTOS.filter((e=>e.chainId==this.showChain)):this.enterpriseList.chainDTOS=[],this.updateProductNamesLength(),this.enterpriseList.enterpriseLabelNames&&(this.showTag=this.enterpriseList.enterpriseLabelNames.slice(0,4)),this.show=!0}))},async getDetailClue(e){let t={clueId:e};this.showChain&&"undefined"!=this.showChain?t.chainId=this.showChain:await this.$api.choosechainAPI({method:"get"}).then((e=>{t.chainId=e.result.chain2Id,this.showChain=e.result.chain2Id})),this.$api.getInvestEnterpriseDetailAPI_investment({data:t,method:"GET"}).then((e=>{this.enterpriseList=e.result,this.getBusinessDataList(this.enterpriseList.unifiedSocialCreditCode),this.enterpriseList.chainDTOS?this.enterpriseList.chainDTOS=this.enterpriseList.chainDTOS.filter((e=>e.chainId==this.showChain)):this.enterpriseList.chainDTOS=[],this.updateProductNamesLength(),this.enterpriseList.enterpriseLabelNames&&(this.showTag=this.enterpriseList.enterpriseLabelNames.slice(0,4)),this.show=!0}))},showMore(){this.pages>this.pageNum&&(this.pageNum++,this.getBusinessDataList(this.enterpriseList.unifiedSocialCreditCode))}}},[["render",function(e,l,m,g,f,C){const b=N(t("page-meta"),T),I=k("tabBar"),w=d,S=p,P=N(t("uni-icons"),A),F=N(t("uni-tag"),D),B=N(t("uni-collapse-item"),V),z=N(t("uni-tooltip"),O),E=N(t("uni-collapse"),R),j=k("bringInto");return s(),v(_,null,[a(b,{"page-style":"overflow:"+(f.popupShow?"hidden":"visible")},null,8,["page-style"]),a(w,null,{default:n((()=>[f.clientVersion&&f.clientVersion>250?(s(),v("div",{key:0},[a(I,{title:"企业详情"}),a(w,{class:"headerBack"},{default:n((()=>[a(w,{class:"back",onClick:C.goBack},null,8,["onClick"]),a(w,{class:"text"},{default:n((()=>[u("企业详情 ")])),_:1})])),_:1})])):o("",!0),f.show?(s(),i(w,{key:1,class:"enterpriceDetail"},{default:n((()=>{var e;return[a(w,null,{default:n((()=>[a(w,{class:"firm"},{default:n((()=>[L("img",{src:f.listicon[f.iconTypeid].icon,alt:"",class:"iconImg"},null,8,["src"]),a(w,{class:"texts"},{default:n((()=>{var e;return[a(w,{class:"userNname"},{default:n((()=>{var e;return[L("span",{class:"enterpriseName",onLongpress:l[0]||(l[0]=e=>C.handleLongPress(f.enterpriseList.enterpriseName))},h(f.enterpriseList.enterpriseName),33),1==f.IdentityType&&!f.isDefault&&f.orgName&&!1!==f.enterpriseList.isExist?(s(),i(S,{key:0,class:"attention",src:(null==(e=f.enterpriseList)?void 0:e.isCollect)?"/static/AboutAi/attention.png":"/static/AboutAi/noAttention.png",onClick:l[1]||(l[1]=x((e=>{var t;return C.changAttention(null==(t=f.enterpriseList)?void 0:t.isCollect)}),["stop"]))},null,8,["src"])):o("",!0)]})),_:1}),(null==(e=f.enterpriseList)?void 0:e.enterpriseLabelNames)?(s(),i(w,{key:0,class:"enterpriseLabeltag"},{default:n((()=>{var e;return[(s(!0),v(_,null,y(f.showTag,((e,t)=>(s(),i(w,{key:t,class:"enterpriseLabel"},{default:n((()=>[L("div",{class:"tagBox"},h(e),1)])),_:2},1024)))),128)),(null==(e=f.enterpriseList)?void 0:e.enterpriseLabelNames.length)>4?(s(),i(w,{key:0,class:"tagBox",onClick:C.more},{default:n((()=>[L("span",{class:"tag-text"},[L("span",{class:"gd-te"},h(f.isshow?"收起":"更多"),1),a(P,{class:"gd-icon",type:f.isshow?"top":"bottom",size:"10",color:"#FF7D00"},null,8,["type"])])])),_:1},8,["onClick"])):o("",!0)]})),_:1})):o("",!0),f.enterpriseList.historyRecommend?(s(),i(w,{key:1,class:"historyRecommendBox2"},{default:n((()=>[u("历史推荐")])),_:1})):o("",!0),a(w,{style:{"margin-top":"10rpx","margin-bottom":"30rpx"}},{default:n((()=>[a(w,{class:"recommend"},{default:n((()=>[a(w,{class:"phoneIcon"}),a(w,null,{default:n((()=>{var e;return[L("span",{onLongpress:l[2]||(l[2]=e=>{var t;return C.handleLongPress(null==(t=f.enterpriseList)?void 0:t.mobile)})},h(null==(e=f.enterpriseList)?void 0:e.mobile),33)]})),_:1})])),_:1}),a(w,{class:"recommend"},{default:n((()=>{var e;return[a(w,{class:"countryIcon"}),"-"==(null==(e=f.enterpriseList)?void 0:e.enterpriseAddress)?(s(),i(w,{key:0,onLongpress:l[3]||(l[3]=e=>{var t,s,i,n,a;return C.handleLongPress(f.enterpriseList.province+((null==(t=f.enterpriseList)?void 0:t.city)!=(null==(s=f.enterpriseList)?void 0:s.province)?(null==(i=f.enterpriseList)?void 0:i.city)+(null==(n=f.enterpriseList)?void 0:n.area):null==(a=f.enterpriseList)?void 0:a.area))})},{default:n((()=>{var e,t,i,n,a;return[L("span",null,h(null==(e=f.enterpriseList)?void 0:e.province),1),(null==(t=f.enterpriseList)?void 0:t.city)!=(null==(i=f.enterpriseList)?void 0:i.province)?(s(),v("span",{key:0},h(null==(n=f.enterpriseList)?void 0:n.city),1)):o("",!0),L("span",null,h(null==(a=f.enterpriseList)?void 0:a.area),1)]})),_:1})):(s(),i(w,{key:1,onLongpress:l[4]||(l[4]=e=>{var t;return C.handleLongPress(null==(t=f.enterpriseList)?void 0:t.enterpriseAddress)})},{default:n((()=>{var e;return[L("span",null,h(null==(e=f.enterpriseList)?void 0:e.enterpriseAddress),1)]})),_:1}))]})),_:1}),a(w,{class:"recommend"},{default:n((()=>[a(w,{class:"increaseIcon"}),a(w,null,{default:n((()=>{var e,t,s;return[u(" 快速成长指数 "),L("span",{style:{"margin-left":"8rpx",color:"#34C759"}},h("0.0"!=(null==(e=f.enterpriseList)?void 0:e.growthIndex)&&(null==(t=f.enterpriseList)?void 0:t.growthIndex)?null==(s=f.enterpriseList)?void 0:s.growthIndex:"-"),1)]})),_:1})])),_:1}),a(w,{class:"recommend"},{default:n((()=>[a(w,{class:"expandIcon"}),a(w,null,{default:n((()=>{var e,t,s;return[u(" 扩张意愿指数 "),L("span",{style:{"margin-left":"8rpx",color:"#FF9500"}},h("0.0"!=(null==(e=f.enterpriseList)?void 0:e.expansionIndex)&&(null==(t=f.enterpriseList)?void 0:t.expansionIndex)?null==(s=f.enterpriseList)?void 0:s.expansionIndex:"-"),1)]})),_:1})])),_:1})])),_:1})]})),_:1})])),_:1})])),_:1}),a(w,{class:"line"}),0!=(null==(e=f.enterpriseList)?void 0:e.chainDTOS.length)?(s(),i(w,{key:0,class:"tag-p"},{default:n((()=>{var e,t,d,p,m,g,L,b;return[a(w,{class:"tag-container"},{default:n((()=>{var e;return[(s(!0),v(_,null,y(null==(e=f.enterpriseList)?void 0:e.chainDTOS,((e,t)=>(s(),i(w,{key:t,class:"tag-title",style:c({"background-color":f.selectedTagIndex==t?"#fff":"#f5f5f5",color:f.selectedTagIndex==t?"#417FFF":"#72787d","font-weight":f.selectedTagIndex==t?"600":"500"}),onClick:e=>C.selectTag(t)},{default:n((()=>[null!==e.chainName?(s(),i(w,{key:0,class:"chainName"},{default:n((()=>[u(h(e.chainName.replace("产业金脑·","")),1)])),_:2},1024)):o("",!0)])),_:2},1032,["style","onClick"])))),128))]})),_:1}),a(w,{class:"tag-it"},{default:n((()=>{var e;return[(s(!0),v(_,null,y(null==(e=f.enterpriseList)?void 0:e.chainDTOS,((e,t)=>(s(),i(w,{key:t},{default:n((()=>[f.selectedTagIndex==t?(s(),i(w,{key:0,class:"nodeNamesTag"},{default:n((()=>[(s(!0),v(_,null,y(e.chainNodeList,((e,t)=>(s(),i(w,{key:t,class:"tag-c",onClick:e=>C.pitchsecond(t)},{default:n((()=>[f.selectedTagIndex2==t?(s(),i(F,{key:0,text:e.nodeName,"custom-style":"background-color: #3370FF; border-color: #3370FF; color: #FFFFFF ;font-weight: 700;"},null,8,["text"])):(s(),i(F,{key:1,text:e.nodeName,"custom-style":"background-color: #eff4ff; border-color: #eff4ff; color: #417FFF ;font-weight: 700;"},null,8,["text"]))])),_:2},1032,["onClick"])))),128))])),_:2},1024)):o("",!0)])),_:2},1024)))),128))]})),_:1}),(null==(d=null==(t=null==(e=f.enterpriseList)?void 0:e.chainDTOS[f.selectedTagIndex])?void 0:t.chainNodeList[f.selectedTagIndex2])?void 0:d.productNames)?(s(),i(w,{key:0,class:r(f.StringTotal>38&&!f.showString?"limitationNameList":"nameList")},{default:n((()=>{var e,t;return[(s(!0),v(_,null,y(null==(t=null==(e=f.enterpriseList)?void 0:e.chainDTOS[f.selectedTagIndex])?void 0:t.chainNodeList[f.selectedTagIndex2].productNames,((e,t)=>(s(),i(w,{key:t,class:"nameItem"},{default:n((()=>[u(h(e),1)])),_:2},1024)))),128))]})),_:1},8,["class"])):o("",!0),(null==(g=null==(m=null==(p=f.enterpriseList)?void 0:p.chainDTOS[f.selectedTagIndex])?void 0:m.chainNodeList[f.selectedTagIndex2])?void 0:g.productNames)?(s(),v("div",{key:1},[f.StringTotal>38&&(null==(b=null==(L=f.enterpriseList)?void 0:L.chainDTOS[f.selectedTagIndex])?void 0:b.chainNodeList[f.selectedTagIndex2].productNames.length)>2?(s(),v("div",{key:0,class:"parent-container"},[a(S,{src:$,class:r(f.showString?"showIcon":"showIcons"),onClick:l[5]||(l[5]=e=>f.showString=!f.showString)},null,8,["class"])])):o("",!0)])):o("",!0)]})),_:1})):o("",!0),a(w,{style:{height:"20rpx"}}),a(w,{class:"detials"},{default:n((()=>[a(E,{class:"detialContent"},{default:n((()=>{var e,t,r;return[(null==(e=f.enterpriseList)?void 0:e.companyTouchpointMethods)?(s(),i(B,{key:0,title:"触达方式",thumb:"https://static.idicc.cn/cdn/zhaoShang/touch.svg",class:"detialHeader",open:"",border:!1},{default:n((()=>[a(w,{class:"content"},{default:n((()=>{var e;return[u(h(null==(e=f.enterpriseList)?void 0:e.companyTouchpointMethods),1)]})),_:1})])),_:1})):o("",!0),(null==(t=f.enterpriseList)?void 0:t.introduction)&&"-"!==(null==(r=f.enterpriseList)?void 0:r.introduction)?(s(),i(B,{key:1,open:"",title:"企业简介",thumb:"https://static.idicc.cn/cdn/zhaoShang/introduce.svg",class:"detialHeader",border:!1},{default:n((()=>[a(w,{class:"content"},{default:n((()=>{var e,t;return[(null==(e=f.enterpriseList)?void 0:e.introduction)&&"-"!==(null==(t=f.enterpriseList)?void 0:t.introduction)?(s(),i(w,{key:0,class:"introduction"},{default:n((()=>{var e;return[u(h(null==(e=f.enterpriseList)?void 0:e.introduction),1)]})),_:1})):o("",!0)]})),_:1})])),_:1})):o("",!0),a(B,{open:"",title:"基本信息",thumb:"https://static.idicc.cn/cdn/zhaoShang/baseInfo.svg",border:!1,class:"detialHeader"},{default:n((()=>[a(w,{class:"content"},{default:n((()=>[a(w,{class:"essentialinformation"},{default:n((()=>[a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"统一社会信用代码"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.unifiedSocialCreditCode,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.unifiedSocialCreditCode)||"-"),1)]})),_:1},8,["content"])]})),_:1}),a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"成立日期"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.registerDate,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.registerDate)||"-"),1)]})),_:1},8,["content"])]})),_:1})])),_:1}),a(w,{class:"essentialinformation"},{default:n((()=>[a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"企业规模"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.scale,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.scale)||"-"),1)]})),_:1},8,["content"])]})),_:1}),a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"融资轮次"),f.enterpriseList.enterpriseFinancingRoundsNames?(s(),i(z,{key:0,content:null==(e=f.enterpriseList)?void 0:e.enterpriseFinancingRoundsNames.join("、"),placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.enterpriseFinancingRoundsNames.join("、"))||"-"),1)]})),_:1},8,["content"])):(s(),v("span",{key:1,class:"result"},"-"))]})),_:1})])),_:1}),a(w,{class:"essentialinformation"},{default:n((()=>[a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"法定代表人"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.legalPerson,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.legalPerson)||"-"),1)]})),_:1},8,["content"])]})),_:1}),a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"注册资本"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.registeredCapital,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.registeredCapital)||"-"),1)]})),_:1},8,["content"])]})),_:1})])),_:1}),a(w,{class:"essentialinformation"},{default:n((()=>[a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"企业类型"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.enterpriseType,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.enterpriseType)||"-"),1)]})),_:1},8,["content"])]})),_:1}),a(w,{class:"one"},{default:n((()=>{var e;return[L("i",{class:"yuan"}),L("span",{class:"name"},"登记状态"),a(z,{content:null==(e=f.enterpriseList)?void 0:e.registerStatus,placement:"bottom"},{default:n((()=>{var e;return[L("span",{class:"result"},h((null==(e=f.enterpriseList)?void 0:e.registerStatus)||"-"),1)]})),_:1},8,["content"])]})),_:1})])),_:1}),a(w,{class:"essentialinformation"},{default:n((()=>[a(w,{class:"one"},{default:n((()=>[L("i",{class:"yuan"}),L("span",{class:"name"},"所在地区"),a(z,{content:f.enterpriseList.province!=f.enterpriseList.city?f.enterpriseList.province+f.enterpriseList.city+f.enterpriseList.area:f.enterpriseList.province+f.enterpriseList.area,placement:"bottom"},{default:n((()=>{var e,t;return[L("span",{class:"result"},[u(h(f.enterpriseList.province||"-"),1),(null==(e=f.enterpriseList)?void 0:e.province)!=(null==(t=f.enterpriseList)?void 0:t.city)?(s(),v("span",{key:0},h(f.enterpriseList.city),1)):o("",!0),u(h(f.enterpriseList.area),1)])]})),_:1},8,["content"])])),_:1}),a(w,{class:"one"},{default:n((()=>[L("i",{class:"yuan"}),L("span",{class:"name"},"所属行业"),a(z,{content:(f.enterpriseList.nationalStandardIndustry||"")+(f.enterpriseList.nationalStandardIndustryBig||"")+(f.enterpriseList.nationalStandardIndustryMiddle||"")+(f.enterpriseList.nationalStandardIndustrySmall||""),placement:"bottom"},{default:n((()=>[L("span",{class:"result"},h(f.enterpriseList.nationalStandardIndustry)+" "+h(f.enterpriseList.nationalStandardIndustryBig)+" "+h(f.enterpriseList.nationalStandardIndustryMiddle)+" "+h(f.enterpriseList.nationalStandardIndustrySmall),1)])),_:1},8,["content"])])),_:1})])),_:1}),a(w,{class:"essentialinformation"},{default:n((()=>[a(w,{class:"ones"},{default:n((()=>[L("i",{class:"yuan"}),L("span",{class:"name"},"注册地址"),a(z,{content:f.enterpriseList.enterpriseAddress,placement:"bottom"},{default:n((()=>[L("span",{class:"result"},h(f.enterpriseList.enterpriseAddress||"-"),1)])),_:1},8,["content"])])),_:1})])),_:1}),a(w,{class:"essentialinformations"},{default:n((()=>[a(w,{class:"ones"},{default:n((()=>[L("i",{class:"yuan"}),L("span",{class:"name"},"经营范围"),L("span",{class:"result"},h(f.enterpriseList.businessScope||"-"),1)])),_:1})])),_:1})])),_:1})])),_:1}),f.enterpriseList&&f.enterpriseList.associationList&&0!=f.enterpriseList.associationList.length?(s(),i(B,{key:2,title:"亲缘关系",thumb:"https://static.idicc.cn/cdn/zhaoShang/relation.svg",open:"",border:!1,class:"detialHeader"},{default:n((()=>[a(w,{class:"content"},{default:n((()=>[f.enterpriseList&&f.enterpriseList.associationList&&0!=f.enterpriseList.associationList.length?(s(),i(w,{key:0,class:"chamberBox"},{default:n((()=>{var e;return[(s(!0),v(_,null,y(null==(e=f.enterpriseList)?void 0:e.associationList,((e,t)=>{var i,n,l,r,c,d,p,m,g,f;return s(),v("div",{key:t,class:"associationItem"},[L("div",{class:"titleBox"},[a(S,{class:"us",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAYFBMVEUAAAAycf82cf8zcP8ycP8xcv8ycP8ycP82cP8zcP8ycP8xcP8zcP8zcf8ycP8zZv8zcP8zcP8zcP8zcP8zcP8zcf8ycf8zcP8xcf8ycv80dv8zgP8zcf8zcf80cP8zcP+ljzYqAAAAH3RSTlMATR7vfznfcBDPYEjpQSgF9L+vpp+bemRYMBMK3ce/9WK7bwAAALlJREFUOMvNkckWgjAMRZsOzLOIgkP+/y9tqgQlsHHF3b570vRFHYvERmg6vZvrCAN2T+jxQ7mdjziTQlnVUohZaADAScEiA55JCBnndxIq+UkzC2cS9P4SBeX5TUmyUMQz5Hq7SrCxL2GqdP3fJVoTauoGesPp9YoGmccFPK4WNTHF25BfXIxr6EJceuEURsiamYZG5Cy0KBjAw4KRAh1kZAEJsYRb7pFKAdQ3IPJofcjfGaZP1JF4Ab0VIphtaJg8AAAAAElFTkSuQmCC"}),u(" "+h(e.relateName)+" ",1),0!=(null==(i=null==e?void 0:e.positions)?void 0:i.length)&&null!=(null==e?void 0:e.positions)?(s(),v("div",{key:0,class:"longString"},"|")):o("",!0),(s(!0),v(_,null,y(null==e?void 0:e.positions,((e,t)=>(s(),v("div",{key:t,class:"chamberTag"},h(e),1)))),128))]),(null==e?void 0:e.ancestorHome)?(s(),v("div",{key:0,class:"Singleline"},[L("div",{class:"chamberKey"}," 籍贯： "),L("div",{class:"chamberValue"},h((null==e?void 0:e.ancestorHome)?null==e?void 0:e.ancestorHome:"-"),1)])):o("",!0),0!=(null==(n=e.schools)?void 0:n.length)&&null!=e.schools?(s(),v("div",{key:1,class:"Singleline"},[L("div",{class:"chamberKey"}," 毕业院校： "),L("div",{class:"chamberValue"},h(0!=(null==(l=null==e?void 0:e.schools)?void 0:l.length)&&null!=(null==e?void 0:e.schools)?null==(r=null==e?void 0:e.schools)?void 0:r.join("、"):"-"),1)])):o("",!0),0!=(null==(c=e.commerceNames)?void 0:c.length)&&null!=e.commerceNames?(s(),v("div",{key:2,class:"Singleline"},[L("div",{class:"chamberKey"}," 所属商会： "),L("div",{class:"chamberValue"},h(0!=(null==(d=null==e?void 0:e.commerceNames)?void 0:d.length)&&null!=(null==e?void 0:e.commerceNames)?null==(p=null==e?void 0:e.commerceNames)?void 0:p.join("、"):"-"),1)])):o("",!0),0!=(null==(m=e.alumniNames)?void 0:m.length)&&null!=(null==e?void 0:e.alumniNames)?(s(),v("div",{key:3,class:"Singleline"},[L("div",{class:"chamberKey"}," 所属校友会： "),L("div",{class:"chamberValue"},h(0!=(null==(g=null==e?void 0:e.alumniNames)?void 0:g.length)&&null!=(null==e?void 0:e.alumniNames)?null==(f=null==e?void 0:e.alumniNames)?void 0:f.join("、"):"-"),1)])):o("",!0)])})),128))]})),_:1})):o("",!0)])),_:1})])),_:1})):o("",!0),0!=f.businessDataList.length?(s(),i(B,{key:3,open:"",title:"企业动态",thumb:"https://static.idicc.cn/cdn/zhaoShang/news.svg",border:!1,class:"detialHeader"},{default:n((()=>[0!=f.businessDataList.length?(s(),i(w,{key:0,class:"content"},{default:n((()=>[a(w,{class:"chamberBox"},{default:n((()=>[(s(!0),v(_,null,y(f.businessDataList,((e,t)=>{var i,n;return s(),v("div",{key:t,class:"dynamicItem"},[L("div",{class:"buleCircle"},[L("div",{class:"kernel"})]),L("div",{class:"title"},h(e.eventTitle),1),L("div",{class:"publishDate"},h(e.publishDate),1),L("div",{class:"eventContent"},[L("span",null,h(e.Expand?e.eventContent:e.showEventContent+((null==(i=e.eventContent)?void 0:i.length)>38?"...":"")),1),(null==(n=e.eventContent)?void 0:n.length)>38?(s(),v("span",{key:0,style:{color:"#3370FF"},onClick:e=>C.RetractionExpansion(t)},h(e.Expand?"收起":"展开"),9,["onClick"])):o("",!0)])])})),128))])),_:1}),0!=f.businessDataList.length&&f.pages>f.pageNum?(s(),v("div",{key:0,class:"showMore",onClick:l[6]||(l[6]=(...e)=>C.showMore&&C.showMore(...e))},[u(" 查看更多 "),a(S,{class:"showMoreIcon",src:"https://static.idicc.cn/cdn/zhaoShang/showMore.svg"})])):o("",!0)])),_:1})):o("",!0)])),_:1})):o("",!0)]})),_:1})])),_:1}),a(w,{style:{height:"140rpx"}})]})),_:1})):o("",!0),L("div",{class:"bottomBtnBox",style:c({justifyContent:f.enterpriseList.historyRecommend&&1==f.IdentityType&&!f.isDefault&&f.orgName?"space-between":"center"})},[f.enterpriseList.historyRecommend?(s(),v("div",{key:0,class:"strategyBtn",onClick:l[7]||(l[7]=(...e)=>C.goDetail&&C.goDetail(...e))},"一企一策")):o("",!0),1==f.IdentityType&&!f.isDefault&&f.orgName?(s(),v("div",{key:1,class:r([f.enterpriseList.isInvestClue?"hasbeenIncluded":"noIncluded",C.getWidthClass()]),onClick:l[8]||(l[8]=e=>C.Intention(f.enterpriseList.isInvestClue))},h(f.enterpriseList.isInvestClue?"已纳入招商意向":"纳入招商意向"),3)):o("",!0)],4),a(j,{ref:"bringInto",onOperatePopup:C.operatePopup},null,8,["onOperatePopup"])])),_:1})],64)}],["__scopeId","data-v-dbce50f4"]]);export{M as default};
