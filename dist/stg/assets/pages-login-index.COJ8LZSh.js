import{s,l as a,p as t,q as e,m as o,z as l,A as i,a as c,f as n,w as r,F as d,i as u,o as p,g as h,B as m,c as g,d as w,b as f,e as k,t as v,a5 as x}from"./index-CBCsGYoT.js";import{_}from"./page-meta.BCoSkkXs.js";import{r as C}from"./uni-app.es.CZb2JZWI.js";import{_ as P}from"./uni-data-checkbox.lGJQWvI7.js";import{_ as b}from"./uni-popup.BLXBf1r-.js";import{_ as y}from"./uni-data-select.DWY_AY9U.js";import{q as U}from"./quicklogin.Da0jxcA0.js";import{_ as j}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-load-more.BMOBclrH.js";import"./uni-transition.Ckb0qY8x.js";import"./uni-icons.Dr3tmUrM.js";const L=j({data:()=>({checkbox:[],hobby:[{text:"",value:0}],passwordNum:0,sex:[{text:"是",value:!0},{text:"否",value:!1}],range:[{value:0,text:"开发环境"},{value:1,text:"测试环境"},{value:2,text:"预发环境"},{value:3,text:"正式环境"}],value:0,env:"https://pangutest.idicc.cn",touchNum:0,showPassword:!1,showPassword2:!1}),components:{quickLogin:U},onLoad(t){t.teamCode&&s("teamCode",t.teamCode),a("consent")&&(this.checkbox=[0]),this.showPassword2=a("showPasswordBtn")||!1,this.getIsShowPassword()},methods:{PasswordAffirm(){this.$refs.PasswordDialog.close()},changeShowPassword(a){this.showPassword2=a,s("showPasswordBtn",a),t({title:"设置成功！",icon:"none"})},changePassword(){this.resetTimer&&clearTimeout(this.resetTimer),this.passwordNum++,this.passwordNum>=5&&(this.$refs.PasswordDialog.open(),this.passwordNum=0),this.resetTimer=setTimeout((()=>{this.passwordNum=0}),1e3)},getIsShowPassword(){this.$api.showPasswordAPI({method:"get"}).then((s=>{var a;"SUCCESS"==s.code&&(this.showPassword=null==(a=s.result)?void 0:a.miniProgramPasswordLogin)}))},Unchecked(){t({title:"请勾选用户协议",icon:"error"})},go(s){"agreement"==s?e({url:`/pages/login/components/${s}`}):o({url:`/pages/login/components/${s}`})},NoLogin(){o({url:"/pages/repository/capacity"})},selectEnv(a){0==this.value?(s("portUrl","https://pangudev.idicc.cn"),s("h5Url","https://staticdev.idicc.cn/static/ai/index.html#/")):1==this.value?(s("portUrl","https://pangutest.idicc.cn"),s("h5Url","https://statictest.idicc.cn/static/ai/index.html#/")):2==this.value?(s("portUrl","https://pangustg.idicc.cn"),s("h5Url","https://staticstg.idicc.cn/static/ai/index.html#/")):(s("portUrl","https://pangu.idicc.cn"),s("h5Url","https://static.idicc.cn/static/ai/index.html#/")),s("value",a),t({title:"切换成功",icon:"none"})},changeEnv(){this.value=a("value"),this.env=a("portUrl"),this.touchNum++,setTimeout((()=>{this.touchNum>=3&&(this.$refs.inputDialog.open(),this.touchNum=0)}),250)},save(){this.$refs.inputDialog.close()},cancel(){this.$refs.inputDialog.close()}}},[["render",function(s,a,t,e,o,U){const j=C(l("page-meta"),_),L=u,N=C(l("uni-data-checkbox"),P),S=x,$=i("quickLogin"),D=C(l("uni-popup"),b),T=C(l("uni-data-select"),y);return p(),c(d,null,[n(j,{"page-style":"background-color: #FAFCFF"}),n(L,null,{default:r((()=>[n(L,{class:"box"},{default:r((()=>[n(L,{class:"t1"},{default:r((()=>[h("快捷登录")])),_:1}),n(L,{class:"t2"},{default:r((()=>[h("未注册的手机号将自动注册并登录")])),_:1}),n(L,{class:"t3"},{default:r((()=>[n(N,{multiple:"",modelValue:o.checkbox,"onUpdate:modelValue":a[0]||(a[0]=s=>o.checkbox=s),localdata:o.hobby},null,8,["modelValue","localdata"]),n(L,{class:"agreement"},{default:r((()=>[h("阅读并同意 "),m("span",{onClick:a[1]||(a[1]=s=>U.go("agreement"))},"《用户协议》")])),_:1})])),_:1}),0==this.checkbox.length?(p(),g(S,{key:0,class:"ClickLogin",type:"primary",onClick:U.Unchecked},{default:r((()=>[h("本机一键登录")])),_:1},8,["onClick"])):(p(),g($,{key:1,blue:!0})),o.showPassword||o.showPassword2?(p(),g(L,{key:2,class:"ClickLogin2"},{default:r((()=>[m("span",{onClick:a[2]||(a[2]=s=>U.go("passwordLogin"))},"使用账号密码登录")])),_:1})):w("",!0),n(L,{class:"else"},{default:r((()=>[m("span",{class:"cancelLogin",onClick:a[3]||(a[3]=s=>U.NoLogin())},"取消登录")])),_:1})])),_:1}),n(L,{class:"rightClick",onClick:U.changePassword},{default:r((()=>[n(D,{ref:"PasswordDialog",type:"dialog","mask-click":!1},{default:r((()=>[m("div",{class:"Passwordbox"},[m("h1",{style:{"margin-top":"32rpx","margin-bottom":"32rpx"}},"是否展示账号密码登录"),(p(!0),c(d,null,f(o.sex,((s,a)=>(p(),c("div",{onClick:a=>U.changeShowPassword(s.value),class:"ShowTimeItem",key:s.id},[m("div",{class:k(o.showPassword2==s.value?"circle-with-dot":"circle")},null,2),h(" "+v(s.text),1)],8,["onClick"])))),128)),n(S,{class:"bs3",type:"primary",onClick:U.PasswordAffirm},{default:r((()=>[h("确认")])),_:1},8,["onClick"])])])),_:1},512)])),_:1},8,["onClick"]),n(L,{class:"clickArea",onClick:U.changeEnv},{default:r((()=>[n(D,{ref:"inputDialog",type:"dialog","mask-click":!1},{default:r((()=>[m("div",{class:"popbox"},[m("h2",{style:{"margin-top":"4rpx"}},"环境切换"),n(L,{class:"clickBox"},{default:r((()=>[n(L,{class:"tips"},{default:r((()=>[h(" tips: 切换环境成功后，点击右上角的···，重新进入小程序即可生效 ")])),_:1}),n(L,{class:"env"},{default:r((()=>[n(L,null,{default:r((()=>[h(" 当前环境：")])),_:1}),h(v(o.env),1)])),_:1}),n(L,{class:"clickBoxItem"},{default:r((()=>[h(" 点击切换环境 ")])),_:1}),n(T,{style:{width:"500rpx","margin-top":"8%"},clear:!1,modelValue:o.value,"onUpdate:modelValue":a[4]||(a[4]=s=>o.value=s),onChange:U.selectEnv,localdata:o.range},null,8,["modelValue","onChange","localdata"])])),_:1}),m("div",{class:"popbtns"},[n(S,{class:"bs",type:"default",onClick:U.cancel},{default:r((()=>[h("取消")])),_:1},8,["onClick"]),n(S,{class:"bs2",type:"primary",onClick:U.save},{default:r((()=>[h("确认")])),_:1},8,["onClick"])])])])),_:1},512)])),_:1},8,["onClick"])])),_:1})],64)}],["__scopeId","data-v-22657350"]]);export{L as default};
