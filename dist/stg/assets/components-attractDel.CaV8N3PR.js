import{l as a,s,A as t,a as e,B as l,t as n,d as i,F as c,b as o,H as d,I as p,f as r,o as m,e as v,g as u,C as y}from"./index-CBCsGYoT.js";import{t as I}from"./index.D5F8338M.js";import{a as f,c as w,s as k,f as h,t as g,b,e as C,d as x}from"./statement.CEtP29ep.js";import{g as L}from"./utils.61Hi-B7M.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";import"./uni-popup.BLXBf1r-.js";import"./uni-transition.Ckb0qY8x.js";import"./uni-app.es.CZb2JZWI.js";import"./uni-easyinput.D_LnJWIZ.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-file-picker.ui7Yqy_z.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-datetime-picker.DkX7SHFR.js";import"./uni-forms.BsccYHPu.js";import"./uni-data-checkbox.lGJQWvI7.js";import"./uni-load-more.BMOBclrH.js";const j=N({components:{tabBar:I,appeal:f,chargeback:w,settle:k,follow:h,todosth:g,comment:b,examine:C,statement:x},data:()=>({title:"哒达助招",dataInfo:{processList:[],reminds:[]},identity:1,state:"",tabIds:1,tabList:[{id:1,name:"进度信息"},{id:2,name:"催办信息"}]}),onLoad(t){this.identity=a("identity"),L("token")&&s("token",L("token")),L("state")&&(this.state=L("state")),L("id")&&this.getDel(L("id"))},methods:{changtabContent(a){this.tabIds=a},previewImg(a,s){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"showAlert",value:{type:"ImageViewer",data:{images:a,imageIndex:s}},module:"Investment"}))},updataList(){this.getDel(this.dataInfo.id)},getDel(a){1==this.identity?this.$api.closeDelAPI({method:"get",data:{entrustId:a}}).then((a=>{this.dataInfo=a.result,this.state=a.result.state,this.dataInfo.processList||(this.dataInfo.processList=[])})):this.$api.workerdetailAPI({method:"get",data:{entrustId:a}}).then((a=>{this.dataInfo=a.result,this.state=a.result.state,this.dataInfo.processList||(this.dataInfo.processList=[])}))},statementFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"Settle",data:a},module:"Investment"}))},examineFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"Examine",data:a},module:"Investment"}))},commentFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"CommentModal",data:a},module:"Investment"}))},todosthFn(a){var s;a.receiveContact=a.logs[0].claim.contact,null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{RNMethodName:"reminderPost",data:a},alertName:"催办",title:"该订单已被产业顾问"+a.receiveContact+"认领，但未提交跟进信息，是否进行催办？"}))},followFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"Follow",data:a},module:"Investment"}))},settleFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"Settle",data:a},module:"Investment"}))},appealFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"Appeal",data:a},module:"Investment"}))},chargebackFn(a){var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"showAlert",value:{type:"ChargeBack",data:a},module:"Investment"}))}}},[["render",function(a,s,I,f,w,k){var h;const g=y,b=t("chargeback"),C=t("appeal"),x=t("follow"),L=t("todosth"),N=t("comment"),j=t("examine"),T=t("statement");return m(),e("div",null,[l("div",{class:"box33"},[l("div",{class:"headText"}," 委托信息 "),l("div",{class:"attDelBox"},[l("div",{class:"row"},[l("span",{class:"key"},"订单号："),l("span",{class:"value"},n((null==(h=w.dataInfo)?void 0:h.orderSn)||""),1)]),l("div",{class:"row"},[l("span",{class:"key"},"意向企业："),l("span",{class:"value"},n(w.dataInfo.enterprise),1)]),l("div",{class:"row"},[l("span",{class:"key"},"企业社会信用代码："),l("span",{class:"value"},n(w.dataInfo.enterpriseUniCode),1)]),l("div",{class:"row"},[l("span",{class:"key"},"发起委托时间："),l("span",{class:"value"},n(w.dataInfo.startDatetime),1)]),l("div",{class:"row"},[l("span",{class:"key"},"期望对接时间："),l("span",{class:"value"},n(w.dataInfo.exceptedDatetime),1)]),w.dataInfo.payOrgName?(m(),e("div",{key:0,class:"row"},[l("span",{class:"key"},"委托单位："),l("span",{class:"value"},n(w.dataInfo.payOrgName),1)])):i("",!0),l("div",{class:"row"},[l("span",{class:"key"},"招商对接人："),l("span",{class:"value"},n(w.dataInfo.contact),1)]),l("div",{class:"row"},[l("span",{class:"key"},"联系方式："),l("span",{class:"value"},n(w.dataInfo.contactPhone),1)]),l("div",{class:"row"},[l("span",{class:"key"},"招商要求："),l("span",{class:"value"},n(w.dataInfo.note),1)]),l("div",{class:"row"},[l("span",{class:"key"},"金额："),l("span",{class:"value"},n(1==w.identity?w.dataInfo.amount:w.dataInfo.receiveAmount),1)])]),0==w.dataInfo.reminds.length?(m(),e("div",{key:0,style:{"margin-bottom":"26rpx"},class:"headText"}," 进度信息 ")):(m(),e("div",{key:1,class:"tabListBox"},[(m(!0),e(c,null,o(w.tabList,((a,s)=>(m(),e("div",{onClick:s=>k.changtabContent(a.id),key:a.id,class:v(w.tabIds==a.id?"tabItems":"tabItem")},n(a.name),11,["onClick"])))),128))])),d(l("div",{class:"contentBox"},[(m(!0),e(c,null,o(w.dataInfo.logs,((a,s)=>(m(),e("div",{key:s,class:"contentItemBox"},[l("div",{class:"Time"},[l("div",{class:"circle1"}),l("span",{class:"remindTime"},n(a.startDatetime||a.gmtCreate),1),l("span",{class:"typeText"},[l("span",{class:"dot"}),u(n(a.statusName),1)])]),"audit"==a.childObjectText&&a.tip?(m(),e("div",{key:0,style:{"font-weight":"400","font-size":"30rpx",color:"#FF0000","margin-left":"36rpx","margin-bottom":"16rpx"}},[l("span",null,"（超过5天，系统自动审批通过）")])):i("",!0),"apply"==a.childObjectText&&a.apply?(m(),e("div",{key:1,class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"申请人：")]),l("span",{class:"value"},n(a.apply.applyName),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"结单说明：")]),l("span",{class:"value"},n(a.apply.note),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"附件：")]),l("div",{class:"annex"},[(m(!0),e(c,null,o(a.apply.attachUrls,((s,t)=>(m(),e("div",{key:t,onClick:s=>k.previewImg(a.apply.attachUrls,t)},[r(g,{src:s,class:"annexImg"},null,8,["src"])],8,["onClick"])))),128))])])])):i("",!0),"audit"==a.childObjectText&&a.audit?(m(),e("div",{key:2,class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"审批人：")]),l("span",{class:"value"},n(a.audit.auditName),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"是否通过：")]),l("span",{class:"value"},n(a.audit.auditStatusName),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"审批说明：")]),l("span",{class:"value"},n(a.audit.note||"无"),1)])])):i("",!0),"comment"==a.childObjectText&&a.comment?(m(),e("div",{key:3,class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"委托人：")]),l("span",{class:"value"},n(a.comment.name),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"评论信息：")]),l("span",{class:"value"},n(a.comment.note),1)])])):i("",!0),"claim"==a.childObjectText&&a.claim?(m(),e("div",{key:4,class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"认领人：")]),l("span",{class:"value"},n(a.claim.contact),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"联系方式：")]),l("span",{class:"value"},n(a.claim.contactPhone),1)])])):i("",!0),"follow"==a.childObjectText&&a.follow?(m(),e("div",{key:5,class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"提交人：")]),l("span",{class:"value"},n(a.follow.createBy),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"企业对接人：")]),l("span",{class:"value"},n(a.follow.enterpriseContact),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"职务：")]),l("span",{class:"value"},n(a.follow.contactPosition),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"联系方式：")]),l("span",{class:"value"},n(a.follow.contactPhone),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"跟进概述：")]),l("span",{class:"value"},n(a.follow.note),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"附件：")]),a.follow.attachUrls?(m(),e("div",{key:0,class:"annex"},[(m(!0),e(c,null,o(a.follow.attachUrls,((s,t)=>(m(),e("div",{key:t,onClick:s=>k.previewImg(a.follow.attachUrls,t)},[r(g,{src:s,class:"annexImg"},null,8,["src"])],8,["onClick"])))),128))])):i("",!0)])])):i("",!0)])))),128))],512),[[p,1==w.tabIds]]),d(l("div",{class:"contentBox"},[(m(!0),e(c,null,o(w.dataInfo.reminds,((a,s)=>(m(),e("div",{key:s,class:"contentItemBox"},[l("div",{class:"Time"},[l("div",{class:"circle1"}),l("span",{class:"remindTime"},n(a.remindTimeStr),1),l("span",{class:"typeText"},[l("span",{class:"dot"}),u(n(a.type),1)])]),l("div",{class:"contentItem"},[l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"委托人：")]),l("span",{class:"value"},n(a.createBy),1)]),l("div",{class:"Itemline"},[l("div",{class:"Itemleft"},[l("span",{class:"GrayCircle"}),l("span",{class:"key"},"催办信息：")]),l("span",{class:"value"},n(a.note),1)])])])))),128))],512),[[p,2==w.tabIds]]),l("div",{style:{height:"150rpx"}})]),1!=w.identity||1!=w.state&&2!=w.state&&3!=w.state&&9!=w.state?i("",!0):(m(),e("div",{key:0,class:"belowBtns"},[1==w.state?(m(),e("div",{key:0,onClick:s[0]||(s[0]=a=>k.chargebackFn(w.dataInfo)),class:"T4BtnDe"},"退单")):i("",!0),2==w.state?(m(),e("div",{key:1,onClick:s[1]||(s[1]=a=>k.todosthFn(w.dataInfo)),class:"T4BtnDe"},"催办")):i("",!0),3==w.state?(m(),e("div",{key:2,onClick:s[2]||(s[2]=a=>k.commentFn(w.dataInfo)),class:"T4BtnDe"},"评论")):i("",!0),9==w.state?(m(),e("div",{key:3,onClick:s[3]||(s[3]=a=>k.examineFn(w.dataInfo)),class:"T4BtnDe"},"结单审批")):i("",!0)])),2!=w.identity||2!=w.state&&3!=w.state&&11!=w.state&&12!=w.state?i("",!0):(m(),e("div",{key:1,class:"belowBtns"},[11==w.state||12==w.state?(m(),e("div",{key:0,onClick:s[4]||(s[4]=a=>k.appealFn(w.dataInfo)),class:"T4BtnDe"},"申诉 ")):i("",!0),3==w.state||11==w.state||12==w.state?(m(),e("div",{key:1,onClick:s[5]||(s[5]=a=>k.statementFn(w.dataInfo)),class:"T4BtnDe"},"结单 ")):i("",!0),2==w.state||3==w.state||12==w.state||11==w.state?(m(),e("div",{key:2,onClick:s[6]||(s[6]=a=>k.followFn(w.dataInfo)),class:"T4Btn"},n(2==w.state?"跟进":"继续跟进"),1)):i("",!0)])),r(b,{onUpdataList:k.updataList,ref:"chargeback"},null,8,["onUpdataList"]),r(C,{onUpdataList:k.updataList,ref:"appeal"},null,8,["onUpdataList"]),r(x,{onUpdataList:k.updataList,ref:"follow"},null,8,["onUpdataList"]),r(L,{onUpdataList:k.updataList,ref:"todosth"},null,8,["onUpdataList"]),r(N,{onUpdataList:k.updataList,ref:"comment"},null,8,["onUpdataList"]),r(j,{onUpdataList:k.updataList,ref:"examine"},null,8,["onUpdataList"]),r(T,{onUpdataList:k.updataList,ref:"statement"},null,8,["onUpdataList"])])}],["__scopeId","data-v-f9923192"]]);export{j as default};
