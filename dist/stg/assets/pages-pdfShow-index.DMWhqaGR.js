import{l as t,Y as a,U as e,Z as s,a6 as i,a7 as o,$ as l,z as n,c as d,w as p,i as r,o as u,f as c,B as h,g as f,t as m,a5 as g}from"./index-CBCsGYoT.js";import{_ as v}from"./uni-popup.BLXBf1r-.js";import{r as w}from"./uni-app.es.CZb2JZWI.js";import{v as _}from"./webviewUrl.D8mk1f89.js";import{_ as $}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.Ckb0qY8x.js";const x=$({data:()=>({url:"",popupText:"",options:null}),onLoad(a){this.showLoading(),this.tokenData=t("token"),this.options=a,this.Download(a)},onHide(t){a(),e({delta:1})},methods:{showLoading(){s({title:"文件下载中"})},closeView(){this.$refs.popup.close(),e({delta:1})},tryAgin(){this.$refs.popup.close(),this.showLoading(),this.Download(this.options)},Download(t){let a=decodeURI(null==t?void 0:t.id).split("_"),e=`${_}showPdf`;this.url="true"===(null==t?void 0:t.isEditor)?`${e}?token=${this.tokenData}&id=${a[0]}&isEditor=${null==t?void 0:t.isEditor}`:`${e}?token=${this.tokenData}&id=${a[0]}`;let s=a[1],o=null==t?void 0:t.time;i({url:"https://pangudev.idicc.cn/pdf/render-url",data:{url:this.url,id:a[0],time:o,filename:s+".pdf",json:!0,options:{screen:!0,page:{format:"A4",landscape:!1,height:null,width:null,printBackground:!0,displayHeaderFooter:!1,margin:{top:"45px",bottom:"45px"}}}},header:{},method:"POST",success:t=>{var a,e;500===(null==t?void 0:t.statusCode)||""===(null==(a=null==t?void 0:t.data)?void 0:a.content)||"systemBusy"===(null==(e=null==t?void 0:t.data)?void 0:e.status)?this.setFailState():this.onPreviewImage(t.data,s)},fail:t=>{this.setFailState()}})},setFailState(){a(),this.popupText="文件下载失败，请再试一次",setTimeout((()=>{this.$refs.popup.open()}),500)},loadSuccess(){a()},jumpToChat(){let t=`${_}showPdf`;this.url=`${t}?token=${this.tokenData}`},handlePostMessage(t){var a,e,s,i;this.onPreviewImage(null==(e=null==(a=t.detail)?void 0:a.data[0])?void 0:e.imageData,null==(i=null==(s=t.detail)?void 0:s.data[0])?void 0:i.imageName)},onPreviewImage(t,e){let s=null==t?void 0:t.content;var i=wx.env.USER_DATA_PATH+`/${e}.pdf`,n=o(s.replace("data:application/pdf;filename=generated.pdf;base64,",""));wx.getFileSystemManager().writeFile({filePath:i,data:n,success:t=>{l({filePath:i,showMenu:!0,success:function(t){a()},fail(t){a()}})}})}}},[["render",function(t,a,e,s,i,o){const l=r,_=g,$=w(n("uni-popup"),v);return u(),d(l,{class:"page-section page-section-gap"},{default:p((()=>[c(l,{class:"pdgBg"},{default:p((()=>[h("img",{class:"headportrait",src:"https://static.idicc.cn/cdn/aiChat/applet/viewBg.png",alt:""})])),_:1}),c($,{class:"pops",ref:"popup",type:"center",animation:!1,"border-radius":"10px 10px 0 0"},{default:p((()=>[c(l,{class:"popView"},{default:p((()=>[c(l,{class:"tipsTitle"},{default:p((()=>[c(l,{class:"tipsText"},{default:p((()=>[f("提示")])),_:1}),c(l,{class:"close",onClick:o.closeView},{default:p((()=>[f("X")])),_:1},8,["onClick"])])),_:1}),c(l,{class:"errorText"},{default:p((()=>[f(m(i.popupText),1)])),_:1}),c(_,{onClick:o.tryAgin,class:"closeBtn"},{default:p((()=>[f("再试一次")])),_:1},8,["onClick"])])),_:1})])),_:1},512)])),_:1})}],["__scopeId","data-v-69f9a50c"]]);export{x as default};
