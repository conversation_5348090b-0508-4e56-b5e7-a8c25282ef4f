import{l as e,s as t,m as a,c as i,w as s,i as n,o,f as l,g as d,t as r,a as c,d as p,n as m,B as u,b as h,e as g,F as f,C as v}from"./index-CBCsGYoT.js";import{g as w}from"./utils.61Hi-B7M.js";import{_ as k}from"./history.BKBs_WXS.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";const I=y({data:()=>({statusBarHeight:e("statusBarHeight"),token:"",dataInfo:{},timeDimensionId:1,timeDimension:[{name:"今日",id:1},{name:"近一周",id:7},{name:"近一个月",id:30},{name:"近三个月",id:90},{name:"全部",id:0}]}),onLoad(a){this.token=e("token");let i=(null==a?void 0:a.id)||"";w("token")&&(this.token=w("token"),t("token",this.token)),w("token")&&(i=w("id")),this.getDel(i)},methods:{go(){var t;if(!e("token"))return a({url:"/pages/login/index"});null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{type:"deriveExcel"},path:"history"}))},changeDimension(t){if(!e("token"))return a({url:"/pages/login/index"});this.timeDimensionId=t},getDel(e){let t={id:e};this.$api.capabilityPoolDetailAPI({data:t}).then((e=>{this.dataInfo=e.result}))},goBack(){},DownloadFn(){if(!e("token"))return a({url:"/pages/login/index"});this.$api.enterpriseExportAPI({data:{days:this.timeDimensionId},method:"post"}).then((e=>{var t,a;"SUCCESS"==e.code&&(null==(t=e.result)?void 0:t.ossUrl)&&(null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"exportFile",value:{name:e.result.reportName,fileType:"xlsx",url:e.result.ossUrl}})))}))},generateExcel(e){let t={enterpriseName:e.enterpriseName,uniCode:e.uniCode,enterpriseId:e.enterpriseId,recommendRegionCode:e.recommendRegionCode||""};this.$api.reportGenerateAPI({data:t,method:"post"}).then((t=>{var a;null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{url:"previewPdf",name:"报告预览",params:{reportId:t.result,type:"pdf",enterpriseName:e.enterpriseName}},path:"webViewPage"}))}))}}},[["render",function(e,t,a,w,y,I){const x=v,D=n;return o(),i(D,null,{default:s((()=>[l(x,{src:"https://static.idicc.cn/cdn/aiChat/applet/bg.png",class:"homeBgc"}),l(D,{class:"topHeader",style:m(`top: ${y.statusBarHeight}px`)},{default:s((()=>[l(D,{class:"header"},{default:s((()=>[l(x,{src:y.dataInfo.iconPath,class:"icon"},null,8,["src"]),l(D,{class:"mgl-20"},{default:s((()=>[l(D,{class:"span1"},{default:s((()=>[d(r(y.dataInfo.name),1)])),_:1}),l(D,{class:"span2"},{default:s((()=>[d("来自：艾瑞数云")])),_:1})])),_:1}),y.token?(o(),c("div",{key:0,onClick:t[0]||(t[0]=e=>I.go()),class:"historyBox"},[l(x,{class:"icon2",src:k}),d(" 我的报告 ")])):p("",!0)])),_:1}),l(D,{class:"headertext"},{default:s((()=>[d(r(y.dataInfo.content),1)])),_:1})])),_:1},8,["style"]),l(D,{class:"timeDimensionBox"},{default:s((()=>[u("div",{class:"timeTitle"},"请选择时间维度"),(o(!0),c(f,null,h(y.timeDimension,((e,t)=>(o(),c("div",{onClick:t=>I.changeDimension(e.id),class:"timeItem",key:e.id},[d(r(e.name)+" ",1),u("div",{class:g(y.timeDimensionId==e.id?"circle-with-dot":"circle")},null,2)],8,["onClick"])))),128))])),_:1}),u("div",{onClick:t[1]||(t[1]=(...e)=>I.DownloadFn&&I.DownloadFn(...e)),class:"deriveBtn"},"导出生成")])),_:1})}],["__scopeId","data-v-e770501a"]]);export{I as default};
