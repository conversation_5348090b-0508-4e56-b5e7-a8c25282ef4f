import{Z as e,l as s,s as t,Y as o,c as i,w as l,i as a,o as n,f as r,d as u,a1 as d}from"./index-CBCsGYoT.js";import{v as m}from"./webviewUrl.D8mk1f89.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";const c=p({data:()=>({webviewStyles:{progress:{color:"#FF3333"}},url:"",options:{type:1,id:null}}),onLoad(t){e({title:"加载中"}),t.tabType&&(this.options.type=t.tabType),t.id&&(this.options.id=t.id),this.token=s("token"),this.jumpToChat()},onShow(e){s("isrefreshMember")&&(this.url="",setTimeout((()=>{this.url=`${m}memberCenter?token=${this.token}&abilityName=${(new Date).getTime()}`,t("isrefreshMember",!1)}),500))},methods:{loadSuccess(){o()},jumpToChat(){this.url=`${m}memberCenter?token=${this.token}&type=${this.options.type}&id=${this.options.id}&abilityName=${(new Date).getTime()}`,console.log(this.url)}}},[["render",function(e,s,t,o,m,p){const c=a,h=d;return n(),i(c,null,{default:l((()=>[r(c,{class:"skeleton"},{default:l((()=>[r(c,{class:"memberBg"}),r(c,{class:"skeletonlist"},{default:l((()=>[r(c,{class:"skeletonBig"}),r(c,{class:"skeletonBig"}),r(c,{class:"skeletonBig"}),r(c,{class:"skeletonBig"}),r(c,{class:"skeletonBig"})])),_:1})])),_:1}),""!==m.url?(n(),i(h,{key:0,src:m.url,onLoad:p.loadSuccess,"webview-styles":m.webviewStyles},null,8,["src","onLoad","webview-styles"])):u("",!0)])),_:1})}],["__scopeId","data-v-a4692493"]]);export{c as default};
