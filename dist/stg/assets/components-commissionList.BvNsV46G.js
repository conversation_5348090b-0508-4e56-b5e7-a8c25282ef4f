import{l as t,z as s,A as e,a,f as i,B as o,F as n,b as l,o as r,t as d,d as c,C as p}from"./index-CBCsGYoT.js";import{_ as m}from"./page-meta.BCoSkkXs.js";import{r as u}from"./uni-app.es.CZb2JZWI.js";import{t as y}from"./index.D5F8338M.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const f=h({components:{tabBar:y},data:()=>({title:"佣金明细",dataInfo:[],bizType:1}),onLoad(t){t.bizType&&(this.bizType=t.bizType),this.getWithdrawalRecord()},methods:{getWithdrawalRecord(){let s=t("userId");this.$api.incomeFlowListAPI({data:{userId:s,bizType:this.bizType,pageNum:1,pageSize:9999}}).then((t=>{this.dataInfo=this.classifyByMonth(t.result.records)}))},classifyByMonth(t){const s=[];return t.forEach((t=>{const e=t.month,a=s.findIndex((t=>t.time===e));-1===a?s.push({time:e,list:[t]}):s[a].list.push(t)})),s}}},[["render",function(t,y,h,f,v,b){const g=u(s("page-meta"),m),z=e("tabBar"),I=p;return r(),a(n,null,[i(g,{"page-style":"background-color: #FAFCFF"}),o("div",null,[i(z,{title:v.title},null,8,["title"]),o("div",{class:"box"},[(r(!0),a(n,null,l(v.dataInfo,((t,s)=>(r(),a("div",{key:s},[o("div",{class:"times"},d(t.time),1),o("div",{class:"contentBox"},[(r(!0),a(n,null,l(t.list,((s,e)=>(r(),a("div",{key:e,class:"content"},[o("div",{class:"top"},[o("div",{class:"left"},[i(I,{src:0==s.type?"../static/user/income0.png":"../static/user/income1.png",class:"succeedIcon"},null,8,["src"]),1==v.bizType?(r(),a("div",{key:0,class:"time"},d(0==t.type?"团队结单分成":"委托单结单收入"),1)):c("",!0),2==v.bizType?(r(),a("div",{key:1,class:"time"},d(0==t.type?"出海智服结单分成":"出海智服结单收入"),1)):c("",!0)]),o("div",{class:"unit"},"+"+d(s.amount),1)]),o("div",{class:"bottom"},[o("div",null,"订单号："+d(s.orderSn),1),o("div",null,d(s.finishDatetime),1)])])))),128))])])))),128))])])],64)}],["__scopeId","data-v-4810a517"]]);export{f as default};
