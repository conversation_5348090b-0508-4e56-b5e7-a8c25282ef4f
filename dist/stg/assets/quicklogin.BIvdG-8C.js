import{T as e,l as o,s as t,m as s,p as n,z as i,o as l,a as r,f as a,w as d,g as c,e as u,B as p,a5 as g,C as h}from"./index-HcMwrp5e.js";import{_ as m}from"./uni-popup.BdZPMDVN.js";import{r as C}from"./uni-app.es.DFp0WTX7.js";import{_ as f}from"./_plugin-vue_export-helper.BCo6x5W8.js";const y=f({props:{blue:{type:Boolean,default:!1}},methods:{bindingId(o){let t=this;e({success(e){t.$api.syncUnion({data:{code:e.code},token:o,method:"post"}).then((e=>{})).catch((e=>{}))}})},closeFn(){this.$refs.Dialog.close()},closeFn2(){this.$refs.Dialog2.close()},whereTogo(){o("teamCode"),this.$api.userMessage().then((e=>{var o,n;t("userIdentityType",null==(o=e.result.userCompletionInfoDO)?void 0:o.userIdentity),5==(null==(n=e.result.userCompletionInfoDO)?void 0:n.userIdentity)?t("identity",2):t("identity",1),s({url:"/pages/repository/capacity"})}))},getorgCode(e){this.$api.ownList({token:e}).then((e=>{var o,s,n;t("orgCode",null==(o=e.result.selected)?void 0:o.orgCode),t("orgName",null==(s=e.result.selected)?void 0:s.orgName),t("isDefault",null==(n=e.result.selected)?void 0:n.isDefault)}))},getcode(i){const l=this;if("getPhoneNumber:ok"!=i.detail.errMsg)return n({title:"获取授权失败",icon:"none"});n({title:"授权成功，正在登录...",icon:"none"}),e({success(e){l.$api.login({method:"POST",data:{phoneCode:null==i?void 0:i.detail.code,loginType:"XA_AI",jsCode:e.code,invitationCode:o("invitationCode")||""}}).then((e=>{n({title:"登陆成功！",icon:"none"}),"SUCCESS"==e.code&&l.$api.identityTypeAPI({token:e.result.token,method:"GET"}).then((o=>{var i,r,a,d;if(1!=(null==(i=o.result)?void 0:i.identityCode)&&5!=(null==(r=o.result)?void 0:r.identityCode))return n({title:"身份限制,请联系管理员",icon:"none"});t("token",e.result.token),t("phone",e.result.username),t("userId",e.result.userId),t("consent",!0),l.getorgCode(e.result.token),l.bindingId(e.result.token),!0===(null==(a=o.result)?void 0:a.needCompletion)?s({url:`/pages/user/perfectionInfo?identityCode=${null==(d=o.result)?void 0:d.identityCode}`}):l.whereTogo()})).catch((e=>{"NOT_ALLOWED"==e.code&&l.whereTogo()}))})).catch((e=>{"ACCOUNT_UNAUTHORIZED"==e.code?setTimeout((()=>{l.$refs.Dialog.open()}),1e3):"41005"==e.code&&setTimeout((()=>{l.$refs.Dialog2.open()}),1e3)}))}})}}},[["render",function(e,o,t,s,n,f){const y=g,v=h,I=C(i("uni-popup"),m);return l(),r("div",null,[a(y,{class:u(t.blue?"ClickLogin":"ClickLogins"),"open-type":"getPhoneNumber",type:"primary",onGetphonenumber:f.getcode},{default:d((()=>[c("本机一键登录")])),_:1},8,["class","onGetphonenumber"]),a(I,{ref:"Dialog",type:"dialog"},{default:d((()=>[a(v,{"show-menu-by-longpress":"true",class:"contrastImg",src:"https://static.idicc.cn/cdn/aiChat/serviceQRCode.png"}),p("div",{onClick:o[0]||(o[0]=(...e)=>f.closeFn&&f.closeFn(...e)),class:"close"})])),_:1},512),a(I,{ref:"Dialog2",type:"dialog"},{default:d((()=>[a(v,{"show-menu-by-longpress":"true",class:"contrastImg",src:"https://static.idicc.cn/cdn/aiChat/applet/loginError2.png"}),p("div",{onClick:o[1]||(o[1]=(...e)=>f.closeFn2&&f.closeFn2(...e)),class:"close"})])),_:1},512)])}],["__scopeId","data-v-69a68f8c"]]);export{y as q};
