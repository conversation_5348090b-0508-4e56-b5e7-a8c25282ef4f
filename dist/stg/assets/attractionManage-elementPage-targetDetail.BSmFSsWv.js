import{o as e,c as s,w as t,a,F as l,b as n,e as i,n as c,f as r,g as o,t as d,h as p,i as u,z as m,V as v,B as I,l as f,s as y,a0 as h,A as k,d as g,H as x,I as w,C as b}from"./index-CBCsGYoT.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{r as _}from"./uni-app.es.CZb2JZWI.js";import{t as T}from"./index.D5F8338M.js";import{a as L,_ as D}from"./error.sIrUkhZc.js";import{_ as N}from"./uni-popup.BLXBf1r-.js";import{l as j,g as P}from"./utils.61Hi-B7M.js";import{_ as F}from"./right2.DB4H0AJ0.js";import"./returnFn.BYkANsDr.js";import"./uni-forms.BsccYHPu.js";import"./uni-data-checkbox.lGJQWvI7.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-load-more.BMOBclrH.js";import"./uni-datetime-picker.DkX7SHFR.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-easyinput.D_LnJWIZ.js";import"./uni-transition.Ckb0qY8x.js";const S=C({name:"UniSegmentedControl",emits:["clickItem"],props:{current:{type:Number,default:0},values:{type:Array,default:()=>[]},activeColor:{type:String,default:"#2979FF"},styleType:{type:String,default:"button"}},data:()=>({currentIndex:0}),watch:{current(e){e!==this.currentIndex&&(this.currentIndex=e)}},created(){this.currentIndex=this.current},methods:{_onClick(e){this.currentIndex!==e&&(this.currentIndex=e,this.$emit("clickItem",{currentIndex:e}))}}},[["render",function(m,v,I,f,y,h){const k=p,g=u;return e(),s(g,{class:i([["text"===I.styleType?"segmented-control--text":"segmented-control--button"],"segmented-control"]),style:c({borderColor:"text"===I.styleType?"":I.activeColor})},{default:t((()=>[(e(!0),a(l,null,n(I.values,((a,l)=>(e(),s(g,{class:i([["text"===I.styleType?"":"segmented-control__item--button",l===y.currentIndex&&"button"===I.styleType?"segmented-control__item--button--active":"",0===l&&"button"===I.styleType?"segmented-control__item--button--first":"",l===I.values.length-1&&"button"===I.styleType?"segmented-control__item--button--last":""],"segmented-control__item"]),key:l,style:c({backgroundColor:l===y.currentIndex&&"button"===I.styleType?I.activeColor:"",borderColor:l===y.currentIndex&&"text"===I.styleType||"button"===I.styleType?I.activeColor:"transparent"}),onClick:e=>h._onClick(l)},{default:t((()=>[r(g,null,{default:t((()=>[r(k,{style:c({color:l===y.currentIndex?"text"===I.styleType?I.activeColor:"#fff":"text"===I.styleType?"#000":I.activeColor}),class:i(["segmented-control__text","text"===I.styleType&&l===y.currentIndex?"segmented-control__item--text":""])},{default:t((()=>[o(d(a),1)])),_:2},1032,["style","class"])])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1},8,["class","style"])}],["__scopeId","data-v-bda4a78b"]]);const G=C({components:{tabBar:T,addFollow:L,cluePath:C({data:()=>({porpsData:""}),methods:{opens(e){this.porpsData=e,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){this.$refs.chargeback.close()}}},[["render",function(i,c,o,p,f,y){const h=_(m("uni-popup"),N),k=u;return e(),s(k,null,{default:t((()=>[(e(),s(v,{to:"body"},[r(h,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:t((()=>[I("div",{class:"applyaffirm"},[I("div",{class:"p1"},"指派路径"),I("div",{class:"p2"},[I("div",{class:"contentBox"},[(e(!0),a(l,null,n(f.porpsData,((s,t)=>(e(),a("div",{key:t,class:"contentItemBox"},[I("div",{class:"Time"},[I("div",{class:"circle1"}),I("span",{class:"remindTime"},d(s.assignDate),1)]),I("div",{class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"key"},"指派人：")]),I("span",{class:"value"},d(s.operateUserName),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"key"},"跟进人：")]),I("span",{class:"value"},d(s.assignUserName),1)])])])))),128))])]),I("div",{class:"popBtn"},[I("div",{onClick:c[0]||(c[0]=(...e)=>y.retreat&&y.retreat(...e)),class:"affirm"}," 确定 ")])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-87c8e6c0"]])},data:()=>({userId:"",dataInfo:{enterprise:"",clueInfo:"",followList:[],entrust:""},listicon:j,showTag:[],isshow:!1,identity:1,tabIds:0,tabList:["自行跟进记录","委托跟踪信息"],pathList:[],tabIds2:1,tabList2:[{id:1,name:"进度信息"},{id:2,name:"催办信息"}]}),onLoad(e){this.identity=f("identity"),this.userId=f("userId"),P("token")&&y("token",P("token")),P("item")&&(this.dataInfo.enterprise=JSON.parse(P("item")),this.getDel())},methods:{changtabContent(e){this.tabIds2=e},onClickItem(e){this.tabIds!==e.currentIndex&&(this.tabIds=e.currentIndex)},getDel(){this.$api.getLatestAssignClueAPI_investment({method:"get",data:{clueId:this.dataInfo.enterprise.id}}).then((e=>{this.dataInfo.clueInfo=e.result||{}})),this.$api.getFollowUpRecordsAPI_investment({method:"get",data:{clueId:this.dataInfo.enterprise.id}}).then((e=>{this.dataInfo.followList=e.result||[]})),this.$api.getAssignPathAPI_investment({method:"get",data:{clueId:this.dataInfo.enterprise.id}}).then((e=>{this.pathList=e.result||[]})),this.$api.clientDetailAPI_entrust({method:"get",data:{uniCode:this.dataInfo.enterprise.uniCode}}).then((e=>{this.dataInfo.entrust=e.result}))},toAddFollow(){this.$refs.addFollow.opens(this.dataInfo.enterprise)},toCluePath(){this.$refs.cluePath.opens(this.pathList)},more(){this.isshow?(this.showTag=this.dataInfo.enterprise.enterpriseLabelNames.slice(0,4),this.isshow=!this.isshow):(this.showTag=this.dataInfo.enterprise.enterpriseLabelNames,this.isshow=!this.isshow)},handleLongPress(e){h({data:e})},previewImg(e,s){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"showAlert",value:{type:"ImageViewer",data:{images:e,imageIndex:s}},module:"Investment"}))},updataList(){this.getDel()},attractDel(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{id:this.dataInfo.enterprise.enterpriseId,clueId:this.dataInfo.enterprise.id,iconTypeid:this.dataInfo.enterprise.enterpriseIconLabelId,enterpriseName:this.dataInfo.enterprise.enterpriseName,clueSource:this.dataInfo.enterprise.clueSource},path:"industryDetail"}))}}},[["render",function(c,p,v,f,y,h){const C=b,T=u,L=_(m("uni-segmented-control"),S),N=k("cluePath"),j=k("addFollow");return e(),a("div",null,[I("div",{class:"box33"},[r(T,{class:"firm"},{default:t((()=>[r(T,{class:"userNname"},{default:t((()=>{var l;return[I("img",{src:y.listicon[null==(l=y.dataInfo.enterprise)?void 0:l.enterpriseIconLabelId].icon,alt:"",class:"iconImg"},null,8,["src"]),r(T,{class:"nameView"},{default:t((()=>[I("span",{onLongpress:p[0]||(p[0]=e=>h.handleLongPress(y.dataInfo.enterprise.enterpriseName)),class:"enterpriseName",onClick:p[1]||(p[1]=(...e)=>h.attractDel&&h.attractDel(...e))},d(y.dataInfo.enterprise.enterpriseName),33),r(T,{class:"tagView2"},{default:t((()=>[2==y.dataInfo.enterprise.clueDealState?(e(),a("div",{key:0,class:"enterpriseTag fail"}," 签约失败 ")):g("",!0),1==y.dataInfo.enterprise.clueDealState?(e(),a("div",{key:1,class:"enterpriseTag success"}," 签约成功 ")):g("",!0),null!=y.dataInfo.enterprise.clueDealState&&0==y.dataInfo.enterprise.clueDealState?(e(),a("div",{key:2,class:"enterpriseTag ing"}," 跟进中 ")):g("",!0),y.dataInfo.enterprise.isNotFollowMonth?(e(),s(T,{key:3,class:"errorView"},{default:t((()=>[r(C,{class:"icon",src:D}),I("span",{class:"txt"},"近一个月未跟进")])),_:1})):g("",!0)])),_:1}),r(T,{class:"texts"},{default:t((()=>[r(T,{class:"ListText"},{default:t((()=>[o(" 企业来源："+d(2==y.dataInfo.enterprise.clueSource?"自行添加":"系统推荐"),1)])),_:1}),r(T,{class:"ListText"},{default:t((()=>{var e;return[o(" 纳入意向日期："+d((null==(e=y.dataInfo.enterprise)?void 0:e.intentionDate)||"--"),1)]})),_:1}),r(T,{class:"ListText"},{default:t((()=>{var e;return[o(" 纳入意向人："+d((null==(e=y.dataInfo.enterprise)?void 0:e.intentionPerson)||"--"),1)]})),_:1}),r(T,{class:"ListText"},{default:t((()=>{var e;return[o(" 指派人："+d((null==(e=y.dataInfo.clueInfo)?void 0:e.operateUserName)||"--"),1)]})),_:1}),r(T,{class:"ListText"},{default:t((()=>{var e;return[o(" 指派日期："+d((null==(e=y.dataInfo.clueInfo)?void 0:e.assignDate)||"--"),1)]})),_:1}),r(T,{class:"ListText"},{default:t((()=>{var e;return[o(" 跟进人："+d((null==(e=y.dataInfo.enterprise)?void 0:e.beAssignPerson)||"--"),1)]})),_:1}),r(T,{class:"ListText"},{default:t((()=>{var e;return[o(" 流转备注："+d((null==(e=y.dataInfo.clueInfo)?void 0:e.remark)||"--"),1)]})),_:1})])),_:1})])),_:1})]})),_:1}),y.pathList.length?(e(),s(T,{key:0,class:"clueBtn",onClick:h.toCluePath},{default:t((()=>[o("指派路径 "),r(C,{src:F,class:"righticon"})])),_:1},8,["onClick"])):g("",!0)])),_:1}),r(T,{class:"infoView"},{default:t((()=>[r(T,{class:"uni-padding-wrap uni-common-mt"},{default:t((()=>[r(L,{current:y.tabIds,values:y.tabList,"style-type":"button","active-color":"#3370FF",onClickItem:h.onClickItem,inActiveColor:"#F4F4F5"},null,8,["current","values","onClickItem"])])),_:1}),0==y.tabIds?(e(),s(T,{key:0},{default:t((()=>[I("div",{class:"contentBox"},[y.dataInfo.followList&&y.dataInfo.followList.length?(e(!0),a(l,{key:0},n(y.dataInfo.followList,((s,t)=>(e(),a("div",{key:t,class:"contentItemBox"},[I("div",{class:"Time"},[I("div",{class:"circle1"}),I("span",{class:"remindTime"},d(s.followUpDate),1),2==s.clueDealState?(e(),a("div",{key:0,class:"enterpriseTag fail"}," 签约失败 ")):g("",!0),1==s.clueDealState?(e(),a("div",{key:1,class:"enterpriseTag success"}," 签约成功 ")):g("",!0),0==s.clueDealState?(e(),a("div",{key:2,class:"enterpriseTag ing"}," 跟进中 ")):g("",!0)]),I("div",{class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"key"},"跟进人：")]),I("span",{class:"value"},d(s.fillInPerson),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"key"},"跟进描述：")]),I("span",{class:"value"},d(s.overview),1)])])])))),128)):(e(),a("div",{key:1,class:"noData"},"暂无数据"))])])),_:1})):g("",!0),1==y.tabIds?(e(),s(T,{key:1},{default:t((()=>[y.dataInfo.entrust?(e(),a("div",{key:0},[I("div",{class:"attDelBox"},[I("div",{class:"row"},[I("span",{class:"key"},"订单号："),I("span",{class:"value"},d(y.dataInfo.entrust.orderSn),1)]),I("div",{class:"row"},[I("span",{class:"key"},"发起委托时间："),I("span",{class:"value"},d(y.dataInfo.entrust.startDatetime),1)]),I("div",{class:"row"},[I("span",{class:"key"},"期望对接时间："),I("span",{class:"value"},d(y.dataInfo.entrust.exceptedDatetime),1)]),y.dataInfo.payOrgName?(e(),a("div",{key:0,class:"row"},[I("span",{class:"key"},"委托单位："),I("span",{class:"value"},d(y.dataInfo.entrust.payOrgName),1)])):g("",!0),I("div",{class:"row"},[I("span",{class:"key"},"招商对接人："),I("span",{class:"value"},d(y.dataInfo.entrust.contact),1)]),I("div",{class:"row"},[I("span",{class:"key"},"联系方式："),I("span",{class:"value"},d(y.dataInfo.entrust.contactPhone),1)]),I("div",{class:"row"},[I("span",{class:"key"},"招商要求："),I("span",{class:"value"},d(y.dataInfo.entrust.note),1)]),I("div",{class:"row"},[I("span",{class:"key"},"金额："),I("span",{class:"value"},d(y.dataInfo.entrust.amount),1)])]),I("div",{class:"tabListBox"},[(e(!0),a(l,null,n(y.tabList2,((s,t)=>(e(),a("div",{onClick:e=>h.changtabContent(s.id),key:s.id,class:i(y.tabIds2==s.id?"tabItems":"tabItem")},d(s.name),11,["onClick"])))),128))]),x(I("div",{class:"contentBox"},[y.dataInfo.entrust.logs&&y.dataInfo.entrust.logs.length?(e(!0),a(l,{key:0},n(y.dataInfo.entrust.logs,((s,t)=>(e(),a("div",{key:t,class:"contentItemBox"},[I("div",{class:"Time"},[I("div",{class:"circle1"}),I("span",{class:"remindTime"},d(s.startDatetime||s.gmtCreate),1),I("span",{class:"typeText"},[I("span",{class:"dot"}),o(d(s.statusName),1)])]),"audit"==s.childObjectText&&s.tip?(e(),a("div",{key:0,style:{"font-weight":"400","font-size":"30rpx",color:"#FF0000","margin-left":"36rpx","margin-bottom":"16rpx"}},[I("span",null,"（超过5天，系统自动审批通过）")])):g("",!0),"apply"==s.childObjectText&&s.apply?(e(),a("div",{key:1,class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"申请人：")]),I("span",{class:"value"},d(s.apply.applyName),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"结单说明：")]),I("span",{class:"value"},d(s.apply.note),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"附件：")]),I("div",{class:"annex"},[(e(!0),a(l,null,n(s.apply.attachUrls,((t,l)=>(e(),a("div",{key:l,onClick:e=>h.previewImg(s.apply.attachUrls,l)},[r(C,{src:t,class:"annexImg"},null,8,["src"])],8,["onClick"])))),128))])])])):g("",!0),"audit"==s.childObjectText&&s.audit?(e(),a("div",{key:2,class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"审批人：")]),I("span",{class:"value"},d(s.audit.auditName),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"是否通过：")]),I("span",{class:"value"},d(s.audit.auditStatusName),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"审批说明：")]),I("span",{class:"value"},d(s.audit.note||"无"),1)])])):g("",!0),"comment"==s.childObjectText&&s.comment?(e(),a("div",{key:3,class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"委托人：")]),I("span",{class:"value"},d(s.comment.name),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"评论信息：")]),I("span",{class:"value"},d(s.comment.note),1)])])):g("",!0),"claim"==s.childObjectText&&s.claim?(e(),a("div",{key:4,class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"认领人：")]),I("span",{class:"value"},d(s.claim.contact),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"联系方式：")]),I("span",{class:"value"},d(s.claim.contactPhone),1)])])):g("",!0),"follow"==s.childObjectText&&s.follow?(e(),a("div",{key:5,class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"提交人：")]),I("span",{class:"value"},d(s.follow.createBy),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"企业对接人：")]),I("span",{class:"value"},d(s.follow.enterpriseContact),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"职务：")]),I("span",{class:"value"},d(s.follow.contactPosition),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"联系方式：")]),I("span",{class:"value"},d(s.follow.contactPhone),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"跟进概述：")]),I("span",{class:"value"},d(s.follow.note),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"附件：")]),s.follow.attachUrls?(e(),a("div",{key:0,class:"annex"},[(e(!0),a(l,null,n(s.follow.attachUrls,((t,l)=>(e(),a("div",{key:l,onClick:e=>h.previewImg(s.follow.attachUrls,l)},[r(C,{src:t,class:"annexImg"},null,8,["src"])],8,["onClick"])))),128))])):g("",!0)])])):g("",!0)])))),128)):(e(),a("div",{key:1,class:"noData"},"暂无数据"))],512),[[w,1==y.tabIds2]]),x(I("div",{class:"contentBox"},[y.dataInfo.entrust.reminds&&y.dataInfo.entrust.reminds.length?(e(!0),a(l,{key:0},n(y.dataInfo.entrust.reminds,((s,t)=>(e(),a("div",{key:t,class:"contentItemBox"},[I("div",{class:"Time"},[I("div",{class:"circle1"}),I("span",{class:"remindTime"},d(s.remindTimeStr),1),I("span",{class:"typeText"},[I("span",{class:"dot"}),o(d(s.type),1)])]),I("div",{class:"contentItem"},[I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"委托人：")]),I("span",{class:"value"},d(s.createBy),1)]),I("div",{class:"Itemline"},[I("div",{class:"Itemleft"},[I("span",{class:"GrayCircle"}),I("span",{class:"key"},"催办信息：")]),I("span",{class:"value"},d(s.note),1)])])])))),128)):(e(),a("div",{key:1,class:"noData"},"暂无数据"))],512),[[w,2==y.tabIds2]])])):(e(),a("div",{key:1,class:"noData2"},"暂无数据"))])),_:1})):g("",!0)])),_:1}),0==y.dataInfo.enterprise.clueDealState&&y.dataInfo.enterprise.beAssignPerson==y.userId?(e(),a("div",{key:0,class:"strategyBtn",onClick:p[2]||(p[2]=(...e)=>h.toAddFollow&&h.toAddFollow(...e))},"添加跟进记录")):g("",!0),I("div",{style:{height:"150rpx"}})]),r(N,{ref:"cluePath"},null,512),r(j,{onUpdataList:h.updataList,ref:"addFollow"},null,8,["onUpdataList"])])}],["__scopeId","data-v-dfcf48e4"]]);export{G as default};
