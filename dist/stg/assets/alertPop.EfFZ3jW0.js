import{_ as a}from"./uni-popup.BdZPMDVN.js";import{z as s,o as e,c as r,w as t,V as p,f as o,B as i,t as c,i as l}from"./index-HcMwrp5e.js";import{r as d}from"./uni-app.es.DFp0WTX7.js";import{_ as n}from"./_plugin-vue_export-helper.BCo6x5W8.js";const f=n({data:()=>({porpsData:""}),methods:{opens(a){this.porpsData=a,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){this.$refs.chargeback.close()}}},[["render",function(n,f,m,u,h,_){const k=d(s("uni-popup"),a),v=l;return e(),r(v,null,{default:t((()=>[(e(),r(p,{to:"body"},[o(k,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:t((()=>[i("div",{class:"applyaffirm"},[i("div",{class:"p1"},"温馨提示"),i("div",{class:"p2"},c(h.porpsData.title),1),i("div",{class:"popBtn"},[i("div",{onClick:f[0]||(f[0]=(...a)=>_.retreat&&_.retreat(...a)),class:"affirm"}," 我知道了 ")])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-ed25fd92"]]);export{f as a};
