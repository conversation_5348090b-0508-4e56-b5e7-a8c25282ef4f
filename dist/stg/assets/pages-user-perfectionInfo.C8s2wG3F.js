import{l as e,y as t,a3 as a,a4 as s,p as o,q as l,s as i,m as n,z as r,A as d,a as u,f as m,w as c,F as f,i as I,o as h,B as p,c as b,d as y,e as g,t as v,g as C,C as V}from"./index-CBCsGYoT.js";import{_ as k}from"./page-meta.BCoSkkXs.js";import{r as _}from"./uni-app.es.CZb2JZWI.js";import{_ as T}from"./uni-data-select.DWY_AY9U.js";import{_ as N,a as j}from"./uni-forms.BsccYHPu.js";import{_ as x}from"./zxz-uni-data-select.CW_37m_-.js";import{_ as L}from"./uni-tooltip.Cee0nqWM.js";import{_ as w}from"./uni-icons.Dr3tmUrM.js";import{_ as S}from"./uni-easyinput.D_LnJWIZ.js";import{_ as U}from"./uni-popup.BLXBf1r-.js";import{t as P}from"./index.D5F8338M.js";import{m as q}from"./mapList.8RZSfjp3.js";import{m as z}from"./mapList2.BCv64X7E.js";import{_ as $}from"./remindericon.Di7cnH87.js";import{_ as F}from"./addImg.DKbfrdtV.js";import{_ as A}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-transition.Ckb0qY8x.js";import"./returnFn.BYkANsDr.js";const O=A({components:{tabBar:P,mapList:q,mapList2:z},data:()=>({baseInfo:{userIdentity:"",focusIndustryIds:[],focusDirection:[],region:[],gender:"",realName:"",code:"",email:"",company:"",jobTitle:"",cityCode:"",cityName:"",advantageCity:"",resume:"",referrer:"",InvitationCode:"",overseaTargetCodes:[],enterpriseProfile:"",serviceCountryCode:"",serviceItemCodes:[],headImg:""},show:!1,regionName:"",sex:[{text:"男",value:1},{text:"女",value:0}],identityList:[{value:1,text:"产业运营方"},{value:5,text:"产业顾问方"}],industryList:[],countryList:[],directionList:[{value:1,text:"产业研究"},{value:2,text:"园区管理"},{value:3,text:"企业招商"},{value:4,text:"政府决策"}],DivisionList:[],title:"信息完善",showMap:!1,rules:{},phone:"",immobilization:!1,categoryList:[],showImg:"",updataAPI:0,editCode:!1}),computed:{pageStyle(){return`overflow:${this.show?"hidden":"visible"}; background-color: #FAFCFF`}},created(){this.token=e("token"),this.userId=e("userId"),this.phone=e("phone"),t({key:"token"}),this.showMap=!0,this.getindustryList(),this.getCountryList(),this.isEdit()},onLoad(a){let s=e("teamCode");a.identityCode?(3!=a.identityCode&&4!=a.identityCode||(this.immobilization=!0),3==a.identityCode&&(this.identityList.push({value:3,text:"出海服务方"}),this.getCategoryList()),this.baseInfo.userIdentity=Number(a.identityCode),t({key:"teamCode"})):s&&(this.immobilization=!0,this.baseInfo.userIdentity=5)},methods:{isEdit(){this.$api.checkIsDefaultOrNullAPI({data:{userId:this.userId},token:this.token,method:"get"}).then((e=>{this.editCode=e.result}))},change(e){this.show=e.show},chooseSuccess(){var e=this;wx.chooseMedia({count:1,mediaType:["image"],sourceType:["album","camera"],camera:"back",success(t){const l=t.tempFiles;a({url:`${s}/ai/upload/uploadImg`,filePath:l[0].tempFilePath,name:"file",success:t=>{t.data=JSON.parse(t.data),o({title:"头像上传成功！",icon:"none"}),e.showImg=l[0].tempFilePath,e.baseInfo.headImg=t.data.result}})}})},getCategoryList(){this.$api.catalogListAPI({token:this.token,method:"POST"}).then((e=>{e.result.forEach((e=>{this.categoryList.push({text:e.catalogName,value:parseInt(e.catalogId)})}))}))},goIdentityStatement(){l({url:"/pages/user/IdentityStatement"})},affirm(e){var t,a,s;this.$refs.popup.close(),this.baseInfo.region=[],this.regionName=(null==(t=e.province)?void 0:t.name)+((null==(a=e.citys)?void 0:a.name)?"/"+e.citys.name:"")+((null==(s=e.area)?void 0:s.name)?"/"+e.area.name:"");let o=Object.keys(e).map((t=>e[t]&&e[t].code&&""!==e[t].code?e[t].code:null));this.baseInfo.region=o.filter((e=>null!==e))},affirm2(e){var t,a,s;this.$refs.popup2.close(),this.baseInfo.cityCode=[],this.baseInfo.cityName=(null==(t=e.province)?void 0:t.name)+((null==(a=e.citys)?void 0:a.name)?"/"+e.citys.name:"")+((null==(s=e.area)?void 0:s.name)?"/"+e.area.name:"");let o=Object.keys(e).map((t=>e[t]&&e[t].code&&""!==e[t].code?e[t].code:null));this.baseInfo.cityCode=o.filter((e=>null!==e))},pop(){this.$refs.popup.open("bottom")},pop2(){this.$refs.popup2.open("bottom")},getindustryList(){this.industryList=[],this.$api.listAllPurchaseChain({token:this.token}).then((e=>{e.result.forEach((e=>{this.industryList.push({text:e.name,value:parseInt(e.id)})}))}))},getCountryList(){this.countryList=[],this.$api.countryListAPI({token:this.token}).then((e=>{e.result.forEach((e=>{this.countryList.push({text:e.country,value:parseInt(e.id)})}))}))},whereTogo(){let t=e("teamCode");this.$api.userMessage().then((e=>{var a,s;i("userIdentityType",null==(a=e.result.userCompletionInfoDO)?void 0:a.userIdentity),5==(null==(s=e.result.userCompletionInfoDO)?void 0:s.userIdentity)?(i("identity",2),n(t?{url:"/pages/repository/capacity"}:{url:"/pages/repository/capacity?identity=counselor"})):(i("identity",1),n({url:"/pages/repository/capacity"}))}))},submit(e){this.$refs[e].validate().then((e=>{if(!this.baseInfo.userIdentity)return o({title:"请选择身份",icon:"none"});if(4!=this.baseInfo.userIdentity&&3!=this.baseInfo.userIdentity){if(0==this.baseInfo.region.length&&this.editCode)return o({title:"请选择招商属地",icon:"none"});if(0==this.baseInfo.focusIndustryIds.length)return o({title:"请选择关注产业",icon:"none"})}if(4==this.baseInfo.userIdentity&&0==this.baseInfo.overseaTargetCodes.length)return o({title:"请选择出海关注目的地",icon:"none"});if(3==this.baseInfo.userIdentity){if(!this.baseInfo.serviceCountryCode)return o({title:"请选择服务国家",icon:"none"});if(0==this.baseInfo.serviceItemCodes.length)return o({title:"请选择服务类目",icon:"none"});if(!this.baseInfo.headImg)return o({title:"请上传企业logo",icon:"none"});if(!this.baseInfo.enterpriseProfile)return o({title:"请输入企业简介",icon:"none"})}if(!this.baseInfo.realName)return o({title:"请输入名称",icon:"none"});if(5==this.baseInfo.userIdentity){if(0==this.baseInfo.cityCode.length)return o({title:"请输入所在城市",icon:"none"});if(!this.baseInfo.advantageCity)return o({title:"请输入优势资源城市",icon:"none"});if(!this.baseInfo.resume)return o({title:"请输入优势资源简述",icon:"none"})}if(3!=this.baseInfo.userIdentity){if(!this.baseInfo.company)return o({title:"请输入所在单位",icon:"none"});if(!this.baseInfo.jobTitle)return o({title:"请输入所在单位职务",icon:"none"})}let t={};t=5==this.baseInfo.userIdentity?{userId:this.userId,userIdentity:this.baseInfo.userIdentity,focusIndustryIds:this.baseInfo.focusIndustryIds,realName:this.baseInfo.realName,email:this.baseInfo.email,company:this.baseInfo.company,jobTitle:this.baseInfo.jobTitle,cityCode:this.baseInfo.cityCode[this.baseInfo.cityCode.length-1],cityName:this.baseInfo.cityName,advantageCity:this.baseInfo.advantageCity,resume:this.baseInfo.resume,referrer:this.baseInfo.referrer}:4==this.baseInfo.userIdentity?{userId:this.userId,userIdentity:this.baseInfo.userIdentity,realName:this.baseInfo.realName,jobTitle:this.baseInfo.jobTitle,email:this.baseInfo.email,company:this.baseInfo.company,focusIndustryIds:this.baseInfo.focusIndustryIds,overseaTargetCodes:this.baseInfo.overseaTargetCodes}:3==this.baseInfo.userIdentity?{userId:this.userId,userIdentity:this.baseInfo.userIdentity,realName:this.baseInfo.realName,enterpriseProfile:this.baseInfo.enterpriseProfile,serviceItemCodes:this.baseInfo.serviceItemCodes,serviceCountryCode:this.baseInfo.serviceCountryCode,email:this.baseInfo.email,headImg:this.baseInfo.headImg}:{userId:this.userId,userIdentity:this.baseInfo.userIdentity,realName:this.baseInfo.realName,jobTitle:this.baseInfo.jobTitle,email:this.baseInfo.email,company:this.baseInfo.company,focusIndustryIds:this.baseInfo.focusIndustryIds},3!=this.baseInfo.userIdentity&&this.editCode&&(t.code=this.baseInfo.region[this.baseInfo.region.length-1]),5==this.baseInfo.userIdentity&&this.baseInfo.InvitationCode?this.$api.completeUserInfo({data:t,token:this.token,method:"POST"}).then((e=>{"SUCCESS"==e.code&&(i("token",this.token),this.$api.workerapplyAPI({data:{invitationCode:this.baseInfo.InvitationCode},method:"post",token:this.token,error:"no"}).then((e=>{o({title:"完善信息成功，加入团队成功",icon:"none"}),setTimeout((()=>{this.whereTogo()}),1e3)})).catch((e=>{o({title:"完善信息成功,"+(null==e?void 0:e.msg),icon:"none"}),setTimeout((()=>{this.whereTogo()}),1e3)})))})).catch((e=>{})):1==this.updataAPI?this.$api.updateHeadImg({data:t,token:this.token,method:"POST"}).then((e=>{"SUCCESS"==e.code&&(o({title:"填写成功！",icon:"none"}),i("token",this.token),setTimeout((()=>{this.whereTogo()}),1e3))})).catch((e=>{})):this.$api.completeUserInfo({data:t,token:this.token,method:"POST"}).then((e=>{"SUCCESS"==e.code&&(o({title:"填写成功！",icon:"none"}),i("token",this.token),setTimeout((()=>{this.whereTogo()}),1e3))})).catch((e=>{}))})).catch((e=>{}))}}},[["render",function(e,t,a,s,o,l){const i=_(r("page-meta"),k),n=d("tabBar"),P=V,q=_(r("uni-data-select"),T),z=_(r("uni-forms-item"),N),A=_(r("zxz-uni-data-select"),x),O=_(r("uni-tooltip"),L),B=_(r("uni-icons"),w),E=_(r("uni-forms"),j),M=_(r("uni-easyinput"),S),D=I,H=d("mapList"),J=_(r("uni-popup"),U),K=d("mapList2");return h(),u(f,null,[m(i,{"page-style":l.pageStyle},null,8,["page-style"]),m(D,{class:"container"},{default:c((()=>[m(n,{title:o.title,showicon:!1},null,8,["title"]),m(D,{class:"example"},{default:c((()=>[m(E,{ref:"valiForm",rules:o.rules,modelValue:o.baseInfo,"label-position":"top","label-width":"100"},{default:c((()=>[m(z,{label:"您的身份",required:""},{default:c((()=>[p("span",{onClick:t[0]||(t[0]=e=>l.goIdentityStatement()),class:"result"},[m(P,{src:$,class:"remindericon2"})]),m(q,{disabled:o.immobilization,modelValue:o.baseInfo.userIdentity,"onUpdate:modelValue":t[1]||(t[1]=e=>o.baseInfo.userIdentity=e),localdata:o.identityList},null,8,["disabled","modelValue","localdata"])])),_:1}),3!=o.baseInfo.userIdentity&&4!=o.baseInfo.userIdentity?(h(),b(z,{key:0,label:"您关注的产业",required:""},{default:c((()=>[m(A,{modelValue:o.baseInfo.focusIndustryIds,"onUpdate:modelValue":t[2]||(t[2]=e=>o.baseInfo.focusIndustryIds=e),multiple:!0,localdata:o.industryList,collapseTagsNum:2,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):y("",!0),3!=o.baseInfo.userIdentity&&4!=o.baseInfo.userIdentity&&o.editCode?(h(),b(z,{key:1,label:"招商属地",required:""},{default:c((()=>[m(O,{content:"注：哒达招商将优先推荐招商属地相关产业资讯，后期无法更改，请谨慎填写",placement:"bottom"},{default:c((()=>[p("span",{class:"result"},[m(P,{src:$,class:"remindericon"})])])),_:1}),p("div",{onClick:t[3]||(t[3]=e=>l.pop()),class:"simulation"},[p("span",{class:g(""==o.regionName?"":"textSpan")},v(""==o.regionName?"请选择":o.regionName),3),C(),m(B,{color:"#999",type:"down",size:"14"})])])),_:1})):y("",!0),4==o.baseInfo.userIdentity?(h(),b(z,{key:2,label:"出海关注目的地",required:""},{default:c((()=>[m(A,{modelValue:o.baseInfo.overseaTargetCodes,"onUpdate:modelValue":t[4]||(t[4]=e=>o.baseInfo.overseaTargetCodes=e),multiple:!0,localdata:o.countryList,collapseTagsNum:2,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):y("",!0),3==o.baseInfo.userIdentity?(h(),b(z,{key:3,label:"服务国家",required:""},{default:c((()=>[m(q,{modelValue:o.baseInfo.serviceCountryCode,"onUpdate:modelValue":t[5]||(t[5]=e=>o.baseInfo.serviceCountryCode=e),localdata:o.countryList},null,8,["modelValue","localdata"])])),_:1})):y("",!0),3==o.baseInfo.userIdentity?(h(),b(z,{key:4,label:"服务类目",required:""},{default:c((()=>[m(A,{modelValue:o.baseInfo.serviceItemCodes,"onUpdate:modelValue":t[6]||(t[6]=e=>o.baseInfo.serviceItemCodes=e),multiple:!0,localdata:o.categoryList,collapseTagsNum:2,maximum:9999,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):y("",!0),3==o.baseInfo.userIdentity?(h(),b(z,{key:5,label:"企业logo (建议上传专属logo,提升品牌形象)",required:""},{default:c((()=>[o.showImg?(h(),u("div",{key:1,onClick:t[8]||(t[8]=(...e)=>l.chooseSuccess&&l.chooseSuccess(...e)),class:"ImgBox"},[m(P,{class:"showImgIcon",src:o.showImg},null,8,["src"])])):(h(),u("div",{key:0,class:"addImgBox",onClick:t[7]||(t[7]=(...e)=>l.chooseSuccess&&l.chooseSuccess(...e))},[m(P,{class:"addImg",src:F})]))])),_:1})):y("",!0)])),_:1},8,["rules","modelValue"]),2==o.baseInfo.userIdentity||1==o.baseInfo.userIdentity||4==o.baseInfo.userIdentity?(h(),u("div",{key:0,class:"ConsultantPerfection"},[p("span",{class:"TitleName"},"更多信息"),p("div",{class:"perfectBox"},[m(E,{ref:"valiForm",rules:o.rules,modelValue:o.baseInfo,"label-align":"left","label-width":"100"},{default:c((()=>[p("div",{class:"customizationInput"},[m(z,{label:"您的姓名",required:""},{default:c((()=>[m(M,{type:"nickname",trim:"all",clearable:!1,modelValue:o.baseInfo.realName,"onUpdate:modelValue":t[9]||(t[9]=e=>o.baseInfo.realName=e),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),m(z,{label:"联系电话",required:""},{default:c((()=>[p("span",{class:"phone"},v(o.phone),1)])),_:1}),m(z,{label:"所在单位",required:""},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.company,"onUpdate:modelValue":t[10]||(t[10]=e=>o.baseInfo.company=e),placeholder:"请输入所在单位"},null,8,["modelValue"])])),_:1}),m(z,{label:"所在单位职务",required:""},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.jobTitle,"onUpdate:modelValue":t[11]||(t[11]=e=>o.baseInfo.jobTitle=e),placeholder:"请输入所在单位职务"},null,8,["modelValue"])])),_:1}),m(z,{label:"您的邮箱"},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.email,"onUpdate:modelValue":t[12]||(t[12]=e=>o.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})])])),_:1},8,["rules","modelValue"])])])):y("",!0),3==o.baseInfo.userIdentity?(h(),u("div",{key:1,class:"ConsultantPerfection"},[p("span",{class:"TitleName"},"更多信息"),p("div",{class:"perfectBox"},[m(E,{ref:"valiForm",rules:o.rules,modelValue:o.baseInfo,"label-align":"left","label-width":"100"},{default:c((()=>[p("div",{class:"customizationInput"},[m(z,{label:"企业名称",required:""},{default:c((()=>[m(M,{type:"nickname",trim:"all",clearable:!1,modelValue:o.baseInfo.realName,"onUpdate:modelValue":t[13]||(t[13]=e=>o.baseInfo.realName=e),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),m(z,{label:"企业简介",required:""},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.enterpriseProfile,"onUpdate:modelValue":t[14]||(t[14]=e=>o.baseInfo.enterpriseProfile=e),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),m(z,{label:"联系电话",required:""},{default:c((()=>[p("span",{class:"phone"},v(o.phone),1)])),_:1}),p("div",{class:"customizationInput"},[m(z,{label:"您的邮箱"},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.email,"onUpdate:modelValue":t[15]||(t[15]=e=>o.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})])])])),_:1},8,["rules","modelValue"])])])):y("",!0),5==o.baseInfo.userIdentity?(h(),u("div",{key:2,class:"ConsultantPerfection"},[p("span",{class:"TitleName"},"更多信息"),p("div",{class:"perfectBox"},[m(E,{ref:"valiForm",rules:o.rules,modelValue:o.baseInfo,"label-align":"left","label-width":"100"},{default:c((()=>[p("div",{class:"customizationInput"},[m(z,{label:"您的姓名",required:""},{default:c((()=>[m(M,{type:"nickname",trim:"all",clearable:!1,modelValue:o.baseInfo.realName,"onUpdate:modelValue":t[16]||(t[16]=e=>o.baseInfo.realName=e),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),m(z,{label:"联系电话",required:""},{default:c((()=>[p("span",{class:"phone"},v(o.phone),1)])),_:1}),m(z,{label:"所在单位",required:""},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.company,"onUpdate:modelValue":t[17]||(t[17]=e=>o.baseInfo.company=e),placeholder:"请输入所在单位"},null,8,["modelValue"])])),_:1}),m(z,{label:"所在单位职务",required:""},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.jobTitle,"onUpdate:modelValue":t[18]||(t[18]=e=>o.baseInfo.jobTitle=e),placeholder:"请输入所在单位职务"},null,8,["modelValue"])])),_:1}),m(z,{label:"所在城市",required:""},{default:c((()=>{var e,a;return[p("div",{onClick:t[19]||(t[19]=e=>l.pop2()),class:"citySelect"},[p("span",{class:g(""==(null==(e=o.baseInfo)?void 0:e.cityName)?"":"citySelectText")},v(""==(null==(a=o.baseInfo)?void 0:a.cityName)?"请选择所在城市":this.baseInfo.cityName),3),m(B,{color:"#999",type:"down",size:"14"})])]})),_:1}),m(z,{label:"优势资源城市",required:""},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.advantageCity,"onUpdate:modelValue":t[20]||(t[20]=e=>o.baseInfo.advantageCity=e),placeholder:"请输入优势资源城市"},null,8,["modelValue"])])),_:1})]),p("div",{class:"resume"},[m(z,{label:"优势资源简述",required:""},{default:c((()=>[m(M,{type:"textarea",trim:"all",clearable:!1,modelValue:o.baseInfo.resume,"onUpdate:modelValue":t[21]||(t[21]=e=>o.baseInfo.resume=e),placeholder:"请输入优势资源，包括但不限于 行业、地域等资源"},null,8,["modelValue"])])),_:1})]),p("div",{class:"customizationInput"},[m(z,{label:"您的邮箱"},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.email,"onUpdate:modelValue":t[22]||(t[22]=e=>o.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})]),p("div",{class:"customizationInput"},[m(z,{label:"加入团队"},{default:c((()=>[m(M,{trim:"all",clearable:!1,modelValue:o.baseInfo.InvitationCode,"onUpdate:modelValue":t[23]||(t[23]=e=>o.baseInfo.InvitationCode=e),placeholder:"请输入团队邀请码"},null,8,["modelValue"])])),_:1})])])),_:1},8,["rules","modelValue"])])])):y("",!0),p("div",{style:{height:"200rpx"}})])),_:1}),p("div",{class:"experience",type:"primary",onClick:t[24]||(t[24]=e=>l.submit("valiForm"))}," 确定 "),m(J,{ref:"popup",onChange:l.change,"background-color":"#fff"},{default:c((()=>[o.showMap?(h(),b(H,{key:0,token:e.token,onAffirm:l.affirm},null,8,["token","onAffirm"])):y("",!0)])),_:1},8,["onChange"]),m(J,{ref:"popup2",onChange:l.change,"background-color":"#fff"},{default:c((()=>[o.showMap?(h(),b(K,{key:0,token:e.token,onAffirm:l.affirm2},null,8,["token","onAffirm"])):y("",!0)])),_:1},8,["onChange"])])),_:1})],64)}],["__scopeId","data-v-b8bc7585"]]);export{O as default};
