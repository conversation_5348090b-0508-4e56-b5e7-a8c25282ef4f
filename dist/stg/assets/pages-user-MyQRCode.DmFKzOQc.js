import{P as e,aF as t,ao as r,ac as n,aG as i,aH as a,aI as o,a9 as s,aJ as l,aK as u,aL as c,o as h,c as d,w as f,n as v,aM as g,i as m,a0 as y,z as A,A as p,a as w,f as C,B as M,g as E,t as P,d as R,F as b,C as S}from"./index-CBCsGYoT.js";import{_ as I}from"./page-meta.BCoSkkXs.js";import{r as N}from"./uni-app.es.CZb2JZWI.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{t as F}from"./index.D5F8338M.js";import{e as z}from"./webviewUrl.D8mk1f89.js";import{_ as O}from"./userimg.DBGjY0t5.js";import"./returnFn.BYkANsDr.js";const B={value:String,icon:String,size:{type:[Number,String],default:160},iconSize:{type:[Number,String],default:40},marginSize:Number,color:{type:String,default:"#000"},bgColor:{type:String,default:"transparent"},bordered:{type:Boolean,default:!0},errorLevel:{type:String,default:"M"},useCanvasToTempFilePath:Boolean,use2d:{type:Boolean,default:!0}};async function T(r,n){let{context:i}=n;return function(t,r,n=!1){return null==r?Promise.reject("context is null"):(r.context&&(r=r.context),new Promise(((i,a)=>{const o=e().in(r).select(t),s=e=>{e?i(e):a("no rect")};n?o.fields({node:!0,size:!0,rect:!0},s).exec():o.boundingClientRect(s).exec()})))}("#"+r,i,false).then((e=>{if(e.node)return e.node;{const n=t(r,i);return{getContext(e){if("2d"==e)return n},width:e.width,height:e.height}}}))}function x(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function D(e,t){for(var r=0;t.length>r;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function U(e,t,r){return t&&D(e.prototype,t),r&&D(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);t>r;r++)n[r]=e[r];return n}function V(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return L(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?L(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return e.length>n?{done:!1,value:e[n++]}:{done:!0}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}
/**
 * @license QR Code generator library (TypeScript)
 * Copyright (c) Project Nayuki.
 * SPDX-License-Identifier: MIT
 */var q,_,H,j;!function(e){var t=function(){function t(e,r,n,a){if(x(this,t),this.version=void 0,this.errorCorrectionLevel=void 0,this.size=void 0,this.mask=void 0,this.modules=[],this.isFunction=[],this.version=e,this.errorCorrectionLevel=r,t.MIN_VERSION>e||e>t.MAX_VERSION)throw new RangeError("Version value out of range");if(-1>a||a>7)throw new RangeError("Mask value out of range");this.size=4*e+17;for(var o=[],s=0;this.size>s;s++)o.push(!1);for(var l=0;this.size>l;l++)this.modules.push(o.slice()),this.isFunction.push(o.slice());this.drawFunctionPatterns();var u=this.addEccAndInterleave(n);if(this.drawCodewords(u),-1==a)for(var c=1e9,h=0;8>h;h++){this.applyMask(h),this.drawFormatBits(h);var d=this.getPenaltyScore();c>d&&(a=h,c=d),this.applyMask(h)}i(a>=0&&7>=a),this.mask=a,this.applyMask(a),this.drawFormatBits(a),this.isFunction=[]}return U(t,[{key:"getModule",value:function(e,t){return e>=0&&this.size>e&&t>=0&&this.size>t&&this.modules[t][e]}},{key:"getModules",value:function(){return this.modules}},{key:"drawFunctionPatterns",value:function(){for(var e=0;this.size>e;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var t=this.getAlignmentPatternPositions(),r=t.length,n=0;r>n;n++)for(var i=0;r>i;i++)0==n&&0==i||0==n&&i==r-1||n==r-1&&0==i||this.drawAlignmentPattern(t[n],t[i]);this.drawFormatBits(0),this.drawVersion()}},{key:"drawFormatBits",value:function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,r=t,a=0;10>a;a++)r=r<<1^1335*(r>>>9);var o=21522^(t<<10|r);i(o>>>15==0);for(var s=0;5>=s;s++)this.setFunctionModule(8,s,n(o,s));this.setFunctionModule(8,7,n(o,6)),this.setFunctionModule(8,8,n(o,7)),this.setFunctionModule(7,8,n(o,8));for(var l=9;15>l;l++)this.setFunctionModule(14-l,8,n(o,l));for(var u=0;8>u;u++)this.setFunctionModule(this.size-1-u,8,n(o,u));for(var c=8;15>c;c++)this.setFunctionModule(8,this.size-15+c,n(o,c));this.setFunctionModule(8,this.size-8,!0)}},{key:"drawVersion",value:function(){if(this.version>=7){for(var e=this.version,t=0;12>t;t++)e=e<<1^7973*(e>>>11);var r=this.version<<12|e;i(r>>>18==0);for(var a=0;18>a;a++){var o=n(r,a),s=this.size-11+a%3,l=Math.floor(a/3);this.setFunctionModule(s,l,o),this.setFunctionModule(l,s,o)}}}},{key:"drawFinderPattern",value:function(e,t){for(var r=-4;4>=r;r++)for(var n=-4;4>=n;n++){var i=Math.max(Math.abs(n),Math.abs(r)),a=e+n,o=t+r;a>=0&&this.size>a&&o>=0&&this.size>o&&this.setFunctionModule(a,o,2!=i&&4!=i)}}},{key:"drawAlignmentPattern",value:function(e,t){for(var r=-2;2>=r;r++)for(var n=-2;2>=n;n++)this.setFunctionModule(e+n,t+r,1!=Math.max(Math.abs(n),Math.abs(r)))}},{key:"setFunctionModule",value:function(e,t,r){this.modules[t][e]=r,this.isFunction[t][e]=!0}},{key:"addEccAndInterleave",value:function(e){var r=this.version,n=this.errorCorrectionLevel;if(e.length!=t.getNumDataCodewords(r,n))throw new RangeError("Invalid argument");for(var a=t.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][r],o=t.ECC_CODEWORDS_PER_BLOCK[n.ordinal][r],s=Math.floor(t.getNumRawDataModules(r)/8),l=a-s%a,u=Math.floor(s/a),c=[],h=t.reedSolomonComputeDivisor(o),d=0,f=0;a>d;d++){var v=e.slice(f,f+u-o+(l>d?0:1));f+=v.length;var g=t.reedSolomonComputeRemainder(v,h);l>d&&v.push(0),c.push(v.concat(g))}for(var m=[],y=function(e){c.forEach((function(t,r){e==u-o&&l>r||m.push(t[e])}))},A=0;c[0].length>A;A++)y(A);return i(m.length==s),m}},{key:"drawCodewords",value:function(e){if(e.length!=Math.floor(t.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var r=0,a=this.size-1;a>=1;a-=2){6==a&&(a=5);for(var o=0;this.size>o;o++)for(var s=0;2>s;s++){var l=a-s,u=0==(a+1&2)?this.size-1-o:o;!this.isFunction[u][l]&&8*e.length>r&&(this.modules[u][l]=n(e[r>>>3],7-(7&r)),r++)}}i(r==8*e.length)}},{key:"applyMask",value:function(e){if(0>e||e>7)throw new RangeError("Mask value out of range");for(var t=0;this.size>t;t++)for(var r=0;this.size>r;r++){var n=void 0;switch(e){case 0:n=(r+t)%2==0;break;case 1:n=t%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+t)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(t/2))%2==0;break;case 5:n=r*t%2+r*t%3==0;break;case 6:n=(r*t%2+r*t%3)%2==0;break;case 7:n=((r+t)%2+r*t%3)%2==0;break;default:throw Error("Unreachable")}!this.isFunction[t][r]&&n&&(this.modules[t][r]=!this.modules[t][r])}}},{key:"getPenaltyScore",value:function(){for(var e=0,r=0;this.size>r;r++){for(var n=!1,a=0,o=[0,0,0,0,0,0,0],s=0;this.size>s;s++)this.modules[r][s]==n?5==++a?e+=t.PENALTY_N1:a>5&&e++:(this.finderPenaltyAddHistory(a,o),n||(e+=this.finderPenaltyCountPatterns(o)*t.PENALTY_N3),n=this.modules[r][s],a=1);e+=this.finderPenaltyTerminateAndCount(n,a,o)*t.PENALTY_N3}for(var l=0;this.size>l;l++){for(var u=!1,c=0,h=[0,0,0,0,0,0,0],d=0;this.size>d;d++)this.modules[d][l]==u?5==++c?e+=t.PENALTY_N1:c>5&&e++:(this.finderPenaltyAddHistory(c,h),u||(e+=this.finderPenaltyCountPatterns(h)*t.PENALTY_N3),u=this.modules[d][l],c=1);e+=this.finderPenaltyTerminateAndCount(u,c,h)*t.PENALTY_N3}for(var f=0;this.size-1>f;f++)for(var v=0;this.size-1>v;v++){var g=this.modules[f][v];g==this.modules[f][v+1]&&g==this.modules[f+1][v]&&g==this.modules[f+1][v+1]&&(e+=t.PENALTY_N2)}var m,y=0,A=V(this.modules);try{for(A.s();!(m=A.n()).done;)y=m.value.reduce((function(e,t){return e+(t?1:0)}),y)}catch(C){A.e(C)}finally{A.f()}var p=this.size*this.size,w=Math.ceil(Math.abs(20*y-10*p)/p)-1;return i(w>=0&&9>=w),i((e+=w*t.PENALTY_N4)>=0&&2568888>=e),e}},{key:"getAlignmentPatternPositions",value:function(){if(1==this.version)return[];for(var e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2)),r=[6],n=this.size-7;e>r.length;n-=t)r.splice(1,0,n);return r}},{key:"finderPenaltyCountPatterns",value:function(e){var t=e[1];i(3*this.size>=t);var r=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(!r||4*t>e[0]||t>e[6]?0:1)+(!r||4*t>e[6]||t>e[0]?0:1)}},{key:"finderPenaltyTerminateAndCount",value:function(e,t,r){return e&&(this.finderPenaltyAddHistory(t,r),t=0),this.finderPenaltyAddHistory(t+=this.size,r),this.finderPenaltyCountPatterns(r)}},{key:"finderPenaltyAddHistory",value:function(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)}}],[{key:"encodeText",value:function(r,n){var i=e.QrSegment.makeSegments(r);return t.encodeSegments(i,n)}},{key:"encodeBinary",value:function(r,n){var i=e.QrSegment.makeBytes(r);return t.encodeSegments([i],n)}},{key:"encodeSegments",value:function(e,n){var o,s,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:40,c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1,h=5>=arguments.length||void 0===arguments[5]||arguments[5];if(t.MIN_VERSION>l||l>u||u>t.MAX_VERSION||-1>c||c>7)throw new RangeError("Invalid value");for(o=l;;o++){var d=8*t.getNumDataCodewords(o,n),f=a.getTotalBits(e,o);if(d>=f){s=f;break}if(o>=u)throw new RangeError("Data too long")}for(var v=0,g=[t.Ecc.MEDIUM,t.Ecc.QUARTILE,t.Ecc.HIGH];g.length>v;v++){var m=g[v];h&&s<=8*t.getNumDataCodewords(o,m)&&(n=m)}var y,A=[],p=V(e);try{for(p.s();!(y=p.n()).done;){var w=y.value;r(w.mode.modeBits,4,A),r(w.numChars,w.mode.numCharCountBits(o),A);var C,M=V(w.getData());try{for(M.s();!(C=M.n()).done;){var E=C.value;A.push(E)}}catch(S){M.e(S)}finally{M.f()}}}catch(S){p.e(S)}finally{p.f()}i(A.length==s);var P=8*t.getNumDataCodewords(o,n);i(P>=A.length),r(0,Math.min(4,P-A.length),A),r(0,(8-A.length%8)%8,A),i(A.length%8==0);for(var R=236;P>A.length;R^=253)r(R,8,A);for(var b=[];A.length>8*b.length;)b.push(0);return A.forEach((function(e,t){return b[t>>>3]|=e<<7-(7&t)})),new t(o,n,b,c)}},{key:"getNumRawDataModules",value:function(e){if(t.MIN_VERSION>e||e>t.MAX_VERSION)throw new RangeError("Version number out of range");var r=(16*e+128)*e+64;if(e>=2){var n=Math.floor(e/7)+2;r-=(25*n-10)*n-55,7>e||(r-=36)}return i(r>=208&&29648>=r),r}},{key:"getNumDataCodewords",value:function(e,r){return Math.floor(t.getNumRawDataModules(e)/8)-t.ECC_CODEWORDS_PER_BLOCK[r.ordinal][e]*t.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][e]}},{key:"reedSolomonComputeDivisor",value:function(e){if(1>e||e>255)throw new RangeError("Degree out of range");for(var r=[],n=0;e-1>n;n++)r.push(0);r.push(1);for(var i=1,a=0;e>a;a++){for(var o=0;r.length>o;o++)r[o]=t.reedSolomonMultiply(r[o],i),r.length>o+1&&(r[o]^=r[o+1]);i=t.reedSolomonMultiply(i,2)}return r}},{key:"reedSolomonComputeRemainder",value:function(e,r){var n,i=r.map((function(e){return 0})),a=V(e);try{var o=function(){var e=n.value^i.shift();i.push(0),r.forEach((function(r,n){return i[n]^=t.reedSolomonMultiply(r,e)}))};for(a.s();!(n=a.n()).done;)o()}catch(s){a.e(s)}finally{a.f()}return i}},{key:"reedSolomonMultiply",value:function(e,t){if(e>>>8!=0||t>>>8!=0)throw new RangeError("Byte out of range");for(var r=0,n=7;n>=0;n--)r=r<<1^285*(r>>>7),r^=(t>>>n&1)*e;return i(r>>>8==0),r}}]),t}();function r(e,t,r){if(0>t||t>31||e>>>t!=0)throw new RangeError("Value out of range");for(var n=t-1;n>=0;n--)r.push(e>>>n&1)}function n(e,t){return 0!=(e>>>t&1)}function i(e){if(!e)throw Error("Assertion error")}t.MIN_VERSION=1,t.MAX_VERSION=40,t.PENALTY_N1=3,t.PENALTY_N2=3,t.PENALTY_N3=40,t.PENALTY_N4=10,t.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],t.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],e.QrCode=t;var a=function(){function e(t,r,n){if(x(this,e),this.mode=void 0,this.numChars=void 0,this.bitData=void 0,this.mode=t,this.numChars=r,this.bitData=n,0>r)throw new RangeError("Invalid argument");this.bitData=n.slice()}return U(e,[{key:"getData",value:function(){return this.bitData.slice()}}],[{key:"makeBytes",value:function(t){var n,i=[],a=V(t);try{for(a.s();!(n=a.n()).done;)r(n.value,8,i)}catch(o){a.e(o)}finally{a.f()}return new e(e.Mode.BYTE,t.length,i)}},{key:"makeNumeric",value:function(t){if(!e.isNumeric(t))throw new RangeError("String contains non-numeric characters");for(var n=[],i=0;t.length>i;){var a=Math.min(t.length-i,3);r(parseInt(t.substring(i,i+a),10),3*a+1,n),i+=a}return new e(e.Mode.NUMERIC,t.length,n)}},{key:"makeAlphanumeric",value:function(t){if(!e.isAlphanumeric(t))throw new RangeError("String contains unencodable characters in alphanumeric mode");var n,i=[];for(n=0;t.length>=n+2;n+=2){var a=45*e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n));r(a+=e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n+1)),11,i)}return t.length>n&&r(e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n)),6,i),new e(e.Mode.ALPHANUMERIC,t.length,i)}},{key:"makeSegments",value:function(t){return""==t?[]:e.isNumeric(t)?[e.makeNumeric(t)]:e.isAlphanumeric(t)?[e.makeAlphanumeric(t)]:[e.makeBytes(e.toUtf8ByteArray(t))]}},{key:"makeEci",value:function(t){var n=[];if(0>t)throw new RangeError("ECI assignment value out of range");if(128>t)r(t,8,n);else if(16384>t)r(2,2,n),r(t,14,n);else{if(t>=1e6)throw new RangeError("ECI assignment value out of range");r(6,3,n),r(t,21,n)}return new e(e.Mode.ECI,0,n)}},{key:"isNumeric",value:function(t){return e.NUMERIC_REGEX.test(t)}},{key:"isAlphanumeric",value:function(t){return e.ALPHANUMERIC_REGEX.test(t)}},{key:"getTotalBits",value:function(e,t){var r,n=0,i=V(e);try{for(i.s();!(r=i.n()).done;){var a=r.value,o=a.mode.numCharCountBits(t);if(a.numChars>=1<<o)return 1/0;n+=4+o+a.bitData.length}}catch(s){i.e(s)}finally{i.f()}return n}},{key:"toUtf8ByteArray",value:function(e){e=encodeURI(e);for(var t=[],r=0;e.length>r;r++)"%"!=e.charAt(r)?t.push(e.charCodeAt(r)):(t.push(parseInt(e.substring(r+1,r+3),16)),r+=2);return t}}]),e}();a.NUMERIC_REGEX=/^[0-9]*$/,a.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,a.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",e.QrSegment=a}(q||(q={})),_=q||(q={}),H=_.QrCode||(_.QrCode={}),(j=U((function e(t,r){x(this,e),this.ordinal=void 0,this.formatBits=void 0,this.ordinal=t,this.formatBits=r}))).LOW=new j(0,1),j.MEDIUM=new j(1,0),j.QUARTILE=new j(2,3),j.HIGH=new j(3,2),H.Ecc=j,function(e){!function(e){var t=function(){function e(t,r){x(this,e),this.modeBits=void 0,this.numBitsCharCount=void 0,this.modeBits=t,this.numBitsCharCount=r}return U(e,[{key:"numCharCountBits",value:function(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}}]),e}();t.NUMERIC=new t(1,[10,12,14]),t.ALPHANUMERIC=new t(2,[9,11,13]),t.BYTE=new t(4,[8,16,16]),t.KANJI=new t(8,[8,10,12]),t.ECI=new t(7,[0,0,0]),e.Mode=t}(e.QrSegment||(e.QrSegment={}))}(q||(q={}));var X=q,Q=["value","size","level","bgColor","fgColor","includeMargin","marginSize","imageSettings"],K={L:X.QrCode.Ecc.LOW,M:X.QrCode.Ecc.MEDIUM,Q:X.QrCode.Ecc.QUARTILE,H:X.QrCode.Ecc.HIGH};function W(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[];return e.forEach((function(e,n){var i=null;e.forEach((function(a,o){if(!a&&null!==i)return r.push("M".concat(i+t," ").concat(n+t,"h").concat(o-i,"v1H").concat(i+t,"z")),void(i=null);if(o!==e.length-1)a&&null===i&&(i=o);else{if(!a)return;r.push(null===i?"M".concat(o+t,",").concat(n+t," h1v1H").concat(o+t,"z"):"M".concat(i+t,",").concat(n+t," h").concat(o+1-i,"v1H").concat(i+t,"z"))}}))})),r.join("")}function G(e,t){return e.slice().map((function(e,r){return t.y>r||r>=t.y+t.h?e:e.map((function(e,r){return(t.x>r||r>=t.x+t.w)&&e}))}))}var Y=function(){function e(t,r){var n=this;for(var i in x(this,e),this.canvas=void 0,this.pixelRatio="undefined"!=typeof window?window.devicePixelRatio:1,this.path2D=!0,this.SUPPORTS_PATH2D=void 0,this.createImage=function(){return new Image},this.createPath2D=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t="createPath2D";return n.canvas&&t in n.canvas?n.canvas[t](e):new Path2D(e)},this.canvas=t,r)i in this&&(this[i]=r[i]);this.SUPPORTS_PATH2D=function(){try{n.createPath2D()}catch(e){return!1}return!0}()}return U(e,[{key:"render",value:function(e,t){var r,n,i=this,a=e.value,o=e.size,s=void 0===o?128:o,l=e.level,u=void 0===l?"L":l,c=e.bgColor,h=void 0===c?"#FFFFFF":c,d=e.fgColor,f=void 0===d?"#000000":d,v=e.includeMargin,g=void 0!==v&&v,m=e.marginSize,y=e.imageSettings,A=(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;a.length>n;n++)0>t.indexOf(r=a[n])&&(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;a.length>n;n++)0>t.indexOf(r=a[n])&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}}(e,Q),null==y?void 0:y.src),p=this.canvas,w=p.getContext("2d");if(w&&a){var C=X.QrCode.encodeText(a,K[u]).getModules(),M=(r=g,null!=(n=m)?Math.floor(n):r?4:0),E=C.length+2*M,P=function(e,t,r,n){if(null==n)return null;var i=e.length+2*r,a=Math.floor(.1*t),o=i/t,s=(n.width||a)*o,l=(n.height||a)*o,u=null==n.x?e.length/2-s/2:n.x*o,c=null==n.y?e.length/2-l/2:n.y*o,h=null;if(n.excavate){var d=Math.floor(u),f=Math.floor(c);h={x:d,y:f,w:Math.ceil(s+u-d),h:Math.ceil(l+c-f)}}return{x:u,y:c,h:l,w:s,excavation:h}}(C,s,M,y),R=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=null!=P&&null!==e&&e.complete&&0!==e.naturalHeight&&0!==e.naturalWidth;r&&null!=P.excavation&&(C=G(C,P.excavation)),t&&t(C);var n=i.pixelRatio;w.clearRect(0,0,p.width,p.height),w.setTransform(1,0,0,1,0,0),p.height=p.width=s*n;var a=s/E*n;if(w.scale(a,a),w.fillStyle=h,w.fillRect(0,0,E,E),w.fillStyle=f,i.SUPPORTS_PATH2D&&i.path2D){var o=i.createPath2D(W(C,M));w.fill(o)}else C.forEach((function(e,t){e.forEach((function(e,r){e&&w.fillRect(r+M,t+M,1,1)}))}));var l=(null==e?void 0:e.path)||e;r&&w.drawImage(l,P.x+M,P.y+M,P.w,P.h),"draw"in w&&w.draw()};if(A){var b=this.createImage(p);b.onload=function(){R(b)},b.onerror=function(e){R(),console.warn(e)},b.src=A}else R()}}}]),e}();function J(e){return/^(-)?\d+(\.\d+)?$/.test(e)}function Z(e){return function(e){return null!=e}(e)?J(e=String(e))?`${e}px`:e:null}const $="undefined"!=typeof window;let ee=class{constructor(){this.currentSrc=null,this.naturalHeight=0,this.naturalWidth=0,this.width=0,this.height=0,this.tagName="IMG",this.path="",this.crossOrigin="",this.referrerPolicy="",this.onload=()=>{},this.onerror=()=>{},this.complete=!1}set src(e){if(console.log("src",e),!e)return this.onerror();e=e.replace(/^@\//,"/"),this.currentSrc=e,r({src:e,success:e=>{this.complete=!0,this.path=e.path,this.naturalWidth=this.width=e.width,this.naturalHeight=this.height=e.height,this.onload()},fail:()=>{this.onerror()}})}get src(){return this.currentSrc}};function te(e){return e&&e.createImage?e.createImage():this&&"canvas"==this.tagName&&!("toBlob"in this)||e&&!("toBlob"in e)?new ee:$?new window.Image:new ee}function re(e,t=0){if(J(e))return Number(e);if("string"==typeof e){const r=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g.exec(e);if(!e||!r)return 0;const i=r[3],a=parseFloat(e);if("rpx"===i)return n(a);if("px"===i)return 1*a;if("%"==i)return a/100*t}return 0}const ne=k(i({name:"l-qrcode",props:B,emits:["success"],setup(e,{emit:t}){const r=u(),n=`l-qrcode${r.uid}`,i=a((()=>`width: ${Z(e.size)}; height: ${Z(e.size)};`));let h=null,d=null;const f=a((()=>{const{value:t,icon:r,size:n,color:i,bgColor:a,bordered:o,iconSize:s,errorLevel:l,marginSize:u}=e,c={src:r,x:void 0,y:void 0,height:re(s),width:re(s),excavate:!0};return{value:t,size:re(n),level:l,bgColor:a,fgColor:i,imageSettings:r?c:void 0,includeMargin:o,marginSize:u??0}})),v=function(e,t=300){let r=null;return function(...n){r&&clearTimeout(r),r=setTimeout((()=>{e.apply(this,n)}),t)}}((e=>{if(!d)return;const t=Object.assign({canvasId:n,canvas:null},e);"toTempFilePath"in d?d.toTempFilePath(t):c(t,r)})),g=()=>{e.useCanvasToTempFilePath&&v({success(e){t("success",e.tempFilePath)}})},m=o(e,(()=>{h&&(h.render(f.value),g())}));return s((()=>{T(n,{context:r}).then((e=>{d=e,h=new Y(e,{path2D:!1,pixelRatio:1,createImage:te}),h.render(f.value),g()}))})),l((()=>{m&&m()})),{canvasId:n,styles:i,props:e,canvasToTempFilePath:v}}}),[["render",function(e,t,r,n,i,a){const o=g,s=m;return h(),d(s,{class:"l-qrcode",style:v([e.styles])},{default:f((()=>[e.use2d?(h(),d(o,{key:0,style:v(e.styles),type:"2d","canvas-id":e.canvasId,id:e.canvasId},null,8,["style","canvas-id","id"])):(h(),d(o,{key:1,style:v(e.styles),"canvas-id":e.canvasId,id:e.canvasId},null,8,["style","canvas-id","id"]))])),_:1},8,["style"])}],["__scopeId","data-v-48d9da46"]]);const ie=k({components:{tabBar:F},data:()=>({title:"引荐人激励计划",userdata:{},codeData:{},qrCodeUrl:"",qrcodes:"",loading:!0}),created(){this.getUserInfo(),this.getCode()},onShareAppMessage(e){return{title:"哒达招商",path:`/pages/repository/capacity?scene=${this.codeData.invitationCode}`}},onShareTimeline:e=>({title:"哒达招商",path:"/pages/repository/capacity"}),methods:{success(e){this.loading=!1,this.qrcodes=e},copy(e){y({data:e,success(e){}})},getCode(){this.$api.geneCodeAPI({data:{envVersion:z,pagePath:"pages/repository/capacity"},method:"post"}).then((e=>{this.codeData=e.result,this.qrCodeUrl=this.codeData.wechatUrl+`?AirInvitationCode=${this.codeData.invitationCode}`,console.log(this.qrCodeUrl)}))},getUserInfo(){this.$api.userMessage().then((e=>{this.userdata=e.result}))}}},[["render",function(e,t,r,n,i,a){const o=N(A("page-meta"),I),s=p("tabBar"),l=S,u=N(A("l-qrcode"),ne),c=g;return h(),w(b,null,[C(o,{"page-style":"background-color: #FAFCFF"}),M("div",null,[C(s,{title:i.title},null,8,["title"]),M("div",{class:"box","canvas-id":"box"},[M("div",{class:"head"},[E("携手共赢"),M("span",{class:"red"},"邀请有礼")]),M("div",{class:"qrcode"},[i.userdata.headImgUrl?(h(),d(l,{key:0,src:i.userdata.headImgUrl,class:"showimg"},null,8,["src"])):(h(),d(l,{key:1,src:O,class:"showimg"})),M("span",{class:"name"},P(i.userdata.realName),1),C(l,{class:"qrImg",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAbgAAAGYBAMAAADCSOq2AAAALVBMVEUAAAAycf8wcf8wcP8wb/8vb/8pbf8xcP8wcP8wcP8vcP8wcv8qbP8wcP8vbf+TOC3iAAAAD3RSTlMATUlDKh4MPCUxNw8GExiqzHr2AAAEWElEQVR42uzZvWtTURzG8UdN0tbawjepJlUKBtFJxIjuFhEHF18G15jZoYIO4pLi5iDN5CS0izhWcHFzUkedXIsg+GdocsBgc85N0ikPPZ8xd3q45/dybpRU+f36CkOaIQyde3azrWktvmwCsx8OqL7b1VQ+tMAlHNRuawoPwCkcPNXEXuAWjmua0H38wk2a7geO4XiuCZTwDMd1jVVpQfDk1SPNvM+/bjUJ6m2N85Pg6jeZWLpBcEljzDNQfSMjH5sMdFWsF7J1ZeVrSLeqQscts0mlkG5bRXboeys7X+g7rQIL9J2XoXv0bSltE6DWlqEwwhpKWiYcXEuhXewppRNajqkewAWlPA5bjKkSQC351PrFSb2id/PeuOL+Vd1awamsyVgLqCuqHArSWAfgjmKOhUfGygCXFXMXqMvaDtBIntiLstZJdY2y9ZAL5lOVNQdUP8lbE9hITLmGzG0CZzRq3b/kpKPASqKfbMncQryjLALYfPFKWQLYHW007rtX0AK60a1zRfbWge1oKa7J3nfgrPZ7OPjV3hHgVHSz3JC9OeDk6GH1X776StFVpOd+3xk2/dXoNXxP9paBenRAWH6N/V8lOq6bgPud4K8TQFX7zdpfwwcFkMM5yuFc5XCucjhXOZyrHM5VDucqh3OVw7nK4VwdvnBZlmXZH/bgQAAAAAAAyP+1EVRVVVVVVVVVVVVVVVVVVdi7gxoAYBAIghrwb7ZJH1ggBzMOVsECAAAAAAAAAAAAAAAATKlv03q1mrgs4lKJS3UlDgAAAAAAAAAAAAAAAAAAHntwIAAAAAAA5P/aCKqqqqqqqqqqqqqqqqqq0t4ds0YRhGEcf9C7M7nT4H83MSZBcBGtRFxRsDSIWGihRrCTeKVYGNDCQoitWOQqKyE2YhlBsBULsdTSLgh+j7CknNthrrsnmV/5bvWws+++M1tsNi0Oyk9XAXI4RzmcqxzOVQ7nKodzlcO5yuFc5XCucjhXOZyrwxeuAr7L3nGgCKo1sC57PaAMqhvAruzNAYtBdQTck70usBRUV4EbstcB5oPqfeC57B0DFoLqE+Ck7B0BTo2tnpG9P8DZoNoH5mVvFdgOqjNAKXs1sBlUBwC/ZW4AsDM+85bMzQJly2q9KHM/WjrHF+C0zL0BVlpef4X7vqBqGUW6AJuy1qFtQq6BC7I2BMrW9bosax+BBY1zFPddTxfgcssl93U5jNyeDfcJrAYWIxM127LVB1iJNdIl2RoRO02ocT5r6BB9rIbWt24Ub4hzGD91fRq78bmT0vJstldDfPKfpXFehh7Q2IrPL41PsvOUxnLCwi3uysyvKqVdjGgU72SlyZbQ6GegUXyQkb8VJO1G/7Hvqs1J2Imb7LuU2FKB4sXjZ5p6P//fBkh9hXUITNsnZQLJc+M3z3CvlWTNMdw1JVrzC3ddyR65hXupCXytncKVdzSRwdvKJVzxeUeT6j18f2X6w517dWtdbfYAAhLTMA86mNQAAAAASUVORK5CYII="}),M("div",{class:"l-qrcode"},[C(u,{useCanvasToTempFilePath:"",onSuccess:a.success,value:i.qrCodeUrl,size:"320rpx",icon:"/static/user/userimg.png",iconSize:"70rpx",bgColor:"#FFFFFF"},null,8,["onSuccess","value"])]),!i.loading&&i.qrcodes?(h(),d(l,{key:2,"show-menu-by-longpress":"true",class:"qrcodeImg",src:i.qrcodes},null,8,["src"])):R("",!0),i.loading?(h(),w("div",{key:3,class:"qrcodeText"},"加载中...")):R("",!0),M("div",{onClick:t[0]||(t[0]=e=>a.copy(i.codeData.invitationCode)),class:"InvitationCode"}," 邀请码："+P(i.codeData.invitationCode),1)])]),M("div",{class:"copyWriter"},[M("div",{class:"titleText"},"邀请策略"),M("div",{class:"subtitle"}," 1.【如何邀请自由顾问加入团队？】 "),M("div",{class:"details"}," 把邀请码或者二维码分享给好友或者相关人士，对方在微信/哒达招商-顾问身份-我的中用扫一扫进行扫码或者在设置中填写团队邀请码，对方则可成功加入团队。 "),M("div",{class:"subtitle"}," 2.【邀请后有啥奖励？】 "),M("div",{class:"details"}," 被邀请对方加入团队后，你可享受对方完成哒达助招需求任务佣金的15%，对方如果发展下级，您可获得二级下级完成哒达助招需求任务佣金的5%。佣金分成全部需要在对方结单后进行分成，否则不参与分成。 "),M("div",{class:"subtitle"}," 3.【分成奖励在哪里进行查看？】 "),M("div",{class:"details"}," 可以在我的团队中查看团队成员订单进度情况，如果状态为已结单，则可以进行分成。分成在哒达助招-我的钱包中进行查看，可以进行提现。 ")]),M("div",{style:{height:"60rpx"}})]),C(c,{class:"canvasPoster","canvas-id":"canvasPoster",style:{width:"1260px",height:"2176px",zoom:"40%"}})],64)}],["__scopeId","data-v-14fdd3f5"]]);export{ie as default};
