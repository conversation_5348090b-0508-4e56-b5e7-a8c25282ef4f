import{P as t,o as e,c as l,w as i,f as n,n as a,a as s,F as r,b as o,e as d,r as c,g as h,t as f,d as u,i as p,S as g}from"./index-HcMwrp5e.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";const m=y({name:"VTabs",props:{value:{type:Number,default:0},modelValue:{type:Number,default:0},tabs:{type:Array,default:()=>[]},bgColor:{type:String,default:"#fff"},padding:{type:String,default:"0"},color:{type:String,default:"#333"},activeColor:{type:String,default:"#2979ff"},fontSize:{type:String,default:"28rpx"},activeFontSize:{type:String,default:"32rpx"},bold:{type:Boolean,default:!1},scroll:{type:Boolean,default:!0},height:{type:String,default:"70rpx"},lineColor:{type:String,default:"#2979ff"},lineHeight:{type:[String,Number],default:"10rpx"},lineScale:{type:Number,default:.5},lineRadius:{type:String,default:"10rpx"},pills:{type:Boolean,default:!1},pillsColor:{type:String,default:"#2979ff"},pillsBorderRadius:{type:String,default:"10rpx"},field:{type:String,default:""},fixed:{type:Boolean,default:!1},paddingItem:{type:String,default:"0 22rpx"},lineAnimation:{type:Boolean,default:!0},zIndex:{type:Number,default:1993}},emits:["update:modelValue","change"],data:()=>({lineWidth:30,currentWidth:0,lineLeft:0,pillsLeft:0,scrollLeft:0,container:{width:0,height:0,left:0,right:0},current:0,scrollWidth:0}),computed:{getDomId(){const t="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",e=t.length;let l="";for(let i=0;i<16;i++)l+=t.charAt(Math.floor(Math.random()*e));return`xfjpeter_${l}`}},watch:{modelValue:{immediate:!0,handler(t){this.current=t>-1&&t<this.tabs.length?t:0,this.$nextTick(this.update)}}},methods:{change:function(t,e){let l,i=0;return function(){const n=this,a=arguments,s=Date.now(),r=e-(s-i);function o(){i=s,t.apply(n,a)}r<=0?(l&&(clearTimeout(l),l=null),o()):l||(l=setTimeout((()=>{l=null,o()}),r))}}((function(t,e){const l=!!this.tabs[t].disabled;this.current===t||l||(this.current=t,this.$emit("update:modelValue",t),this.$emit("change",t,e))}),300),createQueryHandler(){let e;return e=t().in(this),e},update(){const t=this;!function(t){if("function"==typeof queueMicrotask)queueMicrotask(t);else if("function"==typeof MutationObserver){const e=document.createElement("div");new MutationObserver(t).observe(e,{childList:!0}),e.textContent="xfjpeter"}else setTimeout(t,0)}((()=>{this.tabs.length&&t.createQueryHandler().select(`#${this.getDomId}`).boundingClientRect((e=>{const{width:l,height:i,left:n,right:a}=e||{};this.container={width:l,height:i,left:n,right:a-l},t.calcScrollWidth(),t.setScrollLeft(),t.setLine()})).exec()}))},calcScrollWidth(t){const e=this.createQueryHandler().select(`#${this.getDomId}`);e.fields({scrollOffset:!0}),e.scrollOffset((e=>{"function"==typeof t?t(e):this.scrollWidth=e.scrollWidth})).exec()},setScrollLeft(){this.calcScrollWidth((t=>{let e=t.scrollLeft;this.createQueryHandler().select(`#${this.getDomId} .v-tabs__container-item.active`).boundingClientRect((t=>{if(!t)return;let l=(this.container.width-t.width)/2,i=this.scrollWidth-this.container.width;e+=t.left-l-this.container.left,e<0?e=0:e>i&&(e=i),this.scrollLeft=e})).exec()}))},setLine(){this.calcScrollWidth((t=>{const e=t.scrollLeft;this.createQueryHandler().select(`#${this.getDomId} .v-tabs__container-item.active`).boundingClientRect((t=>{t&&(this.pills?(this.currentWidth=t.width,this.pillsLeft=e+t.left-this.container.left):(this.lineWidth=t.width*this.lineScale,this.lineLeft=e+t.left+(t.width-t.width*this.lineScale)/2-this.container.left))})).exec()}))}}},[["render",function(t,y,m,b,x,S){const _=p,w=g;return e(),l(_,{class:"v-tabs"},{default:i((()=>[n(w,{id:S.getDomId,"scroll-x":t.scroll,"scroll-left":t.scroll?x.scrollLeft:0,"scroll-with-animation":t.scroll,style:a({position:t.fixed?"fixed":"relative",zIndex:t.zIndex,width:"100%"})},{default:i((()=>[n(_,{class:"v-tabs__container",style:a({display:t.scroll?"inline-flex":"flex",whiteSpace:t.scroll?"nowrap":"normal",background:t.bgColor,height:t.height,padding:t.padding})},{default:i((()=>[(e(!0),s(r,null,o(t.tabs,((n,s)=>(e(),l(_,{class:d(["v-tabs__container-item",{disabled:!!n.disabled},{active:x.current==s}]),key:s,style:a({color:x.current==s?t.activeColor:t.color,fontSize:(x.current,t.fontSize),fontWeight:t.bold&&x.current==s?"bold":"",justifyContent:t.scroll?"":"center",flex:t.scroll?"":1,padding:t.paddingItem}),onClick:t=>S.change(s,n)},{default:i((()=>[c(t.$slots,"default",{row:n,index:s},(()=>[h(f(t.field?n[t.field]:n),1)]),!0)])),_:2},1032,["class","style","onClick"])))),128)),t.tabs.length?(e(),s(r,{key:0},[t.pills?(e(),l(_,{key:1,class:d(["v-tabs__container-pills",{animation:t.lineAnimation}]),style:a({background:t.pillsColor,borderRadius:t.pillsBorderRadius,width:x.currentWidth+"px",transform:`translate3d(${x.pillsLeft}px, 0, 0)`,height:t.height})},null,8,["class","style"])):(e(),l(_,{key:0,class:d(["v-tabs__container-line",{animation:t.lineAnimation}]),style:a({background:t.lineColor,width:x.lineWidth+"px",height:t.lineHeight,borderRadius:t.lineRadius,transform:`translate3d(${x.lineLeft}px, 0, 0)`})},null,8,["class","style"]))],64)):u("",!0)])),_:3},8,["style"])])),_:3},8,["id","scroll-x","scroll-left","scroll-with-animation","style"]),n(_,{class:"v-tabs__placeholder",style:a({height:t.fixed?t.height:"0",padding:t.padding})},null,8,["style"])])),_:3})}],["__scopeId","data-v-f79b2a0a"]]);export{m as _};
