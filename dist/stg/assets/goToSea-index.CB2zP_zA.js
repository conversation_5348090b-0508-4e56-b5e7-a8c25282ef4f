import{l as e,p as t,A as a,o as s,c as i,w as n,f as o,g as r,d as l,a as c,b as d,B as h,t as u,F as p,C as m,i as g,S as f,z as y,m as v,s as C,n as A,H as L,I as w,e as k,a8 as b,ae as N,W as I,U as S,u as D,k as B}from"./index-CBCsGYoT.js";import{r as x,_ as T}from"./regionCut.CGH9u9zb.js";import{_ as P}from"./history.BKBs_WXS.js";import{_ as M}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as O}from"./v-tabs.JPUtxAEP.js";import{r as E}from"./uni-app.es.CZb2JZWI.js";import{_ as U}from"./uni-easyinput.D_LnJWIZ.js";import{_ as V,a as R}from"./uni-forms.BsccYHPu.js";import{_ as F}from"./uni-tooltip.Cee0nqWM.js";import{_ as Q}from"./uni-data-select.DWY_AY9U.js";import{_ as j}from"./uni-data-checkbox.lGJQWvI7.js";import{_ as H}from"./uni-popup.BLXBf1r-.js";import{_ as q}from"./remindericon.Di7cnH87.js";import{g as W,l as X}from"./utils.61Hi-B7M.js";import{_ as Z}from"./page-meta.BCoSkkXs.js";import{e as G}from"./List.B_Jwv3Df.js";import{t as J,M as _}from"./MultipleChoice.CSsPCbLz.js";import{_ as K}from"./returnFn.BYkANsDr.js";import{i as Y}from"./industryChain.DNIq-Ht6.js";import{m as z}from"./moreScreen.Db_lz0V-.js";import{S as $}from"./SingleLineSelection.BegwTxsO.js";import{_ as ee}from"./right.D4iebSn6.js";import{_ as te}from"./right2.DB4H0AJ0.js";import{_ as ae,a as se}from"./service.zf6UL628.js";import"./uni-load-more.BMOBclrH.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-transition.Ckb0qY8x.js";const ie=M({components:{regionCut:x},data:()=>({statusBarHeight:e("statusBarHeight"),keyword:"",_lastDataShow:[],token:""}),mounted(){this.token=e("token")},methods:{updataPage(e){this.getData(e)},getData(e){let t={type:4,country:e};this.$api.overseasPermission({data:t,method:"post"}).then((e=>{this.getShowList(e.result)}))},getShowList(e){let t={0:{list:["国家概况"],data:[]},1:{list:["ODI备案","公司设立","营商政策","公司运营"],data:[]},2:{list:["重点产业","产业园区"],data:[]},3:{list:["投资并购","投资自建"],data:[]}};for(let a in t)t[a].list.map((s=>{let i=e.find((e=>e.name===s));i&&t[a].data.push(i)}));this._lastDataShow=t},goBack(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:"",path:"goBack"}))},go(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{type:2},path:"history"}))},goChat(e){var a;if(2==e.state)return t({title:"敬请期待",icon:"none"});null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{data:{id:e.id,name:e.name,isOld:e.isOld,dialogueType:3}},path:"chat"}))}}},[["render",function(e,t,y,v,C,A){const L=m,w=g,k=a("regionCut"),b=f;return s(),i(b,{style:{position:"absolute"},class:"mainView","scroll-y":"true"},{default:n((()=>[o(w,{class:"box"},{default:n((()=>[o(w,{class:"topIntroduce"},{default:n((()=>[o(L,{src:"data:image/png;base64,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",class:"AI2"})])),_:1}),o(w,{class:"setInputBox"},{default:n((()=>[o(k,{onUpdataPage:A.updataPage,ref:"regionCut"},null,8,["onUpdataPage"]),C.token?(s(),i(w,{key:0,class:"historyBox",onClick:t[0]||(t[0]=e=>A.go("history"))},{default:n((()=>[o(L,{class:"icon",src:P}),r(" 历史记录 ")])),_:1})):l("",!0)])),_:1}),0!=C._lastDataShow.length?(s(),i(w,{key:0,class:"particulars"},{default:n((()=>{var e,t,a,m,g,f,y,v;return[0!=(null==(t=null==(e=C._lastDataShow)?void 0:e[0])?void 0:t.data.length)?(s(),i(w,{key:0,class:"fetchitem"},{default:n((()=>{var e,t;return[o(w,{class:"headtext"},{default:n((()=>[r("属地国信息")])),_:1}),(s(!0),c(p,null,d(null==(t=null==(e=C._lastDataShow)?void 0:e[0])?void 0:t.data,(e=>(s(),i(w,{key:e.id,onClick:t=>A.goChat(e)},{default:n((()=>[o(w,{class:"item"},{default:n((()=>[o(w,{class:"titlehead"},{default:n((()=>[o(L,{class:"knowledgeImgs",src:null==e?void 0:e.iconPath},null,8,["src"]),h("span",{class:"name"},u(e.name),1)])),_:2},1024),2==e.state?(s(),c("span",{key:0,class:"developing"},"开发中")):l("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))]})),_:1})):l("",!0),0!=(null==(m=null==(a=C._lastDataShow)?void 0:a[1])?void 0:m.data.length)?(s(),i(w,{key:1,class:"fetchitem"},{default:n((()=>{var e,t;return[o(w,{class:"headtext"},{default:n((()=>[r("企业事务")])),_:1}),(s(!0),c(p,null,d(null==(t=null==(e=C._lastDataShow)?void 0:e[1])?void 0:t.data,(e=>(s(),i(w,{key:e.id,onClick:t=>A.goChat(e)},{default:n((()=>[o(w,{class:"item"},{default:n((()=>[o(w,{class:"titlehead"},{default:n((()=>[o(L,{class:"knowledgeImgs",src:null==e?void 0:e.iconPath},null,8,["src"]),h("span",{class:"name"},u(e.name),1)])),_:2},1024),2==e.state?(s(),c("span",{key:0,class:"developing"},"开发中")):l("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))]})),_:1})):l("",!0),0!=(null==(f=null==(g=C._lastDataShow)?void 0:g[2])?void 0:f.data.length)?(s(),i(w,{key:2,class:"fetchitem"},{default:n((()=>{var e,t;return[o(w,{class:"headtext"},{default:n((()=>[r("产业分析")])),_:1}),(s(!0),c(p,null,d(null==(t=null==(e=C._lastDataShow)?void 0:e[2])?void 0:t.data,(e=>(s(),i(w,{key:e.id,onClick:t=>A.goChat(e)},{default:n((()=>[o(w,{class:"item"},{default:n((()=>[o(w,{class:"titlehead"},{default:n((()=>[o(L,{class:"knowledgeImgs",src:null==e?void 0:e.iconPath},null,8,["src"]),h("span",{class:"name"},u(e.name),1)])),_:2},1024),2==e.state?(s(),c("span",{key:0,class:"developing"},"开发中")):l("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))]})),_:1})):l("",!0),0!=(null==(v=null==(y=C._lastDataShow)?void 0:y[3])?void 0:v.data.length)?(s(),i(w,{key:3,class:"fetchitem"},{default:n((()=>{var e,t;return[o(w,{class:"headtext"},{default:n((()=>[r("对外投资")])),_:1}),(s(!0),c(p,null,d(null==(t=null==(e=C._lastDataShow)?void 0:e[3])?void 0:t.data,(e=>(s(),i(w,{key:e.id,onClick:t=>A.goChat(e)},{default:n((()=>[o(w,{class:"item"},{default:n((()=>[o(w,{class:"titlehead"},{default:n((()=>[o(L,{class:"knowledgeImgs",src:null==e?void 0:e.iconPath},null,8,["src"]),h("span",{class:"name"},u(e.name),1)])),_:2},1024),2==e.state?(s(),c("span",{key:0,class:"developing"},"开发中")):l("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))]})),_:1})):l("",!0)]})),_:1})):l("",!0)])),_:1})])),_:1})}],["__scopeId","data-v-5207216f"]]);const ne=M({components:{reportLoading:M({data:()=>({}),methods:{opens(e){this.$refs.reportLoading.open()},toHistory(){this.$refs.reportLoading.close(),this.$emit("toHistory")}}},[["render",function(e,t,a,r,l,c){const d=E(y("uni-popup"),H),u=g;return s(),i(u,null,{default:n((()=>[o(d,{ref:"reportLoading","is-mask-click":!1,type:"dialog"},{default:n((()=>[h("div",{class:"applyaffirm"},[h("div",{class:"p1"},"深度分析进行中..."),h("div",{class:"p2"}," 小艾正在全力处理您的定制化报告 "),h("div",{class:"p3"}," 1、离开本页面不会中断生成，完成后将通过服务消息通知； "),h("div",{class:"p3"}," 2、您可随时在【我的报告】查看生成结果，预计等待时间3-15分钟 "),h("div",{onClick:t[0]||(t[0]=(...e)=>c.toHistory&&c.toHistory(...e)),class:"entrustBtn"}," 确定 ")])])),_:1},512)])),_:1})}],["__scopeId","data-v-f282cda3"]])},options:{styleIsolation:"shared"},data:()=>({canScroll:!0,topPadding:0,statusBarHeight:0,identity:3,tabs:["选址论证报告","投资可研报告"],tabCur:0,formData:{reportType:0,enterpriseName:"",enterpriseUniCode:"",enterpriseIntroduction:"",products:[{productName:"",introduction:"",id:Date.now()}],projectName:"",overseasCountry:e("defaultId"),overseasCountryName:e("defaultName"),chainId:"",nodeId:"",nodeName:"",nodeNameCustom:"",upperNodeId:"",upperNodeName:"",supplyChainRequirements:"",transportationAndLogistics:"",factoryRequirements:"0",priceRequirements:"",overseasProductInfo:[{productName:"",application:"",id:Date.now()+"2"}],haveIntendedRegion:!1,intendedRegions:"",needsWastewaterTreatment:!1,exportGoals:""},rules:{enterpriseName:{rules:[{required:!0,errorMessage:"请填写企业名称"}]},overseasCountry:{rules:[{required:!0,errorMessage:"请选择意向出海国家"}]},chainId:{rules:[{required:!0,errorMessage:"请选择产业链"}]},factoryRequirements:{rules:[{required:!0,errorMessage:"请选择厂房需求"}]},projectName:{rules:[{required:!0,errorMessage:"请输入项目名称"}]}},AssociativeList:[],showAssociativeList:!1,enterpriseTotal:0,enterpriseId:"",forbiddenCode:!1,enterpriseTime:null,fileUrl:"",overseasCountryList:[],industryChainList:[],checkList:[{text:"否",value:!1},{text:"是",value:!0}],wuLiuList:[{text:"公路",value:"公路"},{text:"铁路",value:"铁路"},{text:"航空",value:"航空"},{text:"港口",value:"港口"}],factoryRequirementsList:[{text:"租厂房",value:"0"},{text:"买土地",value:"1"}],priceRequirementsList:[],pageNum:1,pages:0,keyText:"",isLoading:!1,chainNodeList:[],nodeMap:{text:"nodeName",value:"nodeId"},template:{url:"",name:""},RepeatClick:!1,landAnghouse:[],emptyFormData:{reportType:0,enterpriseName:"",enterpriseUniCode:"",enterpriseIntroduction:"",products:[{productName:"",introduction:"",id:Date.now()}],projectName:"",overseasCountry:e("defaultId"),overseasCountryName:e("defaultName"),chainId:"",nodeId:"",nodeName:"",upperNodeId:"",upperNodeName:"",supplyChainRequirements:"",transportationAndLogistics:"",factoryRequirements:"0",priceRequirements:"",overseasProductInfo:[{productName:"",application:"",id:Date.now()+"2"}],haveIntendedRegion:!1,intendedRegions:"",needsWastewaterTreatment:!1,exportGoals:"",token:"",nodeNameCustom:""},customNode:!1,interval:null,needsWastewaterTreatment:!1,haveIntendedRegion:!1,saveState:!1,timer:null,reportParam:{siteSelectionAmount:"",feasibilityStudyAmount:""},localData:[{text:"否",value:!1},{text:"是",value:!0}]}),watch:{customNode(e){e||(this.formData.needsWastewaterTreatment=this.needsWastewaterTreatment,this.formData.haveIntendedRegion=this.haveIntendedRegion)},"formData.needsWastewaterTreatment"(e){this.formData.needsWastewaterTreatment=this.needsWastewaterTreatment},"formData.haveIntendedRegion"(e){this.formData.haveIntendedRegion=this.haveIntendedRegion}},mounted(){this.token=e("token"),this.token&&(this.requestGetReportParam(),this.requestGetCache(),e("stateList")&&(this.overseasCountryList=e("stateList").map((e=>({text:e.country,value:e.id})))))},created(){this.createTimer()},onUnmounted(){this.clearTimer()},onShow(){this.formData=JSON.parse(JSON.stringify(this.emptyFormData)),this.needsWastewaterTreatment=!1,this.haveIntendedRegion=!1,e("stateList")&&(this.overseasCountryList=e("stateList").map((e=>({text:e.country,value:e.id}))))},methods:{createTimer(){this.timer||(this.timer=setInterval((()=>{this.requestSaveCache()}),1e4))},requestGetReportParam(){this.$api.getReportParamAPI({data:{},method:"GET"}).then((e=>{"SUCCESS"==e.code&&(this.reportParam=e.result)}))},clearTimer(){clearInterval(this.timer),this.timer=null},checkWasteWaterTreatment(e){this.formData.needsWastewaterTreatment=e.detail.value,this.needsWastewaterTreatment=e.detail.value},changInput(e,t){var a,s;if("exportGoals"===e){let s=(null==(a=null==t?void 0:t.detail)?void 0:a.value)||this.formData[e];this.formData[e]=s}else this.formData[e]=null==(s=null==t?void 0:t.detail)?void 0:s.value},changePark(e){var t,a;(null==(t=null==e?void 0:e.detail)?void 0:t.value)&&"boolean"==typeof e.detail.value?(this.formData.haveIntendedRegion=null==(a=null==e?void 0:e.detail)?void 0:a.value,this.haveIntendedRegion=e.detail.value):(this.formData.haveIntendedRegion=!1,this.haveIntendedRegion=!1)},isLogin(){var e;if(this.token)return!0;return(null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):v({url:"/pages/login/index"}),!1},tabChange(e){this.formData.reportType=e,this.requestGetTemplate()},requestGetTemplate(){this.$api.getTemplateAPI({data:{reportType:this.tabCur},method:"GET"}).then((e=>{if("SUCCESS"==e.code){this.template.url=e.result;let t=e.result.split("/");this.template.name=t[t.length-1]}}))},requestGetCache(){this.$api.getOverseaIntelligenceCacheAPI({method:"GET"}).then((t=>{var a,s;t.result?(this.tabCur=t.result.reportType?Number(t.result.reportType):0,this.formData=JSON.parse(JSON.stringify(t.result)),this.formData.overseasCountryName=t.result.overseasCountry?t.result.overseasCountry:e("defaultName"),this.overseasCountryList.length?this.formData.overseasCountry=null==(a=this.overseasCountryList.find((e=>e.text==this.formData.overseasCountryName)))?void 0:a.value:this.setcatchCountry(),this.changeChain(t.result),this.needsWastewaterTreatment=t.result.needsWastewaterTreatment,this.haveIntendedRegion=t.result.haveIntendedRegion,this.formData.factoryRequirements=(null==(s=t.result)?void 0:s.factoryRequirements)||"0"):(this.formData=JSON.parse(JSON.stringify(this.emptyFormData)),this.setcatchCountry(),this.getChainList(this.formData.overseasCountryName),this.requestParkGetSearchParam()),this.requestGetTemplate()}))},setcatchCountry(){e("defaultName")&&(this.formData.overseasCountryName=e("defaultName"),this.formData.overseasCountry=e("defaultId"),this.changeChain())},requestParkGetSearchParam(){this.$api.parkGetSearchParamAPI({method:"GET",data:{country:this.formData.overseasCountryName}}).then((e=>{let t={house:[],land:[]},a=e.result.searchParams.find((e=>"厂房出租"==e.paramName)).paramValueList.map(((e,t)=>({value:e,text:e}))),s=e.result.searchParams.find((e=>"土地出让"==e.paramName)).paramValueList.map(((e,t)=>({value:e,text:e})));t.land=s,t.house=a,0===Number(this.formData.factoryRequirements)?this.priceRequirementsList=a:this.priceRequirementsList=s,this.landAnghouse=t}))},onChangeFactoryRequirements(e){console.log("0"===e),this.priceRequirementsList="0"===e?this.landAnghouse.house:this.landAnghouse.land,this.formData.priceRequirements=e===this.formData.factoryRequirements?this.formData.priceRequirements:""},selectCountry(e){this.formData.priceRequirements="",this.formData.overseasCountryName=this.overseasCountryList.find((t=>t.value==e)).text,this.changeChain()},changeChain(e){this.requestParkGetSearchParam(),this.customNode=!1,this.formData.nodeId="",this.formData.nodeName="",this.formData.chainId="",this.formData.chainName="",this.chainNodeList=[],this.getChainList(this.formData.overseasCountryName,e)},getChainList(e,t){this.$api.enterpriseallChainAPI({data:{country:e},method:"GET"}).then((a=>{this.industryChainList=a.result.map((e=>(e.text=e.chainName,e.value=e.chainId,e))),t&&t.overseasCountry==e&&t.chainId&&(this.formData.chainId=t.chainId,this.formData.chainName=t.chainName,this.requestChainNodeList(t.chainId,t))}))},goBack(){this.requestSaveCache()},onClickByTab(e){this.tabCur=e.currentIndex},onchange(){},onnodeclick(){this.canScroll=!0},onpopupopened(){},onpopupclosed(){this.canScroll=!0},goHistry(t){var a;if(!e("token"))return v({url:"/pages/login/index"});this.saveState=!1,null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{type:0===Number(this.tabCur)?"address":"investment"},path:"historyStrategy"}))},enterpriseNameBlur(){this.AssociativeList.length>0?this.AssociativeList.forEach((e=>{e.enterprise===this.formData.enterpriseName&&(this.formData.enterpriseUniCode=e.enterpriseUniCode,this.forbiddenCode=!0,this.enterpriseId=e.enterpriseId)})):(this.AssociativeList=[],this.showAssociativeList=!1)},scrolltolowerFn(){this.pages>this.pageNum&&(this.pageNum++,this.requestSearch(this.keyText,this.pageNum))},enterpriseNameFocus(){this.formData.enterpriseName&&(this.showAssociativeList=!0)},clearAssociativeList(){this.showAssociativeList=!1},SelectEn(e){this.showAssociativeList=!1,this.forbiddenCode=!0,this.formData.enterpriseName=e.enterpriseName,this.formData.enterpriseUniCode=e.unifiedSocialCreditCode,this.enterpriseId=e.id},changeenterpriseName(e){this.enterpriseTotal=0,this.forbiddenCode=!1,this.formData.enterpriseUniCode="",this.enterpriseId="",null!=this.enterpriseTime&&clearTimeout(this.enterpriseTime),this.enterpriseTime=setTimeout((async()=>{e?(this.keyText=e,this.pageNum=1,this.pages=0,this.requestSearch(e,1)):(this.pageNum=1,this.pages=0,this.AssociativeList=[],this.enterpriseTotal=0)}),400)},requestSearch(e,t){this.$api.newEnterpriseSearchAPI({data:{keyword:e,pageNum:t,pageSize:10},method:"POST"}).then((e=>{1==t&&(this.enterpriseTotal=0,this.AssociativeList=[]),this.AssociativeList=this.AssociativeList.concat(e.result.records),this.enterpriseTotal=e.result.total,this.pages=e.result.pages,this.formData.enterpriseName?this.showAssociativeList=!0:this.showAssociativeList=!1}))},addProduct(e){1==e?this.formData.products.push({productName:"",introduction:"",id:Date.now()}):2==e&&this.formData.overseasProductInfo.push({productName:"",application:"",id:Date.now()+"2"})},delProduct(e,t){let a=0;1==t?(a=this.formData.products.findIndex((t=>t.id===e)),this.formData.products.splice(a,1)):2==t&&(a=this.formData.overseasProductInfo.findIndex((t=>t.id===e)),this.formData.overseasProductInfo.splice(a,1))},toAddFile(){var e;C("localTabIndex",this.tabCur+""),null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"toUploadFile",value:"",path:"fileUpload"}))},toDownLoadFile(){console.log("toDownLoadFile")},chainNodeClick(e){this.formData.chainId=e,this.requestChainNodeList(e)},requestChainNodeList(e,t){this.$api.overseasNodeTreeAPI({data:{chainId:e},method:"GET"}).then((a=>{this.chainNodeList=a.result.map((e=>(e.text=e.nodeName,e.value=e.nodeId,e))),t&&e==t.chainId&&(t.nodeName&&!t.nodeId?(this.customNode=!0,this.formData.nodeNameCustom=t.nodeName):(this.customNode=!1,this.formData.nodeName=t.nodeName,this.formData.nodeId=t.nodeId))}))},customNodeClick(e){var t;this.haveIntendedRegion=this.formData.haveIntendedRegion,this.needsWastewaterTreatment=this.formData.needsWastewaterTreatment,this.customNode=(null==(t=null==e?void 0:e.detail)?void 0:t.value)||!1,console.log(this.formData)},onNodeChangeClick(e){this.canScroll=!0;const t=e.detail.value;let a=t.length;a>0?(this.formData.nodeId=t[a-1].value,this.formData.nodeName=t[a-1].text):(this.formData.nodeId=null,this.formData.nodeName="")},gotoTemplate(){var e;this.isLogin()&&(null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{url:this.template.url,name:this.template.name,isBuy:!1},path:"showPDF"})))},submit(){if(this.saveState)return;return e("token")?1===this.tabCur&&""===this.formData.projectName?(t({title:"请填写项目名称",icon:"none"}),void(this.saveState=!1)):""===this.formData.enterpriseName?(t({title:"请填写企业名称",icon:"none"}),void(this.saveState=!1)):this.formData.chainId&&""!==this.formData.chainId?this.formData.factoryRequirements?void this.$refs.baseForm.validate().then((e=>{if(""===this.formData.priceRequirements)return t({title:"请选择价格要求",icon:"none"}),void(this.saveState=!1);for(let a=0;a<this.formData.overseasProductInfo.length;a++){let e=this.formData.overseasProductInfo[a];if(!e.productName)return t({title:"请输入【拟定出海产品】产品"+(a+1)+"的名称",icon:"none"}),void(this.saveState=!1);if(!e.application)return t({title:"请输入【拟定出海产品】产品"+(a+1)+"的应用场景",icon:"none"}),void(this.saveState=!1)}if(!this.getSaveData().nodeName)return t({title:"请选择或填写产业链环节",icon:"none"}),void(this.saveState=!1);this.saveState=!0,this.goPay()})):(t({title:"请选择厂房",icon:"none"}),void(this.saveState=!1)):(t({title:"请选择产业链",icon:"none"}),void(this.saveState=!1)):v({url:"/pages/login/index"})},getSaveData(){let{overseasCountry:e,reportType:t,overseasCountryName:a,nodeId:s,nodeName:i,nodeNameCustom:n,intendedRegions:o,...r}=this.formData,l=this.industryChainList.find((e=>e.value===this.formData.chainId));return{...r,reportType:this.tabCur,overseasCountryName:a,overseasCountry:a,chainName:null==l?void 0:l.text,nodeId:!this.customNode&&s||null,nodeName:this.customNode?null==n?void 0:n.trim():i,intendedRegions:null==o?void 0:o.trim()}},requestSaveCache(){var e,t,a,s;let i=this.getSaveData();i.overseasCountry=""===i.overseasCountry?null==(t=null==(e=this.overseasCountryList)?void 0:e[0])?void 0:t.text:i.overseasCountry,i.overseasCountryName=""===i.overseasCountryName?null==(s=null==(a=this.overseasCountryList)?void 0:a[0])?void 0:s.text:i.overseasCountryName,this.$api.overseaIntelligenceCacheAPI({data:i,method:"POST",loading:"no"})},goPay(){let e=this.getSaveData();this.$api.overseaIntelligenceReportAPI({data:{...e},method:"post"}).then((e=>{this.saveState=!1,"SUCCESS"===e.code?e.result?this.payment(e.result):t({title:"操作太频繁，请重试",icon:"none"}):t({title:e.msg,icon:"none"})})).catch((()=>{this.saveState=!1,t({title:"下单失败",icon:"none"})}))},payment(e){var t;this.clearTimer();let a=this;const s=window.callbacks.register((e=>{console.log("支付成功"+e),a.paySuccess()}),(e=>{console.log("支付失败"+e),a.createTimer()}));null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({callbackId:s,tabCur:this.tabCur,type:"toWechatPay",value:{businessCode:"intelligenceReport",item:{intelligenceReportId:e}}}))},paySuccess(){this.clearTimer(),this.$refs.reportLoading.opens(""),this.saveState=!1,this.formData=JSON.parse(JSON.stringify(this.emptyFormData)),this.setcatchCountry(),this.needsWastewaterTreatment=!1,this.haveIntendedRegion=!1}}},[["render",function(e,t,v,C,I,S){const D=E(y("v-tabs"),O),B=g,x=m,M=E(y("uni-easyinput"),U),H=E(y("uni-forms-item"),V),W=f,X=E(y("uni-tooltip"),F),Z=E(y("uni-data-select"),Q),G=E(y("uni-data-checkbox"),j),J=b,_=E(y("uni-data-picker"),T),K=N,Y=E(y("uni-forms"),R),z=a("reportLoading");return s(),c(p,null,[o(B,{class:"policyTabs"},{default:n((()=>[o(D,{modelValue:I.tabCur,"onUpdate:modelValue":t[0]||(t[0]=e=>I.tabCur=e),tabs:I.tabs,bgColor:"transparent",color:"#7E84A6",activeColor:"#17397D",lineColor:"#17397D",fontSize:"36rpx",fontWeight:"500",scroll:!1,height:"80rpx",lineHeight:"6rpx",paddingItem:"0 50rpx",lineScale:.3,onChange:S.tabChange},null,8,["modelValue","tabs","onChange"])])),_:1}),o(W,{class:"policyContent","scroll-y":""},{default:n((()=>[o(x,{src:"https://static.idicc.cn/cdn/aiChat/applet/home/<USER>",class:"bgWisdomQuestionText"}),0==I.tabCur?(s(),i(B,{key:0,class:"bgWisdomQuestionText2"},{default:n((()=>[o(B,null,{default:n((()=>[r(" AI智能选址，精准锁定蓝海！")])),_:1}),o(B,null,{default:n((()=>[r(" 一键分析市场潜力，让您的出海决策快人一步！ ")])),_:1})])),_:1})):(s(),i(B,{key:1,class:"bgWisdomQuestionText2",style:A(`top: ${I.statusBarHeight+65+I.topPadding}px`)},{default:n((()=>[o(B,null,{default:n((()=>[r("AI智能生成权威出海投资报告，涵盖 市场分析、政策解读、风险评估等 全维度解析，帮助您抢占先机!")])),_:1})])),_:1},8,["style"])),o(B,{class:"bgWisdomQuestionText3"},{default:n((()=>[I.template.name?(s(),c("span",{key:0},[r(" 样例："),h("span",{style:{color:"#3370ff"},onClick:t[1]||(t[1]=(...e)=>S.gotoTemplate&&S.gotoTemplate(...e))},u(I.template.name),1)])):l("",!0)])),_:1}),o(B,{onClick:S.isLogin,class:"box1"},{default:n((()=>[o(B,{class:"fetchitem"},{default:n((()=>[o(B,{class:"fetchitem_head"},{default:n((()=>[o(B,{class:"font1"},{default:n((()=>[r("出海报告")])),_:1}),o(B,{class:"font2",onClick:t[2]||(t[2]=e=>S.goHistry("click"))},{default:n((()=>[o(x,{class:"icon",src:P}),r(" 我的报告 ")])),_:1})])),_:1}),o(B,{class:"labelForm"},{default:n((()=>[o(Y,{ref:"baseForm",modelValue:I.formData,rules:I.rules,"label-position":"top","label-width":"200"},{default:n((()=>{var e;return[1===I.tabCur?(s(),i(H,{key:0,required:"",label:"项目名称",name:"projectName"},{default:n((()=>[h("div",{style:{position:"relative"}},[o(M,{trim:"all",clearable:!1,modelValue:I.formData.projectName,"onUpdate:modelValue":t[3]||(t[3]=e=>I.formData.projectName=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])])),_:1})):l("",!0),o(H,{required:"",label:"企业名称",name:"enterpriseName"},{default:n((()=>[h("div",{style:{position:"relative"}},[o(M,{trim:"all",onFocus:S.enterpriseNameFocus,onBlur:S.enterpriseNameBlur,onInput:S.changeenterpriseName,clearable:!1,modelValue:I.formData.enterpriseName,"onUpdate:modelValue":t[4]||(t[4]=e=>I.formData.enterpriseName=e),placeholder:"请输入企业名称"},null,8,["onFocus","onBlur","onInput","modelValue"]),L(o(B,{class:"associativeList"},{default:n((()=>[o(B,{class:"enterpriseTotal"},{default:n((()=>[o(B,null,{default:n((()=>[r(" 搜索到"),h("span",{style:{color:"#3370ff"}},u(I.enterpriseTotal),1),r("条结果 ")])),_:1}),o(B,{class:"enterpriseBack",onClick:S.clearAssociativeList},{default:n((()=>[r(" 取消 ")])),_:1},8,["onClick"])])),_:1}),o(W,{class:"associativeList2","scroll-y":!0,onScrolltolower:S.scrolltolowerFn},{default:n((()=>[(s(!0),c(p,null,d(I.AssociativeList,((e,t)=>(s(),c("div",{onClick:t=>S.SelectEn(e),class:"associativeItem",key:t},[h("div",{class:"name"},u(e.enterpriseName),1),h("div",{class:"man"},"法定代表人："+u(e.legalPerson),1),h("div",{class:"code"}," 统一社会信用代码："+u(e.unifiedSocialCreditCode),1)],8,["onClick"])))),128))])),_:1},8,["onScrolltolower"])])),_:1},512),[[w,I.showAssociativeList]])])])),_:1}),o(H,{label:"统一社会信用代码"},{default:n((()=>[I.forbiddenCode?(s(),c("div",{key:1,class:"enterpriseUniCode"},u(I.formData.enterpriseUniCode),1)):(s(),i(M,{key:0,trim:"all",clearable:!1,modelValue:I.formData.enterpriseUniCode,"onUpdate:modelValue":t[5]||(t[5]=e=>I.formData.enterpriseUniCode=e),placeholder:"请输入"},null,8,["modelValue"]))])),_:1}),o(H,{label:"企业介绍",name:"enterpriseIntroduction"},{default:n((()=>[o(M,{type:"textarea",clearable:!1,"adjust-position":"",modelValue:I.formData.enterpriseIntroduction,"onUpdate:modelValue":t[6]||(t[6]=e=>I.formData.enterpriseIntroduction=e),placeholder:"请简要阐述贵公司的基本情况，包括主营业务、发展历程、核心优势等关键信息"},null,8,["modelValue"])])),_:1}),o(B,null,{default:n((()=>[o(B,{class:"form-item-button-title"},{default:n((()=>[o(B,null,{default:n((()=>[r("企业产品及产品介绍")])),_:1}),o(B,{class:"form-item-button-add",onClick:t[7]||(t[7]=e=>S.addProduct(1))},{default:n((()=>[r(" 新增 ")])),_:1})])),_:1}),(s(!0),c(p,null,d(I.formData.products,((e,t)=>(s(),i(H,{key:e.id,label:"产品"+(t+1)},{default:n((()=>[o(B,{class:"form-item"},{default:n((()=>[o(M,{modelValue:I.formData.products[t].productName,"onUpdate:modelValue":e=>I.formData.products[t].productName=e,placeholder:"请输入企业产品名称"},null,8,["modelValue","onUpdate:modelValue"]),o(B,{class:"form-item-line"}),o(M,{type:"textarea",clearable:!1,"adjust-position":"",modelValue:I.formData.products[t].introduction,"onUpdate:modelValue":e=>I.formData.products[t].introduction=e,placeholder:"请输入企业产品介绍"},null,8,["modelValue","onUpdate:modelValue"]),o(B,{class:"form-item-buttons"},{default:n((()=>[I.formData.products.length>1?(s(),i(B,{key:0,class:"form-item-button",onClick:t=>S.delProduct(e.id,1)},{default:n((()=>[r(" 删除 ")])),_:2},1032,["onClick"])):l("",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["label"])))),128))])),_:1}),o(B,null,{default:n((()=>[o(B,{class:"form-item-button-title"},{default:n((()=>[o(B,{class:"form-item-button-title-left"},{default:n((()=>[o(B,null,{default:n((()=>[r("拟定出海产品情况")])),_:1}),o(B,null,{default:n((()=>[o(X,{content:"",placement:"top"},{content:n((()=>[o(B,{class:"tooltip-content-product"},{default:n((()=>[r(" 拟定在海外市场推出的具体产品以及应用场景。如：拟定产品名称：“TitanPro智能储能箱““(避免使用中文直译名称“应用场景：针对东南亚岛屿电网：①为离网度假村提供24小时储能支持②替代柴油发电机降低30%用电成本③抗潮湿盐雾腐蚀设计适应海洋气候 ")])),_:1})])),default:n((()=>[h("span",null,[o(x,{src:q,class:"titleIcon"})])])),_:1})])),_:1})])),_:1}),o(B,{class:"form-item-button-add",onClick:t[8]||(t[8]=e=>S.addProduct(2))},{default:n((()=>[r(" 新增 ")])),_:1})])),_:1}),(s(!0),c(p,null,d(I.formData.overseasProductInfo,((e,t)=>(s(),i(H,{key:e.id,label:"产品"+(t+1),required:""},{default:n((()=>[o(B,{class:"form-item"},{default:n((()=>[o(M,{modelValue:I.formData.overseasProductInfo[t].productName,"onUpdate:modelValue":e=>I.formData.overseasProductInfo[t].productName=e,placeholder:"请输入拟定产品名称"},null,8,["modelValue","onUpdate:modelValue"]),o(B,{class:"form-item-line"}),o(M,{type:"textarea",clearable:!1,"adjust-position":"",modelValue:I.formData.overseasProductInfo[t].application,"onUpdate:modelValue":e=>I.formData.overseasProductInfo[t].application=e,placeholder:"请输入产品应用场景"},null,8,["modelValue","onUpdate:modelValue"]),o(B,{class:"form-item-buttons"},{default:n((()=>[I.formData.overseasProductInfo.length>1?(s(),i(B,{key:0,class:"form-item-button",onClick:t=>S.delProduct(e.id,2)},{default:n((()=>[r(" 删除 ")])),_:2},1032,["onClick"])):l("",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["label"])))),128))])),_:1}),o(H,{label:"意向出海目的国",name:"overseasCountry",required:""},{default:n((()=>[o(Z,{clear:!1,modelValue:I.formData.overseasCountry,"onUpdate:modelValue":t[9]||(t[9]=e=>I.formData.overseasCountry=e),localdata:I.overseasCountryList,onChange:S.selectCountry},null,8,["modelValue","localdata","onChange"])])),_:1}),o(H,{label:"产业链",name:"chainId",required:""},{default:n((()=>[o(Z,{clear:!1,placeholder:"请选择产业链",modelValue:I.formData.chainId,"onUpdate:modelValue":t[10]||(t[10]=e=>I.formData.chainId=e),localdata:I.industryChainList,onChange:S.chainNodeClick},null,8,["modelValue","localdata","onChange"])])),_:1}),I.formData.chainId?(s(),i(H,{key:1,label:"产业链环节",name:"customNode",required:""},{default:n((()=>[o(X,{content:"为企业具体的业务领域，若提供的产业链环节不符合您的业务，请选择“自定义”",placement:"bottom"},{default:n((()=>[h("span",null,[o(x,{src:q,class:"reminderIcon",style:{left:"180rpx"}})])])),_:1}),o(G,{modelValue:I.customNode,"onUpdate:modelValue":t[11]||(t[11]=e=>I.customNode=e),localdata:[{value:!1,text:"默认"},{value:!0,text:"自定义"}],onChange:S.customNodeClick},null,8,["modelValue","onChange"])])),_:1})):l("",!0),I.formData.chainId?(s(),i(B,{key:2},{default:n((()=>[o(H,{label:I.customNode?"产业链环节名称":"请选择产业链环节",name:I.customNode?"nodeNameCustom":"nodeId",required:""},{default:n((()=>[o(J,{class:k([I.customNode?"":"showInput","uni-input-form"]),type:"textarea",value:I.formData.nodeNameCustom,placeholder:"请输入产业链环节名称",onInput:t[12]||(t[12]=e=>S.changInput("nodeNameCustom",e))},null,8,["class","value"]),I.customNode?l("",!0):(s(),i(_,{key:0,modelValue:I.formData.nodeId,"onUpdate:modelValue":t[13]||(t[13]=e=>I.formData.nodeId=e),placeholder:"请先选择产业链","adjust-position":"","popup-title":"请选择产业链环节",localdata:I.chainNodeList,onChange:S.onNodeChangeClick,onNodeclick:S.onnodeclick,map:I.nodeMap,onPopupopened:S.onpopupopened,onPopupclosed:S.onpopupclosed},null,8,["modelValue","localdata","onChange","onNodeclick","map","onPopupopened","onPopupclosed"]))])),_:1},8,["label","name"])])),_:1})):l("",!0),I.formData.overseasCountry?(s(),i(H,{key:3,label:"厂房需求",name:"factoryRequirements",required:""},{default:n((()=>[o(Z,{clear:!1,placeholder:"请选择",modelValue:I.formData.factoryRequirements,"onUpdate:modelValue":t[14]||(t[14]=e=>I.formData.factoryRequirements=e),localdata:I.factoryRequirementsList,onChange:S.onChangeFactoryRequirements},null,8,["modelValue","localdata","onChange"])])),_:1})):l("",!0),I.formData.overseasCountry&&""!==I.formData.factoryRequirements?(s(),i(H,{key:4,label:"价格要求",name:"priceRequirements",required:""},{default:n((()=>[o(Z,{clear:!1,placeholder:"请选择",modelValue:I.formData.priceRequirements,"onUpdate:modelValue":t[15]||(t[15]=e=>I.formData.priceRequirements=e),localdata:I.priceRequirementsList},null,8,["modelValue","localdata"])])),_:1})):l("",!0),o(H,{label:"供应链要求",name:"supplyChainRequirements"},{default:n((()=>[o(M,{type:"textarea",clearable:!1,"adjust-position":"",modelValue:I.formData.supplyChainRequirements,"onUpdate:modelValue":t[16]||(t[16]=e=>I.formData.supplyChainRequirements=e),placeholder:"请简单阐述贵公司对供应链上下游需求和其他要求"},null,8,["modelValue"])])),_:1}),o(H,{label:"交通与物流",name:"transportationAndLogistics"},{default:n((()=>[o(Z,{placeholder:"请选择",modelValue:I.formData.transportationAndLogistics,"onUpdate:modelValue":t[17]||(t[17]=e=>I.formData.transportationAndLogistics=e),localdata:I.wuLiuList},null,8,["modelValue","localdata"])])),_:1}),o(H,{label:"是否有意向区域"},{default:n((()=>[o(G,{modelValue:I.formData.haveIntendedRegion,"onUpdate:modelValue":t[18]||(t[18]=e=>I.formData.haveIntendedRegion=e),localdata:I.localData,onChange:S.changePark},null,8,["modelValue","localdata","onChange"])])),_:1}),o(B,{class:k((null==(e=I.formData)?void 0:e.haveIntendedRegion)?"":"showInput")},{default:n((()=>[o(H,{label:"意向区域"},{default:n((()=>[o(B,{class:"priceBox"},{default:n((()=>[o(J,{class:"uni-input-form",value:I.formData.intendedRegions,placeholder:"请输入意向区域",onInput:t[19]||(t[19]=e=>S.changInput("intendedRegions",e))},null,8,["value"])])),_:1})])),_:1})])),_:1},8,["class"]),o(H,{label:"是否需要污水处理"},{default:n((()=>[o(G,{modelValue:I.formData.needsWastewaterTreatment,"onUpdate:modelValue":t[20]||(t[20]=e=>I.formData.needsWastewaterTreatment=e),localdata:[{text:"否",value:!1},{text:"是",value:!0}],onChange:S.checkWasteWaterTreatment},null,8,["modelValue","onChange"])])),_:1}),o(H,{label:"出海目标",name:"exportGoals"},{default:n((()=>[o(K,{class:"uni-input-form-textarea","auto-height":"",value:I.formData.exportGoals,maxlength:"-1",placeholder:"请简单阐述出海希望达成的目标和其他要求",onInput:t[21]||(t[21]=e=>S.changInput("exportGoals",e))},null,8,["value"])])),_:1})]})),_:1},8,["modelValue","rules"]),o(B,{class:"infoText"},{default:n((()=>[o(B,null,{default:n((()=>[r("请输入完整企业信息，否则将无法找到企业数据造成报告生成失败")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1}),o(B,{class:"footer"},{default:n((()=>[o(B,{class:"btns"},{default:n((()=>[o(B,{class:"price"},{default:n((()=>[h("span",{class:"priceLogo"},"￥"),r(" "+u(0==I.tabCur?I.reportParam.siteSelectionAmount:I.reportParam.feasibilityStudyAmount),1)])),_:1}),o(B,{class:"submitBtn",onClick:t[22]||(t[22]=()=>{S.submit()})},{default:n((()=>[r("一键生成")])),_:1})])),_:1})])),_:1}),o(z,{ref:"reportLoading",onToHistory:S.goHistry},null,8,["onToHistory"])],64)}],["__scopeId","data-v-72e7c968"]]);const oe=M({options:{styleIsolation:"shared"},components:{regionCut:x},data:()=>({statusBarHeight:0,tabList:[],tabBodys:[],tabs:"1",value:"117",token:""}),mounted(){this.token=e("token"),this.token||(this.token=W("token"),C("token",this.token))},methods:{updataPage(e){this.getData(e)},getData(e){let t={type:4,country:e};this.$api.overseasPermission({data:t,method:"post"}).then((e=>{this.getTabs()}))},goMineOrder(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{tab:0},path:"seaOrder"}))},getTabs(){this.$api.getNavigation({data:{countryId:this.value},method:"post"}).then((e=>{var t;"SUCCESS"==e.code&&(this.tabList=e.result.map((e=>({name:e.catalogName,key:e.catalogId}))),this.tabs=(null==(t=this.tabList[0])?void 0:t.key)||"",this.getTabLists())}))},getTabLists(){if(this.tabBodys=[],!this.tabs)return!1;this.$api.getNavigationSubset({data:{catalogId:this.tabs},method:"post"}).then((e=>{"SUCCESS"==e.code&&(this.tabBodys=e.result.items.map((e=>({name:e.itemName,key:e.itemId}))))}))},tabClick(e){this.tabs=e.key,this.getTabLists()},goBack(){I().length>1?S({delta:1}):D({url:"/pages/repository/capacity"})},goDetails(e){var t,a;if(!this.token){return void((null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):v({url:"/pages/login/index"}))}let s={type:e.key,name:e.name};null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:s,path:"serviceInfo"}))},onchange(e){this.getTabs()}}},[["render",function(e,t,h,y,v,C){const A=a("regionCut"),L=m,w=g,b=f;return s(),i(b,{style:{position:"absolute"},class:"mainView","scroll-y":"true"},{default:n((()=>[o(w,{class:"box",style:{top:"10px"}},{default:n((()=>[o(w,{class:"setInputBox"},{default:n((()=>[o(A,{onUpdataPage:C.updataPage,ref:"regionCut"},null,8,["onUpdataPage"]),v.token?(s(),i(w,{key:0,class:"historyBox",onClick:C.goMineOrder},{default:n((()=>[o(L,{class:"icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAL2SURBVFiF7Zg/btswFMa/Z6e7eoLqCO4JzI51AsQZCjhGgNpbuzTx1C5F7K3tomQpOhSwh8AO0CEZEmdlbpCeoLpBPacWvw7yH0mWFdNOWg/5AMMU+Uj+RPI9igQetZpk2Ypqa0/BGHch45zx9cWpXqafpQDV5u4BCc+uI2npfrdp21fuTphyxVXlihvNI7Ef9ioDAH7mL7QBge14uzVnEcC5I6hK1SbB1wDckAUNfdk7AoBiaZdhZanrfreT1YEqVWsE2wBw3e8JkJgBkYGArXHbSaWO4AjucAwHAKAs9MYL6tm0XTokPFWqNtMMU0ewuFn9DdKBQIvkWwCgL070TDngi8DPIiHFAViAyOD6svt0nK9K1QJyOYcm2AdQTpaPtZHMUOWKy1s6ACAGx/pqCjZ5K+KYwCEAl4yMcjpiWAdsRXN1v3sDAGprDzRBGaSjyhVXn5/GXngGMKZ8fpCWrfvdpnq5ewORQjbcmJE3ut87X8g2oWzADOmr3jmApTq10Z1h5n/rEXBVrT2gtZN4Z6xBUIxlEjDT5Ph/sAG0GjuSGgkeBNA7ozs0aEe4JkQTME7th8RPAJ1/BtjYEf/zDzZEsJ02ahOFZf6fJ6uHIespfv9KjgCkbuwPobV3krUHtJ7ijyf0YFCerL/kAmR8TTLiRASOv9TFanlYATbbdG6HOIiEkrgXz4GLpD1Yrl87wLoMPrT5whioZJlJeTBJI2Z/O6bJeoo/1UUD0Lb1ltXaO8naA1pP8duv/CWEO1n/jO2/mVsfAf39nbyw6c9qBA88OkEAZ2iAwABBAETTQRCmh4nnwADD8LlQ82h1OrQawaOGDN54fD7cGB2UBMBw1i6Y34Tfadh93VhP8beG+IB9uFhWa+8kj4CragYwdrJnsNjBfBUxmNx6JW8VgDlOIpAOwRoJr7hZPQS50rlirkQcchR2JH37TPdiY1rIiwtCjS6J7vNma6ppFPclYD3NJPOGVW1VFEzOvWesuHI5P3pz9qj71l+/3Eg+TWx0HwAAAABJRU5ErkJggg=="}),r(" 我的订单 ")])),_:1},8,["onClick"])):l("",!0)])),_:1}),o(w,{class:"tabBox"},{default:n((()=>[o(w,{class:"tabBox_left"},{default:n((()=>[(s(!0),c(p,null,d(v.tabList,((e,t)=>(s(),i(w,{class:k(["tabLists",v.tabs==e.key?"active":""]),onClick:t=>C.tabClick(e),key:t},{default:n((()=>[o(w,{class:"active_zi"}),r(" "+u(e.name),1)])),_:2},1032,["onClick","class"])))),128))])),_:1}),o(w,{class:"tabBox_right"},{default:n((()=>[(s(!0),c(p,null,d(v.tabBodys,((e,t)=>(s(),i(w,{class:"tabBodys",key:t,onClick:t=>C.goDetails(e)},{default:n((()=>[t%3==0?(s(),i(L,{key:0,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAA5FBMVEUAAABkkv9mkv85dP84c/84cv9lkf83cv9jjv8ycf9fjv9ij/80cP83cv88df80b/9olf9nk/9hkP////9gj/9Dev9ikf9Kf/8+d/9Aef85dP9djf87df83cv9lkv9Ogv9Thv9cjP9Xif9ai/89dv9Ziv9Mgf9Vh/9QhP81cf9Hff9FfP9mk/9Ifv/5+/9nlP/V4f9olP/l7P/p8P/x9f+sxP/g6f+/0v+Ssv/1+P+Krf97ov9rl//t8v/O3P/I2P+4zf+zyv+nwf+ivv+duv+Bpv9xmv/b5f/X4/+Yt//Q3v/G1//X9v42AAAAE3RSTlMAmPv71pg1NRIS+tb6upiY1rq6qxZsXgAAAohJREFUSMeN0Gt3mkAQgOHBS8ylSdqOkaa2UbEC1UgVUPB+15j8///THVZ20JwQny/LObwz7AHIhfY105X+Sc8JT5HMjXYB0qWWoe59eCqjXUb9t+e0rJT0nSa09JAVBU3cP/N5xq4vQJNdesg0uEmtWFm6gWvZpYfsGs6pagmQ0kUhq0cgPaOENQTgcF/8KOzOqZWAI/T25cQ2pdQbYN2IAe9DRG9VpIQZBuWIDR7gfUh2UcYaEySGFYOGguRVdsPAdaerrQj6pwOGgmQTdgQXJf9lOERiVWMnA30r6HQcZP1q/2TAUlAYinOEScMQhTi37eMBlzbNqZjE3+lYHg3YMagqiLimc0qZUd0MMDJaq4GKcDTgG3TSpUPxejRBMrN8RGol4NshLm1hHF2N9o09JOMlD5gm2Ao6RTpKHk5L8nXZRSGoOSjKiBqg17iwV1FXV+uMDiI6tYUc0AlUlGV5i0NZklLPMk1rRr+ittaJGuCoN9g2TElvdPpdXa/MEcXBwEyqvKAz0g/EeCiOnlPShfaBHIibmo+4lI+jlalvBoHdbk/XFDYPQE96o1+6qFPw1J/W2sWdO9Zf9k2l1QKuRTVA8hbtqwT+qKnPHd+1WgkgQrZAj/5jb9uchc1m6PTabQzbh/QhAnQ9Zm2QOPZ4EtitrrebTUzKGKjbSfbOoY/M6lbHHT+s3EXx4RgcQv6o/oqC/2w6TuCHKnw8gDhkIy/wEB2/v+0N45ABh+xxjsvxppnsfsTgKFSq3J0Azti76g+DtJBK9isCj2dE7GcWcueFsRzcnZGx33koZM8KD7IFgDuOPqjY3zwAXN1+3sVur4Am8tm0jH25p54U8rlsSijr3H2B2v82DxWxhCHjDgAAAABJRU5ErkJggg==",class:"hisImg"})):l("",!0),t%3==1?(s(),i(L,{key:1,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAA5FBMVEUAAAD/sEv/xWb/pzb/qjr/wV//pzb/xmb/wmD/pTT/xGP/pzb/qjv/w2P/xGT/xmb/rD//xmj/wF7/rD7/qTr/pjX/xWr/rTr/////v13/wWD/w2P/qTr/r0P/vFj/skj/tU3/vlr/pzf/xGX/s0r/rUD/rD//qz3/qjz/sEX/tk//ulX/uFL/uVT/pjX/t1D/xmf//Pn/7tb/+fL/3Kz/9uj/u1v/8d7/wWr/9OT/4LX/2aT/0ZL/9+z/6Mf/yHv/vmP/w3H/687/473/153/z4v/yoH/vF//5cH/7NH/1pj/zYU5XRoNAAAAGHRSTlMAEvv7mPrWmZiYNTX61rqX+dbW1rq6NTWBaP7FAAACg0lEQVRIx43W13baQBSF4WPAvaXn2AmMSOTECAgZ9Uavtt//fTJHI80ItCL7uxFrzb+FgBuAfDl93/h74OeBxvXpBUhHt42akHyTGmdHWX8jq2pUdUOL2/pUuxfOAC4aMqrLtOMLOK0Lq87gurbS7qQPcEzda6F2DLVVVRde6XSZg1eyCtCHz/f/C8O42+3kSgP0ng9CWc1cjkVPAxUhopfcyU67dx1E9PWgqyAJOgfGSDr9Auj7IXmSr192XrRI5iIIDgcdBckmtYUIJcder5H0h4WDQdBf2jZHLRoGejAQoK+gsBbXKZa9pChQSw4HHt0ppmJso/Q09LLBKAdDBRFXA2GBiHZ/sOGYma4QkXorkw8oRHT6dKWHTkUwHSOZDB1EqiUYFEaI7kgIs49KRSgXYYJoGQUQZQ75lt545uFiZmW2EQpLn5cHlMpjjEcJXQ1fnfbpo3PfRYOYBCwl2c5xbSgzd2gYgwl9Ff7KVMAoc/m8uL3p20FomsYSMSxiJsiBYiOfmjkxT8XF5TOW04Oi8R3ERL6cJibbODuLscVqb2CW0RNHsU8Hs2Dhs60dhab9LNNeJh8wyUEyYcTaOVNmxtyJBr0SYGUx0m/F3TmbpL1eyl3GMGV52s6UB+JksEHCrfl4Z/VCL5iMzfYekKFiBdyjp/KHthe2kyjetvdBHuo3ZU8oODOT86WTqvAhBypUpt7SQ+ROMHfXKlRAh9pDjG646anoVwnshYqlQu13BkpZHlUz7fERdFobKvCuNtK+S1fwkcK6klLtBFo196v60YKvV28Kc81LgJbKKlXVOf11+FTTaX/IZ/lno9XUWTXSmufUk8vWSbMmlPXJ+SW1/wDI8zOQbRarUgAAAABJRU5ErkJggg==",class:"hisImg"})):l("",!0),t%3==2?(s(),i(L,{key:2,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAw1BMVEUAAAAc3+sc3upQ5/FO5vAd3utN5/Eb3exH4/EV1eNP5/BS5/BP5vAc3upJ5vBH5fD///9L5u9E5O4l3+tI5e8f3uoi3+tP5vAc3upB5O4x4ewp4Ow24u0s4Ow54u084+4z4u1S5vAu4ewZ3eo+4+74/v7Q+Pvs/P219Pjo/P2g8vaN7/WD7fTw/f7h+vzb+vy/9vlo6fFY5/D7/v7k+/zH9/p67PNw6vLW+fub8faV8PWr8/em8vdf6PDe+vzL+Pqu8/fGQ9GgAAAAEHRSTlMAmPqY+tY1NRIS1vq6upjW6T4M7AAAAn9JREFUSMeNkgl7mkAQhgePmLsLoU1FOculIgreR47//6s6425YCOk2L8/jjrvvN4wCEFfd2zv/S54rerfdK+Bcd+8UYo1e9/ri3ys1Qv/gnhJdhdjiEefvtSQF5hV0vxCf8WqpnEe4VXf8zA/oUUeF2MQw4TtWHVB4aLaBlvYfQPab/VP2T4YxFIDcZutPEWHp04hR1Q4wNjmawiOEPscD8SUMw3qA2A+bGAkjUBWA7MeIDa936TrfHF9QWFQB9wJwoQrsshjJGWceb7eMELYMUAc6WIRpHEdM8uouGgEHQo4IbHGvYHW2GUNcR8ADPIz7aypPZEziFQ+8uetmgNQqMMN7uhss4qGzE5MVM/wgdXxBBhz8jUM6oKEzXIuEEUsXHwTaAnAqGDvQUqKUh3RWThjhH2oBrx6IdNrX12yj81PzlQKpEbGx9wHI6djJOV4Ko2o3jDGwMs8iYCEwrjiYJdtykdCnrue5S/orjJnFkQFhTaMXw+Ng94VvWeOUMVwk4NUZx2xVWAKMZ7hMI90iAo4ICIfezQMvi6Nn7eapEwSbWVBh22DVoYnzs0HtnhcbIzD3uW/tZyiiSnwKBHP+qAJinCaFbb1HSe7aNYC8ihOjZxVNS3uZ2XYWTYOAZVXzEQFyOsLdMSJyyiR1bH+yXybeqAHYTZz9akJTGWG8LkfH/KyPlAHcsd4YMve91SpNMqE9VQD36hSTFG8SJYuX8/apBdRM2e2dHcqdjYUyUN92vlB/XYAnBVKTgFpsAyrpJ11NOtBvizLQpg+aomOT34QGg06lq0RBZwCgKawWGgDcPKg8wR/Oww1QQuuoNElHI58YaP2OQuR2XxuQ+xf9rN3Hbif54wAAAABJRU5ErkJggg==",class:"hisImg"})):l("",!0),r(" "+u(e.name),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-360d23c1"]]);const re=M({props:{title:{type:String,default:""},showicon:{type:Boolean,default:!0}},data:()=>({statusBarHeight:0}),created(){this.statusBarHeight=B().statusBarHeight},methods:{goBack(){I().length>1?S({delta:1}):D({url:"/pages/repository/capacity"})}}},[["render",function(e,t,a,c,d,h){const p=m,f=g;return s(),i(f,{class:"bar-con"},{default:n((()=>[o(f,{class:"tabBarCon"},{default:n((()=>[o(f,{id:"tabBar",class:"tabBar"},{default:n((()=>[o(f,{class:"nav",style:A(`\n\t\t\t\t    margin-top: ${d.statusBarHeight}px;\n\t\t\t\t`)},{default:n((()=>[a.showicon?(s(),i(f,{key:0,class:"black",onClick:t[0]||(t[0]=e=>h.goBack())},{default:n((()=>[o(p,{class:"returnFn",src:K})])),_:1})):l("",!0),o(f,{class:"nav-title"},{default:n((()=>[r(u(a.title),1)])),_:1})])),_:1},8,["style"])])),_:1})])),_:1}),o(p,{src:"https://static.idicc.cn/cdn/aiChat/applet/WisdomQuestionBgc.png",class:"headimg",mode:""})])),_:1})}],["__scopeId","data-v-b50140cb"]]);const le=M({props:{active:{type:Number,default:null},token:{type:String,default:null},cityName:{type:String,default:null},BottomBlank:{type:Boolean,default:!1},appletTab:{type:Boolean,default:!1}},data:()=>({province:[],citys:[],area:[],type:1,check:{province:{name:""},citys:{name:""}},param:{},country:""}),methods:{opens(){this.$refs.popup.open("bottom")},entrustCloseFn(){this.$refs.popup.close()},clear(){this.citys=[],this.check={province:{name:""},citys:{name:""}}},affirm(){this.$emit("affirm",this.check),this.entrustCloseFn()},init(e){this.country=e,this.mapList(1)},mapList(e,t){let a={country:this.country,parentMd5:t||""};t&&(a.parentId=t),this.$api.getAdminRegionListAPI({data:a,token:this.token}).then((t=>{1==e?this.province=t.result:2==e&&(this.citys=t.result)}))},provinceEvent(e){this.mapList(2,e.regionMd5),this.check.province.name=e.name,this.check.province.code=e.regionMd5,this.check.citys.name="",this.check.citys.code=""},citysEvent(e){this.check.citys.name=e.name,this.check.citys.code=e.regionMd5}}},[["render",function(e,t,a,m,f,v){const C=g,A=E(y("uni-popup"),H);return s(),i(A,{"safe-area":!1,ref:"popup","background-color":"#fff"},{default:n((()=>[o(C,{class:"region"},{default:n((()=>[o(C,{class:"head"},{default:n((()=>[h("span",{class:"txt"},"请选择")])),_:1}),o(C,{class:"region-con"},{default:n((()=>[o(C,{class:"region-list province"},{default:n((()=>[(s(!0),c(p,null,d(f.province,((e,t)=>(s(),i(C,{class:k(["item",f.check.province.name==e.name?"on":""]),onClick:t=>v.provinceEvent(e),key:t},{default:n((()=>[o(C,{class:"txt"},{default:n((()=>[r(u(e.name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),o(C,{class:k(["region-list citys",f.check.province.name?"on":""])},{default:n((()=>[(s(!0),c(p,null,d(f.citys,((e,t)=>(s(),i(C,{class:k(["item",f.check.citys.name==e.name?"on":""]),onClick:t=>v.citysEvent(e),key:t},{default:n((()=>[o(C,{class:"txt"},{default:n((()=>[r(u(e.name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1},8,["class"])])),_:1}),o(C,{class:"btnbox"},{default:n((()=>[o(C,{onClick:v.clear,class:"clear"},{default:n((()=>[r("重置")])),_:1},8,["onClick"]),o(C,{class:"affirm",onClick:v.affirm},{default:n((()=>[r("确认")])),_:1},8,["onClick"])])),_:1}),a.BottomBlank?(s(),c("div",{key:0,style:{height:"160rpx"}})):l("",!0)])),_:1})])),_:1},512)}],["__scopeId","data-v-711e7862"]]),ce={components:{tabBar:re,enterpriseList:G,industryChain:Y,treeSelect:J,countryMenu:le,moreScreen:z,SingleLineSelection:$,MultipleChoice:_,regionCut:x},data:()=>({title:"",showChain:"",showChainName:"",industryChainList:[],nodeName:"",List:[],keyenterprise:[],isshownodeName:"",showPopup:!1,tagList:[],pitchtag:[],pageNum:1,pageSize:10,total:0,size:0,mosj:!1,amount:0,coilHeight:"0rpx",nodeHeight:0,waibianjv:0,popupShow:!1,oletagList:[],statusBarHeight:0,showTree:!1,defaultValue:[],initialValue:[],list:[],selectType:2,knowId:"",baseInfo:{},moreData:{},token:"",moreScreenList:[],parameterList:[!0,!0,!0],cityName:"",introductionSelectIndex:1}),created(){this.statusBarHeight=e("statusBarHeight"),this.token=e("token"),this.token||(this.token=W("token"),C("token",this.token))},mounted(){},methods:{showChainNameFn:e=>(e.length>4&&(e=e.substring(0,3)+"..."),e),updataPage(e){this.$nextTick((()=>{this.List=[],this.ClearFilter(),this.countryName=e,this.getScreen(),this.$refs.countryMenu.init(e)}))},regionFn(){var e,t,a,s,i;let n="全国";return(null==(t=null==(e=this.baseInfo)?void 0:e.city)?void 0:t.name)?n=this.baseInfo.city.name:(null==(s=null==(a=this.baseInfo)?void 0:a.province)?void 0:s.name)&&(n=null==(i=this.baseInfo)?void 0:i.province.name),n.length>4&&(n=n.substring(0,3)+"..."),n},ClearFilter(){this.moreData={},this.baseInfo={},this.showChain="",this.showChainName="",this.$nextTick((()=>{this.$refs.industryChain.clear(),this.$refs.moreScreen.claimFn(),this.$refs.countryMenu.clear()})),this.changeSelectType()},updatamoreScreen(e){this.moreData=e,this.gettree(),this.changeSelectType()},affirm1(e){this.showChain=e.chainId,this.showChainName=e.chainName,this.gettree(),this.changeSelectType()},affirm2(e){this.baseInfo.province=JSON.parse(JSON.stringify(e.province)),this.baseInfo.city=JSON.parse(JSON.stringify(e.citys)),this.gettree(),this.changeSelectType()},iconColour:e=>e?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png",isEmptyValue(e){return"string"==typeof e?""===e.trim():"number"==typeof e?0===e:"boolean"==typeof e?!1===e:Array.isArray(e)?e.every(this.isEmptyValue):null!==e&&"object"==typeof e&&Object.values(e).every(this.isEmptyValue)},changeSelectType(){this.parameterList[0]=this.isEmptyValue(this.showChain),this.parameterList[1]=this.isEmptyValue(this.baseInfo),this.parameterList[2]=this.isEmptyValue(this.moreData)},getScreen(e){this.$api.enterpriseallChainAPI({data:{country:this.countryName},method:"GET"}).then((e=>{this.industryChainList=e.result,0!=this.industryChainList.length?(this.parameterList[0]=!1,this.showChain=this.industryChainList[0].chainId,this.showChainName=this.industryChainList[0].chainName,this.gettree()):this.showTree=!0})),this.$api.enterprisegetSearchParamAPI({data:{country:this.countryName},method:"GET"}).then((e=>{this.moreScreenList=e.result}))},screenFn(e){var t;if(this.token)0!=this.industryChainList.length&&(1==e?this.$refs.industryChain.opens():2==e?this.$refs.countryMenu.opens():3==e&&this.$refs.moreScreen.opens());else{(null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):v({url:"/pages/login/index"})}},digitalfiltering(e){if(e.sumEnterpriseCount){e.searchEnterpriseCount||(e.searchEnterpriseCount=0);let t=parseFloat(e.searchEnterpriseCount).toString(),a=parseFloat(e.sumEnterpriseCount).toString();return this.parameterList[0]&&this.parameterList[1]&&this.parameterList[2]?`${a}`:`<span style="color: #3370FF">${t}</span>/${a}`}return this.parameterList[0]&&this.parameterList[1]&&this.parameterList[2]?"0":'<span style="color: #3370FF;">0</span>/0'},changeState(e){this.popupShow=e.show},changeState2(e){this.popupShow=e},loadMoreData(){var e,t,a,s,i;if(this.pageNum>this.size)return void(this.mosj=!0);if(this.isLoading)return;this.isLoading=!0;let n=this.processedObject(this.moreData),o={chainId:this.showChain,country:this.countryName,nodeId:this.isshownodeName.nodeId,pageNum:this.pageNum,pageSize:this.pageSize,...n};(null==(t=null==(e=this.baseInfo)?void 0:e.city)?void 0:t.code)?o.regionMd5=this.baseInfo.city.code:(null==(s=null==(a=this.baseInfo)?void 0:a.province)?void 0:s.code)&&(o.regionMd5=null==(i=this.baseInfo)?void 0:i.province.code),this.$api.enterprisePageListAPI({data:o,method:"POST"}).then((e=>{this.mosj=!1,this.total=e.result.total,this.size=Math.ceil(this.total/10),this.pageNum++,this.keyenterprise=this.keyenterprise.concat(e.result.records),this.isLoading=!1})).catch((e=>{this.isLoading=!1}))},processedObject(e){let t={...e};for(let a in t)""===t[a]?t[a]=[]:t[a]=[t[a]];return t},gettree(){var e,t,a,s,i;let n=this.processedObject(this.moreData),o={chainId:this.showChain,country:this.countryName,...n};(null==(t=null==(e=this.baseInfo)?void 0:e.city)?void 0:t.code)?o.regionMd5=this.baseInfo.city.code:(null==(s=null==(a=this.baseInfo)?void 0:a.province)?void 0:s.code)&&(o.regionMd5=null==(i=this.baseInfo)?void 0:i.province.code),this.showTree=!1,this.$api.getChainNodeTreeListAPI({data:o,method:"POST"}).then((e=>{this.List=e.result||[],this.List.forEach(((e,t)=>{0==t&&0!=e.children.length?e.show=!0:e.show=!1,e.children.forEach((e=>{e.show=!1,e.children&&e.children.forEach((e=>{e.show=!1}))}))})),this.showTree=!0})).catch((e=>{this.showTree=!0}))},isshow(e,t){null==t?this.List[e].show=!this.List[e].show:this.List[e].children[t].show=!this.List[e].children[t].show},recommendQ(e){var t,a,s,i,n,o;if(!this.token){return void((null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):v({url:"/pages/login/index"}))}this.isshownodeName=e,this.pageNum=1,this.mosj=!1,this.keyenterprise=[],this.pitchtag=[];let r=this.processedObject(this.moreData),l={chainId:this.showChain,country:this.countryName,nodeId:e.nodeId,pageNum:this.pageNum,pageSize:this.pageSize,...r};(null==(s=null==(a=this.baseInfo)?void 0:a.city)?void 0:s.code)?l.regionMd5=this.baseInfo.city.code:(null==(n=null==(i=this.baseInfo)?void 0:i.province)?void 0:n.code)&&(l.regionMd5=null==(o=this.baseInfo)?void 0:o.province.code),this.$api.enterprisePageListAPI({data:l,method:"POST"}).then((e=>{this.total=e.result.total,this.size=Math.ceil(this.total/10),this.keyenterprise=this.keyenterprise.concat(e.result.records),this.pageNum++,this.$refs.popup.open("bottom")}))}},watch:{keyenterprise(e){this.keyenterprise.forEach((e=>{let t=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let a=e.enterpriseLabelNames[0];a?t.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:a.includes("独角兽")?e.enterpriseIconLabelId=2:a.includes("专精特新")?e.enterpriseIconLabelId=3:a.includes("隐形冠军")?e.enterpriseIconLabelId=4:a.includes("瞪羚")?e.enterpriseIconLabelId=5:a.includes("创新")?e.enterpriseIconLabelId=6:a.includes("技术先进")?e.enterpriseIconLabelId=7:a.includes("科技")?e.enterpriseIconLabelId=8:a.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}}};const de={props:{list:{type:Array,default:()=>[]},single:{type:Boolean,default:!1},BottomBlank:{type:Boolean,default:!1},appletTab:{type:Boolean,default:!1}},data:()=>({checkNames:[]}),methods:{opens(){this.$refs.SelectFinancePop.open("bottom")},entrustCloseFn(){this.$refs.SelectFinancePop.close()},clear(){this.checkNames=[]},affirm(){this.$emit("affirm",this.checkNames),this.entrustCloseFn()},citysEvent(e){const t=this.checkNames.indexOf(e.chainId);t>-1?this.checkNames.splice(t,1):this.checkNames.push(e.chainId)}}};const he=M({components:{handleAsk:ie,handleStrategy:ne,handlyService:oe,industry360:M(ce,[["render",function(e,t,v,C,A,L){const w=E(y("page-meta"),Z),b=a("regionCut"),N=g,I=m,S=f,D=a("enterpriseList"),B=E(y("uni-popup"),H),x=a("countryMenu"),T=a("moreScreen"),P=a("industryChain");return s(),c(p,null,[o(w,{"page-style":"overflow:"+(A.popupShow?"hidden":"visible")},null,8,["page-style"]),o(N,{class:"box"},{default:n((()=>[o(N,{class:"titleBox"},{default:n((()=>[o(b,{onUpdataPage:L.updataPage,ref:"regionCut"},null,8,["onUpdataPage"])])),_:1}),o(N,{class:"contentBox"},{default:n((()=>[h("div",{class:"optionBox"},[h("span",{class:k([A.parameterList[0]?"unselected":"pitchOn","optionItem"]),onClick:t[0]||(t[0]=e=>L.screenFn(1))},[r(u(L.showChainNameFn(A.showChainName)||"暂无产业链")+" ",1),o(I,{src:L.iconColour(A.parameterList[0]),class:"Arrows"},null,8,["src"])],2),h("span",{class:k([A.parameterList[1]?"unselected":"pitchOn","optionItem"]),onClick:t[1]||(t[1]=e=>L.screenFn(2))},[r(u(L.regionFn())+" ",1),o(I,{src:L.iconColour(A.parameterList[1]),class:"Arrows"},null,8,["src"])],2),h("span",{class:k([A.parameterList[2]?"unselected":"pitchOn","optionItem"]),onClick:t[2]||(t[2]=e=>L.screenFn(3))},[r(" 更多查询 "),o(I,{src:L.iconColour(A.parameterList[2]),class:"Arrows"},null,8,["src"])],2)]),0!=A.List.length?(s(),i(S,{key:0,"scroll-y":"true",class:"nodeBox"},{default:n((()=>[(s(!0),c(p,null,d(A.List,((e,t)=>(s(),c("div",{key:t,class:"nodeItem"},[o(N,{class:"stairNode"},{default:n((()=>[h("span",{style:{display:"flex","white-space":"nowrap"},onClick:t=>L.recommendQ(e)},[r(u(e.nodeName)+"(",1),h("span",{style:{"text-align":"center"},innerHTML:L.digitalfiltering(e)},null,8,["innerHTML"]),r(" )")],8,["onClick"]),e.children.length>=1?(s(),c("div",{key:0,onClick:e=>L.isshow(t),class:"OperationExpansion"},[h("img",{src:ee,class:k(e.show?"unfoldIcon":"upackIcon")},null,2)],8,["onClick"])):l("",!0)])),_:2},1024),e.children&&e.show?(s(),c("div",{key:0,class:"secondLevelBox"},[(s(!0),c(p,null,d(e.children,((e,a)=>(s(),c("div",{key:a},[h("div",{class:"secondLevel"},[h("span",{class:"secondLevelText",onClick:t=>L.recommendQ(e)},[h("div",{class:"dot"}),r(" "+u(e.nodeName)+" (",1),h("span",{style:{"text-align":"center"},innerHTML:L.digitalfiltering(e)},null,8,["innerHTML"]),r(") ")],8,["onClick"]),e.children.length>=1?(s(),c("div",{key:0,style:{},onClick:e=>L.isshow(t,a)},[h("img",{src:ee,class:k(e.show?"unfoldIcon":"upackIcon")},null,2)],8,["onClick"])):l("",!0)]),e.children&&e.show?(s(),c("div",{key:0,class:"threeLevelBox"},[(s(!0),c(p,null,d(e.children,((e,t)=>(s(),c("div",{key:t,class:"threeLevel"},[h("span",{class:"threeLevelText",onClick:t=>L.recommendQ(e)},[r(u(e.nodeName)+" (",1),h("span",{style:{"text-align":"center"},innerHTML:L.digitalfiltering(e)},null,8,["innerHTML"]),r(") ")],8,["onClick"])])))),128))])):l("",!0)])))),128))])):l("",!0)])))),128)),h("div",{style:{"min-height":"180rpx"}})])),_:1})):l("",!0),A.showTree&&0==A.List.length?(s(),i(N,{key:1,class:"nodatabox"},{default:n((()=>[o(I,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),h("span",{class:"span"},"暂无内容")])),_:1})):l("",!0)])),_:1})])),_:1}),o(B,{ref:"popup","safe-area":!1,"background-color":"rgba(0,0,0,0)",onChange:L.changeState},{default:n((()=>[o(N,{class:"uni-popups"},{default:n((()=>[o(N,{class:"popup-content"},{default:n((()=>[h("span",{class:"nodeName"},u(A.isshownodeName.nodeName)+"("+u(A.total)+")",1)])),_:1}),o(S,{style:{height:"1100rpx"},"scroll-y":"true",onScrolltolower:L.loadMoreData},{default:n((()=>[o(N,{class:"qyList"},{default:n((()=>[o(D,{showChain:A.showChain,enterpriseList:A.keyenterprise},null,8,["showChain","enterpriseList"])])),_:1}),A.total<1?(s(),i(N,{key:0,class:"noemphasis"},{default:n((()=>[h("img",{src:"https://static.idicc.cn/cdn/aiChat/applet/nozdqy.png",alt:""}),h("span",{class:"nozdqy"},"暂无企业")])),_:1})):l("",!0),A.mosj&&0!=A.total?(s(),i(N,{key:1,class:"noemphasis2"},{default:n((()=>[h("span",{class:"total"},"没有更多啦~")])),_:1})):l("",!0)])),_:1},8,["onScrolltolower"])])),_:1})])),_:1},8,["onChange"]),o(x,{ref:"countryMenu",unrestricted:!0,BottomBlank:!0,appletTab:!0,onAffirm:L.affirm2},null,8,["onAffirm"]),o(T,{MultipleChoice:!0,appletTab:!0,BottomBlank:!0,ref:"moreScreen",title:"更多查询",moreScreenList:A.moreScreenList,onUpdatamoreScreen:L.updatamoreScreen},null,8,["moreScreenList","onUpdatamoreScreen"]),o(P,{CurrentlySelected:A.showChain,list:A.industryChainList,ref:"industryChain",BottomBlank:!0,appletTab:!0,onAffirm:L.affirm1},null,8,["CurrentlySelected","list","onAffirm"])],64)}],["__scopeId","data-v-fbf09485"]]),garden360:M({components:{countryMenu:le,tabBar:re,moreScreen:z,regionCut:x,industryChain:Y,industryChainMulti:M(de,[["render",function(e,t,a,m,f,v){const C=g,A=E(y("uni-popup"),H);return s(),i(A,{"safe-area":!1,ref:"SelectFinancePop","background-color":"#fff"},{default:n((()=>[o(C,{class:"region"},{default:n((()=>[o(C,{class:"head"},{default:n((()=>[h("span",{class:"txt"},"请选择")])),_:1}),o(C,{class:"region-con"},{default:n((()=>[o(C,{class:"region-list citys"},{default:n((()=>[(s(!0),c(p,null,d(a.list,((e,t)=>(s(),i(C,{class:k(["item",f.checkNames.includes(e.chainId)?"on":""]),onClick:t=>v.citysEvent(e),key:t},{default:n((()=>[o(C,{class:"txt"},{default:n((()=>[r(u(e.chainName),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),o(C,{class:"btnbox"},{default:n((()=>[o(C,{onClick:v.clear,class:"clear"},{default:n((()=>[r("重置")])),_:1},8,["onClick"]),o(C,{class:"affirm",onClick:v.affirm},{default:n((()=>[r("确认")])),_:1},8,["onClick"])])),_:1}),a.BottomBlank?(s(),c("div",{key:0,style:{height:"160rpx"}})):l("",!0)])),_:1})])),_:1},512)}],["__scopeId","data-v-93275b36"]])},data:()=>({listicon:X,title:"",enterpriseList:[],industryChainList:[],nodeName:"",List:[],keyenterprise:[],isshownodeName:"",showPopup:!1,tagList:[],pitchtag:[],pageNum:1,pageSize:10,total:0,size:0,mosj:!1,amount:0,coilHeight:"0rpx",nodeHeight:0,waibianjv:0,popupShow:!1,oletagList:[],statusBarHeight:0,showTree:!1,defaultValue:[],initialValue:[],list:[],showNodata:!1,selectType:2,knowId:"",baseInfo:{},moreData:{},affinityData:{},sortData:{},Jurisdiction:!0,token:"",moreScreenList:[],moreScreenList2:[],parameterList:[!0,!0,!0,!0],cityName:"",introductionSelectIndex:1,countryName:"",showChainName:"",showChain:"",sortList:[{chainName:"不限",chainId:""},{chainName:"园区中资企业数量",chainId:"chineseEnterpriseCount"},{chainName:"园区外资企业数量(含中资)",chainId:"foreignEnterpriseCount"}],orderByField:"chineseEnterpriseCount",chainIds:[],exchangeRate:"",triggered:!1}),created(){this.statusBarHeight=e("statusBarHeight"),W("token")&&(this.token=W("token"),C("token",this.token)),this.token=e("token")},mounted(){},methods:{digitalfiltering:e=>`<span>区域生态企业数量</span><span style="font-size: 12px;">(园区企业和园区周边产业相关企业)</span>：${e||"-"}`,isShowFilter(){this.showChain?this.sortList=[{chainName:"不限",chainId:""},{chainName:"园区中资企业数量",chainId:"chineseEnterpriseCount"},{chainName:"园区外资企业数量(含中资)",chainId:"foreignEnterpriseCount"},{chainName:"所选产业企业数量",chainId:"chainEnterpriseMap"},{chainName:"所选产业链完整度",chainId:"chainPercentMap"}]:("chainPercentMap"!=this.orderByField&&"chainEnterpriseMap"!=this.orderByField||(this.orderByField="chineseEnterpriseCount",this.$refs.sortChain.clear(),this.changeSelectType()),this.sortList=[{chainName:"不限",chainId:""},{chainName:"园区中资企业数量",chainId:"chineseEnterpriseCount"},{chainName:"园区外资企业数量(含中资)",chainId:"foreignEnterpriseCount"}])},refreshMyentrustList(){this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.enterpriseList=[],this.getList(),this.triggered=!0,this.getList()},async updataPage(e){this.token?(this.ClearFilter(),this.enterpriseList=[],this.countryName=e,this.showChainName="",this.showChain="",await this.isShowFilter(),this.getList(),this.getScreen(),this.$refs.countryMenu.init(e)):this.showNodata=!0},detailpage(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{id:e.parkMd5,enterpriseName:e.industryParkName},path:"gardenDel"}))},goDetail(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{parkMd5:e.parkMd5,industryParkName:e.industryParkName},path:"businessDirectory"}))},processedObject(e){let t={...e};for(let a in t)""===t[a]?t[a]=[]:t[a]=[t[a]];return t},getList(){var e,t,a,s,i;if(this.isLoading)return;this.isLoading=!0;let n=this.processedObject(this.moreData),o={pageSize:10,pageNum:this.pageNum,country:this.countryName,chainId:this.showChain,...n,orderByField:this.orderByField};(null==(t=null==(e=this.baseInfo)?void 0:e.city)?void 0:t.code)?o.regionMd5=this.baseInfo.city.code:(null==(s=null==(a=this.baseInfo)?void 0:a.province)?void 0:s.code)&&(o.regionMd5=null==(i=this.baseInfo)?void 0:i.province.code),this.$api.parkPageListAPI({data:o,method:"post"}).then((e=>{this.enterpriseList=this.enterpriseList.concat(e.result.records),this.pages=e.result.pages,this.total=e.result.total,this.triggered=!1})).finally((()=>{this.isLoading=!1,this.showNodata=!0,this.triggered=!1}))},scrolltolowerFn(){this.pages>this.pageNum&&(this.pageNum++,this.getList())},regionFn(){var e,t,a,s,i;let n="全国";return(null==(t=null==(e=this.baseInfo)?void 0:e.city)?void 0:t.name)?n=this.baseInfo.city.name:(null==(s=null==(a=this.baseInfo)?void 0:a.province)?void 0:s.name)&&(n=null==(i=this.baseInfo)?void 0:i.province.name),n.length>4&&(n=n.substring(0,3)+"..."),n},showChainNameFn:e=>("不限"==e&&(e="产业链"),e.length>4&&(e=e.substring(0,3)+"..."),e),async ClearFilter(){this.moreData={},this.baseInfo={},this.chainIds=[],this.showChain="",await this.isShowFilter(),this.orderByField="chineseEnterpriseCount",this.$nextTick((()=>{this.$refs.countryMenu.clear(),this.$refs.moreScreen.claimFn(),this.$refs.sortChain.clear(),this.$refs.industryChain.clear()})),this.changeSelectType()},updatamoreScreen(e){this.moreData=e,this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList(),this.changeSelectType()},async affirm(e){this.showChain=e.chainId,this.showChainName=e.chainName,this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,await this.isShowFilter(),this.getList(),this.changeSelectType()},affirm1(e){this.chainIds=e,this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList(),this.changeSelectType()},affirm2(e){this.baseInfo.province=JSON.parse(JSON.stringify(e.province)),this.baseInfo.city=JSON.parse(JSON.stringify(e.citys)),this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList(),this.changeSelectType()},affirm3(e){this.orderByField=e.chainId,this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList(),this.changeSelectType()},iconColour(e){return this.Jurisdiction?e?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png":"/static/AboutAi/grayArrows.png"},isEmptyValue(e){return"string"==typeof e?""===e.trim():"number"==typeof e?0===e:"boolean"==typeof e?!1===e:Array.isArray(e)?e.every(this.isEmptyValue):null!==e&&"object"==typeof e&&Object.values(e).every(this.isEmptyValue)},changeSelectType(){this.parameterList[0]=this.isEmptyValue(this.showChain),this.parameterList[1]=this.isEmptyValue(this.baseInfo),this.parameterList[2]=this.isEmptyValue(this.moreData),this.parameterList[3]=this.isEmptyValue(this.orderByField)},getScreen(){this.token&&(this.$api.enterpriseallChainAPI({data:{country:this.countryName},method:"GET"}).then((e=>{this.industryChainList=e.result,0!=this.industryChainList.length&&this.industryChainList.unshift({chainName:"不限",chainId:""})})),this.$api.parkGetSearchParamAPI({method:"GET",data:{country:this.countryName}}).then((e=>{this.moreScreenList=e.result.searchParams,this.exchangeRate=e.result.rate+e.result.currency})))},screenFn(e){var t;if(this.token)if(1==e){if(0==this.industryChainList.length)return;this.$refs.industryChain.opens()}else 2==e?this.$refs.countryMenu.opens():3==e?this.$refs.moreScreen.opens():4==e&&this.$refs.sortChain.opens();else{(null==navigator?void 0:navigator.userAgent.indexOf("airApp/1.0.0"))>-1?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"login"})):v({url:"/pages/login/index"})}}},watch:{enterpriseList(e){this.enterpriseList.forEach((e=>{let t=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let a=e.enterpriseLabelNames[0];a?t.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:a.includes("独角兽")?e.enterpriseIconLabelId=2:a.includes("专精特新")?e.enterpriseIconLabelId=3:a.includes("隐形冠军")?e.enterpriseIconLabelId=4:a.includes("瞪羚")?e.enterpriseIconLabelId=5:a.includes("创新")?e.enterpriseIconLabelId=6:a.includes("技术先进")?e.enterpriseIconLabelId=7:a.includes("科技")?e.enterpriseIconLabelId=8:a.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}}},[["render",function(e,t,v,C,A,L){const w=E(y("page-meta"),Z),b=a("regionCut"),N=g,I=m,S=f,D=a("countryMenu"),B=a("industryChain"),x=a("moreScreen"),T=a("industryChainMulti");return s(),c(p,null,[o(w,{"page-style":"overflow:"+(A.popupShow?"hidden":"visible")},null,8,["page-style"]),o(N,{class:"box"},{default:n((()=>[o(N,{class:"titleBox"},{default:n((()=>[o(b,{onUpdataPage:L.updataPage,ref:"regionCut"},null,8,["onUpdataPage"])])),_:1}),h("div",{class:"contentBox"},[h("div",{class:"optionBox"},[h("span",{class:k([A.parameterList[0]?"unselected":"pitchOn","optionItems"]),onClick:t[0]||(t[0]=e=>L.screenFn(1))},[r(u(L.showChainNameFn(A.showChainName)||(0==A.industryChainList.length?"暂无产业链":"产业链"))+" ",1),o(I,{src:L.iconColour(A.parameterList[0]),class:"Arrows"},null,8,["src"])],2),h("span",{class:k([A.parameterList[1]?"unselected":"pitchOn","optionItem"]),onClick:t[1]||(t[1]=e=>L.screenFn(2))},[r(u(L.regionFn())+" ",1),o(I,{src:L.iconColour(A.parameterList[1]),class:"Arrows"},null,8,["src"])],2),h("span",{class:k([A.parameterList[2]?"unselected":"pitchOn","optionItem"]),onClick:t[2]||(t[2]=e=>L.screenFn(3))},[r(" 更多查询 "),o(I,{src:L.iconColour(A.parameterList[2]),class:"Arrows"},null,8,["src"])],2),h("span",{class:k([A.parameterList[3]?"unselected":"pitchOn","optionItem"]),onClick:t[3]||(t[3]=e=>L.screenFn(4))},[r(" 排序 "),o(I,{src:L.iconColour(A.parameterList[3]),class:"Arrows"},null,8,["src"])],2)]),0!=A.enterpriseList.length?(s(),i(S,{key:0,"refresher-enabled":!0,onRefresherrefresh:t[4]||(t[4]=e=>L.refreshMyentrustList()),"refresher-triggered":A.triggered,class:"listBox",onScrolltolower:L.scrolltolowerFn,onScroll:e.scroll,"scroll-y":"true"},{default:n((()=>[o(N,null,{default:n((()=>[(s(!0),c(p,null,d(A.enterpriseList,((e,t)=>(s(),i(N,{class:"card",key:t},{default:n((()=>[o(N,{class:"imgNameBox"},{default:n((()=>[o(I,{src:A.listicon[0].icon,class:"icon2"},null,8,["src"]),h("span",{onClick:t=>L.detailpage(e),class:"enterpriseName"},u(e.industryParkNameEn||e.industryParkName),9,["onClick"])])),_:2},1024),o(N,{class:"contentBox2 mgt-24"},{default:n((()=>{var t,a;return[h("p",{class:"contentSpan"},"名称："+u((null==e?void 0:e.industryParkName)||"--"),1),h("p",{class:"contentSpan"},"所属区域："+u((null==(t=null==e?void 0:e.location)?void 0:t.join("、"))||"--"),1),h("p",{class:"contentSpan"},"地理位置："+u(e.address||"--"),1),0!=e.enterpriseCount&&e.chainNames?(s(),c("p",{key:0,class:"contentSpan"},"主导产业："+u(null==(a=null==e?void 0:e.chainNames)?void 0:a.join("、")),1)):l("",!0),0!=e.enterpriseCount?(s(),c("p",{key:1,class:"contentSpan"},[h("span",{innerHTML:L.digitalfiltering(e.enterpriseCount)},null,8,["innerHTML"])])):l("",!0),0!=e.enterpriseCount&&0!=e.chineseEnterpriseCount?(s(),c("p",{key:2,class:"contentSpan"}," 中资企业数量："+u(e.chineseEnterpriseCount||"--"),1)):l("",!0),0!=e.enterpriseCount&&0!=e.foreignEnterpriseCount?(s(),c("p",{key:3,class:"contentSpan"}," 外资企业数量(含中资)："+u(e.foreignEnterpriseCount||"--"),1)):l("",!0)]})),_:2},1024),A.parameterList[0]?l("",!0):(s(),i(N,{key:0,class:"hintBox"},{default:n((()=>{var t,a;return[h("div",null,"所选产业企业数量："+u(null==(t=null==e?void 0:e.chains[0])?void 0:t.enterpriseCount),1),h("div",null,"所选产业链完整度："+u(null==(a=null==e?void 0:e.chains[0])?void 0:a.chainPercent),1)]})),_:2},1024)),0!=e.enterpriseCount?(s(),i(N,{key:1,class:"detail",onClick:t=>L.goDetail(e)},{default:n((()=>[r(" 区域生态企业名录 "),o(I,{src:te,class:"righticon"})])),_:2},1032,["onClick"])):l("",!0)])),_:2},1024)))),128))])),_:1}),h("div",{style:{"min-height":"120rpx"}})])),_:1},8,["refresher-triggered","onScrolltolower","onScroll"])):l("",!0),A.showNodata&&0==A.enterpriseList.length?(s(),i(N,{key:1,class:"nodatabox"},{default:n((()=>[o(I,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),h("span",{class:"span"},"暂无内容")])),_:1})):l("",!0)])])),_:1}),o(D,{ref:"countryMenu",unrestricted:!0,BottomBlank:!0,appletTab:!0,onAffirm:L.affirm2},null,8,["onAffirm"]),o(B,{CurrentlySelected:A.showChain,list:A.industryChainList,ref:"industryChain",BottomBlank:!0,appletTab:!0,onAffirm:L.affirm},null,8,["CurrentlySelected","list","onAffirm"]),o(x,{exchangeRate:A.exchangeRate,MultipleChoice:!0,appletTab:!0,BottomBlank:!0,ref:"moreScreen",title:"更多查询",moreScreenList:A.moreScreenList,onUpdatamoreScreen:L.updatamoreScreen},null,8,["exchangeRate","moreScreenList","onUpdatamoreScreen"]),o(B,{list:A.sortList,CurrentlySelected:A.orderByField,ref:"sortChain",BottomBlank:!0,appletTab:!0,onAffirm:L.affirm3},null,8,["list","CurrentlySelected","onAffirm"]),o(T,{list:A.industryChainList,ref:"industryChainMulti",BottomBlank:!0,appletTab:!0,onAffirm:L.affirm1},null,8,["list","onAffirm"])],64)}],["__scopeId","data-v-0b9cce0e"]])},data:()=>({pitch:1,token:"",IdentityType:4}),onLoad(t){this.token=e("token"),W("pitch")&&(this.pitch=Number(W("pitch"))),W("token")&&(this.token=W("token"),C("token",this.token)),W("identity")&&(this.IdentityType=W("identity"),C("userIdentityType",this.IdentityType))},onHide(){this.$refs.handleStrategyRef&&this.$refs.handleStrategyRef.clearTimer()},onUnload(){this.$refs.handleStrategyRef&&this.$refs.handleStrategyRef.clearTimer()},methods:{changtab(e){this.pitch=e,this.$refs.handleStrategyRef&&(this.$refs.handleStrategyRef.clearTimer(),this.$refs.handleStrategyRef.requestSaveCache())}}},[["render",function(e,t,c,d,h,u){const p=a("handleAsk"),f=a("handleStrategy"),y=a("handlyService"),v=a("industry360"),C=a("garden360"),L=m,w=g;return s(),i(w,null,{default:n((()=>[1==h.pitch?(s(),i(p,{key:0})):l("",!0),2==h.pitch?(s(),i(f,{key:1,ref:"handleStrategyRef"},null,512)):l("",!0),3==h.pitch?(s(),i(y,{key:2})):l("",!0),4==h.pitch?(s(),i(v,{key:3})):l("",!0),5==h.pitch?(s(),i(C,{key:4})):l("",!0),3!=h.IdentityType?(s(),i(w,{key:5,class:"bottomTab"},{default:n((()=>[o(w,{onClick:t[0]||(t[0]=e=>u.changtab(1)),class:"singleTabs",style:A({color:1==h.pitch?"#3370FF":"#86909C"})},{default:n((()=>[1==h.pitch?(s(),i(L,{key:0,class:"my",src:"data:image/png;base64,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"})):(s(),i(L,{key:1,class:"my",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAB6RJREFUaEPtWWFoW9cV/s6VsjrEYRl44Gwdc1gGLaTEYStrO0t6oV1tPTnEGfH0ZDxoaUczVkhGU5axlHo0YynrDxc86I9CO9ZZcpduHrUlt6TkSXJJChvraKAZDSw/PCRofvhHZ6mddM9235OUpycpek/26ML2wGD07jn3fPece75zziPc4g/d4vbjfxuAFovvk0wRAg0DGAIwAGAXgDIYRRZ8GVJcEqWNZdNcXP9PeNu3BzR9clAi8BiBHqoZ3d0uRhmEFcmYzWeS2e4C3ld4BqBpE7vkjr5niHEMoGDzFlwBqAjwGpjKIO4D6HYA6s/9rFRk4MTbK6/81buZnVd6AhCKTY0K5hddBl1j5nMMWgqUCu+Ypll2b6MdOjQgKztHiXgawFjjPaPMxCdz6dQvNwuiK4BwLPETYpxxbHRFAqcDG4U/mKZZ8WqA9q3JYQ4GnwZhoi7DjFlRKjzpR497v5sCCOvG0wSasYW4wkynRKnw/GY2DEeNOIFeBqGvpvflbDr1sNeD8AwgrE8dJ/CsbTt/JBkT+ZXUW71u5JQbGZ/eH5CVFYAGLfXgmVw69dNedLf1wMHx+H5ZFZesU2KUJaoP5DOvvt3LBp1ktAfj+zggLoLQD+ZKlTCymk6943ePtgAieuLPAFRuBzMbuUxqwa9iL+tDevywgFishdJl2ige8BueLQBC+tRhAbaVMhazmeQRL8b0uiaiJ34NQGUpMNN0LjP/Gz+6WgBE9ESmnvKqVTm8+sbCX/wo9LtWG53cy4HA+xa3MMxsJnnQj44mAJo22c/bgx/WYn81m0mG/CjrdW1EN14HaFzdBQrSbvP15HWvupoBjBkaC7pQi/1TuUzqWa+KNrMurBs/INCc0kFcPWJmXrVCOBw1fiRKRZW2W0iyvl8TgLCeeIyAFyxFkIfM9MJSfeF9+tSXt0HOgalfBvhMfqk5pd4XjX9lG4k5MPokqqfdWWtkPL4/IMVzSl9FBh53lhJaNH4Pk7joPjg7mfAabRSPdLrcLgA3iIsquNN8M3mlDqDhZvuHddoofN6pNKIbFwG6x778fDWbSX3V6ZWIbrwH0L7ab5ez6eRd9feqQGQEC7YoZnOZ5A/V/5GY8QGY9jLoTC49/1Q7L3sHEE3kQRixDUSZSoXPOV3bZCDjo2wmudMFoFAnLgBr2XTyS10B6EZDRhKN5Zfn33CDcANwxKK818wsXKoLhMaM+4WgcwD3M9NMLpP8mVNZKGbEhKRzIA4yVKG28LzzfThmPEJSxTlXGHjUyS3ag4k7OIj3ax44Xddt6WSyw5j5KpWKd7pDqQnAQT1+WNaIpV1O1jRNldHBTpfKw3ur/nHLH4wZMVkzlJimTQcXNPGExEO5leSvnAfjzkJDLOhvdpTwC7l06vvt4m6rfwvriZ8TcMpKHq675+KJltTejsgUgCEw1qhU2OOX2nsBF4kaH4BoLxjXspnkHreOBrm24YkWAOGo8QsiOml5oY3LejHwZjKhceN+Iem87XWczaWTP3avd1bGUvIDzqq4BUCzy6yLc9fNiGSzgBqFo8psVNljpn9bdOvUnAQLftzZyXWoRo2XYDftqhp9LpdJPblZQ9vJK6YlorPu/N8CIBbfxyzeq93Npt6hLQAt+u3bmW5TAmpEAgkczaeTr20lCLvPlku1Iu4alUoHOo1eOqVZ69J3MsrOwVisbVCWgibaEUkvoMKxqUdI8pzXhqkphFz3sktPHD9OEPW2siIFJvLLqeVejFYy945PfvEzHJwF46hNTihLpon8SivDOvcIRxNPEMGqowh8t5lO/bH+vvtUwtEbM+hELj3fxLDdwChyox1f2C+Zj4ExfaOZx7qUfNRLnx2JJfJgq4xpqcG6AtD0qWEGqxYTTgCapvXJ7YPPEOjrIKwx8ToYN+p4wgAx3VFrTdXI0fmcJ/74YTPzu7VuB2D151K8W1v3Yjad/J5TpicAI7rxjYAajQDKQO8PsymFOOvnLkX0xO8Be5bUrkP0B4BwGpIHiZrGi5cBHnBUmk5A6oSvMGNFVD9ZNt98rVGee0FtzZCIUrW1r2TTye+65XwBUC0fqDYXZawK4pMXaqMQq5DbuXsX/sn9ENUyNj5c3wwBhqLf+aZA4HztzqwRKne3Izl/AKyLwFcZYsbv9MDLidfXWOVFFYsg6rfnUhjrNNX2A2CdwbNio/hs/WTVqVd3DI4GqvhHVYjPglD2E99uUEqf3L77OBGfbfBPlzTbHYBF4/QE8SdPtcsaobGpUUG82EiPzGY1wCdWl/yPYyLRRBIEwwbGRSkx3S3NdgXgxfXWrLNaUS5XX2ksgvp3+lyUEHOB0t/V6N3TFDsSTVwAQQNwnfjjA17S7JYAUDbbvLBbNSbHHGSlXqlPS1dVH2wfhkoCrD58DLlD0gHgWjbd2he0O8wtA1BXro0ZQ5LoOJE1LnQTWDsb3q0SPbq6PP+n/woATgs1fXJY0ravgXkvWWA4yETKI9chVf3PJy3+YKvRnyVr6mGNZj49D3i5Mw1vHUoMyCqrcuSYS+7WANAAoj4/bQu+VB/p3zIecHvLHm3yDEDlT+0S+wmhdmvr36G9fnLa8iy0WQB+5f8PwO+JbfX6fwFzOtFeuyNQxAAAAABJRU5ErkJggg=="})),r(" 出海智问 ")])),_:1},8,["style"]),o(w,{onClick:t[1]||(t[1]=e=>u.changtab(4)),class:"singleTabs",style:A({color:4==h.pitch?"#3370FF":"#86909C"})},{default:n((()=>[4==h.pitch?(s(),i(L,{key:0,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_97_52350'%3e%3crect%20x='24'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20transform='matrix(-1,0,0,1,48,0)'%20clip-path='url(%23master_svg0_97_52350)'%3e%3cg%3e%3cpath%20d='M43.6,4.51786C43.899,4.51786,44.1429,4.7905999999999995,44.1429,5.125C44.1429,5.125,44.1429,17.875,44.1429,17.875C44.1429,18.209400000000002,43.899,18.482100000000003,43.6,18.482100000000003C43.6,18.482100000000003,28.4,18.482100000000003,28.4,18.482100000000003C28.101,18.482100000000003,27.85714,18.209400000000002,27.85714,17.875C27.85714,17.875,27.85714,7.25,27.85714,7.25C27.85714,6.9155999999999995,28.101,6.642860000000001,28.4,6.642860000000001C28.4,6.642860000000001,36.5619,6.642860000000001,36.5619,6.642860000000001C36.5619,6.642860000000001,36.9585,6.19936,36.9585,6.19936C36.9585,6.19936,38.4619,4.51786,38.4619,4.51786C38.4619,4.51786,43.6,4.51786,43.6,4.51786ZM43.6,3C43.6,3,38.294399999999996,3,38.294399999999996,3C38.0421,3,37.8003,3.111467,37.6222,3.310686C37.6222,3.310686,36,5.125,36,5.125C36,5.125,28.4,5.125,28.4,5.125C27.350335,5.125,26.5,6.076029999999999,26.5,7.25C26.5,7.25,26.5,17.875,26.5,17.875C26.5,19.049,27.350335,20,28.4,20C28.4,20,43.6,20,43.6,20C44.649699999999996,20,45.5,19.049,45.5,17.875C45.5,17.875,45.5,5.125,45.5,5.125C45.5,3.951032,44.649699999999996,3,43.6,3Z'%20fill='%233370ff'%20fill-opacity='1'/%3e%3cpath%20d='M45.6,17.875L45.6,5.125Q45.6,4.2063299999999995,45.0182,3.555608Q44.432,2.9,43.6,2.9000003L38.294399999999996,2.9Q37.8553,2.9,37.5477,3.244032L35.95527,5.025L28.4,5.025Q27.56802,5.025,26.981829,5.68061Q26.4,6.33134,26.4000001,7.25L26.4,17.875Q26.4,18.7937,26.981829,19.4444Q27.56802,20.1,28.4,20.1L43.6,20.1Q44.432,20.1,45.0182,19.4444Q45.6,18.7937,45.6,17.875ZM44.8691,3.688916Q45.4,4.2827,45.4,5.125L45.4,17.875Q45.4,18.7173,44.8691,19.3111Q44.3425,19.9,43.6,19.9L28.4,19.9Q27.65748,19.9,27.130923,19.3111Q26.6,18.7173,26.6,17.875L26.6,7.25Q26.6,6.40771,27.130923,5.8139199999999995Q27.65748,5.225,28.4,5.225L36.04473,5.225L37.696799999999996,3.3773400000000002Q37.9447,3.1,38.294399999999996,3.1L43.6,3.1Q44.3425,3.1,44.8691,3.688916ZM43.6,4.41786L38.4172,4.41786L36.8839,6.1327L36.5172,6.54286L28.4,6.54286Q28.13102,6.54286,27.9419,6.75437Q27.75714,6.96101,27.75714,7.25L27.75714,17.875Q27.75714,18.164,27.9419,18.3706Q28.13102,18.5821,28.4,18.5821L43.6,18.5821Q43.869,18.5821,44.058099999999996,18.3706Q44.2429,18.164,44.2429,17.875L44.2429,5.125Q44.2429,4.83601,44.058099999999996,4.62937Q43.869,4.41786,43.6,4.41786ZM38.5067,4.61786L37.033,6.26601L36.606700000000004,6.74286L28.4,6.74286Q28.22049,6.74286,28.091,6.88768Q27.95714,7.03738,27.95714,7.25L27.95714,17.875Q27.95714,18.087600000000002,28.091,18.237299999999998Q28.22048,18.3821,28.4,18.3821L43.6,18.3821Q43.7795,18.3821,43.909,18.237299999999998Q44.0429,18.087600000000002,44.0429,17.875L44.0429,5.125Q44.0429,4.91238,43.909,4.76268Q43.7795,4.61786,43.6,4.61786L38.5067,4.61786Z'%20fill-rule='evenodd'%20fill='%23FFFFFF'%20fill-opacity='1'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M40.44444,17C40.44444,17,31.555556,17,31.555556,17C31.248264,17,31,16.776563,31,16.5C31,16.223437,31.248264,16,31.555556,16C31.555556,16,40.44444,16,40.44444,16C40.75174,16,41,16.223437,41,16.5C41,16.776563,40.75174,17,40.44444,17Z'%20fill='%233370ff'%20fill-opacity='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})):(s(),i(L,{key:1,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_97_52350'%3e%3crect%20x='24'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20transform='matrix(-1,0,0,1,48,0)'%20clip-path='url(%23master_svg0_97_52350)'%3e%3cg%3e%3cpath%20d='M43.6,4.51786C43.899,4.51786,44.1429,4.7905999999999995,44.1429,5.125C44.1429,5.125,44.1429,17.875,44.1429,17.875C44.1429,18.209400000000002,43.899,18.482100000000003,43.6,18.482100000000003C43.6,18.482100000000003,28.4,18.482100000000003,28.4,18.482100000000003C28.101,18.482100000000003,27.85714,18.209400000000002,27.85714,17.875C27.85714,17.875,27.85714,7.25,27.85714,7.25C27.85714,6.9155999999999995,28.101,6.642860000000001,28.4,6.642860000000001C28.4,6.642860000000001,36.5619,6.642860000000001,36.5619,6.642860000000001C36.5619,6.642860000000001,36.9585,6.19936,36.9585,6.19936C36.9585,6.19936,38.4619,4.51786,38.4619,4.51786C38.4619,4.51786,43.6,4.51786,43.6,4.51786ZM43.6,3C43.6,3,38.294399999999996,3,38.294399999999996,3C38.0421,3,37.8003,3.111467,37.6222,3.310686C37.6222,3.310686,36,5.125,36,5.125C36,5.125,28.4,5.125,28.4,5.125C27.350335,5.125,26.5,6.076029999999999,26.5,7.25C26.5,7.25,26.5,17.875,26.5,17.875C26.5,19.049,27.350335,20,28.4,20C28.4,20,43.6,20,43.6,20C44.649699999999996,20,45.5,19.049,45.5,17.875C45.5,17.875,45.5,5.125,45.5,5.125C45.5,3.951032,44.649699999999996,3,43.6,3Z'%20fill='%233F4A59'%20fill-opacity='1'/%3e%3cpath%20d='M45.6,17.875L45.6,5.125Q45.6,4.2063299999999995,45.0182,3.555608Q44.432,2.9,43.6,2.9000003L38.294399999999996,2.9Q37.8553,2.9,37.5477,3.244032L35.95527,5.025L28.4,5.025Q27.56802,5.025,26.981829,5.68061Q26.4,6.33134,26.4000001,7.25L26.4,17.875Q26.4,18.7937,26.981829,19.4444Q27.56802,20.1,28.4,20.1L43.6,20.1Q44.432,20.1,45.0182,19.4444Q45.6,18.7937,45.6,17.875ZM44.8691,3.688916Q45.4,4.2827,45.4,5.125L45.4,17.875Q45.4,18.7173,44.8691,19.3111Q44.3425,19.9,43.6,19.9L28.4,19.9Q27.65748,19.9,27.130923,19.3111Q26.6,18.7173,26.6,17.875L26.6,7.25Q26.6,6.40771,27.130923,5.8139199999999995Q27.65748,5.225,28.4,5.225L36.04473,5.225L37.696799999999996,3.3773400000000002Q37.9447,3.1,38.294399999999996,3.1L43.6,3.1Q44.3425,3.1,44.8691,3.688916ZM43.6,4.41786L38.4172,4.41786L36.8839,6.1327L36.5172,6.54286L28.4,6.54286Q28.13102,6.54286,27.9419,6.75437Q27.75714,6.96101,27.75714,7.25L27.75714,17.875Q27.75714,18.164,27.9419,18.3706Q28.13102,18.5821,28.4,18.5821L43.6,18.5821Q43.869,18.5821,44.058099999999996,18.3706Q44.2429,18.164,44.2429,17.875L44.2429,5.125Q44.2429,4.83601,44.058099999999996,4.62937Q43.869,4.41786,43.6,4.41786ZM38.5067,4.61786L37.033,6.26601L36.606700000000004,6.74286L28.4,6.74286Q28.22049,6.74286,28.091,6.88768Q27.95714,7.03738,27.95714,7.25L27.95714,17.875Q27.95714,18.087600000000002,28.091,18.237299999999998Q28.22048,18.3821,28.4,18.3821L43.6,18.3821Q43.7795,18.3821,43.909,18.237299999999998Q44.0429,18.087600000000002,44.0429,17.875L44.0429,5.125Q44.0429,4.91238,43.909,4.76268Q43.7795,4.61786,43.6,4.61786L38.5067,4.61786Z'%20fill-rule='evenodd'%20fill='%23FFFFFF'%20fill-opacity='1'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M40.44444,17C40.44444,17,31.555556,17,31.555556,17C31.248264,17,31,16.776563,31,16.5C31,16.223437,31.248264,16,31.555556,16C31.555556,16,40.44444,16,40.44444,16C40.75174,16,41,16.223437,41,16.5C41,16.776563,40.75174,17,40.44444,17Z'%20fill='%233F4A59'%20fill-opacity='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})),r(" 产业360 ")])),_:1},8,["style"]),o(w,{onClick:t[2]||(t[2]=e=>u.changtab(5)),class:"singleTabs",style:A({color:5==h.pitch?"#3370FF":"#86909C"})},{default:n((()=>[5==h.pitch?(s(),i(L,{key:0,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_97_52359'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_97_52359)'%3e%3cg%3e%3cpath%20d='M21.4,19.8094L19.7,19.8094L19.7,2.38903C19.7,1.623078,19.072,1,18.3,1L5.7,1C4.928,1,4.3,1.623078,4.3,2.38903L4.3,19.8094L2.6,19.8094C2.268,19.8094,2,20.0753,2,20.4047C2,20.7341,2.268,21,2.6,21L21.4,21C21.732,21,22,20.7341,22,20.4047C22,20.0753,21.73,19.8094,21.4,19.8094ZM13.4,19.8094L10.6,19.8094L10.6,15.0669C10.6,14.9577,10.69,14.8684,10.8,14.8684L13.2,14.8684C13.31,14.8684,13.4,14.9577,13.4,15.0669L13.4,19.8094ZM14.6,19.8094L14.6,15.0669C14.6,14.3009,13.972,13.6778,13.2,13.6778L10.8,13.6778C10.028,13.6778,9.4,14.3009,9.4,15.0669L9.4,19.8094L5.5,19.8094L5.5,2.38903C5.5,2.27989,5.59,2.1905900000000003,5.7,2.1905900000000003L18.3,2.1905900000000003C18.41,2.1905900000000003,18.5,2.27989,18.5,2.38903L18.5,19.8094L14.6,19.8094Z'%20fill='%233370ff'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M10.27092,4L7.165457,4C6.797237,4,6.5,4.323769,6.5,4.724857C6.5,5.12594,6.797237,5.44971,7.165457,5.44971L10.27092,5.44971C10.639140000000001,5.44971,10.93638,5.12594,10.93638,4.724857C10.93638,4.326185,10.63692,4,10.27092,4ZM10.27092,7.27635L7.165457,7.27635C6.797237,7.27635,6.5,7.60012,6.5,8.00121C6.5,8.4023,6.797237,8.72606,7.165457,8.72606L10.27092,8.72606C10.639140000000001,8.72606,10.93638,8.4023,10.93638,8.00121C10.93638,7.60012,10.63692,7.27635,10.27092,7.27635ZM10.27092,10.55029L7.165457,10.55029C6.797237,10.55029,6.5,10.87406,6.5,11.27514C6.5,11.67623,6.797237,12,7.165457,12L10.27092,12C10.639140000000001,12,10.93638,11.67623,10.93638,11.27514C10.93638,10.87406,10.63692,10.55029,10.27092,10.55029ZM16.8345,4L13.72908,4C13.360859999999999,4,13.06362,4.323769,13.06362,4.724857C13.06362,5.12594,13.360859999999999,5.44971,13.72908,5.44971L16.8345,5.44971C17.2028,5.44971,17.5,5.12594,17.5,4.724857C17.5,4.326185,17.2028,4,16.8345,4ZM16.8345,7.27635L13.72908,7.27635C13.360859999999999,7.27635,13.06362,7.60012,13.06362,8.00121C13.06362,8.4023,13.360859999999999,8.72606,13.72908,8.72606L16.8345,8.72606C17.2028,8.72606,17.5,8.4023,17.5,8.00121C17.5,7.60012,17.2028,7.27635,16.8345,7.27635ZM16.8345,10.55029L13.72908,10.55029C13.360859999999999,10.55029,13.06362,10.87406,13.06362,11.27514C13.06362,11.67623,13.360859999999999,12,13.72908,12L16.8345,12C17.2028,12,17.5,11.67623,17.5,11.27514C17.5,10.87406,17.2028,10.55029,16.8345,10.55029Z'%20fill='%233370ff'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})):(s(),i(L,{key:1,class:"my",src:"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_97_52359'%3e%3crect%20x='0'%20y='0'%20width='24'%20height='24'%20rx='0'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_97_52359)'%3e%3cg%3e%3cpath%20d='M21.4,19.8094L19.7,19.8094L19.7,2.38903C19.7,1.623078,19.072,1,18.3,1L5.7,1C4.928,1,4.3,1.623078,4.3,2.38903L4.3,19.8094L2.6,19.8094C2.268,19.8094,2,20.0753,2,20.4047C2,20.7341,2.268,21,2.6,21L21.4,21C21.732,21,22,20.7341,22,20.4047C22,20.0753,21.73,19.8094,21.4,19.8094ZM13.4,19.8094L10.6,19.8094L10.6,15.0669C10.6,14.9577,10.69,14.8684,10.8,14.8684L13.2,14.8684C13.31,14.8684,13.4,14.9577,13.4,15.0669L13.4,19.8094ZM14.6,19.8094L14.6,15.0669C14.6,14.3009,13.972,13.6778,13.2,13.6778L10.8,13.6778C10.028,13.6778,9.4,14.3009,9.4,15.0669L9.4,19.8094L5.5,19.8094L5.5,2.38903C5.5,2.27989,5.59,2.1905900000000003,5.7,2.1905900000000003L18.3,2.1905900000000003C18.41,2.1905900000000003,18.5,2.27989,18.5,2.38903L18.5,19.8094L14.6,19.8094Z'%20fill='%233F4A59'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3cg%3e%3cpath%20d='M10.27092,4L7.165457,4C6.797237,4,6.5,4.323769,6.5,4.724857C6.5,5.12594,6.797237,5.44971,7.165457,5.44971L10.27092,5.44971C10.639140000000001,5.44971,10.93638,5.12594,10.93638,4.724857C10.93638,4.326185,10.63692,4,10.27092,4ZM10.27092,7.27635L7.165457,7.27635C6.797237,7.27635,6.5,7.60012,6.5,8.00121C6.5,8.4023,6.797237,8.72606,7.165457,8.72606L10.27092,8.72606C10.639140000000001,8.72606,10.93638,8.4023,10.93638,8.00121C10.93638,7.60012,10.63692,7.27635,10.27092,7.27635ZM10.27092,10.55029L7.165457,10.55029C6.797237,10.55029,6.5,10.87406,6.5,11.27514C6.5,11.67623,6.797237,12,7.165457,12L10.27092,12C10.639140000000001,12,10.93638,11.67623,10.93638,11.27514C10.93638,10.87406,10.63692,10.55029,10.27092,10.55029ZM16.8345,4L13.72908,4C13.360859999999999,4,13.06362,4.323769,13.06362,4.724857C13.06362,5.12594,13.360859999999999,5.44971,13.72908,5.44971L16.8345,5.44971C17.2028,5.44971,17.5,5.12594,17.5,4.724857C17.5,4.326185,17.2028,4,16.8345,4ZM16.8345,7.27635L13.72908,7.27635C13.360859999999999,7.27635,13.06362,7.60012,13.06362,8.00121C13.06362,8.4023,13.360859999999999,8.72606,13.72908,8.72606L16.8345,8.72606C17.2028,8.72606,17.5,8.4023,17.5,8.00121C17.5,7.60012,17.2028,7.27635,16.8345,7.27635ZM16.8345,10.55029L13.72908,10.55029C13.360859999999999,10.55029,13.06362,10.87406,13.06362,11.27514C13.06362,11.67623,13.360859999999999,12,13.72908,12L16.8345,12C17.2028,12,17.5,11.67623,17.5,11.27514C17.5,10.87406,17.2028,10.55029,16.8345,10.55029Z'%20fill='%233F4A59'%20fill-opacity='1'%20style='mix-blend-mode:passthrough'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"})),r(" 园区360 ")])),_:1},8,["style"]),o(w,{onClick:t[3]||(t[3]=e=>u.changtab(2)),class:"singleTabs",style:A({color:2==h.pitch?"#3370FF":"#86909C"})},{default:n((()=>[2==h.pitch?(s(),i(L,{key:0,class:"my",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAA1VJREFUaEPtmU9IFHEUxz9PTD0ILeWhiCCpUDCodo2KPHTsUFCUWJBQUFRizEhB3eoWUdFu/4gIKiISCgoK6hIJXQycTcXIg0FQUAehoD8strsvZ9ZIdHdnd2caM3auM7/3e5/33u833/f7CbP8kVnuP2WAmc5gOQOzOgNNHVpbXcURoEWg0k8YhaQofSnhdH9UvuSyXXIJLdmjNfNCvBBo9tPxqbYURtKwJhdEyQBhQw+IcBVIKlwCckapRMCQKAcRalCOWzE5nc1OyQARU28Ae4D7VlRaS3Qy77CwobdF2A08tKKy7W8B3LSisvdvAEwKUhlgWoAnRaecgVzlVy6hfAvzvyyh1YZuF2FFPnAR3lnn5VYhu5bnEmo2tCEtNItkkQlp9iG0AM4itmVFTRVfC3FMx1gevyIjbt96AgibekbgqNskvwHs7yKGnkJYl3eMMmzF5FABdikZIGxomwjdE5OMAt+maRSlToTayQCFOFXMNyUDREy9B+xQ5Wl8gC30SHLqxP/0Io6Y+hzYKBDti0pXtqhlAwgb+kSETcVEOYv67Iv3s94OmpcMFA0wIa8/jkOHvACMK89ESlhoy+dAAZxF3KmNMocGLwBjKd4OXpAhx94fxVucmCu1hLw47lKmwQA0m7pBxVuHlk4x9OqCPAs8A/YamD+Xz04H5e1J8oOF1jUZDbyEwobeFbcfmQucKr3xAdoD34W8BT376MAz4DdE4AB2CblqIRdKgX6rn9bAS8jPRZwYY/HrK/Ip8AxEDF1LBY2eSintqNWXgW+jnpzOMTjwDPgNUQYoVk7/Oxkw9BHC5nzdVsTUB8BW4LoVlf1+O2/bm/iz71SlOx6TXdnmyHq4Gzb1xPiLkwpf0lA/9Wh7ZZcuqkwzYusehc54VC77DeBsyyHeA3UoR62YnCscoEOXSRVvyFxa9PxM0T54UT44W9thXUoFjxEa7eYj8ZN6e8/2E2CVqaFK5a7a3Z3LHDmP1yOGHkE46zimJFQYmriFsc99MrcxyjBCr5/OAyHNtLOZzk4wrfMSyzVH3vuBiKHHxgee9EEiF8+oJKjgeD7nM3wuT1OHLqippg11Ttx8vQfLMXUSYYjv3LF7Ajf/XAHcDMz0+zJAOQMeIzDrS+gXq6xJT4NVJL4AAAAASUVORK5CYII="})):(s(),i(L,{key:1,class:"my",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAA4dJREFUaEPtmUFoE1EQhv9JUmlLDz30YBEPRaU9eKxobZvsSZtthYqG7mILCooKHpQW7E1vUlRQUPAgWBG7kYoWKpsqiJvUQ4UePBTsoYKgEA8FBSUJssnIpqlKm+wmu+m2keSa9+b938y8l5kJocI/VOH6UQXY7AhWI1DRERCEUEOm3jtMoC4wfGWFIegMzHsSyTFNm/peyLbtFBIEoZbrm2cBtJdV+FpjzEuUTO0rBGEbwC/KZwm4B7DOTHdAXNBLtgCZGgk4B0ItM4/GIuGxfHZsAwRE6QFAJ8F4Go0oIVsiLTYFRPkRgEEAU1FVOboxAKDxqDpxamMAck6qAuRx758UqkagcPL9dVL1Dqz30n+ZQt2ifIzAe01fpAx9is0oD4t5tRynUGfPYKuXMu3w8LoygTI4DUIXcpfYKCu43vejGGGU1vdoLyeXrNY6AvAHpetENGJ1yCqAsc4vyteIccBsDxMvxtTweWu7gG0Af1AaIKJw7pBlAD/XHchoAqHhX4BiRJWyxjZAoFeaBNNxADOUiB/RNE1fe/CWvsSBoPwGBIEZt2IR5VI+r+UDCIhyBEBPKV7Os3aeEvEOw2n2I2ADIFdexwE0OgJgpCiZbDbKZ1cBDNHCIbkt7aNWJwBePf1Re/VkwbDhOoAT4eZpWmIpYfcOdAdPdBKxow6NM5mF2Znwa9cjkL0Ddc3fjA7KUSSYdfJRszatLLueQoGgrIDMf8gs4RhzlIwPuf4KWQqzscD1CNjQaLrFdYBAr6zAohYqAvI9JeIh11OobJcYrBPSOzV18qvrEegSpf0eorYivFxwSYZ58a0afuf6M+pEdKG9rkeg3BBVgFLL6a0TAVGaBqgP4PGoGs47NgyI8nMA/QDuR1XlTLnFZy/xyi+7BEY4GlHkfGfkHe76RekKga4C+E6JZMva0XZHX2jHtrRvKTs5Bl+IqeG75QbI9RefATQxYyQWUW4WDSAcDu1mr/cDQD4wa4RfQ1rk2RfDwMHgwK4a8rwA0Aaj+SC9xXizywkgCP2NXF+nZLs7izMKjtf9QXmYCDeywhgpEIwmw4fs3IdWxyyLAM2VUzzARkcnrHZ2DLoYUyduFzrD9P8Bf1C6nE0lpyWyHUJGiolGzcQbZi3/4BDE0PYMagZWJm7rB1x2tJnvIZ1BC54EP9Y0xRjpmH4sAawMbPb3VYBqBBx6oOJT6Dep9ItPTG7MjgAAAABJRU5ErkJggg=="})),r(" 出海智策 ")])),_:1},8,["style"]),o(w,{onClick:t[4]||(t[4]=e=>u.changtab(3)),class:"singleTabs",style:A({color:3==h.pitch?"#3370FF":"#86909C"})},{default:n((()=>[3==h.pitch?(s(),i(L,{key:0,class:"my",src:ae})):(s(),i(L,{key:1,class:"my",src:se})),r(" 出海智服 ")])),_:1},8,["style"])])),_:1})):l("",!0)])),_:1})}],["__scopeId","data-v-7e83a2ae"]]);export{he as default};
