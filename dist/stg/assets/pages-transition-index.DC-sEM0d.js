import{l as t,j as e,s,m as i,a,f as o,n as c,B as n,C as d,o as r}from"./index-CBCsGYoT.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=h({data:()=>({dotsStyles:{bottom:48,backgroundColor:"#9fbff8",border:"#9fbff8",selectedBackgroundColor:"#6891ec",selectedBorder:"#6891ec"},bottomHeight:0,openid:t("openid"),invitationCode:"",indicatorDots:!0,autoplay:!1,interval:2e3,duration:500,current:0,urlList:["https://static.idicc.cn/cdn/aiChat/guideImg1s.png","https://static.idicc.cn/cdn/aiChat/guideImg2s.png","https://static.idicc.cn/cdn/aiChat/guideImg3s.png","https://static.idicc.cn/cdn/aiChat/guideImg4ss.png"],systemInfo:{},show:!1,teamCode:""}),onLoad(t){this.systemInfo=e(),t.teamCode&&(this.teamCode=t.teamCode)},methods:{change(t){let e=0;t.detail.current==this.urlList.length-1&&(e=200),this.current=t.detail.current,setTimeout((()=>{t.detail.current==this.urlList.length-1?this.show=!0:this.show=!1}),e)},go(){s("newUser",!1),this.teamCode?i({url:`/pages/repository/capacity?shade="yes"&scene=${this.teamCode}`}):i({url:'/pages/repository/capacity?shade="yes"'})}}},[["render",function(t,e,s,i,h,g){const l=d;return r(),a("div",{style:c({height:h.systemInfo.screenHeight+"px"}),class:"box"},[o(l,{style:c({height:h.systemInfo.screenHeight+"px"}),class:"bgc",src:"https://static.idicc.cn/cdn/aiChat/guideImg0s.png"},null,8,["style"]),n("div",{onClick:e[0]||(e[0]=(...t)=>g.go&&g.go(...t)),class:"skipButton"},"立即体验")],4)}],["__scopeId","data-v-bc51b06f"]]);export{g as default};
