import{l as e,p as s,z as t,A as i,a,f as n,B as r,D as o,g as l,t as c,w as h,F as p,b as d,d as u,C as y,o as g,e as m,i as A}from"./index-HcMwrp5e.js";import{_ as L}from"./page-meta.X-Lr6csD.js";import{r as I}from"./uni-app.es.DFp0WTX7.js";import{_ as w}from"./uni-easyinput.CAhWtyu2.js";import{_ as k}from"./uni-transition.CHOJlBbg.js";import{e as f}from"./enterpriseList.DZqkps3N.js";import{t as T}from"./index.B6Rbewql.js";import{_ as b}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.B5Z3RbO2.js";import"./uni-tooltip.Bpz4R-Ep.js";import"./utils.61Hi-B7M.js";import"./returnFn.BYkANsDr.js";const C=b({components:{enterpriseList:f,tabBar:T},data:()=>({statusBarHeight:0,keyword:"",time:null,searchResultList:[],calling:!1,chainId:"",HistoryList:[],screenType:1,showPop:!1}),created(){this.statusBarHeight=e("statusBarHeight")},onLoad(e){this.chainId=e.showChain,this.getSearchHistoryList()},watch:{searchResultList(e){this.searchResultList.forEach((e=>{let s=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let t=e.enterpriseLabelNames[0];t?s.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:t.includes("独角兽")?e.enterpriseIconLabelId=2:t.includes("专精特新")?e.enterpriseIconLabelId=3:t.includes("隐形冠军")?e.enterpriseIconLabelId=4:t.includes("瞪羚")?e.enterpriseIconLabelId=5:t.includes("创新")?e.enterpriseIconLabelId=6:t.includes("技术先进")?e.enterpriseIconLabelId=7:t.includes("科技")?e.enterpriseIconLabelId=8:t.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}},methods:{uncheckPop(){this.showPop=!1},changeTypeFn(){this.showPop=!this.showPop},changeListType(e){this.screenType!=e&&(this.screenType=e,this.keyword="",this.takeEffect=!1,this.getSearchHistoryList(),this.searchResultList=[])},getSearchHistoryList(){this.$api.getSearchHistory({data:{type:this.screenType}}).then((e=>{this.HistoryList=e.result}))},delHistoryList(){if(0==this.HistoryList.length)return s({title:"暂无历史记录",icon:"none"});this.$api.deleteSearchHistory({data:{type:this.screenType},method:"POST"}).then((e=>{this.getSearchHistoryList(),setTimeout((()=>{s({title:"已清空历史记录",icon:"none"})}),1e3)}))},goBack(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:"",path:"goBack"}))},automaticSearch(e){this.keyword=e,this.getList(e)},searchFn(e){clearTimeout(this.time),e?this.getList(e):this.searchResultList=[]},changekeyword(e){e||this.getSearchHistoryList(),this.keyword=e,this.calling=!0,null!=this.time&&clearTimeout(this.time),this.time=setTimeout((async()=>{e?this.getList(e):this.searchResultList=[]}),3e3)},getList(e){let s={pageNum:1,pageSize:20,chainId:this.chainId};1==this.screenType?s.keyword=e:s.productName=e,this.$api.pageByChainNode({data:s,method:"post"}).then((e=>{this.searchResultList=e.result.records})).finally((()=>{this.calling=!1}))}}},[["render",function(e,s,f,T,b,C){const B=I(t("page-meta"),L),H=i("tabBar"),j=I(t("uni-easyinput"),w),v=A,S=I(t("uni-transition"),k),F=y,R=i("enterpriseList");return g(),a(p,null,[n(B,{"page-style":"background-color: #FFFFFF"}),n(H,{title:"产业360"}),r("div",{onClick:s[5]||(s[5]=e=>C.uncheckPop()),class:"box"},[r("div",{class:"searchTop"},[r("div",{class:"seinput"},[n(j,{focus:!0,trim:"all",modelValue:b.keyword,"onUpdate:modelValue":s[0]||(s[0]=e=>b.keyword=e),onConfirm:C.searchFn,onInput:C.changekeyword,"adjust-position":!1,placeholder:"请输入关键词",confirmType:"search",placeholderStyle:"font-size:26rpx"},null,8,["modelValue","onConfirm","onInput"]),r("div",{onClick:s[3]||(s[3]=o((e=>C.changeTypeFn()),["stop"])),class:"changeType"},[l(c(1==b.screenType?"按企业":"按产品")+" ",1),r("div",{class:"InvertedTriangle"}),r("div",{class:"longString"}),n(S,{style:{"z-index":"99"},ref:"ani",show:b.showPop},{default:h((()=>[n(v,{class:"userPop"},{default:h((()=>[n(v,{onClick:s[1]||(s[1]=e=>C.changeListType(1)),class:m(1==b.screenType?"popItems":" popItem")},{default:h((()=>[l(" 按企业 ")])),_:1},8,["class"]),n(v,{onClick:s[2]||(s[2]=e=>C.changeListType(2)),class:m(2==b.screenType?"popItems":" popItem")},{default:h((()=>[l(" 按产品 ")])),_:1},8,["class"])])),_:1})])),_:1},8,["show"])]),r("div",{onClick:s[4]||(s[4]=e=>C.getList(b.keyword)),class:"searchBtn"},"搜索")])]),0!=b.searchResultList.length||b.keyword?u("",!0):(g(),a("div",{key:0,class:"searchHistory"},[r("div",{class:"HistoryTitle"},[r("div",{class:"HistoryTitleText"},"搜索历史"),n(F,{onClick:C.delHistoryList,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgBAMAAACBVGfHAAAAG1BMVEUAAACGj5yFkJuGkJyFj5uAjJmAjJmIj5yGkJwoW6//AAAACHRSTlMAy/Wy6ygUS64l1CIAAABWSURBVCjPYwCBkg4wcGeAAQ+IQAtcoANKkSsg0YECGjEFEFohTBoKMAqAMUKAowGEB14A4jBq+lYjAcZnawJTFmYwgeRmMBWEiARVsACrEYyvHMDAAABZtUdTAlNznwAAAABJRU5ErkJggg==",class:"HistoryTitleImg"},null,8,["onClick"])]),r("div",{class:"HistoryBox"},[(g(!0),a(p,null,d(b.HistoryList,((e,s)=>(g(),a("div",{class:"HistoryItem",onClick:s=>C.automaticSearch(e),key:s},c(e),9,["onClick"])))),128))])])),r("div",{class:"searchResult"},[n(R,{showChain:b.chainId,Jurisdiction:!0,enterpriseList:b.searchResultList},null,8,["showChain","enterpriseList"])]),0==b.searchResultList.length&&b.keyword&&!b.calling?(g(),a("div",{key:1,class:"nodatabox"},[n(F,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])):u("",!0)])],64)}],["__scopeId","data-v-9fbdf14f"]]);export{C as default};
