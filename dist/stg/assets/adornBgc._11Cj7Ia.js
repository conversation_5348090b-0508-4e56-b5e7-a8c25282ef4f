import{p as t,z as e,o as i,c as s,w as o,f as l,r as a,D as n,n as r,a as c,g as h,t as d,F as u,d as y,b as f,B as p,e as v,i as m,h as g,C as x,S as w}from"./index-HcMwrp5e.js";import{_ as k}from"./uni-transition.CHOJlBbg.js";import{r as b}from"./uni-app.es.DFp0WTX7.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";const A=C({name:"FyTreeSelect",props:{isCanyan:{type:Boolean,default:!0},noLimit:{type:Boolean,default:!1},initialValue:{type:Array,default:()=>[]},value:{type:Array,default:()=>[]},columns:{type:Number,default:2},closeOnClickMask:{type:<PERSON>olean,default:!0},showBar:{type:Boolean,default:!0},customBarStyle:{type:Object,default:()=>({})},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确定"},cancelColor:{type:String,default:"#666666"},confirmColor:{type:String,default:"#3370FF"},options:{type:Array,default:()=>[]},height:{type:Number,default:400},itemHeight:{type:Number,default:43},duration:{type:Number,default:400},children:{type:String,default:"children"},label:{type:String,default:"label"},primary:{type:String,default:"id"},optionsKey:{type:String,default:"value"},selectTile:{type:String,default:"请选择"},customChild:{type:Boolean,default:!1},fontSize:{type:Number,default:30},color:{type:String,default:"rgb(158, 160, 162)"},activeColor:{type:String,default:"#3370FF"}},data:()=>({maskClass:{position:"fixed",bottom:0,top:0,left:0,right:0,backgroundColor:"rgba(0, 0, 0, 0.6)","z-index":"999999"},transClass:{position:"fixed",left:0,right:0,bottom:0,"z-index":"999999"},pitchKnowId:"",vipList:[],oneEcho:"",twoEcho:"",visible:!1,treeList:[],treePosition:[],currentSelectedId:[],initialData:[],pitchData:{}}),watch:{visible:{immediate:!0,handler:function(t){t&&(this.currentSelectedId=this.initialValue,this.initialData=JSON.parse(JSON.stringify(this.initialValue))),this.oneEcho=this.value[0],this.twoEcho=this.value[1],this.pitchKnowId=this.value[1],this.options.forEach((t=>{t.categoryId==this.oneEcho&&(this.treeList[1]=t.children)})),t&&0===this.treeList.length&&this.updateData(this.options)}}},created(){this.getViP(),this.init()},methods:{changePitchKnowId(t){this.oneEcho=t.categoryId,this.twoEcho=t.knowId,this.pitchKnowId=t.knowId,this.options.forEach((t=>{t.categoryId==this.oneEcho&&(this.treeList[1]=t.children)})),this.pitchData=t},getViP(){this.$api.getVipList().then((t=>{this.vipList=Object.values(t.result.investStatus)}))},echo(t,e){this.treeList=[t,e]},init(){try{const t=this.columns;let e=0;for(;e<t;)e===t-1?this.$set(this.treePosition,e,{index:-1}):this.$set(this.treePosition,e,{index:0}),e+=1}catch(t){console.log(t)}},updateData(t){try{const e=[];let i=this.toJSON(t);if(0===i.length)return this.treeList=[];for(let t=0;t<this.columns;t++)0===t?e[0]=i:this.treePosition[t-1]&&(e[t]=this.getColumnData({data:e[t-1],index:this.treePosition[t-1].index}));this.treeList=this.toJSON(e),Array.isArray(this.value)&&this.value.length>0&&this.setDefaultData()}catch(e){console.log(e)}},setDefaultData(){try{const{treeList:t,value:e,columns:i,optionsKey:s}=this;this.oneEcho=e[0],this.twoEcho=e[1];for(let o=0;o<i;o++){const i=t[o];if(!i||!Array.isArray(i)||0===i.length)break;const l=i.length;if(0!==e[o]&&!e[o])break;for(let t=0;t<l;t++){if(i[t][s]==e[o]){this.$set(this.treePosition,o,{index:t});break}}}}catch(t){console.log(t)}},onChange(e,i,s){var o,l,a,n,r,c,h,d,u,y;if("-1"==s.categoryBuyStatus&&this.noLimit)t({title:"敬请期待",icon:"none",duration:2e3});else try{const f=this.treeList[e],p=f[this.treePosition[e].index];this.treeList.length;this.setPosAttr(e,"index",i);const v=f[i],m={current:this.toJSON(v),prev:this.toJSON(p),column:e,index:i},g=this.getColumnSelectData()||{};if(this.$emit("change",Object.assign({},m,g)),this.currentSelectedId[0]=null==(l=null==(o=g.selected)?void 0:o[0])?void 0:l.value,s.categoryId&&(this.oneEcho=s.categoryId),1===e)if("1"===(null==(n=null==(a=g.selected)?void 0:a[1])?void 0:n.categoryBuyStatus)||this.noLimit){if(this.noLimit&&"-1"===(null==(c=null==(r=g.selected)?void 0:r[1])?void 0:c.categoryBuyStatus))return void t({title:"敬请期待",icon:"none",duration:2e3});this.currentSelectedId[1]=1===e?null==(d=null==(h=g.selected)?void 0:h[1])?void 0:d.value:this.currentSelectedId[1],this.twoEcho=s.knowId,this.pitchKnowId=s.knowId,this.pitchData={}}else{if("0"!==(null==(y=null==(u=g.selected)?void 0:u[1])?void 0:y.categoryBuyStatus))return void t({title:"敬请期待",icon:"none",duration:2e3});t({title:"该产业链未开通会员，请在我的-会员中心中开通",icon:"none",duration:2e3})}if(!0===this.customChild)return;for(let t=e+1;t<this.columns;t++)this.resetColumn(t,v,g),this.$set(this.treeList,t,this.getColumnData({data:this.treeList[t-1],index:this.treePosition[t-1].index}))}catch(f){console.log(f)}},getColumnSelectData(){try{const t=this.treeList,e=t.length,i=this.treePosition,s=[];let o=0;for(;o<e;){if(i[o].index>-1){const e=t[o][i[o].index];s.push(e)}else s.push(null);o+=1}return{selected:JSON.parse(JSON.stringify(s))}}catch(t){console.log(t)}},getColumnData({data:t,index:e}){try{return t[e]&&t[e][this.children]||[]}catch(i){console.log(i)}},resetColumn(t,e,i){this.$set(this.treePosition,t,{index:t==this.columns-1?-1:0})},toJSON:t=>"object"==typeof t?JSON.parse(JSON.stringify(t)):{},setPosAttr(t,e,i){try{this.$set(this.treePosition[t],e,i)}catch(s){console.log(s)}},handlerConfirm(){if(this.pitchKnowId&&this.pitchData.knowId)return this.$emit("confirm",this.pitchData,!0,this.oneEcho,this.twoEcho),void this.handlerCloseModal();try{const t=this.treeList,e=this.columns,i=this.treePosition,s=[];let o=0,l=[],a=[];for(;o<e;){const e=i[o],n=t[o][e.index]||{};l.push(n[this.optionsKey]),a.push(n[this.primary]),s.push(n||null),o+=1}this.$emit("confirm",{primary:a,values:l,selected:this.toJSON(s)}),this.$emit("input",l),this.handlerCloseModal()}catch(t){console.log(t)}},handlerMaskClick(){this.closeOnClickMask&&this.handlerCloseModal()},handlerCloseModal(){this.visible=!1,this.currentSelectedId=this.initialData,this.$emit("close",{visible:!1})},handlerOpenModal(){this.visible=!0,this.$emit("open",{visible:!0})}}},[["render",function(t,C,A,S,P,_){const I=m,L=b(e("uni-transition"),k),N=g,E=x,O=w;return i(),s(I,{class:"fy-tree-select",onTouchmove:n((()=>{}),["stop","prevent"])},{default:o((()=>[l(I,{onClick:n(_.handlerOpenModal,["stop","prevent"])},{default:o((()=>[a(t.$slots,"default",{},void 0,!0)])),_:3},8,["onClick"]),l(L,{"mode-class":["fade"],styles:P.maskClass,duration:A.duration,show:P.visible,onClick:_.handlerMaskClick},null,8,["styles","duration","show","onClick"]),l(L,{"mode-class":["slide-bottom"],styles:P.transClass,duration:A.duration,show:P.visible},{default:o((()=>[l(I,{class:"fy-tree-select_wrapper",onTouchmove:n((()=>{}),["stop","prevent"])},{default:o((()=>[A.showBar?(i(),s(I,{key:0,class:"fy-tree-select_bar",style:r(A.customBarStyle)},{default:o((()=>[t.$slots.bar?a(t.$slots,"bar",{key:0},void 0,!0):(i(),c(u,{key:1},[l(N,{class:"fy-tree-select_bar__btn",style:r({color:A.cancelColor}),onClick:n(_.handlerCloseModal,["stop","prevent"])},{default:o((()=>[h(d(A.cancelText),1)])),_:1},8,["style","onClick"]),l(N,{class:"fy-tree-select_bar__title"},{default:o((()=>[h(d(A.selectTile),1)])),_:1}),l(N,{class:"fy-tree-select_bar__btn",style:r({color:A.confirmColor}),onClick:n(_.handlerConfirm,["stop","prevent"])},{default:o((()=>[h(d(A.confirmText),1)])),_:1},8,["style","onClick"])],64))])),_:3},8,["style"])):y("",!0),0!=P.vipList.length&&A.isCanyan?(i(),c("div",{key:1,class:"headText"}," 会员开通情况 ")):y("",!0),0!=P.vipList.length&&A.isCanyan?(i(),s(O,{key:2,"scroll-x":"true","enable-flex":!0,class:"vipListBox"},{default:o((()=>[(i(!0),c(u,null,f(P.vipList,((t,e)=>(i(),s(I,{onClick:e=>_.changePitchKnowId(t),class:"vipListItem",key:e},{default:o((()=>[l(E,{src:P.pitchKnowId==t.knowId?"https://static.idicc.cn/cdn/aiChat/applet/newHome/chainIdsBox.png":"https://static.idicc.cn/cdn/aiChat/applet/newHome/chainIdBox.png"},null,8,["src"]),p("span",{class:v(P.pitchKnowId==t.knowId?"knowledgeNames":"knowledgeName")},d(t.knowledgeName),3)])),_:2},1032,["onClick"])))),128))])),_:1})):y("",!0),l(I,{class:"fy-tree-select_container",style:r({height:`${A.height}px`})},{default:o((()=>[(i(!0),c(u,null,f(P.treeList,((t,e)=>(i(),s(I,{key:e,class:v("treeItems"+e)},{default:o((()=>[l(O,{"scroll-y":!0,ref_for:!0,ref:`fy-tree-select_column_${e}`,class:v(["fy-tree-select_column",`fy-tree-select_column_${e}`]),style:r({height:`${A.height}px`})},{default:o((()=>[(i(!0),c(u,null,f(t,((t,a)=>(i(),c(u,{key:a},[P.treePosition[e]?(i(),s(I,{class:v(["fy-tree-select_item",t.categoryId==P.oneEcho&&0===e?"actived":""]),key:a,onClick:n((i=>_.onChange(e,a,t)),["stop","prevent"])},{default:o((()=>[l(N,{style:r({"line-height":`${A.itemHeight}px`,"font-size":`${A.fontSize}rpx`,color:P.twoEcho===t.knowId?A.activeColor:"-1"===t.categoryBuyStatus?A.color:"black"}),class:"fy-tree-select_item_text"},{default:o((()=>[h(d(t[A.label]),1)])),_:2},1032,["style"]),(null==t?void 0:t.buyVersion)&&!A.noLimit?(i(),s(N,{key:0,class:"buyVersion"},{default:o((()=>[h(d(null==t?void 0:t.buyVersion),1)])),_:2},1024)):y("",!0),"臻享版"==(null==t?void 0:t.buyVersion)&&A.noLimit?(i(),s(N,{key:1,class:"buyVersion"},{default:o((()=>[h(" 臻享版")])),_:1})):y("",!0)])),_:2},1032,["class","onClick"])):y("",!0)],64)))),128)),l(I,{class:"fy-tree-select_item",style:r({height:`${A.itemHeight}px`,"border-top-width":"0px"})},null,8,["style"])])),_:2},1032,["class","style"])])),_:2},1032,["class"])))),128))])),_:1},8,["style"])])),_:3})])),_:3},8,["styles","duration","show"])])),_:3})}],["__scopeId","data-v-b2015858"]]),S="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEwAAAAkCAMAAAAU7iwuAAABXFBMVEUAAAB1wP+dzv+ly/+Wwv+xzP+Xx/+Nxf6ix/+bx/+0zP99w/99wv+wy/94wv+0y/+zzP+Uxv+KxP+BxP+Sx/+wy/94w/+tyv99wv+BxP+ryv+dx/+2zP+Yx/+ryv+Nxf+1zP+Sxf+gyP+Gxf+Kxf+ryf+nyf+Wx/+dyP91wf+2zP+hyP+Vxv9+w/94wf+GxP+Qx/+fyf+myf+Lxf+jyf+Qxf+uy/6Vxv+ax/+Kxf+Cw/62zP+nyf+lyv+Mxf+Exf+zzP+jyf+qyf+qyv+kyf+Gw/+Gw/+Tx/93v/+iyv+0zv+myP+Txv+HxP+ryv+lyf+Mxf9+wv+Cw/+Aw/93wf+yy/+oyf+fyP+ax/+tyv+iyP+Vxv+Oxf+KxP98wv+vyv97wv+Xxv+Rxf+dx/+Yx/+Fw/+wy/95wf+hyP+cx/+Ew/+nyf+JxP92wf+Qxf+0y/+qyf+kyP+1y/+eyP/k1QndAAAATHRSTlMAnwQhDH42LxOyP/fu6+a/rqWJeWJcT0xCH/vr5uPg29LKyb2thYJ2dXBvbGdmXFZDKvr6+fj49vLw3tzZ0s7Nx724t7eempWQfEQXUrzkmQAAAjJJREFUSMft1GdTGkEYB/D1TpogoUjT2Hvs6b33yB3tOEUEpUYFUfz+M9llH/ayWW4I4Di+8PkAv/k/ZRfd1Y3WcMQWDgeDm5sbq6v+YD+C3brl/7ow/+6lK5GMx3O5o6NqKlWv14d6y2Fd9y28fVGrRaPl8mkikUwaWKoHzL697J0uZLOxWOzwsBbF2inWAMPa/2KSddkxebC7WyicG1iZw6oU6wqtOJ6XSscHGKMaYJijGNW6Y/KaY+LkZG+vdNwBK/+FdU2282NO16+uigQjyfg+IVmC24AJZl16k8n81nW9WGwnE4bGY1UTTF58rapqpqXRZKzP80595ojWEZNCn8bTZ6p6eYkxmowNrdAZi5tgsmVKUdLpM6ypkAyiEQxzFKOacLYcNuJ5uq8oFwTDyRgmrlMYmoCN3Mvn9zFGNEhG++SGljX6FM52yKC0CsUuKIaTZcwwcQOAAdXQtApoCk3W7pOcrfgGauIbAMzTbBIsD9EYRpOZDw024HQ6XS632/29hVlamMaSYQ0woU8420fT8198/o2tsC0y/O8/db/ZaBgYDE3YAEn2ZNbrG9uOSMi8HtA+Kxhr98kujZ3ts9lvYzYzhY9GkwEmvIFJbxeHjwbrpBiNBkObcKzv9Pa7v4INAMaGNrdiQz1XgA1NYZf2+MOajPopaaahcW/g4efAL9RvBbg3MLUko0FqhmHjH0MSGqxCMLTRRRkNXu/J0EYtdnQd9VPTGDV4eTB1q+oPEYKK5jUQp1AAAAAASUVORK5CYII=";export{S as _,A as t};
