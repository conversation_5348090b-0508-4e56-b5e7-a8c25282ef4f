import{o as t,c as e,w as s,f as o,r as n,a as i,F as a,b as r,n as l,D as h,g as c,t as u,h as d,i as p,l as f,U as g,s as m,z as w,B as y,e as b,C as S,S as A}from"./index-HcMwrp5e.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{r as x}from"./uni-app.es.DFp0WTX7.js";import{_ as X}from"./uni-load-more.Dz9gLz3r.js";import{_ as C}from"./returnFn.BYkANsDr.js";let W={},k=null;k=function(){var t=navigator.userAgent,e=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"],s=!0;for(let o=0;o<e.length-1;o++)if(t.indexOf(e[o])>0){s=!1;break}return s}(),W={data:()=>({is_show:"none"}),watch:{show(t){this.is_show=this.show}},created(){this.swipeaction=this.getSwipeAction(),this.swipeaction&&Array.isArray(this.swipeaction.children)&&this.swipeaction.children.push(this)},mounted(){this.is_show=this.show},methods:{closeSwipe(t){this.autoClose&&this.swipeaction&&this.swipeaction.closeOther(this)},change(t){this.$emit("change",t.open),this.is_show!==t.open&&(this.is_show=t.open)},appTouchStart(t){if(k)return;const{clientX:e}=t.changedTouches[0];this.clientX=e,this.timestamp=(new Date).getTime()},appTouchEnd(t,e,s,o){if(k)return;const{clientX:n}=t.changedTouches[0];let i=Math.abs(this.clientX-n),a=(new Date).getTime()-this.timestamp;i<40&&a<300&&this.$emit("click",{content:s,index:e,position:o})},onClickForPC(t,e,s){k&&this.$emit("click",{content:e,index:t,position:s})}}};var T=!1;function Y(t,e){var s=t.instance,o=s.getDataset().disabled,n=s.getState();L(s,e),(o=("string"==typeof o?JSON.parse(o):o)||!1)||(s.requestAnimationFrame((function(){s.removeClass("ani"),e.callMethod("closeSwipe")})),n.x=n.left||0,function(t){var e=t.instance,s=e.getState();N(e);var o=t.touches[0];T&&P()&&(o=t);s.startX=o.clientX,s.startY=o.clientY}(t))}function D(t,e){var s=t.instance,o=s.getDataset().disabled,n=s.getState();(o=("string"==typeof o?JSON.parse(o):o)||!1)||(!function(t){var e=t.instance.getState(),s=t.touches[0];T&&P()&&(s=t);e.deltaX=s.clientX-e.startX,e.deltaY=s.clientY-e.startY,e.offsetY=Math.abs(e.deltaY),e.offsetX=Math.abs(e.deltaX),e.direction=e.direction||function(t,e){if(t>e&&t>10)return"horizontal";if(e>t&&e>10)return"vertical";return""}(e.offsetX,e.offsetY)}(t),"horizontal"===n.direction&&(t.preventDefault&&t.preventDefault(),B(n.x+n.deltaX,s)))}function F(t,e){var s=t.instance,o=s.getDataset().disabled,n=s.getState();(o=("string"==typeof o?JSON.parse(o):o)||!1)||function(t,e,s){var o=e.getState(),n=o.threshold;o.position;var i=o.isopen||"none",a=o.leftWidth,r=o.rightWidth;if(0===o.deltaX)return void _("none",e,s);_("none"===i&&r>0&&-t>n||"none"!==i&&r>0&&r+t<n?"right":"none"===i&&a>0&&t>n||"none"!==i&&a>0&&a-t<n?"left":"none",e,s)}(n.left,s,e)}function B(t,e,s){t=t||0;var o,n,i,a=e.getState(),r=a.leftWidth,l=a.rightWidth;a.left=(o=t,n=-l,i=r,Math.min(Math.max(o,n),i)),e.requestAnimationFrame((function(){e.setStyle({transform:"translateX("+a.left+"px)","-webkit-transform":"translateX("+a.left+"px)"})}))}function L(t,e){var s=t.getState(),o=e.selectComponent(".button-group--left"),n=e.selectComponent(".button-group--right"),i={width:0},a={width:0};o&&(i=o.getBoundingClientRect()),n&&(a=n.getBoundingClientRect()),s.leftWidth=i.width||0,s.rightWidth=a.width||0,s.threshold=t.getDataset().threshold}function _(t,e,s){var o=e.getState(),n=o.leftWidth,i=o.rightWidth,a="";switch(o.isopen=o.isopen?o.isopen:"none",t){case"left":a=n;break;case"right":a=-i;break;default:a=0}o.isopen!==t&&(o.throttle=!0,s.callMethod("change",{open:t})),o.isopen=t,e.requestAnimationFrame((function(){e.addClass("ani"),B(a,e)}))}function N(t){var e=t.getState();e.direction="",e.deltaX=0,e.deltaY=0,e.offsetX=0,e.offsetY=0}function P(){for(var t=navigator.userAgent,e=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"],s=!0,o=0;o<e.length-1;o++)if(t.indexOf(e[o])>0){s=!1;break}return s}"object"==typeof window&&(T=!0);var U=!1;const O={showWatch:function(t,e,s,o){var n=o.getState();L(o,s),t&&"none"!==t?_(t,o,s):(n.left&&_("none",o,s),N(o))},touchstart:Y,touchmove:D,touchend:F,mousedown:function(t,e){T&&P()&&(Y(t,e),U=!0)},mousemove:function(t,e){T&&P()&&U&&D(t)},mouseup:function(t,e){T&&P()&&(F(t,e),U=!1)},mouseleave:function(t,e){T&&P()&&(U=!1)}},E=t=>{t.$wxs||(t.$wxs=[]),t.$wxs.push("wxsswipe"),t.mixins||(t.mixins=[]),t.mixins.push({beforeCreate(){this.wxsswipe=O}})},H={showWatch(t,e,s,o,n){var i=n.state||{};(s.$el||s.$vm&&s.$vm.$el)&&(this.getDom(o,s,n),t&&"none"!==t?this.openState(t,o,s,n):(i.left&&this.openState("none",o,s,n),this.resetTouchStatus(o,n)))},touchstart(t,e,s){let o=t.instance,n=o.getDataset().disabled,i=s.state||{};this.getDom(o,e,s),n=this.getDisabledType(n),n||(o.requestAnimationFrame((function(){o.removeClass("ani"),e.callMethod("closeSwipe")})),i.x=i.left||0,this.stopTouchStart(t,e,s))},touchmove(t,e,s){let o=t.instance;if(!o)return;let n=o.getDataset().disabled,i=s.state||{};if(n=this.getDisabledType(n),n)return;if(this.stopTouchMove(t,s),"horizontal"!==i.direction)return;t.preventDefault&&t.preventDefault();let a=i.x+i.deltaX;this.move(a,o,e,s)},touchend(t,e,s){let o=t.instance,n=o.getDataset().disabled,i=s.state||{};n=this.getDisabledType(n),n||this.moveDirection(i.left,o,e,s)},move(t,e,s,o){t=t||0;let n=o.state||{},i=n.leftWidth,a=n.rightWidth;n.left=this.range(t,-a,i),e.requestAnimationFrame((function(){e.setStyle({transform:"translateX("+n.left+"px)","-webkit-transform":"translateX("+n.left+"px)"})}))},getDom(t,e,s){var o=s.state||{},n=e.$el||e.$vm&&e.$vm.$el,i=n.querySelector(".button-group--left"),a=n.querySelector(".button-group--right");i&&i.offsetWidth?o.leftWidth=i.offsetWidth||0:o.leftWidth=0,a&&a.offsetWidth?o.rightWidth=a.offsetWidth||0:o.rightWidth=0,o.threshold=t.getDataset().threshold},getDisabledType:t=>("string"==typeof t?JSON.parse(t):t)||!1,range:(t,e,s)=>Math.min(Math.max(t,e),s),moveDirection(t,e,s,o){var n=o.state||{},i=n.threshold;n.position;var a=n.isopen||"none",r=n.leftWidth,l=n.rightWidth;0!==n.deltaX?"none"===a&&l>0&&-t>i||"none"!==a&&l>0&&l+t<i?this.openState("right",e,s,o):"none"===a&&r>0&&t>i||"none"!==a&&r>0&&r-t<i?this.openState("left",e,s,o):this.openState("none",e,s,o):this.openState("none",e,s,o)},openState(t,e,s,o){let n=o.state||{},i=n.leftWidth,a=n.rightWidth,r="";switch(n.isopen=n.isopen?n.isopen:"none",t){case"left":r=i;break;case"right":r=-a;break;default:r=0}n.isopen!==t&&(n.throttle=!0,s.callMethod("change",{open:t})),n.isopen=t,e.requestAnimationFrame((()=>{e.addClass("ani"),this.move(r,e,s,o)}))},getDirection:(t,e)=>t>e&&t>10?"horizontal":e>t&&e>10?"vertical":"",resetTouchStatus(t,e){let s=e.state||{};s.direction="",s.deltaX=0,s.deltaY=0,s.offsetX=0,s.offsetY=0},stopTouchStart(t,e,s){let o=t.instance,n=s.state||{};this.resetTouchStatus(o,s);var i=t.touches[0];n.startX=i.clientX,n.startY=i.clientY},stopTouchMove(t,e){t.instance;let s=e.state||{},o=t.touches[0];s.deltaX=o.clientX-s.startX,s.deltaY=o.clientY-s.startY,s.offsetY=Math.abs(s.deltaY),s.offsetX=Math.abs(s.deltaX),s.direction=s.direction||this.getDirection(s.offsetX,s.offsetY)}},J={mounted(t,e,s){this.state={}},methods:{showWatch(t,e,s,o){H.showWatch(t,e,s,o,this)},touchstart(t,e){H.touchstart(t,e,this)},touchmove(t,e){H.touchmove(t,e,this)},touchend(t,e){H.touchend(t,e,this)}}},M=t=>{t.$renderjs||(t.$renderjs=[]),t.$renderjs.push("renderswipe"),t.mixins||(t.mixins=[]),t.mixins.push({beforeCreate(){this.renderswipe=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),t.mixins.push(J)},I={mixins:[W,{},{}],emits:["click","change"],props:{show:{type:String,default:"none"},disabled:{type:Boolean,default:!1},autoClose:{type:Boolean,default:!0},threshold:{type:Number,default:20},leftOptions:{type:Array,default:()=>[]},rightOptions:{type:Array,default:()=>[]}},unmounted(){this.__isUnmounted=!0,this.uninstall()},methods:{uninstall(){this.swipeaction&&this.swipeaction.children.forEach(((t,e)=>{t===this&&this.swipeaction.children.splice(e,1)}))},getSwipeAction(t="uniSwipeAction"){let e=this.$parent,s=e.$options.name;for(;s!==t;){if(e=e.$parent,!e)return!1;s=e.$options.name}return e}}};E(I),M(I);const q=v(I,[["render",function(f,g,m,w,y,b){const S=d,A=p;return t(),e(A,{class:"uni-swipe"},{default:s((()=>[o(A,{class:"uni-swipe_box","change:prop":f.wxsswipe.showWatch,prop:f.is_show,"data-threshold":m.threshold,"data-disabled":m.disabled,onTouchstart:f.wxsswipe.touchstart,onTouchmove:f.wxsswipe.touchmove,onTouchend:f.wxsswipe.touchend},{default:s((()=>[o(A,{class:"uni-swipe_button-group button-group--left"},{default:s((()=>[n(f.$slots,"left",{},(()=>[(t(!0),i(a,null,r(m.leftOptions,((n,i)=>(t(),e(A,{key:i,style:l({backgroundColor:n.style&&n.style.backgroundColor?n.style.backgroundColor:"#C7C6CD"}),class:"uni-swipe_button button-hock",onTouchstart:h(f.appTouchStart,["stop"]),onTouchend:h((t=>f.appTouchEnd(t,i,n,"left")),["stop"]),onClick:h((t=>f.onClickForPC(i,n,"left")),["stop"])},{default:s((()=>[o(S,{class:"uni-swipe_button-text",style:l({color:n.style&&n.style.color?n.style.color:"#FFFFFF",fontSize:n.style&&n.style.fontSize?n.style.fontSize:"16px"})},{default:s((()=>[c(u(n.text),1)])),_:2},1032,["style"])])),_:2},1032,["style","onTouchstart","onTouchend","onClick"])))),128))]),!0)])),_:3}),o(A,{class:"uni-swipe_text--center"},{default:s((()=>[n(f.$slots,"default",{},void 0,!0)])),_:3}),o(A,{class:"uni-swipe_button-group button-group--right"},{default:s((()=>[n(f.$slots,"right",{},(()=>[(t(!0),i(a,null,r(m.rightOptions,((n,i)=>(t(),e(A,{key:i,style:l({backgroundColor:n.style&&n.style.backgroundColor?n.style.backgroundColor:"#C7C6CD"}),class:"uni-swipe_button button-hock",onTouchstart:h(f.appTouchStart,["stop"]),onTouchend:h((t=>f.appTouchEnd(t,i,n,"right")),["stop"]),onClick:h((t=>f.onClickForPC(i,n,"right")),["stop"])},{default:s((()=>[o(S,{class:"uni-swipe_button-text",style:l({color:n.style&&n.style.color?n.style.color:"#FFFFFF",fontSize:n.style&&n.style.fontSize?n.style.fontSize:"16px"})},{default:s((()=>[c(u(n.text),1)])),_:2},1032,["style"])])),_:2},1032,["style","onTouchstart","onTouchend","onClick"])))),128))]),!0)])),_:3})])),_:3},8,["change:prop","prop","data-threshold","data-disabled","onTouchstart","onTouchmove","onTouchend"])])),_:3})}],["__scopeId","data-v-b44c494b"]]);const z=v({name:"uniSwipeAction",data:()=>({}),created(){this.children=[]},methods:{resize(){},closeAll(){this.children.forEach((t=>{t.is_show="none"}))},closeOther(t){this.openItem&&this.openItem!==t&&(this.openItem.is_show="none"),this.openItem=t}}},[["render",function(o,i,a,r,l,h){const c=p;return t(),e(c,null,{default:s((()=>[n(o.$slots,"default")])),_:3})}]]);const R=v({data:()=>({statusBarHeight:f("statusBarHeight"),status:"more",contentText:{contentdown:"点击或上拉加载更多",contentrefresh:"正在加载...",contentnomore:"没有更多数据了"},formData:{pageNum:1,pageSize:10,reportType:0},reportList:[],current:1,delBtnWidth:160,isScroll:!0,options:[{text:"删除",style:{backgroundColor:"#E71122"}}]}),onLoad(t){this.formData.reportType=t.type?t.type:1,this.getData()},methods:{change(){},delItem(){console.log("删除")},goBack(){g()},getData(){this.status="loading",this.$api.reportPageAPI({data:{...this.formData},method:"get"}).then((t=>{"SUCCESS"==t.code&&(this.reportList.push(...t.result.records.map((t=>({...t,right:0})))),this.current=t.result.current,t.result.current==t.result.pages?this.status="noMore":this.status="more")}))},goDetail(t){this.$api.generateDetailAPI({data:{id:t.id},method:"get"}).then((t=>{var e;let s=t.result.docUrl,o=t.result.title;m("pdfUrl",s),null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{url:s,name:o,isBuy:1},path:"showPDF"}))}))},scrolltolower(){console.log(1234),"more"==this.status&&(this.formData.pageNum++,this.getData())}}},[["render",function(n,h,d,f,g,m){const v=S,W=p,k=x(w("uni-swipe-action-item"),q),T=x(w("uni-swipe-action"),z),Y=x(w("uni-load-more"),X),D=A;return t(),e(W,{style:{position:"absolute"},class:"mainView"},{default:s((()=>[o(v,{src:"https://static.idicc.cn/cdn/aiChat/applet/bg.png",class:"homeBgc"}),o(W,{class:"topImg",style:l(`top: ${g.statusBarHeight}px`)},{default:s((()=>[o(W,{class:"black",onClick:h[0]||(h[0]=t=>m.goBack())},{default:s((()=>[o(v,{class:"returnFn",src:C})])),_:1}),c(" 历史报告 ")])),_:1},8,["style"]),o(D,{class:"historyList box",onScrolltolower:m.scrolltolower,"scroll-y":"true",style:l(`top:calc( 50px + ${g.statusBarHeight}px );height: calc( 100% - 100px - ${g.statusBarHeight}px );`)},{default:s((()=>[(t(!0),i(a,null,r(g.reportList,((n,i)=>(t(),e(T,{class:"hisLi",key:i,onClick:t=>m.goDetail(n),"right-options":g.options,onChange:m.change},{default:s((()=>[o(k,null,{default:s((()=>[o(W,{class:"hisLiItem"},{default:s((()=>[o(v,{src:"data:image/png;base64,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",class:"hisImg"}),o(W,{style:{"margin-left":"20rpx",flex:"1"}},{default:s((()=>[o(W,{class:"font1"},{default:s((()=>[c(u(n.reportName),1)])),_:2},1024),o(W,{class:"font2"},{default:s((()=>[c(u(n.gmtCreate),1)])),_:2},1024)])),_:2},1024),o(W,{class:"btns"},{default:s((()=>[y("span",{class:b(`status-${n.status}`)},u(["未生成","生成中","已生成","生成失败"][n.status]),3)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick","right-options","onChange"])))),128)),0==g.reportList.length?(t(),e(W,{key:0,class:"nodatabox"},{default:s((()=>[o(v,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),y("span",{class:"span"},"暂无内容")])),_:1})):(t(),e(W,{key:1,onClick:m.scrolltolower},{default:s((()=>[o(Y,{status:g.status,"content-text":g.contentText},null,8,["status","content-text"])])),_:1},8,["onClick"]))])),_:1},8,["onScrolltolower","style"])])),_:1})}],["__scopeId","data-v-713fa804"]]);export{R as default};
