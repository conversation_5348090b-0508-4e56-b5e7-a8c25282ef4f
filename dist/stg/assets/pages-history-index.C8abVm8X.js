import{l as e,m as s,Z as t,Y as o,A as i,c as a,w as l,o as n,B as d,d as r,a1 as c}from"./index-CBCsGYoT.js";import{v as p}from"./webviewUrl.D8mk1f89.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const h=k({data:()=>({tokenData:"",url:"",isFirstLogin:!0,webV:null,type:1,capType:1,token:"",option:{}}),onLoad(o){if(this.token=e("token"),!this.token)return s({url:"/pages/login/index"});t({title:"加载中"}),o.type&&(this.type=o.type),o.capType&&(this.capType=o.capType),this.option=o},onShow(){var t;if(this.token=e("token"),!this.token)return s({url:"/pages/login/index"});let o="1"===(null==(t=this.option)?void 0:t.isEditor)?"deriveExcel"===this.type?`${p}editor/deriveExcel`:`${p}editor/history`:`${p}history`;this.tokenData=e("token"),this.url=`${o}?token=${this.tokenData}&type=${this.type}&capType=${this.capType}&id=${(new Date).getTime()}`,console.log("history",this.url)},mounted(){},methods:{loadSuccess(){o()}}},[["render",function(e,s,t,o,p,k){const h=c,u=i("View");return n(),a(u,null,{default:l((()=>[d("div",{class:"skeleton"},[d("div",{class:"chatBg"}),d("div",{class:"skeletonlist"},[d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"}),d("div",{class:"skeletonBig"})])]),p.url&&p.token?(n(),a(h,{key:0,src:p.url,onMessage:e.handlePostMessage,title:"历史记录",onLoad:k.loadSuccess},null,8,["src","onMessage","onLoad"])):r("",!0)])),_:1})}],["__scopeId","data-v-dd3e563d"]]);export{h as default};
