import{l as s,q as e,a3 as a,a4 as l,p as t,s as r,y as n,m as i,z as o,A as c,a as u,f as d,w as f,F as m,i as p,o as h,B as g,g as _,t as I,c as y,d as v,C as k}from"./index-CBCsGYoT.js";import{_ as C}from"./page-meta.BCoSkkXs.js";import{r as b}from"./uni-app.es.CZb2JZWI.js";import{t as N}from"./index.D5F8338M.js";import{_ as D}from"./userimg.DBGjY0t5.js";import{_ as w}from"./right.D4iebSn6.js";import{_ as O}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const E=O({data:()=>({baseInfo:{},title:"设置",orgName:"",showMore:!1,userIdentity:1,workerParent:"",isDefault:!0}),components:{tabBar:N},onShow(){this.userdescribeFn(),this.orgName=s("orgName"),this.isDefault=s("isDefault")},methods:{getSuperior(){this.$api.workerParentAPI().then((s=>{var e;this.workerParent=null==(e=s.result)?void 0:e.realName}))},getorganizationList(){this.$api.ownList().then((s=>{var e;this.orgName=s.result.selected.orgName,this.showMore=(null==(e=s.result.optional)?void 0:e.length)>1}))},Editor(s){7==s&&this.workerParent||e({url:`/pages/user/changename?type=${s}`})},userIdentityText(s){switch(Number(s)){case 1:return"产业运营方";case 2:return"产业研究机构";case 3:return"出海服务方";case 4:return"出海方";case 5:return"产业顾问方";default:return""}},userdescribeFn(){this.$api.userMessage().then((s=>{var e;this.baseInfo=s.result,this.userIdentity=null==(e=s.result.userCompletionInfoDO)?void 0:e.userIdentity,5==this.userIdentity&&this.getSuperior()})).catch((s=>{this.baseInfo={},this.enterpriseInfo={}}))},modificationimg(){var e=this;wx.chooseMedia({count:1,mediaType:["image"],sourceType:["album","camera"],camera:"back",success(r){const n=r.tempFiles;a({url:`${l}/ai/upload/uploadImg`,filePath:n[0].tempFilePath,name:"file",success:a=>{a.data=JSON.parse(a.data);let l={headImg:a.data.result,userId:s("userId")};e.$api.updateHeadImg({data:l,method:"post"}).then((s=>{"SUCCESS"===s.code&&t({title:"头像上传成功！",icon:"none"}),setTimeout((()=>{e.userdescribeFn()}),500)}))}})}})},showlogin(){r("token",""),r("userId",""),n({key:"orgCode"}),n({key:"orgName"}),n({key:"userIdentityType"}),r("identity",1),i({url:"/pages/login/index"})}}},[["render",function(s,e,a,l,t,r){const n=b(o("page-meta"),C),i=c("tabBar"),N=p,O=k;return h(),u(m,null,[d(n,{"page-style":"background-color: #FAFCFF"}),d(N,null,{default:f((()=>[d(i,{title:t.title},null,8,["title"]),g("div",{class:"box"},[d(N,{class:"user"},{default:f((()=>[d(N,{class:"t1"},{default:f((()=>[d(N,{onClick:e[0]||(e[0]=s=>r.modificationimg()),class:"columns"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("头像")])),_:1}),d(N,{class:"rights"},{default:f((()=>[t.baseInfo.headImgUrl?(h(),u("img",{key:0,class:"headportrait",src:t.baseInfo.headImgUrl,alt:""},null,8,["src"])):(h(),u("img",{key:1,class:"headportrait",src:D,alt:""})),d(O,{src:w,class:"righticon"})])),_:1})])),_:1}),d(N,{onClick:e[1]||(e[1]=s=>r.Editor(1)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("姓名")])),_:1}),d(N,{class:"right"},{default:f((()=>[g("span",{class:"spillname"},I(t.baseInfo.realName),1),d(O,{src:w,class:"righticon"})])),_:1})])),_:1}),d(N,{class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("账号/手机号")])),_:1}),d(N,{class:"right"},{default:f((()=>[g("span",{class:"spillname"},I(t.baseInfo.mobile),1)])),_:1})])),_:1}),3==t.userIdentity?(h(),y(N,{key:0,onClick:e[2]||(e[2]=s=>r.Editor(12)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("企业简介")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.enterpriseProfile),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1})):v("",!0),d(N,{onClick:e[3]||(e[3]=s=>r.Editor(2)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("邮箱")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.email),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1}),d(N,{class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("身份")])),_:1}),d(N,{class:"right"},{default:f((()=>[g("span",{class:"spillname"},I(r.userIdentityText(t.userIdentity)),1)])),_:1})])),_:1}),!t.isDefault&&t.orgName?(h(),y(N,{key:1,class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("所属机构")])),_:1}),d(N,{class:"right"},{default:f((()=>[g("span",{class:"spillname"},I(t.orgName),1)])),_:1})])),_:1})):v("",!0),4==t.userIdentity?(h(),y(N,{key:2,onClick:e[4]||(e[4]=s=>r.Editor(11)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("出海关注目的地")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.overseaTargetName),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1})):v("",!0),3!=t.userIdentity?(h(),y(N,{key:3,onClick:e[5]||(e[5]=s=>r.Editor(3)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("所在单位")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.company),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1})):v("",!0),3!=t.userIdentity?(h(),y(N,{key:4,onClick:e[6]||(e[6]=s=>r.Editor(10)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("所在单位职务")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.jobTitle),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1})):v("",!0),3!=t.userIdentity&&4!=t.userIdentity?(h(),y(N,{key:5,class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("招商属地")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.codeName),1)]})),_:1})])),_:1})):v("",!0),3!=t.userIdentity&&4!=t.userIdentity?(h(),y(N,{key:6,onClick:e[7]||(e[7]=s=>r.Editor(8)),class:"column"},{default:f((()=>{var s;return[d(N,{class:"left"},{default:f((()=>[_("关注产业链")])),_:1}),(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.chainNames)?(h(),y(N,{key:0,class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.chainNames.join("、")),1),d(O,{src:w,class:"righticon"})]})),_:1})):v("",!0)]})),_:1})):v("",!0)])),_:1})])),_:1}),3==t.userIdentity?(h(),y(N,{key:0,class:"user"},{default:f((()=>[d(N,{class:"t1"},{default:f((()=>[d(N,{class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("服务国家")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.serviceCountryName),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1}),d(N,{onClick:e[8]||(e[8]=s=>r.Editor(14)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("服务类目")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.serviceItemName),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1})])),_:1})])),_:1})):v("",!0),5==t.userIdentity?(h(),y(N,{key:1,class:"user"},{default:f((()=>[d(N,{class:"t1"},{default:f((()=>[d(N,{onClick:e[9]||(e[9]=s=>r.Editor(4)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("所在城市")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.cityName),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1}),d(N,{onClick:e[10]||(e[10]=s=>r.Editor(5)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("优势资源城市")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.advantageCity),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1}),d(N,{onClick:e[11]||(e[11]=s=>r.Editor(6)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_("优势资源简述")])),_:1}),d(N,{class:"right"},{default:f((()=>{var s;return[g("span",{class:"spillname"},I(null==(s=t.baseInfo.userCompletionInfoDO)?void 0:s.resume),1),d(O,{src:w,class:"righticon"})]})),_:1})])),_:1})])),_:1})])),_:1})):v("",!0),5==t.userIdentity?(h(),y(N,{key:2,class:"user"},{default:f((()=>[d(N,{class:"t1"},{default:f((()=>[d(N,{onClick:e[12]||(e[12]=s=>r.Editor(7)),class:"column"},{default:f((()=>[d(N,{class:"left"},{default:f((()=>[_(I(t.workerParent?"所在团队":"加入团队"),1)])),_:1}),d(N,{class:"right"},{default:f((()=>[g("span",{class:"spillname"},I(t.workerParent),1),d(O,{src:w,class:"righticon"})])),_:1})])),_:1})])),_:1})])),_:1})):v("",!0)]),g("div",{onClick:e[13]||(e[13]=(...s)=>r.showlogin&&r.showlogin(...s)),class:"butlogout"},"退出登录")])),_:1})],64)}],["__scopeId","data-v-ec6f4a60"]]);export{E as default};
