import{k as t,l as a,o as s,c as e,w as o,f as r,n as i,d as l,g as n,t as c,C as d,i as u}from"./index-CBCsGYoT.js";import{_ as p}from"./returnFn.BYkANsDr.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const f=g({props:{title:{type:String,default:""},colorType:{type:Number,default:0}},data:()=>({statusBarHeight:40}),created(){this.statusBarHeight=t().statusBarHeight,this.statusBarHeight=a("top")?a("top"):40},methods:{goBack(){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"goBack"}))},goBack2(){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",path:"goBack"}))}}},[["render",function(t,a,g,f,B,h){const _=d,w=u;return s(),e(w,{class:"bar-con"},{default:o((()=>[r(w,{class:"tabBarCon"},{default:o((()=>[r(w,{id:"tabBar",class:"tabBar"},{default:o((()=>[r(w,{class:"nav",style:i(`\n\t\t\t\t    margin-top: ${B.statusBarHeight}px;\n\t\t\t\t`)},{default:o((()=>[0==g.colorType?(s(),e(w,{key:0,class:"black",onClick:a[0]||(a[0]=t=>h.goBack())},{default:o((()=>[r(_,{class:"returnFn",src:p})])),_:1})):l("",!0),r(w,{class:"nav-title"},{default:o((()=>[n(c(g.title),1)])),_:1})])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-4da68494"]]);export{f as t};
