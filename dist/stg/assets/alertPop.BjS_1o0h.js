import{_ as a}from"./uni-popup.BLXBf1r-.js";import{z as s,o as e,c as r,w as t,V as p,f as o,B as i,t as c,i as l}from"./index-CBCsGYoT.js";import{r as n}from"./uni-app.es.CZb2JZWI.js";import{_ as d}from"./_plugin-vue_export-helper.BCo6x5W8.js";const f=d({data:()=>({porpsData:""}),methods:{opens(a){this.porpsData=a,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){this.$refs.chargeback.close()}}},[["render",function(d,f,m,u,h,b){const _=n(s("uni-popup"),a),k=l;return e(),r(k,null,{default:t((()=>[(e(),r(p,{to:"body"},[o(_,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:t((()=>[i("div",{class:"applyaffirm"},[i("div",{class:"p1"},"温馨提示"),i("div",{class:"p2"},c(h.porpsData.title),1),i("div",{class:"popBtn"},[i("div",{onClick:f[0]||(f[0]=(...a)=>b.retreat&&b.retreat(...a)),class:"affirm"}," 我知道了 ")])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-220bba84"]]);export{f as a};
