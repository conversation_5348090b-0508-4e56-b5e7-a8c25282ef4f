import{l as e,s as t,z as a,o as s,c as l,w as i,a as c,t as o,d as n,f as r,e as u,g as h,D as m,F as d,b as p,i as f,h as _,S as y}from"./index-CBCsGYoT.js";import{t as g}from"./uni-cloud.es.Dc96wm0-.js";import{_ as b}from"./uni-icons.Dr3tmUrM.js";import{r as D}from"./uni-app.es.CZb2JZWI.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";const x=C({name:"uni-data-select",mixins:[g.mixinDatacom||{}],props:{localdata:{type:Array,default:()=>[]},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1},format:{type:String,default:""}},data:()=>({showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[],cacheKey:"uni-data-select-lastSelectedValue"}),created(){this.debounceGet=this.debounce((()=>{this.query()}),300),this.collection&&!this.localdata.length&&this.debounceGet()},computed:{typePlaceholder(){const e=this.placeholder,t={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return t?e+t:e},valueCom(){return this.modelValue},textShow(){let e=this.current;return e.length>10?e.slice(0,25)+"...":e}},watch:{localdata:{immediate:!0,handler(e,t){Array.isArray(e)&&t!==e&&(this.mixinDatacomResData=e)}},valueCom(e,t){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler(e){e.length&&this.initDefVal()}}},methods:{debounce(e,t=100){let a=null;return function(...s){a&&clearTimeout(a),a=setTimeout((()=>{e.apply(this,s)}),t)}},query(){this.mixinDatacomEasyGet()},onMixinDatacomPropsChange(){this.collection&&this.debounceGet()},initDefVal(){let e="";if(!this.valueCom&&0!==this.valueCom||this.isDisabled(this.valueCom)){let t;if(this.collection&&(t=this.getCache()),t||0===t)e=t;else{let t="";this.defItem>0&&this.defItem<=this.mixinDatacomResData.length&&(t=this.mixinDatacomResData[this.defItem-1].value),e=t}(e||0===e)&&this.emit(e)}else e=this.valueCom;const t=this.mixinDatacomResData.find((t=>t.value===e));this.current=t?this.formatItemName(t):""},isDisabled(e){let t=!1;return this.mixinDatacomResData.forEach((a=>{a.value===e&&(t=a.disable)})),t},clearVal(){this.emit(""),this.collection&&this.removeCache()},change(e){e.disable||(this.showSelector=!1,this.current=this.formatItemName(e),this.emit(e.value))},emit(e){this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.collection&&this.setCache(e)},toggleSelector(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName(e){let{text:t,value:a,channel_code:s}=e;if(s=s?`(${s})`:"",this.format){let t="";t=this.format;for(let a in e)t=t.replace(new RegExp(`{${a}}`,"g"),e[a]);return t}return this.collection.indexOf("app-list")>0?`${t}(${a})`:t||`未命名${s}`},getLoadData(){return this.mixinDatacomResData},getCurrentCacheKey(){return this.collection},getCache(t=this.getCurrentCacheKey()){return(e(this.cacheKey)||{})[t]},setCache(a,s=this.getCurrentCacheKey()){let l=e(this.cacheKey)||{};l[s]=a,t(this.cacheKey,l)},removeCache(a=this.getCurrentCacheKey()){let s=e(this.cacheKey)||{};delete s[a],t(this.cacheKey,s)}}},[["render",function(e,t,g,C,x,S){const k=f,v=D(a("uni-icons"),b),w=_,R=y;return s(),l(k,{class:"uni-stat__select"},{default:i((()=>[g.label?(s(),c("span",{key:0,class:"uni-label-text hide-on-phone"},o(g.label+"："),1)):n("",!0),r(k,{class:u(["uni-stat-box",{"uni-stat__actived":x.current}])},{default:i((()=>[r(k,{class:u(["uni-select",{"uni-select--disabled":g.disabled}])},{default:i((()=>[r(k,{class:"uni-select__input-box",onClick:S.toggleSelector},{default:i((()=>[x.current?(s(),l(k,{key:0,class:"uni-select__input-text"},{default:i((()=>[h(o(S.textShow),1)])),_:1})):(s(),l(k,{key:1,class:"uni-select__input-text uni-select__input-placeholder"},{default:i((()=>[h(o(S.typePlaceholder),1)])),_:1})),x.current&&g.clear&&!g.disabled?(s(),l(k,{key:2,onClick:m(S.clearVal,["stop"])},{default:i((()=>[r(v,{type:"clear",color:"#c0c4cc",size:"24"})])),_:1},8,["onClick"])):(s(),l(k,{key:3},{default:i((()=>[r(v,{type:x.showSelector?"top":"bottom",size:"14",color:"#999"},null,8,["type"])])),_:1}))])),_:1},8,["onClick"]),x.showSelector?(s(),l(k,{key:0,class:"uni-select--mask",onClick:S.toggleSelector},null,8,["onClick"])):n("",!0),x.showSelector?(s(),l(k,{key:1,class:"uni-select__selector"},{default:i((()=>[r(k,{class:"uni-popper__arrow"}),r(R,{"scroll-y":"true",class:"uni-select__selector-scroll"},{default:i((()=>[0===x.mixinDatacomResData.length?(s(),l(k,{key:0,class:"uni-select__selector-empty"},{default:i((()=>[r(w,null,{default:i((()=>[h(o(g.emptyTips),1)])),_:1})])),_:1})):(s(!0),c(d,{key:1},p(x.mixinDatacomResData,((e,t)=>(s(),l(k,{class:"uni-select__selector-item",key:t,onClick:t=>S.change(e)},{default:i((()=>[r(w,{class:u({"uni-select__selector__disabled":e.disable})},{default:i((()=>[h(o(S.formatItemName(e)),1)])),_:2},1032,["class"])])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):n("",!0)])),_:1},8,["class"])])),_:1},8,["class"])])),_:1})}],["__scopeId","data-v-2bc617ad"]]);export{x as _};
