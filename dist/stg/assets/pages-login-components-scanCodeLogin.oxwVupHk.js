import{k as t,p as s,T as a,z as e,a as o,f as i,w as n,B as c,d as l,g as p,F as r,i as d,a5 as g,o as u,n as A,C as h}from"./index-CBCsGYoT.js";import{_ as F}from"./page-meta.BCoSkkXs.js";import{r as m}from"./uni-app.es.CZb2JZWI.js";import{_ as y}from"./returnFn.BYkANsDr.js";import{_ as W}from"./_plugin-vue_export-helper.BCo6x5W8.js";const q=W({props:{blue:{type:Boolean,default:!1}},data:()=>({statusBarHeight:0,logintype:1,loginId:""}),created(){this.statusBarHeight=t().statusBarHeight},onLoad(t){console.log(t,"options");let s=decodeURIComponent(t.scene);console.log(s),this.loginId=s,console.log(this.loginId),this.getStatusLogin()},methods:{goBack(){wx.exitMiniProgram()},getStatusLogin(){this.$api.getStatusLoginAPI({method:"get",data:{uniqueKey:this.loginId},token:""}).then((t=>{"SCAN_EXPIRE"==t.result.status||"AUTHORIZE_EXPIRE"==t.result.status||"AUTHORIZE_EXIT"==t.result.status||"TOKEN_COMPLETE"==t.result.status?this.logintype=3:this.updataStatus()}))},updataStatus(){this.$api.updateStatusLoginAPI({method:"POST",data:{uniqueKey:this.loginId,status:1},token:""}).then((t=>{}))},getcode(t){const e=this;if("getPhoneNumber:ok"!=t.detail.errMsg)return s({title:"获取授权失败",icon:"none"});s({title:"授权成功，正在登录...",icon:"none"}),a({success(s){e.$api.updateStatusLoginAPI({method:"POST",data:{phoneCode:null==t?void 0:t.detail.code,uniqueKey:e.loginId,jsCode:s.code,status:3},token:""}).then((t=>{"SUCCESS"==t.code&&(e.logintype=2)})).catch((t=>{"41005"==t.code&&setTimeout((()=>{e.logintype=4}),500)}))}})}}},[["render",function(t,s,a,W,q,B){const C=m(e("page-meta"),F),E=h,I=d,f=g;return u(),o(r,null,[i(C,{"page-style":"background-color: #FAFAFA"}),i(I,{class:"bar-con"},{default:n((()=>[i(I,{class:"tabBarCon"},{default:n((()=>[i(I,{id:"tabBar",class:"tabBar"},{default:n((()=>[i(I,{class:"nav",style:A(`\n\t\t\t\t\t    margin-top: ${q.statusBarHeight}px;\n\t\t\t\t\t`)},{default:n((()=>[i(I,{class:"black",onClick:s[0]||(s[0]=t=>B.goBack())},{default:n((()=>[i(E,{class:"returnFn",src:y})])),_:1}),i(I,{class:"nav-title"},{default:n((()=>[p("登录")])),_:1})])),_:1},8,["style"])])),_:1})])),_:1})])),_:1}),1==q.logintype?(u(),o("div",{key:0,class:"box"},[i(E,{class:"arsy",src:"https://static.idicc.cn/cdn/aiChat/applet/arsy.png"}),c("div",{class:"p1"},"哒达招商"),c("div",{class:"p2"},"即将在电脑上登录哒达招商，确认是否本人操作"),i(f,{class:"greenBtn","open-type":"getPhoneNumber",type:"primary",onGetphonenumber:B.getcode},{default:n((()=>[p("本机一键登录")])),_:1},8,["onGetphonenumber"])])):l("",!0),2==q.logintype?(u(),o("div",{key:1,class:"box"},[i(E,{class:"landsuccessfully",src:"https://static.idicc.cn/cdn/aiChat/applet/landsuccessfully.png"}),c("div",{class:"p1"},"登录成功"),c("div",{class:"p2"},"您可以在电脑端操作啦"),i(f,{class:"greenBtn",onClick:s[1]||(s[1]=t=>B.goBack()),type:"primary"},{default:n((()=>[p("完成")])),_:1})])):l("",!0),3==q.logintype?(u(),o("div",{key:2,class:"box"},[i(E,{class:"landsuccessfully",src:"https://static.idicc.cn/cdn/aiChat/applet/error.png"}),c("div",{class:"p3"},"二维码已失效，请重新扫码登录"),i(f,{class:"greenBtn",onClick:s[2]||(s[2]=t=>B.goBack()),type:"primary"},{default:n((()=>[p("退出")])),_:1})])):l("",!0),4==q.logintype?(u(),o("div",{key:3,class:"box",style:{"margin-top":"306rpx"}},[i(E,{style:{"margin-bottom":"4rpx"},class:"landsuccessfully",src:"data:image/png;base64,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"}),c("div",{class:"p4"},"登陆失败"),c("div",{class:"p5"},[p("该微信号与选择手机号不匹配，请重新尝试"),c("br"),p("有疑问请联系小艾客服")]),c("div",{class:"p6"},"—— 扫码添加小艾客服 ——"),i(E,{src:"https://static.idicc.cn/cdn/aiChat/applet/serviceCode2.png","show-menu-by-longpress":"true",class:"serviceImg"}),i(f,{style:{"margin-top":"100rpx"},class:"greenBtn",onClick:s[3]||(s[3]=t=>B.goBack()),type:"primary"},{default:n((()=>[p("返回")])),_:1})])):l("",!0)],64)}],["__scopeId","data-v-496bfee9"]]);export{q as default};
