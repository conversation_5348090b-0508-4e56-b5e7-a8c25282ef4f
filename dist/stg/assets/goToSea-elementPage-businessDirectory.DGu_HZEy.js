import{l as t,z as e,A as s,a,f as i,B as r,t as n,w as o,F as h,i as p,S as c,o as l,e as m,g as u,c as d,d as g,C as y}from"./index-HcMwrp5e.js";import{_ as L}from"./page-meta.X-Lr6csD.js";import{r as f}from"./uni-app.es.DFp0WTX7.js";import{t as C}from"./index.B6Rbewql.js";import{i as S}from"./industryChain.BG25_ils.js";import{m as N}from"./moreScreen.y5u7uXHA.js";import{e as w}from"./List.C3wooyOc.js";import{_ as A}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";import"./uni-popup.BdZPMDVN.js";import"./uni-transition.CHOJlBbg.js";import"./utils.61Hi-B7M.js";const b=A({components:{tabBar:C,industryChain:S,moreScreen:N,enterpriseList:w},data:()=>({title:"区域生态企业名录",industryParkName:"",parkMd5:"",parameterList:[!0,!0],industryChainList:[],moreScreenList:[],showChain:"",showChainName:"",enterpriseList:[],showNodata:!1,pageNum:1,pages:1,total:0,moreData:{}}),onLoad(e){this.parkMd5=e.parkMd5,this.industryParkName=e.industryParkName,this.countryName=t("defaultName"),this.getList(),this.getScreen()},methods:{loadMoreData(){this.pages>this.pageNum&&(this.pageNum++,this.getList())},getScreen(){this.$api.enterpriseallChainAPI({data:{country:this.countryName},method:"GET"}).then((t=>{this.industryChainList=t.result,this.industryChainList.unshift({chainName:"不限",chainId:""})})),this.$api.enterprisegetSearchParamAPI({data:{country:this.countryName},method:"GET"}).then((t=>{this.moreScreenList=t.result}))},changeSelectType(){this.parameterList[0]=this.isEmptyValue(this.showChain),this.parameterList[1]=this.isEmptyValue(this.moreData)},affirm(t){this.showChain=t.chainId,this.showChainName=t.chainName,this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList(),this.changeSelectType()},updatamoreScreen(t){this.moreData=t,this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList(),this.changeSelectType()},screenFn(t){1==t?this.$refs.industryChain.opens():2==t&&this.$refs.moreScreen.opens()},processedObject(t){let e={...t};for(let s in e)""===e[s]?e[s]=[]:e[s]=[e[s]];return e},getList(){let t=this.processedObject(this.moreData),e={chainId:this.showChain,country:this.countryName,pageNum:this.pageNum,pageSize:this.pageSize,parkMd5:this.parkMd5,pageSize:10,...t};this.$api.enterprisePageListAPI({data:e,method:"POST"}).then((t=>{this.total=t.result.total,this.pages=t.result.pages,this.enterpriseList=this.enterpriseList.concat(t.result.records)}))},iconColour:t=>t?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png",isEmptyValue(t){return"string"==typeof t?""===t.trim():"number"==typeof t?0===t:"boolean"==typeof t?!1===t:Array.isArray(t)?t.every(this.isEmptyValue):null!==t&&"object"==typeof t&&Object.values(t).every(this.isEmptyValue)}}},[["render",function(t,C,S,N,w,A){const b=f(e("page-meta"),L),j=y,k=p,I=s("enterpriseList"),P=c,_=s("industryChain"),F=s("moreScreen");return l(),a(h,null,[i(b,{"page-style":"background-color: #FAFCFF"}),r("div",{class:"box"},[r("div",{class:"headline"},[i(j,{src:"https://static.idicc.cn/cdn/aiChat/0.png",class:"headlineImg"}),r("span",null,n(w.industryParkName),1)]),i(k,{class:"optionBox"},{default:o((()=>[r("span",{class:m([w.parameterList[0]?"unselected":"pitchOn","optionItem"]),onClick:C[0]||(C[0]=t=>A.screenFn(1))},[u(n(("不限"==w.showChainName?"所属产业":w.showChainName)||"所属产业")+" ",1),i(j,{src:A.iconColour(w.parameterList[0]),class:"Arrows"},null,8,["src"])],2),r("span",{class:m([w.parameterList[1]?"unselected":"pitchOn","optionItem"]),onClick:C[1]||(C[1]=t=>A.screenFn(2))},[u(" 更多查询 "),i(j,{src:A.iconColour(w.parameterList[1]),class:"Arrows"},null,8,["src"])],2)])),_:1}),i(P,{class:"listBox","scroll-y":"true",onScrolltolower:A.loadMoreData},{default:o((()=>[i(k,{class:"qyList"},{default:o((()=>[i(I,{showChain:w.showChain,enterpriseList:w.enterpriseList},null,8,["showChain","enterpriseList"]),0==w.enterpriseList.length?(l(),d(k,{key:0,class:"nodatabox"},{default:o((()=>[i(j,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])),_:1})):g("",!0)])),_:1})])),_:1},8,["onScrolltolower"]),i(_,{CurrentlySelected:w.showChain,list:w.industryChainList,ref:"industryChain",BottomBlank:!0,appletTab:!0,onAffirm:A.affirm},null,8,["CurrentlySelected","list","onAffirm"]),i(F,{MultipleChoice:!0,ref:"moreScreen",title:"更多查询",moreScreenList:w.moreScreenList,onUpdatamoreScreen:A.updatamoreScreen},null,8,["moreScreenList","onUpdatamoreScreen"])])],64)}],["__scopeId","data-v-6a1a2a5c"]]);export{b as default};
