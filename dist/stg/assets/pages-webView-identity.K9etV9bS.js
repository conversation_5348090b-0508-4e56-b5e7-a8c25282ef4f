import{Z as s,l as e,Y as t,A as a,c as l,w as o,o as i,B as n,d,a1 as c}from"./index-CBCsGYoT.js";import{v as r}from"./webviewUrl.D8mk1f89.js";import{_ as u}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=u({data:()=>({tokenData:"",url:"",isFirstLogin:!0,webV:null}),onLoad(e){s({title:"加载中"})},onShow(){let s=`${r}identity`;this.tokenData=e("token"),this.url=`${s}?token=${this.tokenData}&id=${(new Date).getTime()}`,console.log("identity",this.url)},mounted(){},methods:{loadSuccess(){t()}}},[["render",function(s,e,t,r,u,g){const k=c,v=a("View");return i(),l(v,null,{default:o((()=>[n("div",{class:"skeleton"},[n("div",{class:"chatBg"}),n("div",{class:"skeletonlist"},[n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"})])]),u.url?(i(),l(k,{key:0,src:u.url,onMessage:s.handlePostMessage,title:"身份切换",onLoad:g.loadSuccess},null,8,["src","onMessage","onLoad"])):d("",!0)])),_:1})}],["__scopeId","data-v-a10bdc94"]]);export{g as default};
