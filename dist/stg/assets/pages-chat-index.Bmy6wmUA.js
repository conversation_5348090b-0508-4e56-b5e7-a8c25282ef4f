import{Z as t,s as e,l as s,Y as i,c as o,w as a,i as l,o as n,f as h,d as r,a1 as c}from"./index-HcMwrp5e.js";import{v as d}from"./webviewUrl.D8mk1f89.js";import{_ as u}from"./_plugin-vue_export-helper.BCo6x5W8.js";const w=u({components:{},data:()=>({webviewStyles:{progress:{color:"#FF3333"}},title:"哒达招商",bottomHeight:0,firstLogin:"firstLogin",introduce:"",url:"",tokenData:"",knowledgeId:"",abilityId:"",abilityName:"",loading:!0,type:""}),onLoad(i){console.log(i),t({title:"加载中"}),this.invitationCode=decodeURIComponent(i.scene),e("invitationCode",this.invitationCode),this.abilityId=i.abilityId,this.abilityName=i.abilityName,this.type=i.type,s("newUser"),this.tokenData=s("token");const o=i.isOld;let a=!1,l=!1,n=!1;if(i.showInput&&(l=i.showInput),i.showIntroduce&&(a=i.showIntroduce),i.showDescribe&&(n=i.showDescribe),i.question){const t=i.question;let e=[];i.parameters&&(e=i.parameters),this.jumpToChat(o,t,e,l,a,n)}else this.jumpToChat(o,"",[],l,a,n)},onShow(t){this.firstLogin=s("theFirstTimeLogin")||"firstLogin"},methods:{loadSuccess(){i(),this.loading=!0},jumpToChat(t,e,s,i,o,a){let l="";l="3"===this.abilityId||"33"===this.abilityId?`${d}editor`:"true"===t?`${d}chat`:`${d}aiChat`,this.abilityId&&""!==this.abilityId?(e?this.url=`${l}?token=${this.tokenData||""}&abilityId=${this.abilityId}&abilityName=${(new Date).getTime()}&question=${e}&parameters=${encodeURIComponent(s)}&showInput=${i}&showIntroduce=${o}&showDescribe=${a}`:3==this.type?this.url=`${l}?token=${this.tokenData||""}&abilityId=${this.abilityId}&abilityName=${(new Date).getTime()}&dialogueType=3&showInput=${i}&showIntroduce=${o}&showDescribe=${a}`:this.url=`${l}?token=${this.tokenData||""}&abilityId=${this.abilityId}&abilityName=${(new Date).getTime()}&showInput=${i}&showIntroduce=${o}&showDescribe=${a}`,console.log(this.url,"url")):3==this.type?this.url=`${l}?token=${this.tokenData||""}&dialogueType=3&showInput=${i}&showIntroduce=${o}&showDescribe=${a}`:this.url=`${l}?token=${this.tokenData||""}&showInput=${i}&showIntroduce=${o}&showDescribe=${a}`}}},[["render",function(t,e,s,i,d,u){const w=l,m=c;return n(),o(w,null,{default:a((()=>[h(w,{class:"skeleton"},{default:a((()=>[h(w,{class:"chatBg"}),h(w,{class:"skeletonlist"},{default:a((()=>[h(w,{class:"skeletonHuge"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonSmall"}),h(w,{class:"skeletonShort"})])),_:1})])),_:1}),""!==d.url?(n(),o(m,{key:0,src:d.url,onMessage:t.message,onLoad:u.loadSuccess,"webview-styles":d.webviewStyles},null,8,["src","onMessage","onLoad","webview-styles"])):r("",!0)])),_:1})}],["__scopeId","data-v-d4ccf4c4"]]);export{w as default};
