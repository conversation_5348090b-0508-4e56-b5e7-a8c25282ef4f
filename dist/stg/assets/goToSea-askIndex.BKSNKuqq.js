import{Z as t,s as e,l as s,Y as a,c as i,w as l,i as o,o as n,f as d,d as c,a1 as h}from"./index-HcMwrp5e.js";import{v as r}from"./webviewUrl.D8mk1f89.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";const u=m({components:{},data:()=>({webviewStyles:{progress:{color:"#FF3333"}},title:"哒达招商",bottomHeight:0,firstLogin:"firstLogin",introduce:"",url:"",tokenData:"",knowledgeId:"",abilityId:"",abilityName:"",loading:!0}),onLoad(a){t({title:"加载中"}),this.invitationCode=decodeURIComponent(a.scene),e("invitationCode",this.invitationCode),this.abilityId=a.abilityId,this.abilityName=a.abilityName,s("newUser"),this.tokenData=s("token");const i=a.isOld;if(a.question){const t=a.question,e=a.chainId,s=a.uniCode,l=a.type;this.jumpToChat(i,t,e,s,l)}else this.jumpToChat(i)},onShow(t){this.firstLogin=s("theFirstTimeLogin")||"firstLogin"},methods:{loadSuccess(){console.log(1234),a(),this.loading=!0},jumpToChat(t,e,s,a,i){let l="true"===t?`${r}chat`:`${r}aiChat`;""!==this.tokenData&&this.abilityId&&""!==this.abilityId?(this.url=e?`${l}?token=${this.tokenData}&abilityId=${this.abilityId}&abilityName=${(new Date).getTime()}&question=${e}&uniCode=${a}&chainId=${s}`:"3"==i?`${l}?token=${this.tokenData}&abilityId=${this.abilityId}&abilityName=${(new Date).getTime()}&dialogueType=3`:`${l}?token=${this.tokenData}&abilityId=${this.abilityId}&abilityName=${(new Date).getTime()}`,console.log(this.url)):(this.url=`${l}?token=${this.tokenData}`,console.log(this.url))}}},[["render",function(t,e,s,a,r,m){const u=o,k=h;return n(),i(u,null,{default:l((()=>[d(u,{class:"skeleton"},{default:l((()=>[d(u,{class:"chatBg"}),d(u,{class:"skeletonlist"},{default:l((()=>[d(u,{class:"skeletonHuge"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonSmall"}),d(u,{class:"skeletonShort"})])),_:1})])),_:1}),""!==r.url?(n(),i(k,{key:0,src:r.url,onMessage:t.message,onLoad:m.loadSuccess,"webview-styles":r.webviewStyles},null,8,["src","onMessage","onLoad","webview-styles"])):c("",!0)])),_:1})}],["__scopeId","data-v-25855879"]]);export{u as default};
