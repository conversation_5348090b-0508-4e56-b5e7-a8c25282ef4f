import{o as e,c as t,w as s,f as a,B as i,a as n,b as o,e as r,g as c,t as l,F as h,d as p,i as d,l as m,s as u,m as f,a0 as g,p as y,A as C,z as v,n as k,H as L,I as w,C as _,ar as I,S as b}from"./index-HcMwrp5e.js";import{_ as A}from"./uni-popup.BdZPMDVN.js";import{r as N}from"./uni-app.es.DFp0WTX7.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{m as B}from"./mapList.BhjoS9MT.js";import{m as P}from"./moreScreen.y5u7uXHA.js";import{l as $,g as x,i as R}from"./utils.61Hi-B7M.js";import"./uni-transition.CHOJlBbg.js";const V=S({components:{mapList:B,mapList2:S({props:{active:{type:Number,default:null},token:{type:String,default:null},cityName:{type:String,default:null},BottomBlank:{type:Boolean,default:!1}},data:()=>({province:[],showChain:"",citys:[],area:[],type:1,check:{province:{name:""},citys:{name:""},area:{name:""}},param:{}}),created(){this.province.length},methods:{clear(){this.citys=[],this.area=[],this.check={province:{name:""},citys:{name:""},area:{name:""}}},affirm(){this.$emit("affirm",this.check)},init(){0==this.province.length&&this.mapInfo()},mapInfo(e){this.showChain=e,this.$api.countByDivisionAPI({data:{chainId:this.showChain,divisionLevel:1,divisionCode:""},method:"GET"}).then((e=>{this.province=e.result}))},mapList(e,t){this.$api.countByDivisionAPI({data:{chainId:this.showChain,divisionLevel:2,divisionCode:this.check.province.code},method:"GET"}).then((e=>{this.citys=e.result}))},provinceEvent(e){this.check.province.name=e.name,this.check.province.code=e.code,this.check.province.notAncestorCodes=e.notAncestorCodes,e.notAncestorCodes?this.citys=[]:this.mapList(2,e.id),this.area=[],this.check.citys.name="",this.check.citys.code="",this.check.area.name="",this.check.area.code=""},citysEvent(e){this.check.citys.name=e.name,this.check.citys.code=e.code,this.check.citys.notAncestorCodes=e.notAncestorCodes}}},[["render",function(m,u,f,g,y,C){const v=d;return e(),t(v,{class:"region"},{default:s((()=>[a(v,{class:"head"},{default:s((()=>[i("span",{class:"txt"},"请选择")])),_:1}),a(v,{class:"region-con"},{default:s((()=>[a(v,{class:"region-list province"},{default:s((()=>[(e(!0),n(h,null,o(y.province,((i,n)=>(e(),t(v,{class:r(["item",y.check.province.name==i.name?"on":""]),onClick:e=>C.provinceEvent(i),key:n},{default:s((()=>[a(v,{class:"txt"},{default:s((()=>[c(l(i.name)+"("+l(i.value)+")",1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),a(v,{class:r(["region-list citys",y.check.province.name?"on":""])},{default:s((()=>[(e(!0),n(h,null,o(y.citys,((i,n)=>(e(),t(v,{class:r(["item",y.check.citys.name==i.name?"on":""]),onClick:e=>C.citysEvent(i),key:n},{default:s((()=>[a(v,{class:"txt"},{default:s((()=>[c(l(i.name)+"("+l(i.value)+")",1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1},8,["class"])])),_:1}),a(v,{class:"btnbox"},{default:s((()=>[a(v,{onClick:C.clear,class:"clear"},{default:s((()=>[c("重置")])),_:1},8,["onClick"]),a(v,{class:"affirm",onClick:C.affirm},{default:s((()=>[c("确认")])),_:1},8,["onClick"])])),_:1}),f.BottomBlank?(e(),n("div",{key:0,style:{height:"160rpx"}})):p("",!0)])),_:1})}],["__scopeId","data-v-baf5aaa4"]]),moreScreen:P},data:()=>({statusBarHeight:m("statusBarHeight"),searchForm:{model:[],strategy:[],code:""},cityName:"",listicon:$,pageNum:1,pages:0,baseInfo:{},inStrategyList:[],enterpriseList:[],total:0,token:"",showNodata:!1,isLoading:!1,dataInfo:{},bottomBlank:!1,moreData:{},parameterList:[!0,!0,!0],enterpriseRegionCode:"",ancestorRegionCode:"",divisionCode:"",notAncestorCodes:[]}),onLoad(e){this.token=m("token");let t=(null==e?void 0:e.id)||"";x("token")&&(this.token=x("token"),u("token",this.token)),x("id")&&(t=x("id")),this.getDel(t),this.token?(this.getSearch(),this.getChainId()):this.showNodata=!0},methods:{isWithinLastWeek:R,getChainId(){this.$api.choosechainAPI({method:"get"}).then((e=>{this.showChain=e.result.chain2Id,this.getList(),this.$nextTick((()=>{this.$refs.mapList2.mapInfo(this.showChain)}))}))},getDel(e){let t={id:e};this.$api.capabilityPoolDetailAPI({data:t}).then((e=>{this.dataInfo=e.result}))},getSearch(){this.$api.getSearchParamAPI({method:"GET"}).then((e=>{this.inStrategyList=e.result}))},getList(){if(this.isLoading)return;this.isLoading=!0;let e={pageSize:10,pageNum:this.pageNum,chainId:this.showChain,...this.moreData};this.enterpriseRegionCode&&(e.enterpriseRegionCode=this.enterpriseRegionCode),this.ancestorRegionCode&&(e.ancestorRegionCode=this.ancestorRegionCode),this.notAncestorCodes&&(e.notAncestorCodes=this.notAncestorCodes),this.$api.ancestorListAPI({data:e,method:"post"}).then((e=>{this.enterpriseList=this.enterpriseList.concat(e.result.data||[]),this.pages=Math.ceil(e.result.total/10),this.total=e.result.total})).finally((()=>{this.isLoading=!1,this.showNodata=!0}))},scrolltolowerFn(){this.isLoading||this.pages>this.pageNum&&(this.pageNum++,this.getList())},detailpage(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{id:e.enterpriseId||e.id,iconTypeid:e.enterpriseIconLabelId,enterpriseName:e.enterpriseName,enterpriseLabelIds:JSON.stringify(this.enterpriseLabelIds||[])},path:"industryDetail"}))},goBack(){},goDetail(e){let t={enterpriseName:e.enterpriseName,uniCode:e.uniCode,enterpriseId:e.enterpriseId,recommendRegionCode:e.recommendRegionCode||""};this.$api.reportGenerateAPI({data:t,method:"post"}).then((t=>{var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"changePath",value:{url:"previewPdf",name:"报告预览",params:{reportId:t.result,type:"pdf",enterpriseName:e.enterpriseName+"招商策略报告"}},path:"webViewPage"}))}))},regionFn(){var e,t;let s="全国";return null!=(null==(e=this.baseInfo)?void 0:e.area)&&0!=(null==(t=this.baseInfo)?void 0:t.area.length)?s=this.baseInfo.area[0]:this.baseInfo.city?s=this.baseInfo.city:this.baseInfo.province&&(s=this.baseInfo.province),s.length>4&&(s=s.substring(0,3)+"..."),s},iconColour:e=>e?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png",screenFn(e){this.token||f({url:"/pages/login/index"}),1==e?(this.$refs.popup2.open("bottom"),this.$nextTick((()=>{this.$refs.mapList2?this.$refs.mapList2.init():console.warn("mapList2组件未挂载打开时")}))):2==e?this.$refs.clueModelPop.open("bottom"):3==e&&this.$refs.inStrategyPop.opens()},affirm1(e){this.ancestorRegionCode=e.area.code?e.area.code:e.citys.code?e.citys.code:e.province.code||"",this.notAncestorCodes=e.area.notAncestorCodes?e.area.notAncestorCodes:e.citys.notAncestorCodes?e.citys.notAncestorCodes:e.province.notAncestorCodes||[],this.$refs.popup2.close();let t=e.province.name?e.province.name+"/":"",s=e.citys.name?e.citys.name+"/":"",a=e.area.name?e.area.name+"/":"";this.cityName=t+s+a,this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList(this.showChain),this.changeSelectType()},affirm2(e){this.enterpriseRegionCode=e.area.code?e.area.code:e.citys.code?e.citys.code:e.province.code||"",this.$refs.clueModelPop.close();let t=e.province.name?e.province.name+"/":"",s=e.citys.name?e.citys.name+"/":"",a=e.area.name?e.area.name+"/":"";this.cityName=t+s+a,this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList(this.showChain),this.changeSelectType()},affirm3(e){this.moreData=e,this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList(this.showChain),this.changeSelectType()},changeSelectType(){this.parameterList[0]=this.isEmptyValue(this.ancestorRegionCode),this.parameterList[1]=this.isEmptyValue(this.enterpriseRegionCode),this.parameterList[2]=this.isEmptyValue(this.moreData)},isEmptyValue(e){return"string"==typeof e?""===e.trim():"number"==typeof e?0===e:"boolean"==typeof e?!1===e:Array.isArray(e)?e.every(this.isEmptyValue):null!==e&&"object"==typeof e&&Object.values(e).every(this.isEmptyValue)},copyPhone(e){g({data:e,success:function(){y({title:"复制成功",icon:"none"})}})}}},[["render",function(m,u,f,g,y,S){const B=_,P=d,$=I,x=b,R=C("mapList"),V=N(v("uni-popup"),A),E=C("mapList2"),j=C("moreScreen");return e(),n(h,null,[a(P,null,{default:s((()=>[a(P,{class:"topHeader",style:k(`top: ${y.statusBarHeight}px`)},{default:s((()=>[a(P,{class:"header"},{default:s((()=>[a(B,{src:y.dataInfo.iconPath,class:"icon"},null,8,["src"]),a(P,{class:"airui"},{default:s((()=>[a(P,{class:"span1"},{default:s((()=>[c(l(y.dataInfo.name),1)])),_:1}),a(P,{class:"span2"},{default:s((()=>[c("来自：艾瑞数云")])),_:1})])),_:1})])),_:1}),a(P,{class:"headertext"},{default:s((()=>[c(l(y.dataInfo.content),1)])),_:1}),a(P,{class:"optionBox"},{default:s((()=>[i("span",{class:r([y.parameterList[0]?"unselected":"pitchOn","optionItem"]),onClick:u[0]||(u[0]=e=>S.screenFn(1))},[c(" 高管籍贯 "),a(B,{src:S.iconColour(y.parameterList[0]),class:"Arrows"},null,8,["src"])],2),i("span",{class:r([y.parameterList[1]?"unselected":"pitchOn","optionItem"]),onClick:u[1]||(u[1]=e=>S.screenFn(2))},[c(" 企业注册地 "),a(B,{src:S.iconColour(y.parameterList[1]),class:"Arrows"},null,8,["src"])],2),i("span",{class:r([y.parameterList[2]?"unselected":"pitchOn","optionItem"]),onClick:u[2]||(u[2]=e=>S.screenFn(3))},[c(" 更多查询 "),a(B,{src:S.iconColour(y.parameterList[2]),class:"Arrows"},null,8,["src"])],2)])),_:1}),a(P,{class:"total"},{default:s((()=>[c(" 共 "),i("span",{class:"totalSpan2"},l(y.total),1),c(" 家企业 ")])),_:1})])),_:1},8,["style"]),a(x,{class:"listBox",onScrolltolower:S.scrolltolowerFn,onScroll:m.scroll,style:k(`top: ${y.statusBarHeight}px`),"scroll-y":"true"},{default:s((()=>[a(P,null,{default:s((()=>[(e(!0),n(h,null,o(y.enterpriseList,((r,d)=>(e(),t(P,{class:"card",key:d},{default:s((()=>[a(P,{class:"cadHeader"},{default:s((()=>{var n;return[a(B,{src:null==(n=y.listicon[0])?void 0:n.icon,class:"icon2"},null,8,["src"]),i("span",{onClick:e=>S.detailpage(r),class:"enterpriseName"},[c(l((null==r?void 0:r.enterpriseName)||"-")+" ",1),S.isWithinLastWeek(null==r?void 0:r.enterpriseUpdateDate)?(e(),t($,{key:0,class:"new"},{default:s((()=>[c("新")])),_:1})):p("",!0)],8,["onClick"])]})),_:2},1024),a(P,{class:"contentBox"},{default:s((()=>[(e(!0),n(h,null,o(null==r?void 0:r.refs,((i,o)=>L((e(),t(P,{style:{"margin-bottom":"16rpx"},key:o},{default:s((()=>[i.name&&"-"!=i.name?(e(),n("p",{key:0,class:"contentSpan"},[c(l("关联人"+(1==r.refs.length?"":`${o+1}`)+"：")+" ",1),a($,{class:"listValue"},{default:s((()=>[c(l(null==i?void 0:i.name),1)])),_:2},1024)])):p("",!0),i.jobTitle&&"-"!=i.jobTitle?(e(),n("p",{key:1,class:"contentSpan"},[c("职务： "),a($,{class:"listValue"},{default:s((()=>[c(l(null==i?void 0:i.jobTitle),1)])),_:2},1024)])):p("",!0),i.region&&"-"!=i.region?(e(),n("p",{key:2,class:"contentSpan"},[c("籍贯： "),a($,{class:"listValue"},{default:s((()=>[c(l(null==i?void 0:i.region),1)])),_:2},1024)])):p("",!0)])),_:2},1024)),[[w,null==r?void 0:r.refs]]))),128)),r.enterpriseAddress&&"-"!=r.enterpriseAddress?(e(),n("p",{key:0,class:"contentSpan"},[c("注册地址："),a($,{class:"listValue"},{default:s((()=>[c(l(null==r?void 0:r.enterpriseAddress),1)])),_:2},1024)])):p("",!0),r.enterpriseContact&&"-"!=r.enterpriseContact?(e(),n("p",{key:1,class:"contentSpan"},[c("联系方式： "),a($,{class:"listValue"},{default:s((()=>[c(l(null==r?void 0:r.enterpriseContact),1)])),_:2},1024),a($,{class:"copy",onClick:e=>S.copyPhone(null==r?void 0:r.enterpriseContact)},{default:s((()=>[c("复制")])),_:2},1032,["onClick"])])):p("",!0),r.enterpriseChain&&"-"!=r.enterpriseChain?(e(),n("p",{key:2,class:"contentSpan"},[c("所属产业链： "),a($,{class:"listValue"},{default:s((()=>[c(l(null==r?void 0:r.enterpriseChain),1)])),_:2},1024)])):p("",!0)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1},8,["onScrolltolower","onScroll","style"]),0==y.enterpriseList.length&&y.showNodata?(e(),n("div",{key:0,class:"nodatabox"},[a(B,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),i("span",{class:"span"},"暂无内容")])):p("",!0)])),_:1}),a(V,{ref:"clueModelPop","background-color":"#fff"},{default:s((()=>[a(R,{ref:"mapList",cityName:y.cityName,unrestricted:!0,onAffirm:S.affirm2},null,8,["cityName","onAffirm"])])),_:1},512),a(V,{ref:"popup2","background-color":"#fff"},{default:s((()=>[a(E,{ref:"mapList2",showChain:m.showChain,divisionCode:y.divisionCode,cityName:y.cityName,unrestricted:!0,onAffirm:S.affirm1},null,8,["showChain","divisionCode","cityName","onAffirm"])])),_:1},512),a(j,{ref:"inStrategyPop",BottomBlank:y.bottomBlank,title:"更多查询",moreScreenList:y.inStrategyList,onUpdatamoreScreen:S.affirm3},null,8,["BottomBlank","moreScreenList","onUpdatamoreScreen"])],64)}],["__scopeId","data-v-50461818"]]);export{V as default};
