import{l as e,s as t,p as s,q as i,z as n,o as a,c as l,w as r,a as o,b as c,n as d,f as p,B as u,t as h,d as f,D as g,F as m,g as y,i as I,C as L}from"./index-HcMwrp5e.js";import{_}from"./uni-tooltip.Bpz4R-Ep.js";import{r as b}from"./uni-app.es.DFp0WTX7.js";import{l as v,g as N,i as x}from"./utils.61Hi-B7M.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";const k=S({props:{Jurisdiction:{type:Boolean,default:!1},showChain:{type:String,default:""},enterpriseLabelIds:{type:Array,default:()=>[]},enterpriseList:{type:Array,default:()=>[]}},data:()=>({enterpriseLists:[],clientVersion:"",listicon:v,itId:"",clueId:"",itName:"",taskstate:"",IdentityType:e("userIdentityType"),orgName:e("orgName"),isDefault:e("isDefault"),Successmessage:{},assign:!1}),watch:{enterpriseList(){this.enterpriseLists=JSON.parse(JSON.stringify(this.enterpriseList))}},created(){this.enterpriseLists=JSON.parse(JSON.stringify(this.enterpriseList)),N("clientVersion")&&(this.clientVersion=N("clientVersion"),t("clientVersion",this.clientVersion))},methods:{isWithinLastWeek:x,changAttention(e,t,i){let n={enterpriseUniCode:e.unifiedSocialCreditCode,status:!t};this.$api.collectionAPI_investment({data:n,method:"post"}).then((e=>{s(t?{title:"取消收藏成功！",icon:"none"}:{title:"收藏成功！已添加至'招商管理-我的收藏'",icon:"none"}),this.enterpriseLists[i].isCollect=!t}))},getMaxLabelCount(e){let t=0,s=0;for(let i=0;i<e.length&&(t+=e[i].length,!(t>20));i++)s=i+1;return s},itemStyle(e){return!this.Jurisdiction&&e>1?"filter: blur(3px)":""},eventfree(){},async detailpage(e){var t;if(!this.Jurisdiction)return this.$emit("openJurisdictionPop");const s=this.clientVersion;s?null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{id:e.enterpriseId||e.id,iconTypeid:e.enterpriseIconLabelId,enterpriseName:e.enterpriseName,enterpriseLabelIds:JSON.stringify(this.enterpriseLabelIds||[]),clientVersion:s,showChain:this.showChain},path:"industryDetail"})):i({url:`/pages/newMapEnterprise/components/enterprise?id=${e.enterpriseId||e.id}&iconTypeid=${e.enterpriseIconLabelId}&showChain=${this.showChain}&enterpriseName=${e.enterpriseName}&enterpriseLabelIds=${JSON.stringify(this.enterpriseLabelIds||[])}`})}}},[["render",function(e,t,s,i,v,N){const x=I,S=L,k=b(n("uni-tooltip"),_);return a(),l(x,null,{default:r((()=>[(a(!0),o(m,null,c(v.enterpriseLists,((e,t)=>(a(),l(x,{style:d(N.itemStyle(t)),class:"firm",key:t},{default:r((()=>[p(x,{style:{display:"flex"},onClick:t=>N.detailpage(e)},{default:r((()=>[u("img",{src:null==e.enterpriseIconLabelId?v.listicon[0].icon:v.listicon[e.enterpriseIconLabelId].icon,alt:"",class:"iconImg"},null,8,["src"]),p(x,{class:"texts"},{default:r((()=>{var s;return[p(x,{class:"enterpriseNameBox"},{default:r((()=>[p(x,{class:"enterpriseName"},{default:r((()=>[u("span",null,h(e.enterpriseName),1),N.isWithinLastWeek(e.lastModifyDate)?(a(),o("span",{key:0,class:"upBgc"},"新")):f("",!0)])),_:2},1024),1==v.IdentityType&&!v.isDefault&&v.orgName?(a(),l(S,{key:0,onClick:g((s=>N.changAttention(e,null==e?void 0:e.isCollect,t)),["stop"]),class:"attention",src:(null==e?void 0:e.isCollect)?"/static/AboutAi/attention.png":"/static/AboutAi/noAttention.png"},null,8,["onClick","src"])):f("",!0)])),_:2},1024),(null==(s=e.enterpriseLabelNames)?void 0:s.length)>=1?(a(),l(x,{key:0,class:"enterpriseLabeltag"},{default:r((()=>[e.enterpriseLabelNames?(a(),l(x,{key:0,class:"enterpriseLabel"},{default:r((()=>[(a(!0),o(m,null,c(e.enterpriseLabelNames.slice(0,N.getMaxLabelCount(e.enterpriseLabelNames)),((e,t)=>(a(),l(x,{key:t},{default:r((()=>[u("div",{class:"tagBox"},h(e),1)])),_:2},1024)))),128)),e.enterpriseLabelNames.length>N.getMaxLabelCount(e.enterpriseLabelNames)?(a(),o("div",{key:0,class:"tagBox"}," ... ")):f("",!0)])),_:2},1024)):f("",!0)])),_:2},1024)):f("",!0),p(x,{class:"recommendIcon"},{default:r((()=>[p(x,{class:"expandIcon"}),p(x,null,{default:r((()=>[y(" 快速成长指数"),u("span",{style:{"margin-left":"8rpx",color:"#34C759"}},h((null==e?void 0:e.growthIndex)&&"0.0"!=(null==e?void 0:e.growthIndex)?null==e?void 0:e.growthIndex:"-"),1)])),_:2},1024)])),_:2},1024),p(x,{class:"recommendIcon"},{default:r((()=>[p(x,{class:"increaseIcon"}),p(x,null,{default:r((()=>[y(" 扩张意愿指数 "),u("span",{style:{"margin-left":"8rpx",color:"#FF9500"}},h((null==e?void 0:e.expansionIndex)&&"0.0"!=(null==e?void 0:e.expansionIndex)?null==e?void 0:e.expansionIndex:"-"),1)])),_:2},1024)])),_:2},1024),p(x,{class:"recommendIcon"},{default:r((()=>[p(x,{class:"countryIcon"}),u("span",null,[y(h(e.province),1),e.province!=e.city?(a(),o("span",{key:0},h(e.city),1)):f("",!0),y(h(e.area),1)])])),_:2},1024)]})),_:2},1024)])),_:2},1032,["onClick"]),e.historyRecommend||null!=e.clueDealState?(a(),l(x,{key:0,class:"footer"},{default:r((()=>[e.historyRecommend?(a(),l(k,{key:0,content:"推荐时间:"+e.historyRecommendDate,placement:"top"},{default:r((()=>[p(x,{class:"histroy"},{default:r((()=>[p(S,{class:"footerBgc",src:"https://static.idicc.cn/cdn/zhaoShang/历史推荐.svg"}),y(" 历史推荐 ")])),_:1})])),_:2},1032,["content"])):f("",!0),null!=e.clueDealState&&0==e.clueDealState?(a(),l(k,{key:1,content:"跟进人:"+e.beAssignPerson,placement:"top"},{default:r((()=>[p(x,{class:"pendding"},{default:r((()=>[p(S,{class:"footerBgc",src:"https://static.idicc.cn/cdn/zhaoShang/跟进中.svg"}),y(" 跟进中 ")])),_:1})])),_:2},1032,["content"])):f("",!0),null!=e.clueDealState&&2==e.clueDealState?(a(),l(x,{key:2,class:"error"},{default:r((()=>[p(S,{class:"footerBgc",src:"https://static.idicc.cn/cdn/zhaoShang/error.svg"}),y(" 签约失败 ")])),_:1})):f("",!0),null!=e.clueDealState&&1==e.clueDealState?(a(),l(x,{key:3,class:"success"},{default:r((()=>[p(S,{class:"footerBgc",src:"https://static.idicc.cn/cdn/zhaoShang/success.svg"}),y(" 签约成功 ")])),_:1})):f("",!0)])),_:2},1024)):f("",!0)])),_:2},1032,["style"])))),128))])),_:1})}],["__scopeId","data-v-c5f3c585"]]);export{k as e};
