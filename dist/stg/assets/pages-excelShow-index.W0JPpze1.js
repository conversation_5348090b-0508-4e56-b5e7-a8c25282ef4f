import{l as e,U as t,a6 as o,a4 as a,$ as s,p as n,_ as l,z as i,A as d,a as u,f as c,w as r,F as p,i as h,o as m,c as f,g as w,t as v,d as x}from"./index-CBCsGYoT.js";import{_}from"./page-meta.BCoSkkXs.js";import{r as g}from"./uni-app.es.CZb2JZWI.js";import{t as D}from"./index.D5F8338M.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const A=P({components:{tabBar:D},data:()=>({url:"",token:"",text:""}),onLoad(t){this.token=e("token"),this.text="文件加载中...",(null==t?void 0:t.url)?this.DownloadAll(t):"editor"===(null==t?void 0:t.type)?this.DownloadEditor(t):"DownloadPath"===(null==t?void 0:t.type)?this.DownloadFn(t):"deriveExcel"===(null==t?void 0:t.type)?this.DownloadFn2(t):this.Download(t)},onHide(e){this.text="",t({delta:1})},methods:{DownloadAll(e){let t=decodeURI(null==e?void 0:e.url).split("_");o({url:`${a}/ai/oss/file/preview`,timeout:5e5,data:{id:t[0],contentId:null==e?void 0:e.contentId},responseType:"arraybuffer",header:{token:this.token,"content-type":"application/json"},method:"POST",success:o=>{if(200===o.statusCode){let a=wx.env.USER_DATA_PATH+`/${t[1]}`,l=o.data;wx.getFileSystemManager().writeFile({filePath:a,data:l,encoding:"binary",success:t=>{s({filePath:a,showMenu:"SVIP"===e.vip,success:function(e){this.text="文件预览"},fail(e){n({icon:"none",mask:!0,title:"失败请重新下载",duration:3e3})}})}})}},fail:e=>{}})},Download(e){let t=decodeURI(null==e?void 0:e.id).split("_");var o=wx.env.USER_DATA_PATH+`/${null==t?void 0:t[1]}.xlsx`;l({method:"get",url:`${a}/ai/llm/tableDownload?id=${null==t?void 0:t[0]}`,timeout:5e5,header:{token:this.token,"content-type":"application/json"},filePath:o,success:e=>{200===e.statusCode&&s({filePath:o,showMenu:!0,success:function(e){}})},fail:e=>{console.log(e,"err"),n({icon:"none",mask:!0,title:"失败请重新下载",duration:3e3})}})},DownloadFn(e){var t=wx.env.USER_DATA_PATH+`/${null==e?void 0:e.DownloadName}.pdf`;l({method:"get",url:decodeURIComponent(null==e?void 0:e.DownloadPath),timeout:5e5,filePath:t,success:e=>{200===e.statusCode&&s({filePath:t,showMenu:!1,success:function(e){}})},fail:e=>{n({icon:"none",mask:!0,title:"失败请重新下载",duration:3e3})}})},DownloadFn2(e){var t=wx.env.USER_DATA_PATH+`/${null==e?void 0:e.reportName}.xlsx`;l({method:"get",url:decodeURIComponent(null==e?void 0:e.ossUrl),timeout:5e5,filePath:t,success:e=>{200===e.statusCode&&s({filePath:t,showMenu:!0,success:function(e){}})},fail:e=>{n({icon:"none",mask:!0,title:"失败请重新下载",duration:3e3})}})},DownloadEditor(e){let t=decodeURI(null==e?void 0:e.id).split("_");this.$api.getExampleById({method:"get",data:{exampleId:t[0]}}).then((e=>{"SUCCESS"===e.code&&l({url:e.result,success:function(e){const t=e.tempFilePath;s({filePath:t,success:function(e){console.log("打开文档成功")}})}})}))}}},[["render",function(e,t,o,a,s,n){const l=g(i("page-meta"),_),D=d("tabBar"),P=h;return m(),u(p,null,[c(l,{"page-style":"background-color: #FAFCFF"}),c(P,{class:"page-section page-section-gap"},{default:r((()=>[c(D,{title:"文件预览"}),c(P,{class:"webview"},{default:r((()=>[s.text?(m(),f(P,{key:0,class:"loading"},{default:r((()=>[w(v(s.text),1)])),_:1})):x("",!0)])),_:1})])),_:1})],64)}],["__scopeId","data-v-884129e3"]]);export{A as default};
