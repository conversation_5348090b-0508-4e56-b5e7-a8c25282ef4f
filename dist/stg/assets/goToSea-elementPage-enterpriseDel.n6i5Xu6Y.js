import{U as e,z as t,a as s,f as n,w as a,F as i,i as l,o as r,c as o,d as c,B as d,g as u,t as f,b as p,n as h,e as g,h as I,C as m}from"./index-HcMwrp5e.js";import{_ as T}from"./page-meta.X-Lr6csD.js";import{r as v}from"./uni-app.es.DFp0WTX7.js";import{_ as S}from"./uni-tag.nXvhaS4z.js";import{t as _}from"./index.B6Rbewql.js";import{l as y}from"./utils.61Hi-B7M.js";import{_ as k}from"./right.D4iebSn6.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const x=N({components:{tabBar:_},data:()=>({showString:!1,listicon:y,enterpriseInfo:{chainDTOS:[]},selectedTagIndex:0,selectedTagIndex2:0,show:!1,productNamesLength:0,StringTotal:0,title:"企业详情",showChain:""}),onLoad(e){this.showChain=e.showChain,this.getdetail(e.id)},computed:{currentProductNames(){var e,t,s;return(null==(s=null==(t=null==(e=this.enterpriseInfo)?void 0:e.chainDTOS[this.selectedTagIndex])?void 0:t.chainNodeList[this.selectedTagIndex2])?void 0:s.productNames)||[]}},watch:{selectedTagIndex(){this.updateProductNamesLength()},selectedTagIndex2(){this.updateProductNamesLength()},currentProductNames(e){this.productNamesLength=e.length}},methods:{Scalefiltering(e){let t="";return"Large"==e?t="大型":"Medium"==e?t="中型":"Small"==e?t="小型":"Micro"==e&&(t="微型"),t},goBack(){e({delta:1})},getTotalStringLength(e){let t=0;return e.forEach((e=>{t+=e.length})),t},updateProductNamesLength(){this.$nextTick((()=>{this.showString=!1;const e=this.currentProductNames;this.productNamesLength=(null==e?void 0:e.length)||0;let t=this.getTotalStringLength(e);console.log(t),this.StringTotal=t}))},selectTag(e){this.selectedTagIndex=e},pitchsecond(e){this.selectedTagIndex2=e},async getdetail(e){let t={enterpriseMd5:e,chainId:this.showChain};this.$api.getEnterpiseDetailAPI({data:t,method:"GET"}).then((e=>{this.enterpriseInfo=e.result,this.showChain&&(this.enterpriseInfo.chainDTOS=this.enterpriseInfo.chainDTOS.filter((e=>e.chainId==this.showChain))),this.updateProductNamesLength(),this.show=!0}))}}},[["render",function(e,_,y,N,x,D){const w=v(t("page-meta"),T),F=I,b=l,L=v(t("uni-tag"),S),C=m;return r(),s(i,null,[n(w,{"page-style":"background-color: #FAFCFF"}),n(b,null,{default:a((()=>[x.show?(r(),o(b,{key:0,class:"enterpriceDetail"},{default:a((()=>{var e,t,l;return[n(b,{class:"infoBox"},{default:a((()=>[2==x.enterpriseInfo.investStatus?(r(),s("div",{key:0,class:"enterpriseTag zhong"}," 中资 ")):c("",!0),1==x.enterpriseInfo.investStatus?(r(),s("div",{key:1,class:"enterpriseTag wai"}," 外资 ")):c("",!0),null!=x.enterpriseInfo.investStatus&&0==x.enterpriseInfo.investStatus?(r(),s("div",{key:2,class:"enterpriseTag ben"}," 本地 ")):c("",!0),n(b,{class:"topBox"},{default:a((()=>[d("img",{src:1==x.enterpriseInfo.listedStatus?x.listicon[1].icon:x.listicon[0].icon,alt:"",class:"iconImg"},null,8,["src"]),n(F,{selectable:"true",class:"enterpriseName"},{default:a((()=>[u(f(x.enterpriseInfo.englishName||x.enterpriseInfo.enterpriseName),1)])),_:1})])),_:1}),n(b,{class:"DelBox"},{default:a((()=>[n(b,{class:"delTitle"},{default:a((()=>[u("基本信息")])),_:1}),x.enterpriseInfo.enterpriseName?(r(),o(b,{key:0,class:"infoItem"},{default:a((()=>[u(" 名称"),n(F,{selectable:"true",class:"itemDel"},{default:a((()=>[u(f(x.enterpriseInfo.enterpriseName),1)])),_:1})])),_:1})):c("",!0),x.enterpriseInfo.registrationNumber?(r(),o(b,{key:1,class:"infoItem"},{default:a((()=>[u(" 注册号"),d("span",{class:"itemDel"},f(x.enterpriseInfo.registrationNumber),1)])),_:1})):c("",!0),x.enterpriseInfo.registerDate?(r(),o(b,{key:2,class:"infoItem"},{default:a((()=>[u(" 成立日期"),d("span",{class:"itemDel"},f(x.enterpriseInfo.registerDate),1)])),_:1})):c("",!0),x.enterpriseInfo.enterpriseType?(r(),o(b,{key:3,class:"infoItem"},{default:a((()=>[u(" 公司类型"),d("span",{class:"itemDel"},f(x.enterpriseInfo.enterpriseType),1)])),_:1})):c("",!0),x.enterpriseInfo.registeredCapital?(r(),o(b,{key:4,class:"infoItem"},{default:a((()=>[u(" 注册资本"),d("span",{class:"itemDel"},f(x.enterpriseInfo.registeredCapital),1)])),_:1})):c("",!0),x.enterpriseInfo.nationalStandardIndustrySmall?(r(),o(b,{key:5,class:"infoItem"},{default:a((()=>[u(" 所属行业"),d("span",{class:"itemDel"},f(x.enterpriseInfo.nationalStandardIndustrySmall),1)])),_:1})):c("",!0),x.enterpriseInfo.scale?(r(),o(b,{key:6,class:"infoItem"},{default:a((()=>[u(" 企业规模"),d("span",{class:"itemDel"},f(D.Scalefiltering(x.enterpriseInfo.scale)),1)])),_:1})):c("",!0),x.enterpriseInfo.legalPerson?(r(),o(b,{key:7,class:"infoItem"},{default:a((()=>[u(" 法人"),d("span",{class:"itemDel"},f(x.enterpriseInfo.legalPerson),1)])),_:1})):c("",!0),n(b,{class:"infoItem"},{default:a((()=>[u(" 上市情况"),d("span",{class:"itemDel"},f(1==x.enterpriseInfo.listedStatus?"已上市":"未上市"),1)])),_:1}),x.enterpriseInfo.province||x.enterpriseInfo.city||x.enterpriseInfo.area?(r(),o(b,{key:8,class:"infoItem"},{default:a((()=>{var e,t,s;return[u(" 行政区划"),d("span",{class:"itemDel"},f([null==(e=x.enterpriseInfo)?void 0:e.province,null==(t=x.enterpriseInfo)?void 0:t.city,null==(s=x.enterpriseInfo)?void 0:s.area].filter((e=>null!=e)).join("")||"--"),1)]})),_:1})):c("",!0),x.enterpriseInfo.enterpriseAddress?(r(),o(b,{key:9,class:"infoItem"},{default:a((()=>[u(" 企业地址"),d("span",{class:"itemDel"},f(x.enterpriseInfo.enterpriseAddress),1)])),_:1})):c("",!0),x.enterpriseInfo.mobile?(r(),o(b,{key:10,class:"infoItem"},{default:a((()=>[u(" 联系方式"),d("span",{class:"itemDel"},f(x.enterpriseInfo.mobile),1)])),_:1})):c("",!0)])),_:1})])),_:1}),(null==(e=x.enterpriseInfo)?void 0:e.chainDTOS)&&0!=(null==(l=null==(t=x.enterpriseInfo)?void 0:t.chainDTOS)?void 0:l.length)?(r(),o(b,{key:0,class:"tag-p"},{default:a((()=>{var e,t,l,d,I,m,T,v;return[n(b,{class:"tag-container"},{default:a((()=>{var e;return[(r(!0),s(i,null,p(null==(e=x.enterpriseInfo)?void 0:e.chainDTOS,((e,t)=>(r(),o(b,{onClick:e=>D.selectTag(t),class:"tag-title",style:h({"background-color":x.selectedTagIndex==t?"#fff":"#f5f5f5",color:x.selectedTagIndex==t?"#417FFF":"#72787d","font-weight":x.selectedTagIndex==t?"600":"500"}),key:t},{default:a((()=>[null!==e.chainName?(r(),o(b,{key:0,class:"chainName"},{default:a((()=>[u(f(e.chainName.replace("产业金脑·","")),1)])),_:2},1024)):c("",!0)])),_:2},1032,["onClick","style"])))),128))]})),_:1}),n(b,{class:"tag-it"},{default:a((()=>{var e;return[(r(!0),s(i,null,p(null==(e=x.enterpriseInfo)?void 0:e.chainDTOS,((e,t)=>(r(),o(b,{key:t},{default:a((()=>[x.selectedTagIndex==t?(r(),o(b,{key:0,class:"nodeNamesTag"},{default:a((()=>[(r(!0),s(i,null,p(e.chainNodeList,((e,t)=>(r(),o(b,{onClick:e=>D.pitchsecond(t),class:"tag-c",key:t},{default:a((()=>[x.selectedTagIndex2==t?(r(),o(L,{key:0,text:e.nodeName,"custom-style":"background-color: #3370FF; border-color: #3370FF; color: #FFFFFF ;font-weight: 700;"},null,8,["text"])):(r(),o(L,{key:1,text:e.nodeName,"custom-style":"background-color: #eff4ff; border-color: #eff4ff; color: #417FFF ;font-weight: 700;"},null,8,["text"]))])),_:2},1032,["onClick"])))),128))])),_:2},1024)):c("",!0)])),_:2},1024)))),128))]})),_:1}),(null==(l=null==(t=null==(e=x.enterpriseInfo)?void 0:e.chainDTOS[x.selectedTagIndex])?void 0:t.chainNodeList[x.selectedTagIndex2])?void 0:l.productNames)?(r(),o(b,{key:0,class:g(x.StringTotal>38&&!x.showString?"limitationNameList":"nameList")},{default:a((()=>{var e,t;return[(r(!0),s(i,null,p(null==(t=null==(e=x.enterpriseInfo)?void 0:e.chainDTOS[x.selectedTagIndex])?void 0:t.chainNodeList[x.selectedTagIndex2].productNames,((e,t)=>(r(),o(b,{class:"nameItem",key:t},{default:a((()=>[u(f(e),1)])),_:2},1024)))),128))]})),_:1},8,["class"])):c("",!0),(null==(m=null==(I=null==(d=x.enterpriseInfo)?void 0:d.chainDTOS[x.selectedTagIndex])?void 0:I.chainNodeList[x.selectedTagIndex2])?void 0:m.productNames)?(r(),s("div",{key:1},[x.StringTotal>38&&(null==(v=null==(T=x.enterpriseInfo)?void 0:T.chainDTOS[x.selectedTagIndex])?void 0:v.chainNodeList[x.selectedTagIndex2].productNames.length)>2?(r(),s("div",{key:0,class:"parent-container"},[n(C,{src:k,class:g(x.showString?"showIcon":"showIcons"),onClick:_[0]||(_[0]=e=>x.showString=!x.showString)},null,8,["class"])])):c("",!0)])):c("",!0)]})),_:1})):c("",!0),n(b,{class:"businessScopeBox"},{default:a((()=>[n(b,{class:"businessScopetitle"},{default:a((()=>[u("经营范围")])),_:1}),n(b,{class:"businessScope"},{default:a((()=>[u(f(x.enterpriseInfo.businessScope||"-"),1)])),_:1})])),_:1}),n(b,{class:"placeholder"})]})),_:1})):c("",!0)])),_:1})],64)}],["__scopeId","data-v-4e097a74"]]);export{x as default};
