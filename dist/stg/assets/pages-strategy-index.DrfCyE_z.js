import{l as e,s as t,m as s,A as a,z as i,a as n,f as o,w as r,F as l,i as c,o as p,n as d,g as m,t as h,B as u,e as f,c as g,b as y,d as I,C as L,S as A}from"./index-CBCsGYoT.js";import{_ as v}from"./uni-popup.BLXBf1r-.js";import{r as b}from"./uni-app.es.CZb2JZWI.js";import{m as N}from"./mapList.8RZSfjp3.js";import{m as S}from"./moreScreen.Db_lz0V-.js";import{l as w,g as C}from"./utils.61Hi-B7M.js";import{_ as k}from"./right2.DB4H0AJ0.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.Ckb0qY8x.js";const F=P({components:{mapList2:N,moreScreen:S},watch:{enterpriseList(e){this.enterpriseList.forEach((e=>{let t=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let s=e.enterpriseLabelNames[0];s?t.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:s.includes("独角兽")?e.enterpriseIconLabelId=2:s.includes("专精特新")?e.enterpriseIconLabelId=3:s.includes("隐形冠军")?e.enterpriseIconLabelId=4:s.includes("瞪羚")?e.enterpriseIconLabelId=5:s.includes("创新")?e.enterpriseIconLabelId=6:s.includes("技术先进")?e.enterpriseIconLabelId=7:s.includes("科技")?e.enterpriseIconLabelId=8:s.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}},data:()=>({statusBarHeight:e("statusBarHeight"),searchForm:{model:[],strategy:[],code:""},cityName:"",listicon:w,pageNum:1,pages:0,baseInfo:{},clueModelList:[{paramName:"线索模型",paramValueList:["亲缘招商","链式招商","舆情招商","资本招商","人才招商"],paramKey:"clueModel",unlimited:!0}],inStrategyList:[{paramName:"招商策略",paramValueList:["资本招商","场景招商","亲缘招商"],paramKey:"inStrategy",unlimited:!0}],enterpriseList:[],total:0,token:"",showNodata:!1,isLoading:!1,dataInfo:{}}),onLoad(s){this.token=e("token");let a=(null==s?void 0:s.id)||"";C("token")&&(this.token=C("token"),t("token",this.token)),C("id")&&(a=C("id")),this.getDel(a),this.token?this.getList():this.showNodata=!0},methods:{oneClickExportFn(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{id:47},path:"deriveExcel"}))},getDel(e){let t={id:e};this.$api.capabilityPoolDetailAPI({data:t}).then((e=>{this.dataInfo=e.result}))},getList(){if(this.isLoading)return;this.isLoading=!0;let e={pageSize:10,pageNum:this.pageNum,code:this.searchForm.code||"",modelTypes:this.getCodeByName(this.searchForm.model),strategyTypes:this.getCodeByName2(this.searchForm.strategy)};this.$api.reportListAPI({data:e,method:"post"}).then((e=>{this.enterpriseList=this.enterpriseList.concat(e.result.records),this.pages=e.result.pages,this.total=e.result.total})).finally((()=>{this.isLoading=!1,this.showNodata=!0}))},scrolltolowerFn(){this.isLoading||this.pages>this.pageNum&&(this.pageNum++,this.getList())},detailpage(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{id:e.enterpriseId||e.id,iconTypeid:e.enterpriseIconLabelId,enterpriseName:e.enterpriseName,enterpriseLabelIds:JSON.stringify(this.enterpriseLabelIds||[])},path:"industryDetail"}))},getCodeByName(e){const t={"亲缘招商":1,"链式招商":3,"舆情招商":5,"人才招商":7,"资本招商":8};return e.map((e=>t[e]||null))||[]},getCodeByName2(e){const t={"亲缘招商":1,"资本招商":2,"场景招商":3};return e.map((e=>t[e]||null))||[]},goBack(){},goDetail(e){let t={enterpriseName:e.enterpriseName,uniCode:e.uniCode,enterpriseId:e.enterpriseId,recommendRegionCode:e.recommendRegionCode||""};this.$api.reportGenerateAPI({data:t,method:"post"}).then((t=>{var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"changePath",value:{url:"previewPdf",name:"报告预览",params:{reportId:t.result,type:"pdf",enterpriseName:e.enterpriseName+"招商策略报告"}},path:"webViewPage"}))}))},regionFn(){var e,t;let s="全国";return null!=(null==(e=this.baseInfo)?void 0:e.area)&&0!=(null==(t=this.baseInfo)?void 0:t.area.length)?s=this.baseInfo.area[0]:this.baseInfo.city?s=this.baseInfo.city:this.baseInfo.province&&(s=this.baseInfo.province),s.length>4&&(s=s.substring(0,3)+"..."),s},iconColour:e=>e?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png",screenFn(e){this.token||s({url:"/pages/login/index"}),1==e?(this.$refs.popup2.open("bottom"),this.$nextTick((()=>{this.$refs.mapList2?this.$refs.mapList2.init():console.warn("mapList2组件未挂载打开时")}))):2==e?this.$refs.clueModelPop.opens():3==e&&this.$refs.inStrategyPop.opens()},affirm1(e){this.searchForm.model=e?e.clueModel:[],this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList()},affirm2(e){this.searchForm.code=e.area.code?e.area.code:e.citys.code?e.citys.code:e.province.code,this.$refs.popup2.close(),this.baseInfo.province=e.province.name,this.baseInfo.city=e.citys.name,this.baseInfo.area=[],e.area.name&&(this.baseInfo.area[0]=e.area.name);let t=e.province.name?e.province.name+"/":"",s=e.citys.name?e.citys.name+"/":"",a=e.area.name?e.area.name+"/":"";this.cityName=t+s+a,console.log(this.cityName,"this.cityName "),this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList()},affirm3(e){this.searchForm.strategy=e?e.inStrategy:[],this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList()},isEmptyValue(e){return"string"==typeof e?""===e.trim():"number"==typeof e?0===e:"boolean"==typeof e?!1===e:Array.isArray(e)?e.every(this.isEmptyValue):null!==e&&"object"==typeof e&&Object.values(e).every(this.isEmptyValue)}}},[["render",function(e,t,s,N,S,w){const C=L,P=c,F=A,B=a("moreScreen"),E=a("mapList2"),M=b(i("uni-popup"),v);return p(),n(l,null,[o(P,null,{default:r((()=>[o(P,{class:"topHeader",style:d(`top: ${S.statusBarHeight}px`)},{default:r((()=>[o(P,{class:"header"},{default:r((()=>[o(C,{src:S.dataInfo.iconPath,class:"icon"},null,8,["src"]),o(P,{class:"mgl-20"},{default:r((()=>[o(P,{class:"span1"},{default:r((()=>[m(h(S.dataInfo.name),1)])),_:1}),o(P,{class:"span2"},{default:r((()=>[m("来自：艾瑞数云")])),_:1})])),_:1}),o(P,{onClick:w.oneClickExportFn,class:"oneClickExport"},{default:r((()=>[o(C,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAABAlBMVEUAAAA/SVg/Sllmk/9slv99o/9+pf9nlP+Jq/9rl/97o/9AQFWAgP+Lrf9znf96ov8/SVmKrP92n/97ov9xmv94oP90nv9plf+Bp/8+SVh5of6EqP+Fqf+Hqv93nv90mP9AgP+EqP9xmv5jkv9tmP+Gqv58ov+Cp/9rl/8/SlmJrP+CqP92nv9ok/9vmf+Fqf91nf99o/9xmv+Hqf9vmv+Apf92oP8+SlmKrf98o/98ov96of9vmP9rmP+Gq/89SVlxnf9AR1WAqv8/Sll+pP90nf+Fqf98o/+Hqv56of94oP9xm/9mk/+Cp/6KrP+Apv9umf9qlv+Bpv92n/92n/52nv62er/dAAAAQ3RSTlMA858IGvrKnpZXJAwE+e3p39W/qZ2Xi4l5b1tZQzcqFQT7+/fz8+/r6+vj29PTz8O3r6ejl5OTh4N/c29cT0w/NCQMiMQDoQAAAR9JREFUOMvl1NdugzAUgGEbCCFkp9m7e++9B3VdRltG+/6vUuMKCRDHzVVu8ktIB+mTz41ltMgVJWzwth8RTyvXr0YZUDKiLhGv/Mna0YZyCuLI1Z4R76H+zls/HbzEISMold49fOMtayLIG+5+hG3+Aweq7/tfrHMhHKmvYTn2jUWwywStXuvHlKoIhqUlSunGjYwmOdPsA5A70zQbCptuLasqw7BlWVarFE573z9tBMJ+EAQXfHoihIxBqFQIOZrysU3sBgJhk5DK5G88se0CCPUV276LDm92piA8c90DFAuC8prrJtb1ar1MeO95W3LyBuJMmPe8PLAs+bfvOIWZoOM4ykyws8o3C2HUfCE2BOEYlERQSj0pQFgqooXtF/7eVcUkio+WAAAAAElFTkSuQmCC"}),m(" 一键导出 ")])),_:1},8,["onClick"])])),_:1}),o(P,{class:"headertext"},{default:r((()=>[m(h(S.dataInfo.content),1)])),_:1}),o(P,{class:"optionBox"},{default:r((()=>[u("span",{class:f([w.isEmptyValue(S.baseInfo)?"unselected":"pitchOn","optionItem"]),onClick:t[0]||(t[0]=e=>w.screenFn(1))},[m(h(w.regionFn())+" ",1),o(C,{src:w.iconColour(w.isEmptyValue(S.baseInfo)),class:"Arrows"},null,8,["src"])],2),u("span",{class:f([""==S.searchForm.model?"unselected":"pitchOn","optionItem"]),onClick:t[1]||(t[1]=e=>w.screenFn(2))},[m(" 线索模型 "),o(C,{src:w.iconColour(S.searchForm.model.length<=0),class:"Arrows"},null,8,["src"])],2),u("span",{class:f([""==S.searchForm.strategy?"unselected":"pitchOn","optionItem"]),onClick:t[2]||(t[2]=e=>w.screenFn(3))},[m(" 招商策略 "),o(C,{src:w.iconColour(S.searchForm.strategy.length<=0),class:"Arrows"},null,8,["src"])],2)])),_:1}),o(P,{class:"total"},{default:r((()=>[m(" 共 "),u("span",{class:"totalSpan2"},h(S.total),1),m(" 家企业 ")])),_:1})])),_:1},8,["style"]),0!=S.enterpriseList.length?(p(),g(F,{key:0,class:"listBox",onScrolltolower:w.scrolltolowerFn,onScroll:e.scroll,style:d(`top: ${S.statusBarHeight}px`),"scroll-y":"true"},{default:r((()=>[o(P,null,{default:r((()=>[(p(!0),n(l,null,y(S.enterpriseList,(e=>(p(),g(P,{class:"card",key:e},{default:r((()=>[o(P,{class:"cadHeader"},{default:r((()=>[o(C,{src:null==(null==e?void 0:e.enterpriseIconLabelId)?S.listicon[0].icon:S.listicon[e.enterpriseIconLabelId].icon,class:"icon2"},null,8,["src"]),u("span",{onClick:t=>w.detailpage(e),class:"mgl-10"},h(e.enterpriseName),9,["onClick"])])),_:2},1024),o(P,{class:"contentBox mgt-24"},{default:r((()=>[u("p",{class:"contentSpan"},"线索模型："+h(e.clueTypeName),1),u("p",{class:"contentSpan"},"招商策略："+h(e.strategyTypeName),1),u("p",{class:"contentSpan"},"推荐时间："+h(e.recommendDate),1),u("p",{class:"contentSpan"},"企业地址："+h(e.enterpriseAddress),1),u("p",{class:"contentSpan"},"推送地区："+h(e.recommendRegionName),1)])),_:2},1024),o(P,{class:"detail",onClick:t=>w.goDetail(e)},{default:r((()=>[m(" 一企一策 "),o(C,{src:k,class:"righticon"})])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["onScrolltolower","onScroll","style"])):I("",!0),0==S.enterpriseList.length&&S.showNodata?(p(),n("div",{key:1,class:"nodatabox"},[o(C,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),u("span",{class:"span"},"暂无内容")])):I("",!0)])),_:1}),o(B,{ref:"clueModelPop",title:"请选择",safearea:!0,moreScreenList:S.clueModelList,onUpdatamoreScreen:w.affirm1},null,8,["moreScreenList","onUpdatamoreScreen"]),o(M,{ref:"popup2","background-color":"#fff"},{default:r((()=>[o(E,{ref:"mapList2",cityName:S.cityName,unrestricted:!0,onAffirm:w.affirm2},null,8,["cityName","onAffirm"])])),_:1},512),o(B,{ref:"inStrategyPop",title:"请选择",safearea:!0,moreScreenList:S.inStrategyList,onUpdatamoreScreen:w.affirm3},null,8,["moreScreenList","onUpdatamoreScreen"])],64)}],["__scopeId","data-v-95e3957d"]]);export{F as default};
