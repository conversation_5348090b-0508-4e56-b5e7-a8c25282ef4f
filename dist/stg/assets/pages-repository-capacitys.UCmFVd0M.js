import{m as t,p as e,o as i,a as s,B as a,f as l,F as n,b as o,t as c,d,H as r,I as h,C as u,l as p,s as g,u as m,z as y,A as v,c as k,n as w,w as f,g as I,i as C,S as N}from"./index-CBCsGYoT.js";import{_ as A}from"./page-meta.BCoSkkXs.js";import{r as L}from"./uni-app.es.CZb2JZWI.js";import{t as b}from"./index.D5F8338M.js";import{_ as S,t as j}from"./adornBgc.B22CpsrG.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{n as _}from"./noAccess.DU-PrsIx.js";import{g as D}from"./utils.61Hi-B7M.js";import{_ as V}from"./returnFn.BYkANsDr.js";import{_ as T}from"./history.BKBs_WXS.js";import"./uni-transition.Ckb0qY8x.js";import"./uni-popup.BLXBf1r-.js";const B=x({components:{tabBar:b,Capacitypool:x({props:{item:{type:Object,default:{}},token:{type:String,default:""},buy:{type:Boolean,default:!1},knowledgeId:{type:String,default:""},lastDataShow:{type:Object,default:null},AI2jurisdiction:{type:Object,default:{}}},data:()=>({figureList:[],attractList:[],restList:[],_lastDataShow:{}}),created(){},methods:{changeList(t){this._lastDataShow=t},classify(){this.figureList=this.item.sort(((t,e)=>t.state-e.state))},select(i){var s,a;return this.token||"产业360"!=i.name&&"招商管理"!=i.name?"一企一策"==i.name&&this.token&&!this.AI2jurisdiction.chain2Id||"线索一键导出"==i.name&&this.token&&!this.AI2jurisdiction.chain2Id||"招商管理"==i.name&&this.token&&!this.AI2jurisdiction.chain2Id?this.$emit("noAccessFn",!1):"产业360"!=i.name||this.AI2jurisdiction.chain2Id?2==i.state?e({title:"敬请期待",icon:"none"}):void("招商管理"==i.name?null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"changePath",value:{},path:"attractionManage"})):null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{data:{id:i.id,name:i.name,isOld:i.isOld,showChain:this.AI2jurisdiction.chain2Id,title:this.AI2jurisdiction.knowType2Name,knowId:this.AI2jurisdiction.knowType2Id}},path:"chat"}))):e({title:"暂未开通产业链，请在我的-会员中心开通",icon:"none"}):t({url:"/pages/login/index"})}}},[["render",function(t,e,p,g,m,y){var v,k,w,f,I,C,N,A,L,b,j,x,_,D,V,T;const B=u;return i(),s("div",{class:"particulars"},[0!=(null==(k=null==(v=m._lastDataShow)?void 0:v[0])?void 0:k.data.length)?(i(),s("div",{key:0,class:"fetchitem"},[a("div",{class:"headtext"},[a("div",{class:"textTitle"},[a("span",{class:"title"},"产业洞察"),l(B,{class:"adornBgc",src:S})])]),(i(!0),s(n,null,o(null==(f=null==(w=m._lastDataShow)?void 0:w[0])?void 0:f.data,((t,e)=>(i(),s("div",{key:t.id,onClick:i=>y.select(t,e)},[a("div",{class:"item"},[a("div",{class:"titlehead"},[l(B,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),a("span",{class:"name"},c(t.name),1)]),2==t.state?(i(),s("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)])],8,["onClick"])))),128))])):d("",!0),0!=(null==(C=null==(I=m._lastDataShow)?void 0:I[1])?void 0:C.data.length)?(i(),s("div",{key:1,class:"fetchitem"},[a("div",{class:"headtext"},[a("div",{class:"textTitle"},[a("span",{class:"title"},"招商线索"),l(B,{class:"adornBgc",src:S})])]),(i(!0),s(n,null,o(null==(A=null==(N=m._lastDataShow)?void 0:N[1])?void 0:A.data,((t,e)=>(i(),s("div",{key:t.id,onClick:i=>y.select(t,e)},[a("div",{class:"item"},[a("div",{class:"titlehead"},[l(B,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),a("span",{class:"name"},c(t.name),1)]),2==t.state?(i(),s("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)])],8,["onClick"])))),128))])):d("",!0),0!=(null==(b=null==(L=m._lastDataShow)?void 0:L[2])?void 0:b.data.length)?(i(),s("div",{key:2,class:"fetchitem"},[a("div",{class:"headtext"},[a("div",{class:"textTitle"},[a("span",{class:"title"},"招商工具"),l(B,{class:"adornBgc",src:S})])]),(i(!0),s(n,null,o(null==(x=null==(j=m._lastDataShow)?void 0:j[2])?void 0:x.data,((t,e)=>(i(),s("div",{key:t.id,onClick:i=>y.select(t,e)},[r(a("div",{class:"item"},[a("div",{class:"titlehead"},[l(B,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),a("span",{class:"name"},c(t.name),1)]),2==t.state?(i(),s("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)],512),[[h,"招商管理"==t.name&&p.AI2jurisdiction.chain2Id||"招商管理"!=t.name||!p.token]])],8,["onClick"])))),128))])):d("",!0),0!=(null==(D=null==(_=m._lastDataShow)?void 0:_[3])?void 0:D.data.length)?(i(),s("div",{key:3,class:"fetchitem"},[a("div",{class:"headtext"},[a("div",{class:"textTitle"},[a("span",{class:"title"},"特色场景"),l(B,{class:"adornBgc",src:S})])]),(i(!0),s(n,null,o(null==(T=null==(V=m._lastDataShow)?void 0:V[3])?void 0:T.data,((t,e)=>(i(),s("div",{key:t.id,onClick:i=>y.select(t,e)},[a("div",{class:"item"},[a("div",{class:"titlehead"},[l(B,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),a("span",{class:"name"},c(t.name),1)]),2==t.state?(i(),s("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)])],8,["onClick"])))),128))])):d("",!0),a("div",{style:{height:"100px"}})])}],["__scopeId","data-v-46ca9cf6"]]),treeSelect:j,noAccess:_},data:()=>({title:"",bottomHeight:0,token:"",capacityList:[],keyword:"",time:null,begdata:!1,showshade:!1,statusBarHeight:p("statusBarHeight"),lastDataShow:{},knowledgeId:"",industryChainList:[],list:[],defaultValue:[],initialValue:[],showChain:"",orgName:"",isDefault:"",AI2jurisdiction:{},scrollY:!0,clientVersion:0,identity:1}),onLoad(t){let i={token:"",id:""};D("token")&&(i.token=D("token"),g("token",i.token)),D("id")&&(i.id=D("id")),D("clientVersion")&&(this.clientVersion=Number(D("clientVersion"))),D("orgName")?(this.isDefault=!1,this.orgName=D("orgName"),g("orgName",this.orgName)):(this.isDefault=!0,g("orgName","")),g("isDefault",this.isDefault),D("identity")&&(this.identity=D("identity"),g("userIdentityType",this.identity)),this.getList(),this.token=p("token");let s=p("userIdentityType");if(this.token&&(3==s||4==s))return e({title:"已登陆，该身份无权限，查看请退出登录",icon:"none",duration:2e3}),void setTimeout((()=>{m({url:"/pages/repository/capacity"})}),2e3);this.getindustryList(),this.orgName=p("orgName")||"",this.isDefault=p("isDefault"),console.log(this.isDefault)},onShow(){},methods:{noAccessFn(t){this.scrollY=t,this.$refs.noAccess.opens()},init(){var t,e,i,s,a;let l={token:"",id:""},n=null==(t=window.location.href.split("?token="))?void 0:t[1],o=null==n?void 0:n.split("&id=");l.token=null==(i=null==(e=null==o?void 0:o[0])?void 0:e.split("&orgName="))?void 0:i[0];let c="",d=(null==(a=null==(s=null==o?void 0:o[0])?void 0:s.split("&orgName="))?void 0:a[1])||"";if(d){let t=null==d?void 0:d.split("&clientVersion=");if(t.length>1){c=decodeURIComponent(t[0]);const e=t[1];e&&(this.clientVersion=Number(e))}else c=decodeURIComponent(d)}g("token",l.token),g("orgName",c)},echoChain(){this.$api.choosechainAPI({method:"get"}).then((t=>{t.result.knowType2Name&&(this.AI2jurisdiction=t.result,this.title=t.result.knowType2Name,this.defaultValue=[t.result.category2Id,t.result.knowType2Id],this.initialValue=[t.result.category2Id,t.result.knowType2Id],setTimeout((()=>{this.list.map((t=>{t.value==this.initialValue[0]&&this.$refs.treeSelect.echo(this.list,t.children)}))}),1e3))}))},getindustryList(){this.$api.industrychainList({data:{capType:2},method:"get"}).then((t=>{this.industryChainList=t.result.map((t=>{let e=t.chainList.map((t=>({...t,label:t.chainName,value:t.knowId,categoryBuyStatus:t.buyStatus}))),{chainList:i,...s}=t;return{...s,label:t.categoryName,value:t.categoryId,children:e}})),this.list=this.industryChainList,this.token&&this.echoChain()}))},onChange(){},onConfirm(t){var e;let{selected:i,values:s}=t;"1"===(null==(e=i[1])?void 0:e.categoryBuyStatus)&&this.$api.updatechoosechainAPI({data:{capType:2,knowId:s[1]},method:"get"}).then((t=>{var e;this.defaultValue=[s[0],s[1]],this.initialValue=[s[0],s[1]],this.title=null==(e=i[1])?void 0:e.chainName,this.$api.choosechainAPI({method:"get"}).then((t=>{t.result.knowType2Name&&(this.AI2jurisdiction=t.result)}))}))},goBack(){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"goBack"}))},goLog:()=>t({url:"/pages/login/index"}),go(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:{capType:2},path:"history"}))},changekeyword(t){null!=this.time&&clearTimeout(this.time),this.time=setTimeout((async()=>{this.$api.queryCapabilityListWithoutPermission({data:{capabilityName:t,type:2},method:"post"}).then((t=>{this.capacityList=t.result,this.getShowList(t.result)}))}),500)},getList(){this.$api.queryCapabilityListWithoutPermission({data:{type:2},method:"post"}).then((t=>{this.capacityList=t.result,this.getShowList(t.result),this.begdata=!0})).catch((t=>{this.begdata=!0}))},getShowList(t){let e={0:{list:["产业360","产业图谱绘制","产业链AI诊断","产业政策分析","产业报告速编"],data:[]},1:{list:["亲缘招商","链式招商","集群招商","舆情招商","资本招商","人才招商"],data:[]},2:{list:["一企一策","线索一键导出"],data:[]},3:{list:["回归专项"],data:[]}},i=p("userIdentityType"),s=p("orgName"),a=p("isDefault");i&&1!=i||(!this.token||s&&!a)&&e[2].list.push("招商管理"),console.log(e,"types"),(!this.clientVersion||this.clientVersion<241)&&(e[0].list=["产业图谱绘制","产业链AI诊断","产业政策分析","产业报告速编"]);for(let l in e)e[l].list.map((i=>{let s=t.find((t=>t.name===i));s&&e[l].data.push(s)}));this.lastDataShow=e,0!=this.capacityList.length&&this.$nextTick((()=>{this.$refs.showList.changeList(this.lastDataShow)}))}}},[["render",function(t,e,o,r,h,p){const g=L(y("page-meta"),A),m=u,b=C,S=v("tree-select"),j=v("Capacitypool"),x=N,_=v("noAccess");return i(),s(n,null,[h.clientVersion>=240?(i(),k(g,{key:0,"page-style":"background-color: transparent;overflow: hidden !important;max-width: 100vw;"})):(i(),k(g,{key:1,"page-style":"background-color: #FAFCFF;overflow: hidden !important;max-width: 100vw;"})),h.clientVersion?d("",!0):(i(),k(m,{key:2,src:"https://static.idicc.cn/cdn/aiChat/applet/home/<USER>",style:{width:"750rpx",position:"absolute",height:"1624rpx"}})),h.clientVersion?d("",!0):(i(),s("div",{key:3,class:"topImg",style:w(`top: ${h.statusBarHeight}px`)},[l(b,{class:"black",onClick:e[0]||(e[0]=t=>p.goBack())},{default:f((()=>[l(m,{class:"returnFn",src:V})])),_:1})],4)),a("div",{class:"box",style:w(`top: ${h.clientVersion?30:h.statusBarHeight+60}px `)},[a("div",{class:"topIntroduce"},[l(m,{src:"https://static.idicc.cn/cdn/aiChat/applet/home/<USER>",class:"AI2"}),!h.isDefault&&h.orgName?(i(),k(b,{key:0,class:"orgName"},{default:f((()=>[I(c(h.orgName),1)])),_:1})):d("",!0)]),a("div",{class:"seinput"},[h.token?(i(),k(S,{key:0,isCanyan:!1,ref:"treeSelect",class:"changeIndustry","select-tile":"切换产业链","options-key":"value",value:h.defaultValue,"initial-value":h.initialValue,primary:"value",options:h.list,onChange:p.onChange,onConfirm:p.onConfirm},{default:f((()=>[a("div",{class:"selectBox",style:{display:"flex","align-items":"center"}},[a("span",{style:{"white-space":"nowrap"}},c(h.title?h.title.length>5?h.title.substring(0,4)+"...":h.title:"未开通"),1),a("div",{class:"InvertedTriangle"})])])),_:1},8,["value","initial-value","options","onChange","onConfirm"])):(i(),s("div",{key:1,class:"selectBox",style:{display:"flex","align-items":"center"}},[a("span",{style:{"white-space":"nowrap"},onClick:e[1]||(e[1]=(...t)=>p.goLog&&p.goLog(...t))},"未开通"),a("div",{class:"InvertedTriangle"})])),h.token?(i(),s("div",{key:2,class:"historyBox",onClick:e[2]||(e[2]=t=>p.go("history"))},[l(m,{class:"icon",src:T}),I(" 历史记录 ")])):d("",!0)]),l(x,{"scroll-y":h.scrollY,class:"repository",style:w({"padding-bottom":h.token?"60rpx":"160rpx"})},{default:f((()=>[0==h.capacityList.length&&h.begdata?(i(),s("div",{key:0,class:"nodatabox"},[l(m,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata.png"}),a("span",{class:"span"},"暂无内容")])):(i(),k(b,{key:1},{default:f((()=>[l(j,{ref:"showList",item:h.capacityList,token:h.token,"knowledge-id":h.knowledgeId,"last-data-show":h.lastDataShow,AI2jurisdiction:h.AI2jurisdiction,onNoAccessFn:p.noAccessFn},null,8,["item","token","knowledge-id","last-data-show","AI2jurisdiction","onNoAccessFn"])])),_:1}))])),_:1},8,["scroll-y","style"])],4),l(_,{ref:"noAccess",onNoAccessFn:p.noAccessFn},null,8,["onNoAccessFn"])],64)}],["__scopeId","data-v-2681c9f3"]]);export{B as default};
