import{l as t,a3 as a,a4 as e,p as s,z as i,o as l,c as o,w as n,f as d,B as r,t as c,a as u,d as p,n as h,C as m,i as f,L as g,F as b,b as y,a0 as v,A,g as I,D as k,e as L,S as T,k as F,W as w,U as _,u as C,s as S}from"./index-CBCsGYoT.js";import{_ as D}from"./uni-easyinput.D_LnJWIZ.js";import{r as B}from"./uni-app.es.CZb2JZWI.js";import{_ as $,a as U}from"./uni-forms.BsccYHPu.js";import{_ as P}from"./uni-file-picker.ui7Yqy_z.js";import{_ as V}from"./uni-popup.BLXBf1r-.js";import{_ as x}from"./addImg.DKbfrdtV.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as M}from"./uni-data-checkbox.lGJQWvI7.js";import{_ as j}from"./dingdan.lwTJ-xBu.js";import{r as R}from"./regionCut.CGH9u9zb.js";import{_ as z}from"./fuwuleixing.C6AoCp6M.js";import{w as E,_ as O,a as H}from"./mywallet.DdYt-M5R.js";import{_ as Q}from"./returnFn.BYkANsDr.js";import{g as Y}from"./utils.61Hi-B7M.js";import{_ as q,a as J}from"./service.zf6UL628.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-transition.Ckb0qY8x.js";import"./uni-load-more.BMOBclrH.js";import"./right.D4iebSn6.js";const W=N({props:{userIdentityType:{type:Number,default:()=>4}},data:()=>({porpsData:"",safeArea:t("safeAreaRpx"),form:{content:""},submitLoading:!1,uploadFileList:[],rules:{content:{rules:[{required:!0,errorMessage:"请输入申请信息"}]}}}),methods:{updateImg(t){const s=t.tempFilePaths;s.forEach(((t,i)=>{a({url:`${e}/jh/upload`,filePath:s[i],name:"file",success:a=>{a.data=JSON.parse(a.data),this.uploadFileList.push({name:t,value:a.data.data.value})}})}))},deleteImg(t){this.uploadFileList=this.uploadFileList.filter((a=>a.name!=t.tempFilePath))},opens(t){this.porpsData=t,this.continueFn()},continueFn(){this.$emit("updataTab",!1),this.$refs.follow.open("bottom")},canleFn(){this.$refs.hint.close()},claimFn(){this.form={content:""},this.uploadFileList=[],this.$refs.filePicker.clearFiles(),this.$refs.follow.close(),this.$emit("updataTab",!0)},submit(){this.submitLoading||(this.submitLoading=!0,this.$refs.valiForm.validate().then((t=>{let a={smartServiceId:this.porpsData.id,content:this.form.content,filePath:this.uploadFileList.map((t=>t.value))};3==this.userIdentityType?this.$api.providerStatementAPI({method:"post",data:a}).then((t=>{this.submitLoading=!1,s({title:"申请成功，请耐心等待审核",icon:"none"}),this.$emit("updataList"),this.claimFn()})).catch((t=>{this.submitLoading=!1})):this.$api.clientStatementAPI({method:"post",data:a}).then((t=>{this.submitLoading=!1,s({title:"申请成功，请耐心等待审核",icon:"none"}),this.$emit("updataList"),this.claimFn()})).catch((t=>{this.submitLoading=!1}))})).catch((t=>{this.submitLoading=!1})))}}},[["render",function(t,a,e,s,g,b){const y=B(i("uni-easyinput"),D),v=B(i("uni-forms-item"),$),A=m,I=B(i("uni-file-picker"),P),k=B(i("uni-forms"),U),L=B(i("uni-popup"),V),T=f;return l(),o(T,null,{default:n((()=>[d(L,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},c(3==e.userIdentityType?"申请结单":"确认结单"),1),r("div",{class:"followForm"},[3!=e.userIdentityType?(l(),u("div",{key:0},"为保证您的售后权益，请确认服务完成后再确认结单")):p("",!0),d(k,{ref:"valiForm",rules:g.rules,modelValue:g.form,"label-position":"top","label-width":"100"},{default:n((()=>[d(v,{name:"content",label:"申请信息",required:""},{default:n((()=>[r("div",{class:"summarizeInput"},[d(y,{type:"textarea",trim:"all",clearable:!1,modelValue:g.form.content,"onUpdate:modelValue":a[0]||(a[0]=t=>g.form.content=t),placeholder:"请输入"},null,8,["modelValue"])])])),_:1}),d(v,{name:"filePath",label:"附件"},{default:n((()=>[d(I,{ref:"filePicker","file-mediatype":"image",limit:"4",onSelect:b.updateImg,onDelete:b.deleteImg},{default:n((()=>[d(A,{class:"addImg",src:x})])),_:1},8,["onSelect","onDelete"])])),_:1})])),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:a[1]||(a[1]=(...t)=>b.claimFn&&b.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:a[2]||(a[2]=(...t)=>b.submit&&b.submit(...t))},"提交")]),r("div",{style:h({height:g.safeArea})},null,4)])])),_:1},512)])),_:1})}],["__scopeId","data-v-25ff033c"]]);const G={props:{title:{type:String,default:""},showicon:{type:Boolean,default:!0}},data:()=>({statusBarHeight:0}),created(){this.statusBarHeight=F().statusBarHeight},methods:{goBack(){w().length>1?_({delta:1}):C({url:"/pages/repository/capacity"})}}};const X=N({components:{mineOrderList:N({components:{follow:N({data:()=>({porpsData:"",safeArea:t("safeAreaRpx"),form:{content:""},submitLoading:!1,uploadFileList:[],rules:{content:{rules:[{required:!0,errorMessage:"请输入跟进信息"}]}}}),methods:{updateImg(t){const s=t.tempFilePaths;s.forEach(((t,i)=>{a({url:`${e}/jh/upload`,filePath:s[i],name:"file",success:a=>{a.data=JSON.parse(a.data),this.uploadFileList.push({name:t,value:a.data.data.value})}})}))},deleteImg(t){this.uploadFileList=this.uploadFileList.filter((a=>a.name!=t.tempFilePath))},opens(t){this.porpsData=t,this.continueFn()},continueFn(){this.$emit("updataTab",!1),this.$refs.follow.open("bottom")},canleFn(){this.$refs.hint.close()},claimFn(){this.form={content:""},this.uploadFileList=[],this.$refs.filePicker.clearFiles(),this.$refs.follow.close(),this.$emit("updataTab",!0)},submit(){this.submitLoading||(this.submitLoading=!0,this.$refs.valiForm.validate().then((t=>{let a={smartServiceId:this.porpsData.id,content:this.form.content,filePath:this.uploadFileList.map((t=>t.value))};this.$api.providerProcessAPI({method:"post",data:a}).then((t=>{this.submitLoading=!1,s({title:"跟进成功",icon:"none"}),this.$emit("updataList"),this.claimFn()})).catch((t=>{this.submitLoading=!1}))})).catch((t=>{this.submitLoading=!1})))}}},[["render",function(t,a,e,s,c,u){const p=B(i("uni-easyinput"),D),g=B(i("uni-forms-item"),$),b=m,y=B(i("uni-file-picker"),P),v=B(i("uni-forms"),U),A=B(i("uni-popup"),V),I=f;return l(),o(I,null,{default:n((()=>[d(A,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},"跟进"),r("div",{class:"followForm"},[d(v,{ref:"valiForm",rules:c.rules,modelValue:c.form,"label-position":"top","label-width":"100"},{default:n((()=>[d(g,{name:"content",label:"跟进信息",required:""},{default:n((()=>[r("div",{class:"summarizeInput"},[d(p,{type:"textarea",trim:"all",clearable:!1,modelValue:c.form.content,"onUpdate:modelValue":a[0]||(a[0]=t=>c.form.content=t),placeholder:"请输入"},null,8,["modelValue"])])])),_:1}),d(g,{name:"filePath",label:"附件"},{default:n((()=>[d(y,{ref:"filePicker","file-mediatype":"image",limit:"4",onSelect:u.updateImg,onDelete:u.deleteImg},{default:n((()=>[d(b,{class:"addImg",src:x})])),_:1},8,["onSelect","onDelete"])])),_:1})])),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:a[1]||(a[1]=(...t)=>u.claimFn&&u.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:a[2]||(a[2]=(...t)=>u.submit&&u.submit(...t))},"提交")]),r("div",{style:h({height:c.safeArea})},null,4)])])),_:1},512)])),_:1})}],["__scopeId","data-v-56af6e16"]]),statement:W,chargeback:N({data:()=>({porpsData:"",safeArea:t("safeAreaRpx"),form:{content:""},submitLoading:!1,uploadFileList:[],rules:{content:{rules:[{required:!0,errorMessage:"请输入退单说明"}]}}}),methods:{updateImg(t){const s=t.tempFilePaths;s.forEach(((t,i)=>{a({url:`${e}/jh/upload`,filePath:s[i],name:"file",success:a=>{a.data=JSON.parse(a.data),this.uploadFileList.push({name:t,value:a.data.data.value})}})}))},deleteImg(t){this.uploadFileList=this.uploadFileList.filter((a=>a.name!=t.tempFilePath))},opens(t){this.porpsData=t,this.continueFn()},continueFn(){this.$emit("updataTab",!1),this.$refs.follow.open("bottom")},canleFn(){this.$refs.hint.close()},claimFn(){this.form={content:""},this.uploadFileList=[],this.$refs.filePicker.clearFiles(),this.$refs.follow.close(),this.$emit("updataTab",!0)},submit(){this.submitLoading||(this.submitLoading=!0,this.$refs.valiForm.validate().then((t=>{let a={smartServiceId:this.porpsData.id,content:this.form.content,filePath:this.uploadFileList.map((t=>t.value))};this.$api.clientRefundAPI({method:"post",data:a}).then((t=>{this.submitLoading=!1,s({title:"申请成功！",icon:"none"}),this.$emit("updataList"),this.claimFn()})).catch((t=>{this.submitLoading=!1}))})).catch((t=>{this.submitLoading=!1})))}}},[["render",function(t,a,e,s,c,u){const p=B(i("uni-easyinput"),D),g=B(i("uni-forms-item"),$),b=m,y=B(i("uni-file-picker"),P),v=B(i("uni-forms"),U),A=B(i("uni-popup"),V),I=f;return l(),o(I,null,{default:n((()=>[d(A,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},"申请退单"),r("div",{class:"followForm"},[d(v,{ref:"valiForm",rules:c.rules,modelValue:c.form,"label-position":"top","label-width":"100"},{default:n((()=>[d(g,{name:"content",label:"退单说明",required:""},{default:n((()=>[r("div",{class:"summarizeInput"},[d(p,{type:"textarea",trim:"all",clearable:!1,modelValue:c.form.content,"onUpdate:modelValue":a[0]||(a[0]=t=>c.form.content=t),placeholder:"请输入"},null,8,["modelValue"])])])),_:1}),d(g,{name:"filePath",label:"附件"},{default:n((()=>[d(y,{ref:"filePicker","file-mediatype":"image",limit:"4",onSelect:u.updateImg,onDelete:u.deleteImg},{default:n((()=>[d(b,{class:"addImg",src:x})])),_:1},8,["onSelect","onDelete"])])),_:1})])),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:a[1]||(a[1]=(...t)=>u.claimFn&&u.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:a[2]||(a[2]=(...t)=>u.submit&&u.submit(...t))},"提交")]),r("div",{style:h({height:c.safeArea})},null,4)])])),_:1},512)])),_:1})}],["__scopeId","data-v-ed3375a5"]]),revocation:N({data:()=>({porpsData:"",loading:!1}),methods:{opens(t){this.porpsData=t,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){this.loading||(this.loading=!0,this.$api.clientReverseRefund({data:{smartServiceId:this.porpsData.id},method:"post"}).then((t=>{s({title:"撤销成功",icon:"none"}),setTimeout((()=>{this.claimFn(),this.loading=!1,this.$emit("updataList")}),500)})).catch((t=>{this.loading=!1})))}}},[["render",function(t,a,e,s,c,u){const p=B(i("uni-popup"),V),h=f;return l(),o(h,null,{default:n((()=>[d(p,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"p1"},"撤销"),r("div",{class:"p2"},"确认撤销退单申请吗？ "),r("div",{class:"popBtn"},[r("div",{onClick:a[0]||(a[0]=(...t)=>u.claimFn&&u.claimFn(...t)),class:"canleBt"}," 取消 "),r("div",{onClick:a[1]||(a[1]=(...t)=>u.retreat&&u.retreat(...t)),class:"affirm"}," 确认 ")])])])),_:1},512)])),_:1})}],["__scopeId","data-v-cf90be23"]]),ReviewTheStatement:N({data:()=>({porpsData:"",safeArea:t("safeAreaRpx"),form:{content:"",pass:1},submitLoading:!1,uploadFileList:[],sex:[{text:"通过",value:1},{text:"不通过",value:2}],rules:{content:{rules:[{required:!0,errorMessage:"请输入审批意见"}]}}}),methods:{previewImg(t,a){g({current:a,urls:t})},opens(t){this.porpsData=t,this.continueFn()},continueFn(){this.$emit("updataTab",!1),this.$refs.follow.open("bottom")},claimFn(){this.$refs.follow.close(),this.form={content:"",pass:1}},submit(){this.submitLoading||(this.submitLoading=!0,this.$refs.valiForm.validate().then((t=>{if(2==this.form.pass&&!this.form.content)return this.submitLoading=!1,s({title:"不通过时审核意见为必填",icon:"none"});let a={smartServiceId:this.porpsData.id,note:this.form.content,status:this.form.pass};this.$api.clientAuditStatement({method:"post",data:a}).then((t=>{this.submitLoading=!1,s({title:"审核成功！",icon:"none"}),this.$emit("updataList"),this.claimFn()})).catch((t=>{this.submitLoading=!1}))})).catch((t=>{this.submitLoading=!1})))}}},[["render",function(t,a,e,s,g,v){const A=B(i("uni-forms-item"),$),I=m,k=B(i("uni-data-checkbox"),M),L=B(i("uni-easyinput"),D),T=B(i("uni-forms"),U),F=B(i("uni-popup"),V),w=f;return l(),o(w,null,{default:n((()=>[d(F,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"head"},"结单审批"),r("div",{class:"followForm"},[d(T,{ref:"valiForm",rules:g.rules,modelValue:g.form,"label-position":"top","label-width":"100"},{default:n((()=>{var t;return[d(A,{label:"申请人"},{default:n((()=>[r("div",{class:"summarizeInput"},c(g.porpsData.providerName),1)])),_:1}),d(A,{label:"申请时间"},{default:n((()=>[r("div",{class:"summarizeInput"},c(g.porpsData.statementTime),1)])),_:1}),d(A,{label:"申请说明"},{default:n((()=>[r("div",{class:"summarizeInput"},c(g.porpsData.content),1)])),_:1}),g.porpsData.urls&&0!=(null==(t=g.porpsData.urls)?void 0:t.length)?(l(),o(A,{key:0,label:"附件"},{default:n((()=>[r("div",{class:"annex"},[(l(!0),u(b,null,y(g.porpsData.urls,((t,a)=>(l(),u("div",{key:a,onClick:t=>v.previewImg(g.porpsData.urls,a)},[d(I,{src:t,class:"annexImg"},null,8,["src"])],8,["onClick"])))),128))])])),_:1})):p("",!0),d(A,{label:"是否通过",required:""},{default:n((()=>[d(k,{modelValue:g.form.pass,"onUpdate:modelValue":a[0]||(a[0]=t=>g.form.pass=t),localdata:g.sex},null,8,["modelValue","localdata"])])),_:1}),d(A,{label:"审批意见",required:2==g.form.pass},{default:n((()=>[r("div",{class:"summarizeInput"},[d(L,{type:"textarea",trim:"all",clearable:!1,modelValue:g.form.content,"onUpdate:modelValue":a[1]||(a[1]=t=>g.form.content=t),placeholder:"请输入"},null,8,["modelValue"])])])),_:1},8,["required"])]})),_:1},8,["rules","modelValue"])]),r("div",{class:"btnBox"},[r("div",{class:"canle",onClick:a[2]||(a[2]=(...t)=>v.claimFn&&v.claimFn(...t))},"取消"),r("div",{class:"submit",onClick:a[3]||(a[3]=(...t)=>v.submit&&v.submit(...t))},"提交")]),r("div",{style:h({height:g.safeArea})},null,4)])])),_:1},512)])),_:1})}],["__scopeId","data-v-bffdf161"]])},props:{dataList:{type:Array,default:()=>[]},userIdentityType:{type:String,default:"4"}},data:()=>({}),methods:{copyFn(t){v({data:t,success(t){}})},updataList(){this.$emit("updataList")},stateShow(t){let a={1:"对接中",4:"对接中",6:"对接中",9:"对接中",11:"对接中",2:"结单审核中",3:"结单审核中",5:"结单审核中",7:"交易成功",10:"已退单",8:"退单中"};return 3==this.userIdentityType&&(a={1:"对接中",2:"结单审核中",3:"结单审核中",4:"对接中",5:"结单审核中",6:"对接中",7:"交易成功",8:"退单审核中",9:"对接中",10:"已退单",11:"对接中"}),a[t]||""},updataTab(t){this.$emit("changeBottomTab",t)},ReviewTheStatementFn(t){this.updataTab(!1),this.$refs.ReviewTheStatement.opens(t)},revocationFn(t){this.updataTab(!1),this.$refs.revocation.opens(t)},chargebackFn(t){this.updataTab(!1),this.$refs.chargeback.opens(t)},followFn(t){this.updataTab(!1),this.$refs.follow.opens(t)},commentFn(t){this.updataTab(!1),this.$refs.statement.opens(t)},gotoDetail(t){var a;let e={id:t.id,userIdentityTyp:this.userIdentityType};null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:e,path:"orderDetail"}))}}},[["render",function(t,a,e,s,i,o){const h=m,f=A("follow"),g=A("statement"),v=A("chargeback"),L=A("revocation"),T=A("ReviewTheStatement"),F=A("root-portal");return l(),u(b,null,[(l(!0),u(b,null,y(e.dataList,((t,a)=>(l(),u("div",{onClick:a=>o.gotoDetail(t),class:"dataListClass",key:t.entrustId},[r("div",{class:"t1"},[r("div",{class:"startTime"}," 下单时间："+c(t.startDatetime),1),r("div",{class:"myListstate"},c(o.stateShow(t.state)),1)]),r("div",{class:"t2"},[r("div",{class:"enterprise"},[d(h,{class:"enterpriseIcon",src:j}),r("span",{class:"enterpriseText"},c(t.serviceName),1)]),r("div",null,[r("span",{class:"currency"},"￥"),r("span",{class:"amount"},c(t.amount),1)])]),3==e.userIdentityType?(l(),u("div",{key:0,class:"t3"},[r("div",{class:"line"},"出海方："+c(t.userName),1),r("div",{onClick:k((a=>o.copyFn(t.userAccount)),["stop"]),class:"line"},[I("手机号码："+c(t.userAccount)+" ",1),d(h,{class:"copyIcon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAQlBMVEUAAACIiIiIiIiHh4eIiIiHh4eFhYWIiIiGhoaHh4eIiIiIiIiIiIiIiIiHh4eAgICAgICAgICHh4eIiIiIiIiIiIiZlHhmAAAAFXRSTlMAQN8gv2A45U717+vTxysYEgqXbyc9hBwPAAAAcklEQVQoz+XNOQ6AMAxEUTsOTtjXuf9VkZAgNlIqSl43+sXQdys7k23awdtMjOiCBWiJAUzWiKYe4x11gBOlxDXhZSxR0U5iZGB5IiOQJYD8JKoM1XiRekyZ6p83RnrH+RkLkILRo6dih9dmMg52ZvrsBHwHDYb7HOWHAAAAAElFTkSuQmCC"})],8,["onClick"]),r("div",{class:"line"},"订单编号："+c(t.orderSn),1)])):p("",!0),3!=e.userIdentityType?(l(),u("div",{key:1,class:"t3"},[r("div",{class:"line"},"服务方："+c(t.providerName),1),r("div",{class:"line"},"订单编号："+c(t.orderSn),1),r("div",{class:"line"},"服务介绍："+c(t.serviceDetail),1)])):p("",!0),r("div",{class:"btnBox"},[3==e.userIdentityType||1!=t.state&&4!=t.state&&6!=t.state&&9!=t.state&&11!=t.state?p("",!0):(l(),u("div",{key:0,onClick:k((a=>o.chargebackFn(t)),["stop"]),class:"T4BtnDe"}," 退单 ",8,["onClick"])),3!=e.userIdentityType&&3==t.state?(l(),u("div",{key:1,onClick:k((a=>o.ReviewTheStatementFn(t)),["stop"]),class:"T4BtnDe"}," 结单审批 ",8,["onClick"])):p("",!0),3!=e.userIdentityType&&8==t.state?(l(),u("div",{key:2,onClick:k((a=>o.revocationFn(t)),["stop"]),class:"T4BtnDe"}," 撤销退单 ",8,["onClick"])):p("",!0),1==t.state||4==t.state||6==t.state||9==t.state||11==t.state?(l(),u("div",{key:3,onClick:k((a=>o.commentFn(t)),["stop"]),class:"T4BtnDe"},c(3==e.userIdentityType?"申请结单":"确认结单"),9,["onClick"])):p("",!0),3!=e.userIdentityType||1!=t.state&&4!=t.state&&6!=t.state&&9!=t.state&&11!=t.state&&2!=t.state&&3!=t.state&&5!=t.state?p("",!0):(l(),u("div",{key:4,onClick:k((a=>o.followFn(t)),["stop"]),class:"T4Btn"}," 跟进 ",8,["onClick"]))])],8,["onClick"])))),128)),d(F,null,{default:n((()=>[d(f,{onUpdataList:o.updataList,onUpdataTab:o.updataTab,ref:"follow"},null,8,["onUpdataList","onUpdataTab"]),d(g,{userIdentityType:e.userIdentityType,onUpdataList:o.updataList,onUpdataTab:o.updataTab,ref:"statement"},null,8,["userIdentityType","onUpdataList","onUpdataTab"]),d(v,{onUpdataList:o.updataList,onUpdataTab:o.updataTab,ref:"chargeback"},null,8,["onUpdataList","onUpdataTab"]),d(L,{onUpdataList:o.updataList,onUpdataTab:o.updataTab,ref:"revocation"},null,8,["onUpdataList","onUpdataTab"]),d(T,{onUpdataList:o.updataList,onUpdataTab:o.updataTab,ref:"ReviewTheStatement"},null,8,["onUpdataList","onUpdataTab"])])),_:1})],64)}],["__scopeId","data-v-14c90055"]]),serviceManager:N({options:{styleIsolation:"shared"},components:{regionCut:R},data:()=>({statusBarHeight:t("statusBarHeight"),tabList:[],tabBodys:[],tabs:"1",value:"117",token:"",stateId:-1,titleList:[{name:"全部",id:-1},{name:"上架中",id:1},{name:"已下架",id:0}],porpsData:{},loading:!1,showNodata:!1}),mounted(){this.token=t("token"),this.updataPage()},methods:{claimFn(){this.$refs.chargeback.close()},retreat(){if(this.loading)return;this.loading=!0;let t={marketId:this.porpsData.marketId,publishStatus:1==this.porpsData.publishStatus?0:1};this.$api.marketModifyAPI({data:t,method:"post"}).then((t=>{s({title:"操作成功！",icon:"none"}),this.claimFn(),setTimeout((()=>{this.getTabLists(),this.loading=!1}),500)})).catch((t=>{this.loading=!1}))},updataPage(t){this.getData()},getData(){this.$api.serverCatalogListAPI({method:"post"}).then((t=>{if("SUCCESS"==t.code){let a=t.result.map((t=>({name:t.catalogName,key:t.catalogId})));this.echoInfo(a)}}))},echoInfo(t){this.$api.userMessage().then((a=>{var e;const s=new Set(a.result.userCompletionInfoDO.serviceItemCodes);this.tabList=t.filter((t=>s.has(t.key.toString())||s.has(Number(t.key)))),this.tabs=(null==(e=this.tabList[0])?void 0:e.key)||"",this.getTabLists()}))},getTabLists(){if(this.tabBodys=[],this.showNodata=!1,!this.tabs)return!1;this.$api.marketPageAPI({data:{catalogId:this.tabs,publishStatus:-1==this.stateId?"":this.stateId},method:"post"}).then((t=>{var a;"SUCCESS"==t.code&&(this.tabBodys=null==(a=t.result)?void 0:a.records)})).finally((()=>{this.showNodata=!0}))},tabClick(t){this.tabs=t.key,this.getTabLists()},onchange(t){this.getTabs()},changeState(t,a){(this.stateId!==t||a)&&(this.stateId=t,this.getTabLists())},gotoAdd(t){var a;const e=encodeURIComponent(JSON.stringify(t));null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{item:e},path:"addService"}))},goDown(t){this.porpsData=t,this.$refs.chargeback.open()}}},[["render",function(t,a,e,s,h,g){const v=f,A=m,k=T,F=B(i("uni-popup"),V);return l(),u(b,null,[r("div",{class:"stateTab"},[(l(!0),u(b,null,y(h.titleList,((t,a)=>(l(),u("div",{onClick:a=>g.changeState(t.id),class:L(h.stateId==t.id?"statePitch":"stateClass"),key:t.id},c(t.name),11,["onClick"])))),128))]),d(k,{class:"mainView"},{default:n((()=>[d(v,{class:"tabBox"},{default:n((()=>[d(v,{class:"tabBox_left"},{default:n((()=>[(l(!0),u(b,null,y(h.tabList,((t,a)=>(l(),o(v,{class:L(["tabLists",h.tabs==t.key?"active":""]),onClick:a=>g.tabClick(t),key:a},{default:n((()=>[d(v,{class:"active_zi"}),I(" "+c(t.name),1)])),_:2},1032,["onClick","class"])))),128))])),_:1}),d(v,{class:"tabBox_right"},{default:n((()=>[(l(!0),u(b,null,y(h.tabBodys,((t,a)=>(l(),o(v,{class:"tabItem",key:a},{default:n((()=>[d(v,{class:"titleView"},{default:n((()=>[d(A,{src:z,class:"hisImg"}),I(" "+c(t.itemName),1)])),_:2},1024),d(v,{class:"content"},{default:n((()=>[d(v,{class:"price"},{default:n((()=>[r("span",{class:"priceLogo"},"￥"),I(c(t.priceQuote),1)])),_:2},1024),d(v,{class:"btns"},{default:n((()=>[r("div",{class:"grayBtn",onClick:a=>g.gotoAdd(t)},"编辑",8,["onClick"]),r("div",{class:"grayBtn",onClick:a=>g.goDown(t)},c(1==t.publishStatus?"下架":"上架"),9,["onClick"])])),_:2},1024)])),_:2},1024),t.introduction?(l(),o(v,{key:0,class:"des"},{default:n((()=>[I(c(t.introduction),1)])),_:2},1024)):p("",!0),d(v,{class:"line"})])),_:2},1024)))),128)),0==h.tabBodys.length&&h.showNodata?(l(),u("div",{key:0,class:"nodatabox"},[d(A,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])):p("",!0)])),_:1})])),_:1})])),_:1}),d(v,{class:"addBtn",onClick:a[0]||(a[0]=t=>g.gotoAdd())},{default:n((()=>[I("新增服务")])),_:1}),d(F,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:n((()=>[r("div",{class:"applyaffirm"},[r("div",{class:"p1"},"温馨提示"),r("div",{class:"p2"},"确认"+c(1==h.porpsData.publishStatus?"下架":"上架")+"该商品吗？ ",1),r("div",{class:"popBtn"},[r("div",{onClick:a[1]||(a[1]=(...t)=>g.claimFn&&g.claimFn(...t)),class:"canleBt"}," 取消 "),r("div",{onClick:a[2]||(a[2]=(...t)=>g.retreat&&g.retreat(...t)),class:"affirm"}," 确认 ")])])])),_:1},512)],64)}],["__scopeId","data-v-c8264f03"]]),wallet:E,tabBar:N(G,[["render",function(t,a,e,s,i,r){const u=m,g=f;return l(),o(g,{class:"bar-con"},{default:n((()=>[d(g,{class:"tabBarCon"},{default:n((()=>[d(g,{id:"tabBar",class:"tabBar"},{default:n((()=>[d(g,{class:"nav",style:h(`\n\t\t\t\t    margin-top: ${i.statusBarHeight}px;\n\t\t\t\t`)},{default:n((()=>[e.showicon?(l(),o(g,{key:0,class:"black",onClick:a[0]||(a[0]=t=>r.goBack())},{default:n((()=>[d(u,{class:"returnFn",src:Q})])),_:1})):p("",!0),d(g,{class:"nav-title"},{default:n((()=>[I(c(e.title),1)])),_:1})])),_:1},8,["style"])])),_:1})])),_:1}),d(u,{src:"https://static.idicc.cn/cdn/aiChat/applet/head2.png",class:"headimg",mode:""})])),_:1})}],["__scopeId","data-v-21336f6a"]])},data:()=>({pitch:1,userIdentityType:4,listData:[],showNoData:!0,stateId:1,titleList:[{name:"对接中",id:1},{name:"已完成",id:7},{name:"退单中",id:8}],showBtns:!0}),onLoad(a){this.userIdentityType=t("userIdentityType"),Y("identity")&&(this.userIdentityType=Number(Y("identity"))),Y("token")&&S("token",Y("token")),Y("stateId")&&(this.stateId=Number(Y("stateId"))),this.updataList()},onShow(){3==this.userIdentityType&&this.$nextTick((()=>{2==this.pitch?this.$refs.serviceManager.getTabLists():3==this.pitch&&(this.$refs.wallet.getUser(),this.$refs.wallet.getWithdrawalRecord())}))},methods:{changeBottomTab(t){this.showBtns=t},changTab(t){this.pitch=t},updataList(){3==this.userIdentityType?this.$api.providerOrderListAPI({data:{pageSize:1e3,pageNum:1,state:this.stateId},method:"get"}).then((t=>{this.showNoData=!0,this.listData=t.result.records})).catch((t=>{this.showNoData=!0})):this.$api.clientOrderListAPI({data:{pageSize:1e3,pageNum:1,state:this.stateId},method:"get"}).then((t=>{this.showNoData=!0,this.listData=t.result.records})).catch((t=>{this.showNoData=!0}))},changeState(t,a){(this.stateId!==t||a)&&(this.stateId=t,this.showNoData=!1,this.updataList())}}},[["render",function(t,a,e,s,i,g){const v=f,k=m,T=A("mineOrderList"),F=A("serviceManager"),w=A("wallet");return l(),o(v,{class:L(["contentView",4===i.userIdentityType?"contentView2":""])},{default:n((()=>[1===i.pitch?(l(),o(v,{key:0},{default:n((()=>[d(v,{class:"myList"},{default:n((()=>[d(v,{class:"stateTab"},{default:n((()=>[(l(!0),u(b,null,y(i.titleList,((t,a)=>(l(),o(v,{onClick:a=>g.changeState(t.id),class:L(i.stateId==t.id?"statePitch":"stateClass"),key:t.id},{default:n((()=>[I(c(t.name),1)])),_:2},1032,["onClick","class"])))),128))])),_:1}),0==i.listData.length&&i.showNoData?(l(),o(v,{key:0,class:"noDataView"},{default:n((()=>[d(k,{class:"noDataImg",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"noDataText"},"暂无内容")])),_:1})):(l(),o(v,{key:1,class:"stateList"},{default:n((()=>[r("div",{class:"repository"},[d(T,{onChangeBottomTab:g.changeBottomTab,onUpdataList:g.updataList,userIdentityType:i.userIdentityType,dataList:i.listData},null,8,["onChangeBottomTab","onUpdataList","userIdentityType","dataList"]),r("div",{style:{"min-height":"100rpx"}})])])),_:1}))])),_:1}),d(v,{style:{height:"200rpx"}})])),_:1})):p("",!0),2==i.pitch&&3==i.userIdentityType?(l(),o(F,{key:1,ref:"serviceManager"},null,512)):p("",!0),3==i.pitch&&3==i.userIdentityType?(l(),o(w,{key:2,ref:"wallet",bizType:2},null,512)):p("",!0),3==i.userIdentityType&&i.showBtns?(l(),o(v,{key:3,class:"bottomTab"},{default:n((()=>[d(v,{onClick:a[0]||(a[0]=t=>g.changTab(1)),class:"singleTabs",style:h({color:1==i.pitch?"#3370FF":"#86909C"})},{default:n((()=>[1==i.pitch?(l(),o(k,{key:0,class:"my",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAANlBMVEUAAAAycP80cP8ycP8zcP8xcf8xc/8zcP8zb/8tbv8gYP8ycP8ycP80b/8yb/8yb/8yav8zcP/mEVZkAAAAEXRSTlMAf0Dfnz8fv8UYCI1wY/NMJHYWWYYAAACcSURBVEjH7dHRDsMQGIZhP1pUtfvu/2a3LilKsYM1kabPoXjDD7sjMxyY5v4FB0ur4EhwVjA52khAkacASRs3Zfs1dsQ8wk6nhUM9gEsCAmb+MQNCeSKsUh7w06GH72opCLOEuzeC3BOcBWaMmB8CgYi4IrAyYrt4pQ4DLSL6gn9ITuhi6Cf4byCpSObBioaVJSyqLMu8eMXI7uQNrIcaFI2qofcAAAAASUVORK5CYII="})):(l(),o(k,{key:1,class:"my",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAAMFBMVEUAAAA+Slg/SFg/SVk/SVg/SlhBSVk+SldAQEA/SVg+SVg9SVk+SFg/SVg8Slc/Sllql5PeAAAAD3RSTlMAf0DfwJ8fGAjHjXBj80xS9tlzAAAApUlEQVQ4y2OgJyhSAgN1dHH2+//B4G8BmgTTfyhQQBbtERQU/e8oCAQi/wMFBU/AxDnmg5QKgJiMINbPBqgEz39Uif8HgCwIb5OS9n8TFyBwBjMF4BIKyJYzIUuA7YGYjyaBAINKgj0NDAowJJgh/jAgXoIrFAwWDC4PshiDgQMhf2DqGFz+oLlEoCASEIVL8P1HAw9gmtejiv+Cm8qmhAISGOgEAJYTw6MpjVLtAAAAAElFTkSuQmCC"})),I(" 我的订单 ")])),_:1},8,["style"]),d(v,{onClick:a[1]||(a[1]=t=>g.changTab(2)),class:"singleTabs",style:h({color:2==i.pitch?"#3370FF":"#86909C"})},{default:n((()=>[2==i.pitch?(l(),o(k,{key:0,class:"my",src:q})):(l(),o(k,{key:1,class:"my",src:J})),I(" 服务管理 ")])),_:1},8,["style"]),d(v,{onClick:a[2]||(a[2]=t=>g.changTab(3)),class:"singleTabs",style:h({color:3==i.pitch?"#3370FF":"#86909C"})},{default:n((()=>[3==i.pitch?(l(),o(k,{key:0,class:"my",src:O})):(l(),o(k,{key:1,class:"my",src:H})),I(" 我的钱包 ")])),_:1},8,["style"])])),_:1})):p("",!0)])),_:1},8,["class"])}],["__scopeId","data-v-697b2225"]]);export{X as default};
