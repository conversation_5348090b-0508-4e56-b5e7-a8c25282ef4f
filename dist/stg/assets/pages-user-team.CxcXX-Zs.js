import{Z as s,l as e,Y as a,A as t,c as l,w as o,o as i,B as n,d,a1 as c}from"./index-CBCsGYoT.js";import{v as r}from"./webviewUrl.D8mk1f89.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const u=k({data:()=>({tokenData:"",url:"",isFirstLogin:!0,webV:null}),onLoad(e){s({title:"加载中"})},onShow(){let s=`${r}team`;this.tokenData=e("token"),this.url=`${s}?token=${this.tokenData}&id=${(new Date).getTime()}`},mounted(){},methods:{loadSuccess(){a()}}},[["render",function(s,e,a,r,k,u){const g=c,v=t("View");return i(),l(v,null,{default:o((()=>[n("div",{class:"skeleton"},[n("div",{class:"chatBg"}),n("div",{class:"skeletonlist"},[n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"}),n("div",{class:"skeletonBig"})])]),k.url?(i(),l(g,{key:0,src:k.url,onMessage:s.handlePostMessage,title:"我的团队",onLoad:u.loadSuccess},null,8,["src","onMessage","onLoad"])):d("",!0)])),_:1})}],["__scopeId","data-v-26ef62c5"]]);export{u as default};
