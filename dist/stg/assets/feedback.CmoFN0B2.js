import{p as e,z as s,o as i,c as t,w as a,V as r,f as o,B as n,i as l,l as d,H as p,I as m,a as c,b as h,D as u,t as f,F as v,n as b}from"./index-HcMwrp5e.js";import{_ as C}from"./uni-popup.BdZPMDVN.js";import{r as g}from"./uni-app.es.DFp0WTX7.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as I}from"./uni-easyinput.CAhWtyu2.js";import{_ as L,a as F}from"./uni-forms.i1XM9iHP.js";const A=k({data:()=>({porpsData:"",loading:!1}),methods:{opens(e){this.porpsData=e,this.$refs.chargeback.open()},claimFn(){this.$refs.chargeback.close()},retreat(){this.loading||(this.loading=!0,this.$api.demandcancelAPI({data:{demandId:this.porpsData.demandId},method:"post"}).then((s=>{e({title:"撤销成功",icon:"none"}),setTimeout((()=>{this.$emit("updataList"),this.claimFn(),this.loading=!1}),500)})).catch((e=>{this.loading=!1})))}}},[["render",function(e,d,p,m,c,h){const u=g(s("uni-popup"),C),f=l;return i(),t(f,null,{default:a((()=>[(i(),t(r,{to:"body"},[o(u,{ref:"chargeback","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[n("div",{class:"applyaffirm"},[n("div",{class:"p1"},"撤销"),n("div",{class:"p2"},"执行撤销后将不能收到该需求相关推荐企业反馈信息，是否继续撤销 "),n("div",{class:"popBtn"},[n("div",{onClick:d[0]||(d[0]=(...e)=>h.claimFn&&h.claimFn(...e)),class:"affirm"}," 取消 "),n("div",{onClick:d[1]||(d[1]=(...e)=>h.retreat&&h.retreat(...e)),class:"canleBt"}," 确认 ")])])])),_:1},512)]))])),_:1})}],["__scopeId","data-v-0a7c6010"]]);const y=k({data:()=>({time:null,AssociativeList:[],showAssociativeList:!1,forbiddenCode:!1,porpsData:"",safeArea:d("safeAreaRpx"),form:{enterpriseName:"",enterpriseUniCode:"",reason:""},enterpriseId:"",submitLoading:!1,rules:{enterpriseName:{rules:[{required:!0,errorMessage:"请输入企业名称"}]},enterpriseUniCode:{rules:[{required:!0,errorMessage:"请输入社会信用代码"}]},reason:{rules:[{required:!0,errorMessage:"请输入推荐理由"},{maxLength:500,errorMessage:"推荐理由最多500个字"}]}}}),methods:{enterpriseNameBlur(){this.AssociativeList.forEach((e=>{e.enterprise===this.form.enterpriseName&&(this.form.enterpriseUniCode=e.enterpriseUniCode,this.forbiddenCode=!0,this.enterpriseId=e.enterpriseId)}))},enterpriseNameFocus(){this.showAssociativeList=!0},SelectEn(e){this.showAssociativeList=!1,this.forbiddenCode=!0,this.form.enterpriseName=e.enterprise,this.form.enterpriseUniCode=e.enterpriseUniCode,this.enterpriseId=e.enterpriseId},opens(e){this.porpsData=e,this.$refs.hint.open()},changeenterpriseName(e){this.forbiddenCode=!1,this.form.enterpriseUniCode="",this.enterpriseId="",null!=this.time&&clearTimeout(this.time),this.time=setTimeout((async()=>{e?this.$api.searchenterprise({data:{keyword:e},method:"get"}).then((e=>{this.AssociativeList=e.result.slice(0,5),this.showAssociativeList=!0})):this.AssociativeList=[]}),400)},continueFn(){this.$refs.hint.close(),this.$refs.follow.open("bottom"),this.$emit("updataTab",!1)},canleFn(){this.$refs.hint.close()},claimFn(){this.form={enterpriseName:"",enterpriseUniCode:"",reason:""},this.AssociativeList=[],this.showAssociativeList=!1,this.enterpriseId="",this.$refs.follow.close(),this.$emit("updataTab",!0)},submit(){this.submitLoading||(this.submitLoading=!0,this.$refs.valiForm.validate().then((s=>{let i={demandId:this.porpsData.id,enterpriseName:this.form.enterpriseName,enterpriseUniCode:this.form.enterpriseUniCode,enterpriseId:this.enterpriseId,reason:this.form.reason};this.$api.workerrecommendAPI({method:"post",data:i}).then((s=>{this.submitLoading=!1,e({title:"反馈成功,请等待运营平台审核",icon:"none"}),this.claimFn(),setTimeout((()=>{this.$emit("updataList")}),1e3)})).catch((e=>{this.submitLoading=!1}))})).catch((e=>{this.submitLoading=!1})))}}},[["render",function(e,r,d,k,A,y){const _=g(s("uni-easyinput"),I),U=g(s("uni-forms-item"),L),w=g(s("uni-forms"),F),N=g(s("uni-popup"),C),$=l;return i(),t($,null,{default:a((()=>[o(N,{ref:"follow","safe-area":!1,"is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[n("div",{onClick:r[5]||(r[5]=e=>A.showAssociativeList=!1),class:"applyaffirm"},[n("div",{class:"head"},"反馈"),n("div",{class:"followForm"},[o(w,{ref:"valiForm",rules:A.rules,modelValue:A.form,"label-position":"top","label-width":"100"},{default:a((()=>[o(U,{name:"enterpriseName",label:"企业名称",required:""},{default:a((()=>[n("div",{class:"followInput"},[n("div",{style:{position:"relative"}},[o(_,{trim:"all",onFocus:y.enterpriseNameFocus,onBlur:y.enterpriseNameBlur,onInput:y.changeenterpriseName,clearable:!1,modelValue:A.form.enterpriseName,"onUpdate:modelValue":r[0]||(r[0]=e=>A.form.enterpriseName=e),placeholder:"请输入"},null,8,["onFocus","onBlur","onInput","modelValue"]),p(n("div",{class:"AssociativeList"},[(i(!0),c(v,null,h(A.AssociativeList,((e,s)=>(i(),c("div",{onClick:u((s=>y.SelectEn(e)),["stop"]),class:"AssociativeItem",key:s},f(e.enterprise),9,["onClick"])))),128))],512),[[m,A.showAssociativeList]])])])])),_:1}),o(U,{name:"enterpriseUniCode",label:"企业社会信用代码",required:""},{default:a((()=>[n("div",{class:"followInput"},[A.forbiddenCode?(i(),c("div",{key:1,class:"enterpriseUniCode"},f(A.form.enterpriseUniCode),1)):(i(),t(_,{key:0,trim:"all",clearable:!1,modelValue:A.form.enterpriseUniCode,"onUpdate:modelValue":r[1]||(r[1]=e=>A.form.enterpriseUniCode=e),placeholder:"请输入"},null,8,["modelValue"]))])])),_:1}),o(U,{name:"reason",label:"推荐理由",required:""},{default:a((()=>[n("div",{class:"summarizeInput"},[o(_,{type:"textarea",trim:"all",clearable:!1,modelValue:A.form.reason,"onUpdate:modelValue":r[2]||(r[2]=e=>A.form.reason=e),placeholder:"请输入"},null,8,["modelValue"])])])),_:1})])),_:1},8,["rules","modelValue"])]),n("div",{class:"btnBox"},[n("div",{class:"canle",onClick:r[3]||(r[3]=(...e)=>y.claimFn&&y.claimFn(...e))},"取消"),n("div",{class:"submit",onClick:r[4]||(r[4]=(...e)=>y.submit&&y.submit(...e))},"提交")]),n("div",{style:b({height:A.safeArea})},null,4)])])),_:1},512),o(N,{ref:"hint","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:a((()=>[n("div",{class:"hintBox"},[n("div",{class:"p1"},"温馨提示"),n("div",{class:"p2"}," 推荐企业的信息在反馈给需求发布人之前，需要经过运营平台的人工校验、修正和审核。只有通过审核的企业信息才会反馈给需求发布人。此外，委托人需先表达意向并发起委托单，随后根据跟进情况决定是否支付费用。如果委托人无意向，则无需支付任何费用。 是否继续？ "),n("div",{class:"popBtn"},[n("div",{onClick:r[6]||(r[6]=(...e)=>y.canleFn&&y.canleFn(...e)),class:"canleBt"}," 取消 "),n("div",{onClick:r[7]||(r[7]=(...e)=>y.continueFn&&y.continueFn(...e)),class:"affirm"}," 继续反馈 ")])])])),_:1},512)])),_:1})}],["__scopeId","data-v-e0f8e6b6"]]);export{y as f,A as r};
