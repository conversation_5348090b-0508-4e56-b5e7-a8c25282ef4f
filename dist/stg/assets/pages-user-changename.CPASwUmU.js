import{p as e,U as a,l as s,z as t,A as o,a as i,f as l,w as r,F as n,i as u,o as d,c as m,d as p,B as c,e as f,t as y}from"./index-CBCsGYoT.js";import{_ as h}from"./page-meta.BCoSkkXs.js";import{r as I}from"./uni-app.es.CZb2JZWI.js";import{_ as b}from"./uni-easyinput.D_LnJWIZ.js";import{_ as g,a as v}from"./uni-forms.BsccYHPu.js";import{_ as C}from"./uni-icons.Dr3tmUrM.js";import{_ as V}from"./zxz-uni-data-select.CW_37m_-.js";import{_ as k}from"./uni-data-select.DWY_AY9U.js";import{_}from"./uni-popup.BLXBf1r-.js";import{t as L}from"./index.D5F8338M.js";import{m as N}from"./mapList2.BCv64X7E.js";import{m as j}from"./mapList.8RZSfjp3.js";import{_ as q}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-transition.Ckb0qY8x.js";import"./returnFn.BYkANsDr.js";const T=q({components:{tabBar:L,mapList2:N,mapList:j},data:()=>({baseInfo:{realName:""},title:"编辑个人信息",rules:{realName:{rules:[{required:!0,errorMessage:"请输入姓名"},{maxLength:5,errorMessage:"昵称最多五个字"}]},jobTitle:{rules:[{required:!0,errorMessage:"请输入所在单位职务"}]},company:{rules:[{required:!0,errorMessage:"请输入所在单位"}]},advantageCity:{rules:[{required:!0,errorMessage:"请输入优势资源城市"}]},resume:{rules:[{required:!0,errorMessage:"请输入优势资源简述"}]},referrer:{rules:[{required:!0,errorMessage:"请输入引荐人"}]},focusIndustryIds:{rules:[{required:!0,errorMessage:"请选择关注产业链"}]},InvitationCode:{rules:[{required:!0,errorMessage:"请输入邀请码"}]},overseaTargetCodes:{rules:[{required:!0,errorMessage:"请输入出海关注目的地"}]},enterpriseProfile:{rules:[{required:!0,errorMessage:"请输入企业简介"}]},serviceCountryCode:{rules:[{required:!0,errorMessage:"请选择服务国家"}]},serviceItemCodes:{rules:[{required:!0,errorMessage:"请选择服务类目"}]}},type:1,industryList:[],countryList:[],showMap:!1,categoryList:[]}),computed:{},onLoad(e){this.type=e.type,8==this.type?this.getindustryList():11==this.type||13==this.type?this.getCountryList():14==this.type?this.getCategoryList():this.userdescribeFn()},methods:{getCategoryList(){this.$api.catalogListAPI({token:this.token,method:"POST"}).then((e=>{e.result.forEach((e=>{this.categoryList.push({text:e.catalogName,value:parseInt(e.catalogId)})})),this.userdescribeFn()}))},getCountryList(){this.industryList=[],this.$api.countryListAPI({token:this.token}).then((e=>{e.result.forEach((e=>{this.countryList.push({text:e.country,value:e.id})})),this.userdescribeFn()}))},getindustryList(){this.industryList=[],this.$api.listAllPurchaseChain({token:this.token}).then((e=>{e.result.forEach((e=>{this.industryList.push({text:e.name,value:parseInt(e.id)})})),this.userdescribeFn()}))},userdescribeFn(){this.$api.userMessage().then((e=>{this.baseInfo=e.result;for(let a in this.baseInfo.userCompletionInfoDO)this.baseInfo.userCompletionInfoDO.hasOwnProperty(a)&&(this.baseInfo[a]=this.baseInfo.userCompletionInfoDO[a]);this.baseInfo.focusIndustryIds&&(this.baseInfo.focusIndustryIds=this.baseInfo.focusIndustryIds.map((e=>parseInt(e)))),4!=this.type&&9!=this.type||(this.showMap=!0)})).catch((e=>{this.baseInfo={}}))},affirm(e){var a,s,t;this.$refs.popup.close(),this.baseInfo.code=[],this.baseInfo.codeName=(null==(a=e.province)?void 0:a.name)+((null==(s=e.citys)?void 0:s.name)?"/"+e.citys.name:"")+((null==(t=e.area)?void 0:t.name)?"/"+e.area.name:"");let o=Object.keys(e).map((a=>e[a]&&e[a].code&&""!==e[a].code?e[a].code:null));this.baseInfo.code=o.filter((e=>null!==e))},affirm2(e){var a,s,t;this.$refs.popup2.close(),this.baseInfo.cityCode=[],this.baseInfo.cityName=(null==(a=e.province)?void 0:a.name)+((null==(s=e.citys)?void 0:s.name)?"/"+e.citys.name:"")+((null==(t=e.area)?void 0:t.name)?"/"+e.area.name:"");let o=Object.keys(e).map((a=>e[a]&&e[a].code&&""!==e[a].code?e[a].code:null));this.baseInfo.cityCode=o.filter((e=>null!==e))},pop(){this.$refs.popup.open("bottom")},pop2(){this.$refs.popup2.open("bottom")},jointheteam(){this.$api.workerapplyAPI({data:{invitationCode:this.baseInfo.InvitationCode},method:"post"}).then((s=>{e({title:"加入成功",icon:"success"}),setTimeout((()=>{a({delta:1})}),1e3)}))},submit(t){this.$refs[t].validate().then((t=>{if(7==this.type)return void this.jointheteam();let o={};if(1==this.type)o.realName=this.baseInfo.realName;else if(2==this.type)o.email=this.baseInfo.email;else if(3==this.type)o.company=this.baseInfo.company;else if(4==this.type){if(0==this.baseInfo.cityCode.length)return e({title:"请选择所在地区",icon:"none"});o.cityName=this.baseInfo.cityName,o.cityCode=this.baseInfo.cityCode[this.baseInfo.cityCode.length-1]}else if(5==this.type)o.advantageCity=this.baseInfo.advantageCity;else if(6==this.type)o.resume=this.baseInfo.resume;else if(8==this.type)o.focusIndustryIds=this.baseInfo.focusIndustryIds;else if(9==this.type){if(0==this.baseInfo.code.length)return e({title:"请选择招商属地",icon:"none"});o.codeName=this.baseInfo.codeName,o.code=this.baseInfo.code[this.baseInfo.code.length-1]}else 10==this.type?o.jobTitle=this.baseInfo.jobTitle:11==this.type?o.overseaTargetCodes=this.baseInfo.overseaTargetCodes:12==this.type?o.enterpriseProfile=this.baseInfo.enterpriseProfile:13==this.type?o.serviceCountryCode=this.baseInfo.serviceCountryCode:14==this.type&&(o.serviceItemCodes=this.baseInfo.serviceItemCodes);o.userId=s("userId"),this.$api.updateHeadImg({data:o,method:"POST"}).then((s=>{"SUCCESS"==s.code?(e({title:"提交成功",icon:"success"}),setTimeout((()=>{a({delta:1})}),1e3)):e({title:s.msg||"更改失败",icon:"none"})}))})).catch((e=>{}))}}},[["render",function(e,a,s,L,N,j){const q=I(t("page-meta"),h),T=o("tabBar"),M=I(t("uni-easyinput"),b),x=I(t("uni-forms-item"),g),U=I(t("uni-icons"),C),F=I(t("zxz-uni-data-select"),V),P=I(t("uni-data-select"),k),$=I(t("uni-forms"),v),w=u,A=o("mapList2"),S=I(t("uni-popup"),_),O=o("mapList");return d(),i(n,null,[l(q,{"page-style":"background-color: #FAFCFF"}),l(w,{class:"container"},{default:r((()=>[l(T,{title:N.title},null,8,["title"]),l(w,{class:"example"},{default:r((()=>[l($,{ref:"valiForm",rules:N.rules,modelValue:N.baseInfo,"label-position":"top","label-width":"100"},{default:r((()=>[1==N.type?(d(),m(x,{key:0,label:"姓名",required:"",name:"realName"},{default:r((()=>[l(M,{modelValue:N.baseInfo.realName,"onUpdate:modelValue":a[0]||(a[0]=e=>N.baseInfo.realName=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1})):p("",!0),2==N.type?(d(),m(x,{key:1,label:"邮箱",name:"email"},{default:r((()=>[l(M,{modelValue:N.baseInfo.email,"onUpdate:modelValue":a[1]||(a[1]=e=>N.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})):p("",!0),3==N.type?(d(),m(x,{key:2,label:"所在单位",required:"",name:"company"},{default:r((()=>[l(M,{modelValue:N.baseInfo.company,"onUpdate:modelValue":a[2]||(a[2]=e=>N.baseInfo.company=e),placeholder:"请输入所在单位"},null,8,["modelValue"])])),_:1})):p("",!0),4==N.type?(d(),m(x,{key:3,label:"所在城市",required:"",name:"cityCode"},{default:r((()=>{var e,s;return[c("div",{onClick:a[3]||(a[3]=e=>j.pop2()),class:"citySelect"},[c("span",{class:f(""==(null==(e=N.baseInfo)?void 0:e.cityName)?"":"citySelectText")},y(""==(null==(s=N.baseInfo)?void 0:s.cityName)?"请选择所在城市":this.baseInfo.cityName),3),l(U,{color:"#999",type:"down",size:"14"})])]})),_:1})):p("",!0),5==N.type?(d(),m(x,{key:4,label:"优势资源城市",required:"",name:"advantageCity"},{default:r((()=>[l(M,{modelValue:N.baseInfo.advantageCity,"onUpdate:modelValue":a[4]||(a[4]=e=>N.baseInfo.advantageCity=e),placeholder:"请输入优势资源城市"},null,8,["modelValue"])])),_:1})):p("",!0),6==N.type?(d(),m(x,{key:5,label:"优势资源简述",required:"",name:"resume"},{default:r((()=>[l(M,{modelValue:N.baseInfo.resume,"onUpdate:modelValue":a[5]||(a[5]=e=>N.baseInfo.resume=e),placeholder:"请输入优势资源简述"},null,8,["modelValue"])])),_:1})):p("",!0),7==N.type?(d(),m(x,{key:6,label:"邀请码",required:"",name:"InvitationCode"},{default:r((()=>[l(M,{modelValue:N.baseInfo.InvitationCode,"onUpdate:modelValue":a[6]||(a[6]=e=>N.baseInfo.InvitationCode=e),placeholder:"请填写邀请码(填写后不允许修改)"},null,8,["modelValue"])])),_:1})):p("",!0),8==N.type?(d(),m(x,{key:7,label:"关注产业链",required:"",name:"focusIndustryIds"},{default:r((()=>[l(F,{modelValue:N.baseInfo.focusIndustryIds,"onUpdate:modelValue":a[7]||(a[7]=e=>N.baseInfo.focusIndustryIds=e),multiple:!0,localdata:N.industryList,collapseTagsNum:2,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):p("",!0),9==N.type?(d(),m(x,{key:8,label:"招商属地",required:"",name:"focusIndustryIds"},{default:r((()=>{var e,s;return[c("div",{onClick:a[8]||(a[8]=e=>j.pop()),class:"citySelect"},[c("span",{class:f(""==(null==(e=N.baseInfo)?void 0:e.codeName)?"":"citySelectText")},y(""==(null==(s=N.baseInfo)?void 0:s.codeName)?"请选择招商属地":this.baseInfo.codeName),3),l(U,{color:"#999",type:"down",size:"14"})])]})),_:1})):p("",!0),10==N.type?(d(),m(x,{key:9,label:"所在单位职务",required:"",name:"jobTitle"},{default:r((()=>[l(M,{modelValue:N.baseInfo.jobTitle,"onUpdate:modelValue":a[9]||(a[9]=e=>N.baseInfo.jobTitle=e),placeholder:"请输入所在单位职务"},null,8,["modelValue"])])),_:1})):p("",!0),11==N.type?(d(),m(x,{key:10,label:"出海关注目的地",required:"",name:"overseaTargetCodes"},{default:r((()=>[l(F,{modelValue:N.baseInfo.overseaTargetCodes,"onUpdate:modelValue":a[10]||(a[10]=e=>N.baseInfo.overseaTargetCodes=e),multiple:!0,localdata:N.countryList,collapseTagsNum:2,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):p("",!0),12==N.type?(d(),m(x,{key:11,label:"企业简介",required:"",name:"enterpriseProfile"},{default:r((()=>[l(M,{modelValue:N.baseInfo.enterpriseProfile,"onUpdate:modelValue":a[11]||(a[11]=e=>N.baseInfo.enterpriseProfile=e),placeholder:"请输入企业简介"},null,8,["modelValue"])])),_:1})):p("",!0),13==N.type?(d(),m(x,{key:12,label:"服务国家",required:"",name:"serviceCountryCode"},{default:r((()=>[l(P,{modelValue:N.baseInfo.serviceCountryCode,"onUpdate:modelValue":a[12]||(a[12]=e=>N.baseInfo.serviceCountryCode=e),localdata:N.countryList},null,8,["modelValue","localdata"])])),_:1})):p("",!0),14==N.type?(d(),m(x,{key:13,label:"服务类目",required:"",name:"serviceItemCodes"},{default:r((()=>[l(F,{modelValue:N.baseInfo.serviceItemCodes,"onUpdate:modelValue":a[13]||(a[13]=e=>N.baseInfo.serviceItemCodes=e),multiple:!0,localdata:N.categoryList,collapseTagsNum:2,maximum:9999,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):p("",!0)])),_:1},8,["rules","modelValue"]),c("div",{class:"experience",type:"primary",onClick:a[14]||(a[14]=e=>j.submit("valiForm"))}," 保存 ")])),_:1}),l(S,{ref:"popup2","background-color":"#fff"},{default:r((()=>[N.showMap?(d(),m(A,{key:0,onAffirm:j.affirm2,cityName:N.baseInfo.cityName},null,8,["onAffirm","cityName"])):p("",!0)])),_:1},512),l(S,{ref:"popup","background-color":"#fff"},{default:r((()=>[N.showMap?(d(),m(O,{key:0,onAffirm:j.affirm,cityName:N.baseInfo.codeName},null,8,["onAffirm","cityName"])):p("",!0)])),_:1},512)])),_:1})],64)}],["__scopeId","data-v-70e5435e"]]);export{T as default};
