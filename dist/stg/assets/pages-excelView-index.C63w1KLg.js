import{l as e,Z as t,Y as s,c as a,w as o,i as r,o as i,f as n,a1 as l}from"./index-HcMwrp5e.js";import{v as d}from"./webviewUrl.D8mk1f89.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";const c=p({data:()=>({tokenData:"",url:"",isFirstLogin:!0,webV:null}),onLoad(s){this.tokenData=e("token"),t({title:"加载中"}),this.setUrl(s)},onShow(e){},methods:{loadSuccess(){s()},setUrl(e){decodeURI(e.type);let t="pdf"===e.type?`${d}previewPdf`:`${d}previewExcel`;"pdf"===e.type&&e.id?this.url=`${t}?reportId=${e.id}&enterpriseName=${e.enterpriseName}&token=${this.tokenData}&id=${(new Date).getTime()}`:this.url=`${t}?id=${(new Date).getTime()}&token=${this.tokenData}`,console.log(this.url)}}},[["render",function(e,t,s,d,p,c){const u=l,h=r;return i(),a(h,{class:"webview"},{default:o((()=>[n(u,{src:p.url,onMessage:e.handlePostMessage,title:"历史记录",onLoad:c.loadSuccess},null,8,["src","onMessage","onLoad"])])),_:1})}],["__scopeId","data-v-b8590376"]]);export{c as default};
