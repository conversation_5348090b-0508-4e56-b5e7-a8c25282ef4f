import{l as e,p as o,m as t,q as a,T as s,s as i,z as l,A as n,a as r,f as c,w as u,F as d,i as m,o as p,B as g,g as h,c as f,a5 as y,C as v}from"./index-CBCsGYoT.js";import{_ as C}from"./page-meta.BCoSkkXs.js";import{r as k}from"./uni-app.es.CZb2JZWI.js";import{_ as b}from"./uni-easyinput.D_LnJWIZ.js";import{_ as D,a as _}from"./uni-forms.BsccYHPu.js";import{_ as F}from"./uni-data-checkbox.lGJQWvI7.js";import{_ as I}from"./uni-popup.BLXBf1r-.js";import{J as x}from"./JSEncrypt.BbYaXJGF.js";import{q as j}from"./quicklogin.Da0jxcA0.js";import{_ as A}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.Dr3tmUrM.js";import"./uni-cloud.es.Dc96wm0-.js";import"./uni-load-more.BMOBclrH.js";import"./uni-transition.Ckb0qY8x.js";const q=A({components:{quickLogin:j},data:()=>({consent:!1,whetherClick:!1,publicKey:"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB",valiFormData:{name:"",age:""},checkbox:[],hobby:[{text:"",value:0}],rules:{name:{rules:[{required:!0,errorMessage:"手机号不能为空"},{minLength:11,maxLength:11,errorMessage:"请输入11位合法手机号"}]},age:{rules:[{required:!0,errorMessage:"密码不能为空"}]}}}),created(){},onShow(){},onLoad(){e("consent")&&(this.checkbox=[0]),this.valiFormData.name=e("username")},methods:{closeFn(){this.$refs.Dialog.close()},Unchecked(){o({title:"请勾选用户协议",icon:"error"})},NoLogin(){t({url:"/pages/repository/capacity"})},go(e){"agreement"==e?a({url:`/pages/login/components/${e}`}):t({url:`/pages/login/components/${e}`})},bindingId(e){let o=this;s({success(t){o.$api.syncUnion({data:{code:t.code},token:e,method:"post"}).then((e=>{})).catch((e=>{}))}})},getorgCode(e){this.$api.ownList({token:e}).then((e=>{var o,t,a;i("orgCode",null==(o=e.result.selected)?void 0:o.orgCode),i("orgName",null==(t=e.result.selected)?void 0:t.orgName),i("isDefault",null==(a=e.result.selected)?void 0:a.isDefault)}))},whereTogo(){e("teamCode"),this.$api.userMessage().then((e=>{var o,a;i("userIdentityType",null==(o=e.result.userCompletionInfoDO)?void 0:o.userIdentity),5==(null==(a=e.result.userCompletionInfoDO)?void 0:a.userIdentity)?i("identity",2):i("identity",1),t({url:"/pages/repository/capacity"})}))},submit(a){if(0==this.checkbox.length)return o({title:"请勾选用户协议",icon:"error"});this.$refs[a].validate().then((a=>{const s=new x;s.setPublicKey(this.publicKey);let l=s.encrypt(this.valiFormData.age),n=this,r={username:n.valiFormData.name,password:l,loginType:"XC_AI",invitationCode:e("invitationCode")||""};n.$api.login({data:r,method:"post"}).then((e=>{"SUCCESS"==e.code?n.$api.identityTypeAPI({method:"GET",token:e.result.token}).then((a=>{var s,l,r,c;if(1!=(null==(s=a.result)?void 0:s.identityCode)&&5!=(null==(l=a.result)?void 0:l.identityCode))return o({title:"身份限制,请联系管理员",icon:"none"});i("token",e.result.token),i("phone",e.result.username),i("userId",e.result.userId),i("consent",!0),n.getorgCode(e.result.token),n.bindingId(e.result.token),!0===(null==(r=a.result)?void 0:r.needCompletion)?t({url:`/pages/user/perfectionInfo?identityCode=${null==(c=a.result)?void 0:c.identityCode}`}):n.whereTogo()})).catch((e=>{"NOT_ALLOWED"==e.code&&n.whereTogo()})):o({title:e.msg,icon:"none"})})).catch((e=>{"ACCOUNT_UNAUTHORIZED"==e.code&&setTimeout((()=>{this.$refs.Dialog.open()}),1e3)}))})).catch((e=>{}))}}},[["render",function(e,o,t,a,s,i){const x=k(l("page-meta"),C),j=k(l("uni-easyinput"),b),A=k(l("uni-forms-item"),D),q=k(l("uni-forms"),_),V=k(l("uni-data-checkbox"),F),L=y,w=n("quickLogin"),U=m,B=v,O=k(l("uni-popup"),I);return p(),r(d,null,[c(x,{"page-style":"background-color: #FAFCFF"}),c(U,{class:"login"},{default:u((()=>[g("div",{class:"t1"},"账号密码登录"),g("div",{class:"t2"},"请使用已注册的账号密码"),c(U,{class:"example"},{default:u((()=>[c(q,{ref:"valiForm",rules:s.rules,modelValue:s.valiFormData},{default:u((()=>[c(A,{class:"forms-item",required:"",name:"name"},{default:u((()=>[c(j,{clearable:!1,modelValue:s.valiFormData.name,"onUpdate:modelValue":o[0]||(o[0]=e=>s.valiFormData.name=e),placeholder:"请输入手机号",placeholderStyle:"font-size:26rpx",trim:!0},null,8,["modelValue"])])),_:1}),c(A,{class:"forms-item",required:"",name:"age"},{default:u((()=>[c(j,{type:"password",modelValue:s.valiFormData.age,"onUpdate:modelValue":o[1]||(o[1]=e=>s.valiFormData.age=e),placeholder:"请输入密码",placeholderStyle:"font-size:26rpx",trim:!0},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","modelValue"]),g("div",{class:"t3"},[c(V,{multiple:"",modelValue:s.checkbox,"onUpdate:modelValue":o[2]||(o[2]=e=>s.checkbox=e),localdata:s.hobby},null,8,["modelValue","localdata"]),g("div",{class:"agreement"},[h("阅读并同意 "),g("span",{onClick:o[3]||(o[3]=e=>i.go("agreement"))},"《用户协议》")])]),c(L,{class:"loginButton",onClick:o[4]||(o[4]=e=>i.submit("valiForm")),type:"primary"},{default:u((()=>[h("登 录")])),_:1}),0==s.checkbox.length?(p(),f(L,{key:0,class:"ClickLogin",type:"primary",onClick:i.Unchecked},{default:u((()=>[h("本机一键登录")])),_:1},8,["onClick"])):(p(),f(w,{key:1})),c(U,{class:"else"},{default:u((()=>[g("span",{class:"cancelLogin",onClick:o[5]||(o[5]=e=>i.NoLogin())},"取消登录")])),_:1})])),_:1}),c(O,{ref:"Dialog",type:"dialog"},{default:u((()=>[c(B,{"show-menu-by-longpress":"true",class:"contrastImg",src:"https://static.idicc.cn/cdn/aiChat/newServiceQRCode.png"}),g("div",{onClick:o[6]||(o[6]=(...e)=>i.closeFn&&i.closeFn(...e)),class:"close"})])),_:1},512)])),_:1})],64)}],["__scopeId","data-v-af4a1705"]]);export{q as default};
