import{U as e,z as n,a as t,f as s,w as a,F as i,i as r,o as l,c as o,B as c,g as p,t as d,d as u,b as f,e as I,H as m,I as v,h,C as A}from"./index-HcMwrp5e.js";import{_ as k}from"./page-meta.X-Lr6csD.js";import{r as g}from"./uni-app.es.DFp0WTX7.js";import{_}from"./uni-tooltip.Bpz4R-Ep.js";import{_ as y}from"./uni-transition.CHOJlBbg.js";import{_ as b}from"./uni-popup.BdZPMDVN.js";import{t as S}from"./index.B6Rbewql.js";import{l as D}from"./utils.61Hi-B7M.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const x=P({components:{tabBar:S},data:()=>({showString:!1,codeTabId:1,listicon:D,enterpriseInfo:{},iconTypeid:0,showPop:!1,show:!1,title:"园区详情",chainsIndex:0}),onLoad(e){this.getdetail(e.id),e.iconTypeid&&(this.iconTypeid=e.iconTypeid)},methods:{priceFn(){var e,n,t,s,a;return(null==(e=this.enterpriseInfo)?void 0:e.factoryRentPriceMax)!=(null==(n=this.enterpriseInfo)?void 0:n.factoryRentPriceMin)?(null==(t=this.enterpriseInfo)?void 0:t.factoryRentPriceMin)+"-"+(null==(s=this.enterpriseInfo)?void 0:s.factoryRentPriceMax)+this.enterpriseInfo.rentPriceUnit:(null==(a=this.enterpriseInfo)?void 0:a.factoryRentPriceMin)+this.enterpriseInfo.rentPriceUnit},priceFn2(){var e,n,t,s,a;return(null==(e=this.enterpriseInfo)?void 0:e.landSalePriceMax)!=(null==(n=this.enterpriseInfo)?void 0:n.landSalePriceMin)?(null==(t=this.enterpriseInfo)?void 0:t.landSalePriceMin)+"-"+(null==(s=this.enterpriseInfo)?void 0:s.landSalePriceMax)+this.enterpriseInfo.salePriceUnit:(null==(a=this.enterpriseInfo)?void 0:a.landSalePriceMin)+this.enterpriseInfo.salePriceUnit},changeTextType(e){this.chainsIndex=e},changeChainsFn(){this.showPop=!this.showPop},qrCodePop(){var e,n,t,s;((null==(n=null==(e=this.enterpriseInfo)?void 0:e.parkImageDTO)?void 0:n.officialSettledPath)||(null==(s=null==(t=this.enterpriseInfo)?void 0:t.parkImageDTO)?void 0:s.interSettledPath))&&this.$refs.qrCode.open()},canlePop(){this.$refs.qrCode.close()},goDetail(e){var n;null==(n=null==window?void 0:window.ReactNativeWebView)||n.postMessage(JSON.stringify({type:"changePath",value:{parkMd5:this.enterpriseInfo.parkMd5,industryParkName:this.enterpriseInfo.industryParkName},path:"businessDirectory"}))},comparisonFn(e){let n="";return this.enterpriseInfo.infoList&&this.enterpriseInfo.infoList.map((t=>{t.infoName==e&&(n=t.content)})),n},goBack(){e({delta:1})},async getdetail(e){let n={parkMd5:e};this.$api.getParkDetailAPI({data:n,method:"GET"}).then((e=>{this.enterpriseInfo=e.result,this.show=!0}))},saveImage(e){var n;null==(n=null==window?void 0:window.ReactNativeWebView)||n.postMessage(JSON.stringify({type:"saveImage",value:e}))}}},[["render",function(e,S,D,P,x,C){const T=g(n("page-meta"),k),B=h,w=r,O=A,F=g(n("uni-tooltip"),_),N=g(n("uni-transition"),y),M=g(n("uni-popup"),b);return l(),t(i,null,[s(T,{"page-style":"background-color: #FAFCFF"}),s(w,null,{default:a((()=>[x.show?(l(),o(w,{key:0,class:"enterpriceDetail"},{default:a((()=>[s(w,{class:"infoBox"},{default:a((()=>{var e,n,t,i;return[s(w,{class:"topBox"},{default:a((()=>[c("img",{src:x.listicon[x.iconTypeid].icon,alt:"",class:"iconImg"},null,8,["src"]),s(B,{selectable:"true",class:"enterpriseName"},{default:a((()=>[p(d(x.enterpriseInfo.industryParkNameEn||x.enterpriseInfo.industryParkName),1)])),_:1})])),_:1}),(null==(n=null==(e=x.enterpriseInfo)?void 0:e.parkImageDTO)?void 0:n.parkImagePath)?(l(),o(O,{key:0,class:"gardenImg",src:null==(i=null==(t=x.enterpriseInfo)?void 0:t.parkImageDTO)?void 0:i.parkImagePath},null,8,["src"])):u("",!0),s(w,{class:"DelBox"},{default:a((()=>{var e;return[s(w,{class:"delTitle"},{default:a((()=>[p("基本信息")])),_:1}),x.enterpriseInfo.industryParkName?(l(),o(w,{key:0,selectable:"true",class:"infoItem"},{default:a((()=>{var e;return[p(" 名称 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.industryParkName,placement:"bottom"},{default:a((()=>[s(B,{selectable:"true",class:"itemDel"},{default:a((()=>{var e;return[p(d(null==(e=x.enterpriseInfo)?void 0:e.industryParkName),1)]})),_:1})])),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.establishYea?(l(),o(w,{key:1,class:"infoItem"},{default:a((()=>{var e;return[p(" 成立年份 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.establishYear,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.establishYear),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.operatingEnterpriseName?(l(),o(w,{key:2,class:"infoItem"},{default:a((()=>{var e;return[p(" 运营机构 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.operatingEnterpriseName,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.operatingEnterpriseName),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.website?(l(),o(w,{key:3,class:"infoItem"},{default:a((()=>{var e;return[p(" 网站 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.website,placement:"bottom"},{default:a((()=>[s(B,{selectable:"true",class:"itemDel"},{default:a((()=>[p(d(x.enterpriseInfo.website),1)])),_:1})])),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.email?(l(),o(w,{key:4,class:"infoItem"},{default:a((()=>{var e;return[p(" 邮箱 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.email,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.email),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.chainNames&&0!=(null==(e=x.enterpriseInfo.chainNames)?void 0:e.length)?(l(),o(w,{key:5,class:"infoItem"},{default:a((()=>{var e,n;return[p(" 主导产业链 "),s(F,{class:"tooltipBox",content:null==(n=null==(e=x.enterpriseInfo)?void 0:e.chainNames)?void 0:n.join("、"),placement:"bottom"},{default:a((()=>{var e,n;return[c("span",{class:"itemDel"},d(null==(n=null==(e=x.enterpriseInfo)?void 0:e.chainNames)?void 0:n.join("、")),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.enterpriseCount?(l(),o(w,{key:6,class:"infoItem"},{default:a((()=>{var e;return[c("span",null,[p("区域生态企业数量"),c("span",{style:{"font-size":"22rpx"}},"(园区企业和园区周边产业相关企业)")]),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.enterpriseCount,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.enterpriseCount),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.landArea?(l(),o(w,{key:7,class:"infoItem"},{default:a((()=>{var e;return[p(" 面积 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.landArea,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.landArea),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.freeSpace?(l(),o(w,{key:8,class:"infoItem"},{default:a((()=>{var e;return[p(" 闲置面积 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.freeSpace,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.freeSpace),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.chineseEnterpriseCount?(l(),o(w,{key:9,class:"infoItem"},{default:a((()=>{var e;return[p(" 中资企业数量 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.chineseEnterpriseCount,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.chineseEnterpriseCount),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.foreignEnterpriseCount?(l(),o(w,{key:10,class:"infoItem"},{default:a((()=>{var e;return[p(" 外资企业数量(含中资) "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo)?void 0:e.foreignEnterpriseCount,placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo)?void 0:e.foreignEnterpriseCount),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.factoryRentPriceMin?(l(),o(w,{key:11,class:"infoItem"},{default:a((()=>[p(" 厂房出租价格"),c("span",{class:"itemDel"}),s(F,{class:"tooltipBox",content:C.priceFn(),placement:"bottom"},{default:a((()=>[c("span",{class:"itemDel"},d(C.priceFn()),1)])),_:1},8,["content"])])),_:1})):u("",!0),x.enterpriseInfo.landSalePriceMin?(l(),o(w,{key:12,class:"infoItem"},{default:a((()=>[p(" 土地出让价格 "),s(F,{class:"tooltipBox",content:C.priceFn2(),placement:"bottom"},{default:a((()=>[c("span",{class:"itemDel"},d(C.priceFn2()),1)])),_:1},8,["content"])])),_:1})):u("",!0),x.enterpriseInfo.location?(l(),o(w,{key:13,class:"infoItem"},{default:a((()=>{var e;return[p(" 区域 "),s(F,{class:"tooltipBox",content:null==(e=x.enterpriseInfo.location)?void 0:e.join("、"),placement:"bottom"},{default:a((()=>{var e;return[c("span",{class:"itemDel"},d(null==(e=x.enterpriseInfo.location)?void 0:e.join("、")),1)]})),_:1},8,["content"])]})),_:1})):u("",!0),x.enterpriseInfo.address?(l(),o(w,{key:14,class:"infoItem"},{default:a((()=>[p(" 地理位置 "),s(F,{class:"tooltipBox",content:x.enterpriseInfo.address,placement:"bottom"},{default:a((()=>[c("span",{class:"itemDel"},d(x.enterpriseInfo.address),1)])),_:1},8,["content"])])),_:1})):u("",!0)]})),_:1})]})),_:1}),x.enterpriseInfo.chains?(l(),o(w,{key:0,class:"infoBox",style:{"margin-top":"24rpx","box-shadow":"0px 4px 12px 0px #EEF1F8"}},{default:a((()=>[c("div",{class:"DelBox"},[s(w,{onClick:S[0]||(S[0]=e=>C.changeChainsFn()),class:"delTitle",style:{"margin-top":"0"}},{default:a((()=>[p(d(x.enterpriseInfo.chains[x.chainsIndex].chainName)+" ",1),s(O,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAJ1BMVEUAAAA/SllASVhASlo/S1pASlpAS1hAUFA/Slo/SlpATFhAUGA/SllyxDYnAAAADHRSTlMA3yAw74BwEK+fQBC19ylaAAAAO0lEQVQI12MgDaicAQJhICMHxDgKZCiCGEJABheIYcAAkTsKVs1x5swEMINb5qACxABDJ6hJ7AVYLQAA8PcVgdxsnx8AAAAASUVORK5CYII=",class:"Arrows"}),s(N,{style:{"z-index":"99"},ref:"ani",show:x.showPop},{default:a((()=>[s(w,{class:"userPop"},{default:a((()=>[(l(!0),t(i,null,f(x.enterpriseInfo.chains,((e,n)=>(l(),o(w,{onClick:e=>C.changeTextType(n),key:n,class:I(x.chainsIndex==n?"popItems":" popItem")},{default:a((()=>[p(d(e.chainName),1)])),_:2},1032,["onClick","class"])))),128))])),_:1})])),_:1},8,["show"])])),_:1}),s(w,{class:"infoItem"},{default:a((()=>[p(" 产业链完整度"),c("span",{class:"itemDel"},d(x.enterpriseInfo.chains[x.chainsIndex].chainPercent),1)])),_:1}),s(w,{class:"infoItem"},{default:a((()=>[p(" 产业链企业集中度"),c("span",{class:"itemDel"},d(x.enterpriseInfo.chains[x.chainsIndex].enterpriseCount),1)])),_:1})])])),_:1})):u("",!0),C.comparisonFn("配套的基础设施")?(l(),o(w,{key:1,class:"businessScopeBox"},{default:a((()=>[s(w,{class:"businessScopetitle"},{default:a((()=>[p("基础配套设施")])),_:1}),s(w,{class:"businessScope"},{default:a((()=>[p(d(C.comparisonFn("配套的基础设施")),1)])),_:1})])),_:1})):u("",!0),C.comparisonFn("水、电、气及排污收费标准")?(l(),o(w,{key:2,class:"businessScopeBox"},{default:a((()=>[s(w,{class:"businessScopetitle"},{default:a((()=>[p("水、电、气及排污收费标准")])),_:1}),s(w,{class:"businessScope"},{default:a((()=>[p(d(C.comparisonFn("水、电、气及排污收费标准")),1)])),_:1})])),_:1})):u("",!0),C.comparisonFn("各工种薪资水平")?(l(),o(w,{key:3,class:"businessScopeBox"},{default:a((()=>[s(w,{class:"businessScopetitle"},{default:a((()=>[p("各工种薪资水平")])),_:1}),s(w,{class:"businessScope"},{default:a((()=>[p(d(C.comparisonFn("各工种薪资水平")),1)])),_:1})])),_:1})):u("",!0),C.comparisonFn("企业入驻流程")?(l(),o(w,{key:4,class:"businessScopeBox"},{default:a((()=>[s(w,{class:"businessScopetitle"},{default:a((()=>[p("企业入驻流程")])),_:1}),s(w,{class:"businessScope"},{default:a((()=>[p(d(C.comparisonFn("企业入驻流程")),1)])),_:1})])),_:1})):u("",!0),C.comparisonFn("企业入驻优惠政策")?(l(),o(w,{key:5,class:"businessScopeBox"},{default:a((()=>[s(w,{class:"businessScopetitle"},{default:a((()=>[p("企业入驻优惠政策")])),_:1}),s(w,{class:"businessScope"},{default:a((()=>[p(d(C.comparisonFn("企业入驻优惠政策")),1)])),_:1})])),_:1})):u("",!0),s(w,{class:"placeholder"})])),_:1})):u("",!0),s(w,{class:"BottomButton"},{default:a((()=>{var e,n,s,i,r,c,d,f;return[0!=x.enterpriseInfo.enterpriseCount&&(x.enterpriseInfo.industryParkName||x.enterpriseInfo.industryParkNameEn)?(l(),o(w,{key:0,onClick:C.goDetail,class:"directories"},{default:a((()=>[p("区域生态企业名录 ")])),_:1},8,["onClick"])):u("",!0),(null==(n=null==(e=x.enterpriseInfo)?void 0:e.parkImageDTO)?void 0:n.officialSettledPath)||(null==(i=null==(s=x.enterpriseInfo)?void 0:s.parkImageDTO)?void 0:i.interSettledPath)?(l(),t("div",{key:1,style:{"min-width":"22rpx"}})):u("",!0),(null==(c=null==(r=x.enterpriseInfo)?void 0:r.parkImageDTO)?void 0:c.officialSettledPath)||(null==(f=null==(d=x.enterpriseInfo)?void 0:d.parkImageDTO)?void 0:f.interSettledPath)?(l(),o(w,{key:2,onClick:S[1]||(S[1]=e=>C.qrCodePop()),class:"consult"},{default:a((()=>[p("入驻咨询")])),_:1})):u("",!0)]})),_:1}),s(M,{ref:"qrCode","is-mask-click":!1,type:"dialog"},{default:a((()=>{var e,n,a,i,r,f,h,A,k,g;return[c("div",{class:"qrCodeBox"},[s(O,{onClick:C.canlePop,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAACiSURBVDiNY2AYVKChc7YMITUtU+bII/OZkDWzsTM+bp04Jx+X5taJc/KZ/jA8aO2eo4NpQHnqk/+MDAWM/xkmYDOkdeKcfMb/DBP+MzIUVJemXIGJM+JVmJ8yEZcYTgPQNTAwMDDg0ozTAGRDGBgYGHBpRgkDcgH1vUBRIOJTiEsObkBD/3QFNkbW+/gCDG7IbwZd5LSAUICUwnABYtTQFwAAtuxzpJCZ6i0AAAAASUVORK5CYII=",class:"canleIcon"},null,8,["onClick"]),c("div",{class:"qrCodeHead"},[(null==(n=null==(e=x.enterpriseInfo)?void 0:e.parkImageDTO)?void 0:n.officialSettledPath)&&(null==(i=null==(a=x.enterpriseInfo)?void 0:a.parkImageDTO)?void 0:i.interSettledPath)?(l(),t("div",{key:0},[c("div",{class:"switch-tabs"},[c("div",{class:I({active:"1"==x.codeTabId}),onClick:S[2]||(S[2]=e=>x.codeTabId="1")},"官方客服 ",2),c("div",null,"|"),c("div",{class:I({active:"2"==x.codeTabId}),onClick:S[3]||(S[3]=e=>x.codeTabId="2")},"中介客服",2)]),m(s(O,{onClick:S[4]||(S[4]=e=>{var n,t;return C.saveImage(null==(t=null==(n=x.enterpriseInfo)?void 0:n.parkImageDTO)?void 0:t.officialSettledPath)}),src:x.enterpriseInfo.parkImageDTO.officialSettledPath,class:"qr-image"},null,8,["src"]),[[v,"1"==x.codeTabId]]),m(s(O,{onClick:S[5]||(S[5]=e=>{var n,t;return C.saveImage(null==(t=null==(n=x.enterpriseInfo)?void 0:n.parkImageDTO)?void 0:t.interSettledPath)}),src:x.enterpriseInfo.parkImageDTO.interSettledPath,class:"qr-image"},null,8,["src"]),[[v,"2"==x.codeTabId]])])):(l(),t("div",{key:1},[c("div",{class:"switch-title"},d((null==(f=null==(r=x.enterpriseInfo)?void 0:r.parkImageDTO)?void 0:f.officialSettledPath)?"官方客服":"中介客服"),1),(null==(A=null==(h=x.enterpriseInfo)?void 0:h.parkImageDTO)?void 0:A.officialSettledPath)?(l(),o(O,{key:0,onClick:S[6]||(S[6]=e=>{var n,t;return C.saveImage(null==(t=null==(n=x.enterpriseInfo)?void 0:n.parkImageDTO)?void 0:t.officialSettledPath)}),src:x.enterpriseInfo.parkImageDTO.officialSettledPath,class:"qr-image"},null,8,["src"])):u("",!0),(null==(g=null==(k=x.enterpriseInfo)?void 0:k.parkImageDTO)?void 0:g.interSettledPath)?(l(),o(O,{key:1,onClick:S[7]||(S[7]=e=>{var n,t;return C.saveImage(null==(t=null==(n=x.enterpriseInfo)?void 0:n.parkImageDTO)?void 0:t.interSettledPath)}),src:x.enterpriseInfo.parkImageDTO.interSettledPath,class:"qr-image"},null,8,["src"])):u("",!0)])),c("div",{class:"Introduction"},[p(" 点击保存添加官方客服"),c("br"),p(" 提供咨询服务 ")])])])]})),_:1},512)])),_:1})],64)}],["__scopeId","data-v-d1af83e9"]]);export{x as default};
