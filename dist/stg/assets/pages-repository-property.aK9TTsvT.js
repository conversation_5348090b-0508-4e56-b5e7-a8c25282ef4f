import{p as t,o as e,a,B as i,f as s,F as l,b as n,t as o,d,C as c,s as r,l as h,k as u,u as p,m as g,z as y,A as m,c as v,n as k,w,g as f,i as C,S as L}from"./index-HcMwrp5e.js";import{_ as I}from"./page-meta.X-Lr6csD.js";import{r as S}from"./uni-app.es.DFp0WTX7.js";import{t as b}from"./index.B6Rbewql.js";import{_ as N,t as x}from"./adornBgc._11Cj7Ia.js";import{_ as V}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{g as _}from"./utils.61Hi-B7M.js";import{_ as D}from"./returnFn.BYkANsDr.js";import{_ as B}from"./history.BKBs_WXS.js";import"./uni-transition.CHOJlBbg.js";const T=V({components:{tabBar:b,Capacitypool:V({props:{item:{type:Object,default:{}},token:{type:String,default:""},buy:{type:Boolean,default:!1},knowledgeId:{type:String,default:""},lastDataShow:{type:Object,default:null}},data:()=>({figureList:[],attractList:[],restList:[],_lastDataShow:{}}),created(){this.classify()},watch:{item:function(t,e){this.classify()}},methods:{changeList(t){this._lastDataShow=t},classify(){this.figureList=this.item.sort(((t,e)=>t.state-e.state))},select(e){var a;if(2==e.state)return t({title:"敬请期待",icon:"none"});null==(a=null==window?void 0:window.ReactNativeWebView)||a.postMessage(JSON.stringify({type:"changePath",value:{data:{id:e.id,name:e.name,isOld:e.isOld}},path:"chat"}))}}},[["render",function(t,r,h,u,p,g){var y,m,v,k,w,f,C,L,I,S,b,x;const V=c;return e(),a("div",{class:"particulars"},[0!=(null==(m=null==(y=p._lastDataShow)?void 0:y[0])?void 0:m.data.length)?(e(),a("div",{key:0,class:"fetchitem"},[i("div",{class:"headtext"},[i("div",{class:"textTitle"},[i("span",{class:"title"},"产业洞察"),s(V,{class:"adornBgc",src:N})])]),(e(!0),a(l,null,n(null==(k=null==(v=p._lastDataShow)?void 0:v[0])?void 0:k.data,((t,l)=>(e(),a("div",{key:t.id,onClick:e=>g.select(t,l)},[i("div",{class:"item"},[i("div",{class:"titlehead"},[s(V,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),i("span",{class:"name"},o(t.name),1)]),2==t.state?(e(),a("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)])],8,["onClick"])))),128))])):d("",!0),0!=(null==(f=null==(w=p._lastDataShow)?void 0:w[1])?void 0:f.data.length)?(e(),a("div",{key:1,class:"fetchitem"},[i("div",{class:"headtext"},[i("div",{class:"textTitle"},[i("span",{class:"title"},"产研工具"),s(V,{class:"adornBgc",src:N})])]),(e(!0),a(l,null,n(null==(L=null==(C=p._lastDataShow)?void 0:C[1])?void 0:L.data,((t,l)=>(e(),a("div",{key:t.id,onClick:e=>g.select(t,l)},[i("div",{class:"item"},[i("div",{class:"titlehead"},[s(V,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),i("span",{class:"name"},o(t.name),1)]),2==t.state?(e(),a("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)])],8,["onClick"])))),128))])):d("",!0),0!=(null==(S=null==(I=p._lastDataShow)?void 0:I[2])?void 0:S.data.length)?(e(),a("div",{key:2,class:"fetchitem"},[i("div",{class:"headtext"},[i("div",{class:"textTitle"},[i("span",{class:"title"},"报告智编"),s(V,{class:"adornBgc",src:N})])]),(e(!0),a(l,null,n(null==(x=null==(b=p._lastDataShow)?void 0:b[2])?void 0:x.data,((t,l)=>(e(),a("div",{key:t.id,onClick:e=>g.select(t,l)},[i("div",{class:"item"},[i("div",{class:"titlehead"},[s(V,{class:"knowledgeImgs",src:null==t?void 0:t.iconPath},null,8,["src"]),i("span",{class:"name"},o(t.name),1)]),2==t.state?(e(),a("span",{key:0,class:"developing"},"数据上线中...")):d("",!0)])],8,["onClick"])))),128))])):d("",!0),i("div",{style:{height:"100px"}})])}],["__scopeId","data-v-ce1b7606"]]),treeSelect:x},data:()=>({title:"",token:"",capacityList:[],keyword:"",time:null,begdata:!1,showshade:!1,statusBarHeight:0,lastDataShow:{},knowledgeId:"",invitationCode:"",industryChainList:[],list:[],defaultValue:[],initialValue:[],showChain:"",orgName:"",isDefault:"",clientVersion:0}),onLoad(e){let a={token:"",id:""};_("token")&&(a.token=_("token"),r("token",a.token)),_("id")&&(a.id=_("id")),_("clientVersion")&&(this.clientVersion=Number(_("clientVersion"))),_("orgName")?(this.isDefault=!1,this.orgName=_("orgName"),r("orgName",this.orgName)):(this.isDefault=!0,r("orgName","")),r("isDefault",this.isDefault),_("identity")&&(this.identity=_("identity"),r("userIdentityType",this.identity)),this.token=h("token"),this.statusBarHeight=u().statusBarHeight,this.orgName=h("orgName")||"",this.isDefault=h("isDefault"),this.getList();let i=h("userIdentityType");if(this.token&&(3==i||4==i))return t({title:"已登陆，该身份无权限，查看请退出登录",icon:"none",duration:2e3}),void setTimeout((()=>{p({url:"/pages/repository/capacity"})}),2e3);this.getindustryList()},onShow(){},methods:{init(){var t,e,a,i,s;let l={token:"",id:""},n=null==(t=window.location.href.split("?token="))?void 0:t[1],o=null==n?void 0:n.split("&id=");l.token=null==(a=null==(e=null==o?void 0:o[0])?void 0:e.split("&orgName="))?void 0:a[0];let d="",c=(null==(s=null==(i=null==o?void 0:o[0])?void 0:i.split("&orgName="))?void 0:s[1])||"";if(c){let t=null==c?void 0:c.split("&clientVersion=");if(t.length>1){d=decodeURIComponent(t[0]);const e=t[1];e&&(this.clientVersion=Number(e))}else d=decodeURIComponent(c)}r("token",l.token),r("orgName",d)},echoChain(){this.$api.choosechainAPI({method:"get"}).then((t=>{t.result.knowType1Name&&(this.title=t.result.knowType1Name,this.defaultValue=[t.result.category1Id,t.result.knowType1Id],this.initialValue=[t.result.category1Id,t.result.knowType1Id],setTimeout((()=>{this.list.map((t=>{t.value==this.initialValue[0]&&this.$refs.treeSelect.echo(this.list,t.children)}))}),1e3))}))},goLog:()=>g({url:"/pages/login/index"}),getindustryList(){this.$api.industrychainList({data:{capType:1},method:"get"}).then((t=>{this.industryChainList=t.result.map((t=>{let e=t.chainList.map((t=>({...t,label:t.chainName,value:t.knowId,categoryBuyStatus:t.buyStatus}))),{chainList:a,...i}=t;return{...i,label:t.categoryName,value:t.categoryId,children:e}})),this.list=this.industryChainList,this.token&&this.echoChain()}))},onChange(){},onConfirm(t){var e;let{selected:a,values:i}=t;"1"===(null==(e=a[1])?void 0:e.categoryBuyStatus)&&this.$api.updatechoosechainAPI({data:{capType:1,knowId:i[1]},method:"get"}).then((t=>{var e;this.defaultValue=[i[0],i[1]],this.initialValue=[i[0],i[1]],this.title=null==(e=a[1])?void 0:e.chainName}))},changekeyword(t){null!=this.time&&clearTimeout(this.time),this.time=setTimeout((async()=>{this.$api.queryCapabilityListWithoutPermission({data:{capabilityName:t,type:1},method:"post"}).then((t=>{this.capacityList=t.result,this.getShowList(t.result)}))}),500)},getList(){this.$api.queryCapabilityListWithoutPermission({data:{type:1},method:"post"}).then((t=>{this.capacityList=t.result,this.getShowList(t.result),this.begdata=!0})).catch((t=>{this.begdata=!0}))},getShowList(t){let e={0:{list:[],data:[]},1:{list:["产业图谱绘制","产业链AI诊断","产业政策分析","专业产业园区"],data:[]},2:{list:["产业报告速编","产业发展规划","产业分析报告"],data:[]}};for(let a in e)e[a].list.map((i=>{let s=t.find((t=>t.name===i));s&&e[a].data.push(s)}));this.lastDataShow=e,0!=this.capacityList.length&&this.$nextTick((()=>{this.$refs.showList.changeList(this.lastDataShow)}))},goBack(){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:"",path:"goBack"}))},go(t){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:"",path:"history"}))}}},[["render",function(t,n,r,h,u,p){const g=S(y("page-meta"),I),b=c,N=C,x=m("tree-select"),V=m("Capacitypool"),_=L;return e(),a(l,null,[u.clientVersion>=240?(e(),v(g,{key:0,"page-style":"background-color: transparent;overflow: hidden !important;max-width: 100vw;"})):(e(),v(g,{key:1,"page-style":"background-color: #FAFCFF;overflow: hidden !important;max-width: 100vw;"})),u.clientVersion?d("",!0):(e(),v(b,{key:2,src:"https://static.idicc.cn/cdn/aiChat/applet/home/<USER>",style:{width:"750rpx",position:"absolute",height:"1624rpx"}})),u.clientVersion?d("",!0):(e(),a("div",{key:3,class:"topImg",style:k(`top: ${u.statusBarHeight}px`)},[s(N,{class:"black",onClick:n[0]||(n[0]=t=>p.goBack())},{default:w((()=>[s(b,{class:"returnFn",src:D})])),_:1})],4)),i("div",{class:"box",style:k(`top: ${u.clientVersion?30:u.statusBarHeight+60}px `)},[i("div",{class:"topIntroduce"},[s(b,{src:"https://static.idicc.cn/cdn/aiChat/applet/home/<USER>",class:"AI2"}),!u.isDefault&&u.orgName?(e(),v(N,{key:0,class:"orgName"},{default:w((()=>[f(o(u.orgName),1)])),_:1})):d("",!0)]),i("div",{class:"seinput"},[u.token?(e(),v(x,{key:0,isCanyan:!1,class:"changeIndustry",selectTile:"切换产业链",optionsKey:"value",value:u.defaultValue,initialValue:u.initialValue,primary:"value",ref:"treeSelect",options:u.list,onChange:p.onChange,onConfirm:p.onConfirm},{default:w((()=>[i("div",{class:"selectBox",style:{display:"flex","align-items":"center"}},[i("span",{style:{"white-space":"nowrap"}},o(u.title?u.title.length>5?u.title.substring(0,4)+"...":u.title:"未开通"),1),i("div",{class:"InvertedTriangle"})])])),_:1},8,["value","initialValue","options","onChange","onConfirm"])):(e(),a("div",{key:1,class:"selectBox",style:{display:"flex","align-items":"center"}},[i("span",{onClick:n[1]||(n[1]=(...t)=>p.goLog&&p.goLog(...t)),style:{"white-space":"nowrap"}},"未开通"),i("div",{class:"InvertedTriangle"})])),u.token?(e(),a("div",{key:2,onClick:n[2]||(n[2]=t=>p.go("history")),class:"historyBox"},[s(b,{class:"icon",src:B}),f(" 历史记录 ")])):d("",!0)]),s(_,{"scroll-y":"true",class:"repository",style:k({"padding-bottom":u.token?"60rpx":"160rpx"})},{default:w((()=>[0==u.capacityList.length&&u.begdata?(e(),a("div",{key:0,class:"nodatabox"},[s(b,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata.png"}),i("span",{class:"span"},"暂无内容")])):(e(),v(N,{key:1},{default:w((()=>[s(V,{ref:"showList",item:u.capacityList,token:u.token,knowledgeId:u.knowledgeId,lastDataShow:u.lastDataShow},null,8,["item","token","knowledgeId","lastDataShow"])])),_:1}))])),_:1},8,["style"])],4)],64)}],["__scopeId","data-v-d2c5081d"]]);export{T as default};
