.skeleton {
  width: 100%;
  height: 100vh;
 
.memberBg{
  width: 750rpx;
  height: 300rpx;
  background: linear-gradient(180deg, #ffedd4 0%, #ffffff00 100%);
  position: absolute;
  z-index: 8;
}
  .chatBg {
    width: 750rpx;
    height: 300rpx;
    background: linear-gradient(180deg, #dde6ff 0%, #ffffff00 100%);
    position: absolute;
    z-index: 8;
  }

  .skeletonlist {
    padding: 0 30rpx;
    width: 100%;
    z-index: 10;
    position: relative;
    padding-top: 65rpx;
    box-sizing: border-box;

    .skeletonHuge {
      width: 45%;
      height: 64rpx;
      margin-bottom: 20rpx;
    }

    .skeletonSmall, .skeletonHuge ,.skeletonBig{
      background: linear-gradient(90deg, hsla(0, 0%, 74.5%, .2) 25%, hsla(0, 0%, 50.6%, .24) 37%, hsla(0, 0%, 74.5%, .2) 63%);
      background-size: 400% 100%;
      animation: adm-skeleton-loading 1.4s ease infinite;
      border-radius: 4rpx;

    }

      .skeletonSmall {
      width: 100%;
      height: 36rpx;
      margin-bottom: 27rpx;
 
    }
.skeletonBig{
  width: 100%;
      height: 125rpx;
      margin-bottom: 32rpx;
      border-radius: 10rpx !important;
      // margin-top: 32rpx;
}
    .skeletonShort {
      width: 65%;
      height: 36rpx;
      background: linear-gradient(90deg, hsla(0, 0%, 74.5%, .2) 25%, hsla(0, 0%, 50.6%, .24) 37%, hsla(0, 0%, 74.5%, .2) 63%);
      background-size: 400% 100%;
      animation: adm-skeleton-loading 1.4s ease infinite;
      border-radius: 4rpx;
      margin-bottom: 27rpx;
 
    }
  }
}