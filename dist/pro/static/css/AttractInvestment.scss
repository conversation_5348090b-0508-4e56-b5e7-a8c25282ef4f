.enterpriseUniCode {
  font-weight: 400;
  font-size: 30rpx !important;
  color: #c8c9cc;
  margin-left: 12rpx;
}
.returnFn {
  width: 48rpx;
  height: 48rpx;
}
.total {
  font-size: 28rpx;
  color: #86909c;

  .totalSpan2 {
    color: #417fff;
  }
}
.black {
  position: absolute;
  left: 32rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.attractFormInput2 {
  width: 690rpx;
  // height: 670rpx;
  // overflow-x: scroll;
}
.attractFormInput2::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}
.orderTicketImg {
  width: 738rpx;
  margin-left: 6rpx;
  height: 950rpx;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 180rpx;
}
.orderTicket {
  width: 690rpx;
  margin-left: 30rpx;
  position: relative;
  // display: flex;
  margin-top: -330rpx;
  z-index: 40;
  // background-color: #ff0;
  box-shadow: 0rpx 4rpx 24rpx 0rpx rgba(130, 130, 131, 0.2);
  border-radius: 20rpx 20rpx 20rpx 20rpx;

  .attractFormHead {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    position: absolute;
    top: 0;
    left: 0;
    .btnBgImg {
      width: 100%;
      height: 88rpx;
    }
  }
  .formtitle {
    width: 100%;
    font-size: 32rpx;
    color: #3370ff;
    display: flex;
    height: 88rpx;
    z-index: 20;
    background-color: transparent;
    text-align: center;
    line-height: 88rpx;
    position: relative;
    .line {
      width: 30rpx;
      height: 6rpx;
      background-color: #3370ff;
      position: absolute;
      bottom: 10rpx;
      border-radius: 3rpx 3rpx 3rpx 3rpx;
    }
    .pitchonOne {
      width: 50%;
      display: flex;
      font-weight: 500;
      font-size: 32rpx;
      color: #3370ff;
      text-align: center;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .pitchonIntelligence {
      width: 50%;
      display: flex;
      font-weight: 500;
      font-size: 32rpx;
      color: #3370ff;
      text-align: center;
      align-items: center;
      justify-content: center;
    }
    .topBtm {
      width: 50%;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #fff;
      text-align: center;
    }
    .topBtm2 {
      width: 50%;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #fff;
      text-align: center;
    }
  }
  .attractForm {
    // position: absolute;
    //margin-left: 30rpx;
    width: 100%;
    height: auto;
    background: #ffffff;
    //box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
    border-radius: 0rpx 0rpx 20rpx 20rpx;
    padding: 30rpx;
    padding-top: 0rpx;
    box-sizing: border-box;
    z-index: 3;
    // top: 88rpx;
    // left: 50%;
    // transform: translateX(-50%);
    // background-image: url("https://static.idicc.cn/cdn/aiChat/applet/bg_bottom.png");
  }
}
.unselected2 {
  width: 386rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 30rpx;
  color: #c8c9cc;
  padding-left: 16rpx;
  box-sizing: border-box;
  justify-content: space-between;
  line-height: 100rpx;
  .calendaricons {
    position: relative;
    margin-right: 4rpx;
    // margin-top: 4rpx;
    top: 4rpx;
  }
  .clearIcon {
    margin-top: 4rpx;
    margin-right: 6rpx;
  }
}
.unselected {
  width: 286rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 30rpx;
  color: #c8c9cc;
  padding-left: 16rpx;
  box-sizing: border-box;
}

.selected {
  width: 270rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 28rpx;
  color: #1d2129;
  box-sizing: border-box;
  padding-left: 16rpx;

  .text {
    white-space: nowrap;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.changeType {
  width: 130rpx;
  height: 100rpx;
  padding-left: 30rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #3370ff;
  position: absolute;
  display: flex;
  align-items: center;

  .userPop {
    position: absolute;
    top: 90rpx;
    left: 0rpx;
    width: 200rpx;
    height: auto;
    background-color: #fff;
    box-shadow: 0rpx 4rpx 24rpx 0rpx rgba(130, 130, 131, 0.2);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    z-index: 20;

    .popItem {
      font-weight: 400;
      height: 100rpx;
      font-size: 30rpx;
      color: #1d2129;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .popItems {
      font-weight: 400;
      height: 100rpx;
      font-size: 30rpx;
      color: #3370ff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .InvertedTriangle {
    width: 0;
    height: 0;
    border-top: 12rpx solid #3370ff;
    margin-left: 8rpx;
    border-right: 8rpx solid transparent;
    border-left: 8rpx solid transparent;
    border-bottom: 0 solid transparent;
  }
}

::v-deep {
  .uni-easyinput__placeholder-class {
    color: #c8c9cc;
    font-size: 30rpx;
  }
  .demandInput {
    width: 100%;
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    position: relative;
    height: 100rpx;
    margin-top: 18rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .content-clear-icon {
      padding-left: 30rpx;
    }

    .uni-easyinput__content {
      border: 0rpx solid #e6ebf0 !important;
    }

    .uni-easyinput__content {
      width: 690rpx;
      border-radius: 40rpx;
      background-color: #fff !important;
    }

    .uni-easyinput {
      flex: 0 !important;
    }
  }

  .seinput {
    width: 100%;
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    margin-top: 18rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .content-clear-icon {
      padding-left: 30rpx;
    }

    .uni-easyinput__content {
      border: 0rpx solid #e6ebf0 !important;
    }

    .uni-easyinput__content {
      width: 576rpx;
      padding-left: 130rpx;
      border-radius: 40rpx;
    }

    .uni-easyinput {
      flex: 0 !important;
    }
  }

  .file-picker__box {
    background: #f5f7fa;
    border-radius: 12rpx 12rpx 12rpx 12rpx !important;
  }

  .file-picker__box-content {
    margin: 0rpx !important;
  }
}

.screenBox {
  font-weight: 400;
  font-size: 26rpx;
  color: #3f4a59;
  height: 32rpx;
  display: flex;
  align-items: center;

  .screenImg {
    width: 28rpx;
    height: 28rpx;
  }
}

.nodatabox {
  display: flex;
  align-items: center;
  flex-direction: column;
  font-weight: 400;
  font-size: 30rpx;
  color: #3f4a59;
  margin-top: 100rpx;
  position: relative;
  //box-sizing: border-box;

  .nodata {
    width: 750rpx;
    height: 634rpx;
    min-width: 750rpx;
    min-height: 634rpx;
    //margin-left: -30rpx;
  }

  .span {
    position: absolute;
    top: 472rpx;
    //margin-left: -30rpx;
  }
}

.AssociativeList {
  width: 100%;
  height: auto;
  position: absolute;
  top: 88rpx;
  z-index: 90;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
  border-radius: 20rpx 20rpx 20rpx 20rpx;

  .AssociativeItem {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #1d2129;
    display: flex;
    align-items: center;
    padding-left: 30rpx;
    box-sizing: border-box;
    white-space: nowrap;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.numShow {
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  background: #ee0a24;
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  top: -5rpx;
  right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 22rpx;
  color: #ffffff;
}

.myList {
  width: 690rpx;
  margin-left: 30rpx;
  margin-top: 32rpx;
}

.myentrustListClass {
  width: 690rpx;
  height: auto;
  //margin-left: 30rpx;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin-top: 30rpx;
  padding: 24rpx 30rpx;
  box-sizing: border-box;

  .t1 {
    display: flex;
    width: 100%;
    height: 45rpx;
    align-items: center;
    justify-content: space-between;

    .startTime {
      font-weight: 400;
      font-size: 26rpx;
      color: #86909c;
    }

    .myListstate {
      font-weight: 400;
      font-size: 26rpx;
      color: #3370ff;
    }
  }

  .t2 {
    display: flex;
    width: 100%;
    height: 50rpx;
    align-items: center;
    justify-content: space-between;
    margin-top: 32rpx;

    .enterpriseIcon {
      width: 40rpx;
      height: 40rpx;
      min-width: 40rpx;
      min-height: 40rpx;
      margin-right: 16rpx;
    }

    .enterprise {
      font-weight: 500;
      font-size: 30rpx;
      color: #1d2129;
      height: 100%;
      display: flex;
      align-items: center;
      .enterpriseText {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 1;
      }
    }

    .currency {
      font-weight: 500;
      font-size: 26rpx;
      color: #1d2129;
    }

    .amount {
      font-weight: 500;
      font-size: 36rpx;
      color: #1d2129;
    }
  }

  .t3 {
    width: 630rpx;
    height: auto;
    margin-bottom: 16rpx;
    margin-top: 24rpx;
    background: #fafafa;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    display: flex;
    //align-items: center;
    flex-direction: column;
    padding-left: 16rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #3f4a59;
    padding-top: 8rpx;
    padding-bottom: 8rpx;

    .line {
      line-height: 32rpx;
      margin-bottom: 6rpx;
      display: block;
      width: 614rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .T4Btn {
    width: 160rpx;
    height: 64rpx;
    margin-top: 24rpx;
    margin-bottom: 6rpx;
    border-radius: 32rpx;
    border: 2rpx solid #3370ff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #3370ff;
    margin-left: 470rpx;
  }
}

.stateTab {
  display: flex;
  width: 100%;
  height: 60rpx;
  background-color: #f5f7fa;
  border-radius: 30rpx;

  .statepitch {
    width: 25%;
    border-radius: 30rpx;
    height: 60rpx;
    background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
    box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    position: relative;
  }

  .stateClass {
    width: 25%;
    height: 60rpx;
    border-radius: 30rpx;
    box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #86909c;
    position: relative;
  }
}

.bottomTab {
  display: flex;
  align-items: center;
  //justify-content: space-between;
  z-index: 60;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 750rpx;
  height: 144rpx;
  padding-bottom: 40rpx;
  background: rgba(250, 252, 255, 0.95);
  //box-shadow: 0rpx -8rpx 8rpx 0rpx rgba(0, 0, 0, 0.05);
  //border-radius: 0rpx 0rpx 0rpx 0rpx;
  box-sizing: border-box;
  border-top: 2rpx solid #eef1f5;

  .singleTabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    //height: 60rpx;
  }

  .single {
    width: 25%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    font-size: 20rpx;
    //height: 60rpx;
  }

  .my {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 4rpx;
    //margin-right: 8rpx;
  }
}

.applyaffirm {
  width: 622rpx;
  height: auto;
  background: #ffffff;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  box-sizing: border-box;
  padding: 52rpx 48rpx 32rpx 48rpx;
  display: flex;
  align-items: center;
  flex-direction: column;

  .p1 {
    font-weight: 500;
    font-size: 32rpx;
    color: #323233;
    margin-bottom: 16rpx;
  }

  .p2 {
    font-weight: 400;
    font-size: 28rpx;
    color: #3f4a59;
    margin-bottom: 68rpx;
    text-align: center;
  }

  .affirm {
    width: 526rpx;
    height: 72rpx;
    background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .popBtn {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .affirm {
      width: 251rpx;
      height: 72rpx;
      background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .canleBt {
      width: 251rpx;
      height: 72rpx;
      background: #ffffff;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      border: 2rpx solid #ebedf0;
      font-weight: 400;
      font-size: 32rpx;
      color: #323233;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.header {
  height: 100rpx;
}

.repository {
  width: 100%;
  overflow: scroll;
  height: calc(100vh - 100rpx);
  // height: 1354rpx;
}

.attractImg {
  width: 750rpx;
  height: 690rpx;
  margin-left: 0rpx;
}

.entrustBox {
  width: 100%;
  height: 120rpx;
  //align-items: center;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  .priceBox {
    display: flex;
    //align-items: center;
    flex-direction: column;
    justify-content: center;

    .symbol {
      font-weight: 500;
      font-size: 26rpx;
      color: #1d2129;
    }

    .price {
      font-weight: 500;
      font-size: 40rpx;
      color: #1d2129;
      //margin-top: 12rpx;
    }

    .BillingRule {
      font-weight: 400;
      font-size: 22rpx;
      color: #3370ff;
      margin-top: 6rpx;
      margin-left: 8rpx;
    }

    .type {
      font-weight: 400;
      font-size: 20rpx;
      color: #86909c;
      margin-left: 6rpx;
    }
  }
}

//一键委托
.entrust {
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%),
    linear-gradient(90deg, #ff9e45 0%, #ff5c00 100%),
    linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
}

.topImg {
  position: fixed;
  height: 88rpx;
  width: 750rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #000000;
  z-index: 996;
}

.titleImg {
  width: 56rpx;
  height: 56rpx;
  margin-right: 20rpx;
}

//顶部样式
.headimg {
  width: 100%;
  height: 200rpx;
  background: linear-gradient(180deg, #dde6ff, #fff0);
  position: absolute;
}

.box {
  z-index: 3;
  position: relative;
  //top: 176rpx;
  width: 100%;
  min-height: 1000rpx;
  //padding: 0 30rpx;
  box-sizing: border-box;

  display: flex;

  // padding-bottom: 68rpx;
  //justify-content: center;
  flex-direction: column;
  height: calc(100vh - 168rpx);

  //align-items: center;
  //按钮
  .experience {
    position: fixed;
    bottom: 82rpx;
    left: 30rpx;
    width: 690rpx;
    height: 88rpx;
    background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
    box-shadow: 0rpx 8rpx 24rpx 0rpx #c2d9ff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

::v-deep {
  .is-input-border {
    border: 0rpx solid #e6ebf0 !important;
    // background: transparent !important;
  }
}
