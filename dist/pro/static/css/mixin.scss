/****************************
*       flex 布局相关        *
* **************************/
.flex {
  display: flex;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.flex-around {
  display: flex;
  justify-content: space-around;
}
.flex-evenly {
  display: flex;
  justify-content: space-evenly;
}
.flex-c {
  display: flex;
  justify-content: center;
}
.flex-1 {
  flex: 1;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}
.text-center {
  text-align: center;
  
}
.flex-shrink {
  flex-shrink: 0;
}
/****************************
*           常用间距         *
* **************************/
$directions-position:top,right,bottom,left;
$directions-class:'t','r','b','l';
@for $i from 1 through 200 {
  @each $p in $directions-position {
    $index: index($directions-position, $p);
    $class: nth($directions-class, $index);
    .mg#{$class}-#{$i} {
      margin-#{$p}:$i + rpx;
    }
    .pd#{$class}-#{$i} {
      padding-#{$p}:$i + rpx;
    }
  }
}
