import{p as e,U as a,l as s,z as t,A as o,a as i,f as l,w as r,F as n,i as u,o as d,c as m,d as p,B as c,e as f,t as y}from"./index-D8Q9TKMD.js";import{_ as h}from"./page-meta.CQ11A97m.js";import{r as I}from"./uni-app.es.KQfvbE5W.js";import{_ as b}from"./uni-easyinput.Kc2lvObb.js";import{_ as g,a as v}from"./uni-forms.4s1Yg_LJ.js";import{_ as C}from"./uni-icons.HwX4OE83.js";import{_ as V}from"./zxz-uni-data-select.sf5GWo2Y.js";import{_ as k}from"./uni-data-select.CG2vDbmm.js";import{_}from"./uni-popup.D6FGSq45.js";import{t as N}from"./index.DW56S9T8.js";import{m as L}from"./mapList2.CVHjRncW.js";import{m as j}from"./mapList.C3TkrYrq.js";import{_ as q}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-cloud.es.D5Qo5u-4.js";import"./uni-transition.CNGNZNaw.js";import"./returnFn.BYkANsDr.js";const T=q({components:{tabBar:N,mapList2:L,mapList:j},data:()=>({baseInfo:{realName:""},title:"编辑个人信息",rules:{realName:{rules:[{required:!0,errorMessage:"请输入姓名"},{maxLength:5,errorMessage:"昵称最多五个字"}]},jobTitle:{rules:[{required:!0,errorMessage:"请输入所在单位职务"}]},company:{rules:[{required:!0,errorMessage:"请输入所在单位"}]},advantageCity:{rules:[{required:!0,errorMessage:"请输入优势资源城市"}]},resume:{rules:[{required:!0,errorMessage:"请输入优势资源简述"}]},referrer:{rules:[{required:!0,errorMessage:"请输入引荐人"}]},focusIndustryIds:{rules:[{required:!0,errorMessage:"请选择关注产业链"}]},InvitationCode:{rules:[{required:!0,errorMessage:"请输入邀请码"}]},overseaTargetCodes:{rules:[{required:!0,errorMessage:"请输入出海关注目的地"}]},enterpriseProfile:{rules:[{required:!0,errorMessage:"请输入企业简介"}]},serviceCountryCode:{rules:[{required:!0,errorMessage:"请选择服务国家"}]},serviceItemCodes:{rules:[{required:!0,errorMessage:"请选择服务类目"}]}},type:1,industryList:[],countryList:[],showMap:!1,categoryList:[]}),computed:{},onLoad(e){this.type=e.type,8==this.type?this.getindustryList():11==this.type||13==this.type?this.getCountryList():14==this.type?this.getCategoryList():this.userdescribeFn()},methods:{getCategoryList(){this.$api.catalogListAPI({token:this.token,method:"POST"}).then((e=>{e.result.forEach((e=>{this.categoryList.push({text:e.catalogName,value:parseInt(e.catalogId)})})),this.userdescribeFn()}))},getCountryList(){this.industryList=[],this.$api.countryListAPI({token:this.token}).then((e=>{e.result.forEach((e=>{this.countryList.push({text:e.country,value:e.id})})),this.userdescribeFn()}))},getindustryList(){this.industryList=[],this.$api.listAllPurchaseChain({token:this.token}).then((e=>{e.result.forEach((e=>{this.industryList.push({text:e.name,value:parseInt(e.id)})})),this.userdescribeFn()}))},userdescribeFn(){this.$api.userMessage().then((e=>{this.baseInfo=e.result;for(let a in this.baseInfo.userCompletionInfoDO)this.baseInfo.userCompletionInfoDO.hasOwnProperty(a)&&(this.baseInfo[a]=this.baseInfo.userCompletionInfoDO[a]);this.baseInfo.focusIndustryIds&&(this.baseInfo.focusIndustryIds=this.baseInfo.focusIndustryIds.map((e=>parseInt(e)))),4!=this.type&&9!=this.type||(this.showMap=!0)})).catch((e=>{this.baseInfo={}}))},affirm(e){var a,s,t;this.$refs.popup.close(),this.baseInfo.code=[],this.baseInfo.codeName=(null==(a=e.province)?void 0:a.name)+((null==(s=e.citys)?void 0:s.name)?"/"+e.citys.name:"")+((null==(t=e.area)?void 0:t.name)?"/"+e.area.name:"");let o=Object.keys(e).map((a=>e[a]&&e[a].code&&""!==e[a].code?e[a].code:null));this.baseInfo.code=o.filter((e=>null!==e))},affirm2(e){var a,s,t;this.$refs.popup2.close(),this.baseInfo.cityCode=[],this.baseInfo.cityName=(null==(a=e.province)?void 0:a.name)+((null==(s=e.citys)?void 0:s.name)?"/"+e.citys.name:"")+((null==(t=e.area)?void 0:t.name)?"/"+e.area.name:"");let o=Object.keys(e).map((a=>e[a]&&e[a].code&&""!==e[a].code?e[a].code:null));this.baseInfo.cityCode=o.filter((e=>null!==e))},pop(){this.$refs.popup.open("bottom")},pop2(){this.$refs.popup2.open("bottom")},jointheteam(){this.$api.workerapplyAPI({data:{invitationCode:this.baseInfo.InvitationCode},method:"post"}).then((s=>{e({title:"加入成功",icon:"success"}),setTimeout((()=>{a({delta:1})}),1e3)}))},submit(t){this.$refs[t].validate().then((t=>{if(7==this.type)return void this.jointheteam();let o={};if(1==this.type)o.realName=this.baseInfo.realName;else if(2==this.type)o.email=this.baseInfo.email;else if(3==this.type)o.company=this.baseInfo.company;else if(4==this.type){if(0==this.baseInfo.cityCode.length)return e({title:"请选择所在地区",icon:"none"});o.cityName=this.baseInfo.cityName,o.cityCode=this.baseInfo.cityCode[this.baseInfo.cityCode.length-1]}else if(5==this.type)o.advantageCity=this.baseInfo.advantageCity;else if(6==this.type)o.resume=this.baseInfo.resume;else if(8==this.type)o.focusIndustryIds=this.baseInfo.focusIndustryIds;else if(9==this.type){if(0==this.baseInfo.code.length)return e({title:"请选择招商属地",icon:"none"});o.codeName=this.baseInfo.codeName,o.code=this.baseInfo.code[this.baseInfo.code.length-1]}else 10==this.type?o.jobTitle=this.baseInfo.jobTitle:11==this.type?o.overseaTargetCodes=this.baseInfo.overseaTargetCodes:12==this.type?o.enterpriseProfile=this.baseInfo.enterpriseProfile:13==this.type?o.serviceCountryCode=this.baseInfo.serviceCountryCode:14==this.type&&(o.serviceItemCodes=this.baseInfo.serviceItemCodes);o.userId=s("userId"),this.$api.updateHeadImg({data:o,method:"POST"}).then((s=>{"SUCCESS"==s.code?(e({title:"提交成功",icon:"success"}),setTimeout((()=>{a({delta:1})}),1e3)):e({title:s.msg||"更改失败",icon:"none"})}))})).catch((e=>{}))}}},[["render",function(e,a,s,N,L,j){const q=I(t("page-meta"),h),T=o("tabBar"),M=I(t("uni-easyinput"),b),x=I(t("uni-forms-item"),g),U=I(t("uni-icons"),C),F=I(t("zxz-uni-data-select"),V),P=I(t("uni-data-select"),k),$=I(t("uni-forms"),v),w=u,A=o("mapList2"),S=I(t("uni-popup"),_),O=o("mapList");return d(),i(n,null,[l(q,{"page-style":"background-color: #FAFCFF"}),l(w,{class:"container"},{default:r((()=>[l(T,{title:L.title},null,8,["title"]),l(w,{class:"example"},{default:r((()=>[l($,{ref:"valiForm",rules:L.rules,modelValue:L.baseInfo,"label-position":"top","label-width":"100"},{default:r((()=>[1==L.type?(d(),m(x,{key:0,label:"姓名",required:"",name:"realName"},{default:r((()=>[l(M,{modelValue:L.baseInfo.realName,"onUpdate:modelValue":a[0]||(a[0]=e=>L.baseInfo.realName=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1})):p("",!0),2==L.type?(d(),m(x,{key:1,label:"邮箱",name:"email"},{default:r((()=>[l(M,{modelValue:L.baseInfo.email,"onUpdate:modelValue":a[1]||(a[1]=e=>L.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})):p("",!0),3==L.type?(d(),m(x,{key:2,label:"所在单位",required:"",name:"company"},{default:r((()=>[l(M,{modelValue:L.baseInfo.company,"onUpdate:modelValue":a[2]||(a[2]=e=>L.baseInfo.company=e),placeholder:"请输入所在单位"},null,8,["modelValue"])])),_:1})):p("",!0),4==L.type?(d(),m(x,{key:3,label:"所在城市",required:"",name:"cityCode"},{default:r((()=>{var e,s;return[c("div",{onClick:a[3]||(a[3]=e=>j.pop2()),class:"citySelect"},[c("span",{class:f(""==(null==(e=L.baseInfo)?void 0:e.cityName)?"":"citySelectText")},y(""==(null==(s=L.baseInfo)?void 0:s.cityName)?"请选择所在城市":this.baseInfo.cityName),3),l(U,{color:"#999",type:"down",size:"14"})])]})),_:1})):p("",!0),5==L.type?(d(),m(x,{key:4,label:"优势资源城市",required:"",name:"advantageCity"},{default:r((()=>[l(M,{modelValue:L.baseInfo.advantageCity,"onUpdate:modelValue":a[4]||(a[4]=e=>L.baseInfo.advantageCity=e),placeholder:"请输入优势资源城市"},null,8,["modelValue"])])),_:1})):p("",!0),6==L.type?(d(),m(x,{key:5,label:"优势资源简述",required:"",name:"resume"},{default:r((()=>[l(M,{modelValue:L.baseInfo.resume,"onUpdate:modelValue":a[5]||(a[5]=e=>L.baseInfo.resume=e),placeholder:"请输入优势资源简述"},null,8,["modelValue"])])),_:1})):p("",!0),7==L.type?(d(),m(x,{key:6,label:"邀请码",required:"",name:"InvitationCode"},{default:r((()=>[l(M,{modelValue:L.baseInfo.InvitationCode,"onUpdate:modelValue":a[6]||(a[6]=e=>L.baseInfo.InvitationCode=e),placeholder:"请填写邀请码(填写后不允许修改)"},null,8,["modelValue"])])),_:1})):p("",!0),8==L.type?(d(),m(x,{key:7,label:"关注产业链",required:"",name:"focusIndustryIds"},{default:r((()=>[l(F,{modelValue:L.baseInfo.focusIndustryIds,"onUpdate:modelValue":a[7]||(a[7]=e=>L.baseInfo.focusIndustryIds=e),multiple:!0,localdata:L.industryList,collapseTagsNum:2,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):p("",!0),9==L.type?(d(),m(x,{key:8,label:"招商属地",required:"",name:"focusIndustryIds"},{default:r((()=>{var e,s;return[c("div",{onClick:a[8]||(a[8]=e=>j.pop()),class:"citySelect"},[c("span",{class:f(""==(null==(e=L.baseInfo)?void 0:e.codeName)?"":"citySelectText")},y(""==(null==(s=L.baseInfo)?void 0:s.codeName)?"请选择招商属地":this.baseInfo.codeName),3),l(U,{color:"#999",type:"down",size:"14"})])]})),_:1})):p("",!0),10==L.type?(d(),m(x,{key:9,label:"所在单位职务",required:"",name:"jobTitle"},{default:r((()=>[l(M,{modelValue:L.baseInfo.jobTitle,"onUpdate:modelValue":a[9]||(a[9]=e=>L.baseInfo.jobTitle=e),placeholder:"请输入所在单位职务"},null,8,["modelValue"])])),_:1})):p("",!0),11==L.type?(d(),m(x,{key:10,label:"出海关注目的地",required:"",name:"overseaTargetCodes"},{default:r((()=>[l(F,{modelValue:L.baseInfo.overseaTargetCodes,"onUpdate:modelValue":a[10]||(a[10]=e=>L.baseInfo.overseaTargetCodes=e),multiple:!0,localdata:L.countryList,collapseTagsNum:2,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):p("",!0),12==L.type?(d(),m(x,{key:11,label:"企业简介",required:"",name:"enterpriseProfile"},{default:r((()=>[l(M,{modelValue:L.baseInfo.enterpriseProfile,"onUpdate:modelValue":a[11]||(a[11]=e=>L.baseInfo.enterpriseProfile=e),placeholder:"请输入企业简介"},null,8,["modelValue"])])),_:1})):p("",!0),13==L.type?(d(),m(x,{key:12,label:"服务国家",required:"",name:"serviceCountryCode"},{default:r((()=>[l(P,{modelValue:L.baseInfo.serviceCountryCode,"onUpdate:modelValue":a[12]||(a[12]=e=>L.baseInfo.serviceCountryCode=e),localdata:L.countryList},null,8,["modelValue","localdata"])])),_:1})):p("",!0),14==L.type?(d(),m(x,{key:13,label:"服务类目",required:"",name:"serviceItemCodes"},{default:r((()=>[l(F,{modelValue:L.baseInfo.serviceItemCodes,"onUpdate:modelValue":a[13]||(a[13]=e=>L.baseInfo.serviceItemCodes=e),multiple:!0,localdata:L.categoryList,collapseTagsNum:2,maximum:9999,collapseTags:!0},null,8,["modelValue","localdata"])])),_:1})):p("",!0)])),_:1},8,["rules","modelValue"]),c("div",{class:"experience",type:"primary",onClick:a[14]||(a[14]=e=>j.submit("valiForm"))}," 保存 ")])),_:1}),l(S,{ref:"popup2","background-color":"#fff"},{default:r((()=>[L.showMap?(d(),m(A,{key:0,onAffirm:j.affirm2,cityName:L.baseInfo.cityName},null,8,["onAffirm","cityName"])):p("",!0)])),_:1},512),l(S,{ref:"popup","background-color":"#fff"},{default:r((()=>[L.showMap?(d(),m(O,{key:0,onAffirm:j.affirm,cityName:L.baseInfo.codeName},null,8,["onAffirm","cityName"])):p("",!0)])),_:1},512)])),_:1})],64)}],["__scopeId","data-v-70e5435e"]]);export{T as default};
