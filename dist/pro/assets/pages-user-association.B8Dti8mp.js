import{_ as s}from"./page-meta.CQ11A97m.js";import{z as t,A as a,a as e,f as o,B as p,c as i,F as c,o as n,C as r}from"./index-D8Q9TKMD.js";import{r as d}from"./uni-app.es.KQfvbE5W.js";import{t as l}from"./index.DW56S9T8.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./returnFn.BYkANsDr.js";const g=m({components:{tabBar:l},data:()=>({type:1}),onLoad(s){s.type&&(this.type=s.type)},methods:{}},[["render",function(l,m,g,u,h,y){const x=d(t("page-meta"),s),f=a("tabBar"),v=r;return n(),e(c,null,[o(x,{"page-style":"background-color: #FAFCFF"}),p("div",null,[o(f,{title:1==h.type?"加入社群":"联系客服"},null,8,["title"]),p("div",{class:"box"},[p("div",{class:"top"},[1==h.type?(n(),i(v,{key:0,src:"https://static.idicc.cn/cdn/aiChat/applet/xiaoAI1.png",class:"showHeadimg",mode:""})):(n(),i(v,{key:1,src:"https://static.idicc.cn/cdn/aiChat/applet/xiaoAI2.png",class:"showHeadimg",mode:""}))]),p("div",{class:"qtCode"},[p("span",{class:"text"},"—— 扫码添加 ——"),o(v,{"show-menu-by-longpress":"true",src:"https://static.idicc.cn/cdn/aiChat/applet/newServiceCode2.png",class:"qtCodeimg"}),p("div",{class:"explain"},"扫描二维码 添加小艾客服")])])])],64)}],["__scopeId","data-v-302e015d"]]);export{g as default};
