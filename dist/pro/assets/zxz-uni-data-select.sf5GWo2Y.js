import{j as e,P as t,p as i,l as a,s as l,z as s,o as r,c as n,w as c,a as h,t as u,d as o,f as d,e as p,n as m,F as f,b as y,B as g,D as _,g as x,i as D,a8 as b,S as V}from"./index-D8Q9TKMD.js";import{Y as w}from"./uni-cloud.es.D5Qo5u-4.js";import{_ as C}from"./uni-icons.HwX4OE83.js";import{r as I}from"./uni-app.es.KQfvbE5W.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const S=k({name:"uni-stat-select",mixins:[w.mixinDatacom||{}],props:{collapseTagsNum:{type:Number,default:1},collapseTags:{type:Boolean,default:!1},dataKey:{type:[String],default:"text"},dataValue:{type:[String],default:"value"},multiple:{type:Boolean,default:!1},filterable:{type:Boolean,default:!1},localdata:{type:Array,default:()=>[]},modelValue:{type:[String,Number,Array],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1},format:{type:String,default:""},maximum:{type:Number,default:3}},data:()=>({showSelector:!1,current:[],mixinDatacomResData:[],apps:[],channels:[],cacheKey:"uni-data-select-lastSelectedValue",placeholderOld:"",currentArr:[],filterInput:"",isFocus:!1,windowHeight:0,isDown:!0}),created(){this.windowHeight=e().windowHeight,this.multiple?(this.currentArr=this.modelValue||[],this.current.length>0&&(this.current=[]),this.modelValue&&this.modelValue.length>0&&this.filterMixinDatacomResData.length>0&&(this.current=this.modelValue.map((e=>({...this.mixinDatacomResData.find((t=>t[this.dataValue]==e))}))))):(this.modelValue||0==this.value)&&(this.current=this.formatItemName(this.filterMixinDatacomResData.find((e=>e[this.dataValue]==this.modelValue)))),this.placeholderOld=this.placeholder,this.debounceGet=this.debounce((()=>{this.query()}),300),this.collection&&!this.localdata.length&&this.debounceGet()},computed:{filterMixinDatacomResData(){return this.filterable&&this.filterInput?this.mixinDatacomResData.filter((e=>e[this.dataKey].includes(this.filterInput))):this.mixinDatacomResData},typePlaceholder(){const e=this.placeholder,t={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return t?e+t:e},valueCom(){return this.modelValue}},watch:{localdata:{immediate:!0,handler(e,t){Array.isArray(e)&&t!==e&&(this.mixinDatacomResData=e||[])}},valueCom:{handler(e,t){this.initDefVal()},deep:!0,immediate:!0},mixinDatacomResData:{immediate:!0,handler(e){e.length&&this.initDefVal()}}},methods:{getIsDown(){const e=t().in(this),i=this;e.select(".uni-stat-box").boundingClientRect((e=>{i.windowHeight-e.top>200?i.isDown=!0:i.isDown=!1})).exec()},debounce(e,t=100){let i=null;return function(...a){i&&clearTimeout(i),i=setTimeout((()=>{e.apply(this,a)}),t)}},query(){this.mixinDatacomEasyGet()},onMixinDatacomPropsChange(){this.collection&&this.debounceGet()},initDefVal(){let e="";if(!this.valueCom&&0!==this.valueCom||this.isDisabled(this.valueCom)){let t;if(this.collection&&(t=this.getCache()),t||0===t)e=t;else{let t="";this.defItem>0&&this.defItem<=this.mixinDatacomResData.length&&(t=this.mixinDatacomResData[this.defItem-1][this.dataValue]),e=t}(e||0===e)&&this.emit(e)}else e=this.valueCom;if(this.multiple){const t=this.mixinDatacomResData||[];e||(e=[]),this.current=e.map((e=>({...t.find((t=>t[this.dataValue]==e))}))),this.currentArr=this.current.map((e=>e[this.dataValue])),e.length<1&&(this.currentArr=[])}else{const t=this.mixinDatacomResData.find((t=>t[this.dataValue]===e));this.current=t?this.formatItemName(t):""}},isDisabled(e){let t=!1;return this.mixinDatacomResData.forEach((i=>{i[this.dataValue]===e&&(t=i.disable)})),t},inputChange(e){this.$emit("inputChange",e.detail.value)},clearVal(){this.disabled||(this.multiple?(this.current=[],this.currentArr=[],this.emit([])):(this.current="",this.currentArr=[],this.emit("")),this.collection&&this.removeCache(),this.placeholderOld=this.placeholder,this.filterInput="")},change(e){if(!e.disable)if(this.multiple){if(this.current||(this.current=[]),this.currentArr||(this.currentArr=[]),this.currentArr.includes(e[this.dataValue])){let t=this.current.findIndex((t=>t[this.dataValue]==e[this.dataValue]));this.current.splice(t,1),this.currentArr.splice(t,1),this.emit(this.current)}else{if(this.current.length>=this.maximum)return i({title:`最多只能选择${this.maximum}个选项`,icon:"none"});this.current.push(e),this.currentArr.push(e[this.dataValue]),this.emit(this.current)}this.filterInput=""}else this.showSelector=!1,this.current=this.formatItemName(e),this.filterable&&(this.filterInput=e[this.dataKey]),this.emit(e[this.dataValue])},delItem(e){if(!this.disabled&&this.currentArr.includes(e[this.dataValue])){let t=this.current.findIndex((t=>t[this.dataValue]==e[this.dataValue]));this.current.splice(t,1),this.currentArr.splice(t,1),this.emit(this.current)}},emit(e){if(this.multiple){this.$emit("input",this.currentArr),this.$emit("update:modelValue",this.currentArr);const e=this.mixinDatacomResData.filter((e=>this.currentArr.includes(e[this.dataValue])));this.$emit("change",e)}else{this.$emit("input",e),this.$emit("update:modelValue",e);const t=this.mixinDatacomResData.find((t=>e==t[this.dataValue]));console.log(t),this.$emit("change",t)}this.collection&&this.setCache(e)},toggleSelector(){this.disabled||(this.getIsDown(),this.showSelector=!this.showSelector,this.isFocus=this.showSelector,this.filterable&&this.current&&this.showSelector?this.multiple||(this.placeholderOld=this.current):!this.filterable||this.current||this.showSelector||this.placeholderOld!=this.placeholder&&(this.multiple||(this.current=this.placeholderOld)),this.filterInput="")},formatItemName(e){if(!e)return"";let t=e[this.dataKey],i=e[this.dataValue],{channel_code:a}=e;if(a=a?`(${a})`:"",this.format){let t="";t=this.format;for(let i in e)t=t.replace(new RegExp(`{${i}}`,"g"),e[i]);return t}return this.collection.indexOf("app-list")>0?`${t}(${i})`:t||`未命名${a}`},getLoadData(){return this.mixinDatacomResData},getCurrentCacheKey(){return this.collection},getCache(e=this.getCurrentCacheKey()){return(a(this.cacheKey)||{})[e]},setCache(e,t=this.getCurrentCacheKey()){let i=a(this.cacheKey)||{};i[t]=e,l(this.cacheKey,i)},removeCache(e=this.getCurrentCacheKey()){let t=a(this.cacheKey)||{};delete t[e],l(this.cacheKey,t)}}},[["render",function(e,t,i,a,l,w){const k=I(s("uni-icons"),C),S=D,A=b,R=V;return r(),n(S,{class:"uni-stat__select"},{default:c((()=>[i.label?(r(),h("span",{key:0,class:"uni-label-text"},u(i.label+"："),1)):o("",!0),d(S,{class:p(["uni-stat-box",{"uni-stat__actived":l.current}])},{default:c((()=>[d(S,{class:p(["uni-select",{"uni-select--disabled":i.disabled}]),style:m({height:i.multiple?"100%":" 35px"})},{default:c((()=>[d(S,{class:"uni-select__input-box",style:m({height:i.multiple?"100%":"35px"}),onClick:w.toggleSelector},{default:c((()=>[i.multiple&&l.current.length>0?(r(),n(S,{key:0,class:"",style:{display:"flex","flex-wrap":"wrap",width:"100%"}},{default:c((()=>[(r(!0),h(f,null,y(i.collapseTags?l.current.slice(0,i.collapseTagsNum):l.current,((e,t)=>(r(),n(S,{class:"tag-calss",key:e[i.dataValue]},{default:c((()=>[g("span",{class:"text"},u(e[i.dataKey]),1),d(S,{class:"",onClick:_((t=>w.delItem(e)),["stop"])},{default:c((()=>[d(k,{type:"clear",style:{"margin-left":"4px"},color:"#c0c4cc"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),l.current.length>i.collapseTagsNum&&i.collapseTags?(r(),n(S,{key:0,class:"tag-calss"},{default:c((()=>[g("span",{class:"text"},"+"+u(l.current.length-i.collapseTagsNum),1)])),_:1})):o("",!0),i.filterable&&!i.disabled?(r(),n(A,{key:1,onInput:w.inputChange,class:"uni-select__input-text",type:"text",style:{"font-size":"12px",height:"52rpx","margin-left":"6px",width:"auto"},placeholder:"请输入",modelValue:l.filterInput,"onUpdate:modelValue":t[0]||(t[0]=e=>l.filterInput=e)},null,8,["onInput","modelValue"])):o("",!0)])),_:1})):l.current&&l.current.length>0&&!l.showSelector?(r(),n(S,{key:1,class:"uni-select__input-text"},{default:c((()=>[x(u(l.current),1)])),_:1})):i.filterable&&l.showSelector?(r(),n(A,{key:2,focus:l.isFocus,onInput:w.inputChange,disabled:i.disabled,onClick:t[1]||(t[1]=_((()=>{}),["stop"])),class:"uni-select__input-text",type:"text",style:{"font-size":"12px",position:"absolute","z-index":"1"},placeholder:l.placeholderOld,modelValue:l.filterInput,"onUpdate:modelValue":t[2]||(t[2]=e=>l.filterInput=e)},null,8,["focus","onInput","disabled","placeholder","modelValue"])):(r(),n(S,{key:3,class:"uni-select__input-text uni-select__input-placeholder"},{default:c((()=>[x(u(w.typePlaceholder),1)])),_:1})),l.current.length>0&&i.clear&&!i.disabled||l.currentArr.length>0&&i.clear&&!i.disabled?(r(),n(k,{key:4,type:"clear",color:"#c0c4cc",size:"24",style:{position:"absolute",right:"0"},onClick:w.clearVal},null,8,["onClick"])):(r(),n(k,{key:5,style:{right:"0",position:"absolute"},type:l.showSelector?"top":"bottom",size:"14",color:"#999"},null,8,["type"]))])),_:1},8,["style","onClick"]),l.showSelector?(r(),n(S,{key:0,class:"uni-select--mask",onClick:w.toggleSelector},null,8,["onClick"])):o("",!0),l.showSelector?(r(),n(S,{key:1,class:p(["uni-select__selector",l.isDown?"uni-select__selector__down":"uni-select__selector__upwards"])},{default:c((()=>[d(S,{class:"uni-popper__arrow"}),d(R,{"scroll-y":"true",class:"uni-select__selector-scroll"},{default:c((()=>[0===w.filterMixinDatacomResData.length?(r(),n(S,{key:0,class:"uni-select__selector-empty"},{default:c((()=>[g("span",null,u(i.emptyTips),1)])),_:1})):(r(!0),h(f,{key:1},y(w.filterMixinDatacomResData,((e,t)=>(r(),n(S,{class:p(["uni-select__selector-item",{"uni-select_selector-item_active":i.multiple&&l.currentArr.includes(e[i.dataValue])}]),style:{display:"flex","justify-content":"space-between","align-items":"center"},key:t,onClick:t=>w.change(e)},{default:c((()=>[g("span",{class:p({"uni-select__selector__disabled":e.disable})},u(w.formatItemName(e)),3),i.multiple&&l.currentArr.includes(e[i.dataValue])?(r(),n(k,{key:0,type:"checkmarkempty",color:"#007aff"})):o("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["class"])):o("",!0)])),_:1},8,["style","class"])])),_:1},8,["class"])])),_:1})}],["__scopeId","data-v-4c1f4004"]]);export{S as _};
