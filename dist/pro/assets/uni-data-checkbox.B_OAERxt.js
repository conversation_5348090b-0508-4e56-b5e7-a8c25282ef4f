import{Y as e}from"./uni-cloud.es.D5Qo5u-4.js";import{_ as t}from"./uni-load-more.FsbQ8f1j.js";import{z as s,o as l,c as a,w as i,g as o,t as d,a as c,F as n,e as r,b as h,n as u,f as m,d as f,h as g,i as p,aq as y,ar as x,as as b,at as k,au as v}from"./index-D8Q9TKMD.js";import{r as _}from"./uni-app.es.KQfvbE5W.js";import{_ as D}from"./_plugin-vue_export-helper.BCo6x5W8.js";const C=D({name:"uniDataChecklist",mixins:[e.mixinDatacom||{}],emits:["input","update:modelValue","change"],props:{mode:{type:String,default:"default"},multiple:{type:Boolean,default:!1},value:{type:[Array,String,Number],default:()=>""},modelValue:{type:[Array,String,Number],default:()=>""},localdata:{type:Array,default:()=>[]},min:{type:[Number,String],default:""},max:{type:[Number,String],default:""},wrap:{type:Boolean,default:!1},icon:{type:String,default:"left"},selectedColor:{type:String,default:""},selectedTextColor:{type:String,default:""},emptyText:{type:String,default:"暂无数据"},disabled:{type:Boolean,default:!1},map:{type:Object,default:()=>({text:"text",value:"value"})}},watch:{localdata:{handler(e){this.range=e,this.dataList=this.getDataList(this.getSelectedValue(e))},deep:!0},mixinDatacomResData(e){this.range=e,this.dataList=this.getDataList(this.getSelectedValue(e))},value(e){this.dataList=this.getDataList(e)},modelValue(e){this.dataList=this.getDataList(e)}},data:()=>({dataList:[],range:[],contentText:{contentdown:"查看更多",contentrefresh:"加载中",contentnomore:"没有更多"},isLocal:!0,styles:{selectedColor:"#2979ff",selectedTextColor:"#666"},isTop:0}),computed:{dataValue(){return""===this.value?this.modelValue:(this.modelValue,this.value)}},created(){this.localdata&&0!==this.localdata.length?(this.isLocal=!0,this.range=this.localdata,this.dataList=this.getDataList(this.getSelectedValue(this.range))):this.collection&&(this.isLocal=!1,this.loadData())},methods:{loadData(){this.mixinDatacomGet().then((e=>{this.mixinDatacomResData=e.result.data,0===this.mixinDatacomResData.length?(this.isLocal=!1,this.mixinDatacomErrorMessage=this.emptyText):this.isLocal=!0})).catch((e=>{this.mixinDatacomErrorMessage=e.message}))},getForm(e="uniForms"){let t=this.$parent,s=t.$options.name;for(;s!==e;){if(t=t.$parent,!t)return!1;s=t.$options.name}return t},change(e){const t=e.detail.value;let s={value:[],data:[]};if(this.multiple)this.range.forEach((e=>{t.includes(e[this.map.value]+"")&&(s.value.push(e[this.map.value]),s.data.push(e))}));else{const e=this.range.find((e=>e[this.map.value]+""===t));e&&(s={value:e[this.map.value],data:e})}this.$emit("input",s.value),this.$emit("update:modelValue",s.value),this.$emit("change",{detail:s}),this.multiple?this.dataList=this.getDataList(s.value,!0):this.dataList=this.getDataList(s.value)},getDataList(e){let t=JSON.parse(JSON.stringify(this.range)),s=[];return this.multiple&&(Array.isArray(e)||(e=[])),t.forEach(((t,l)=>{if(t.disabled=t.disable||t.disabled||!1,this.multiple)if(e.length>0){let s=e.find((e=>e===t[this.map.value]));t.selected=void 0!==s}else t.selected=!1;else t.selected=e===t[this.map.value];s.push(t)})),this.setRange(s)},setRange(e){let t=e.filter((e=>e.selected)),s=Number(this.min)||0,l=Number(this.max)||"";return e.forEach(((a,i)=>{if(this.multiple){if(t.length<=s){void 0!==t.find((e=>e[this.map.value]===a[this.map.value]))&&(a.disabled=!0)}if(t.length>=l&&""!==l){void 0===t.find((e=>e[this.map.value]===a[this.map.value]))&&(a.disabled=!0)}}this.setStyles(a,i),e[i]=a})),e},setStyles(e,t){e.styleBackgroud=this.setStyleBackgroud(e),e.styleIcon=this.setStyleIcon(e),e.styleIconText=this.setStyleIconText(e),e.styleRightIcon=this.setStyleRightIcon(e)},getSelectedValue(e){if(!this.multiple)return this.dataValue;let t=[];return e.forEach((e=>{e.selected&&t.push(e[this.map.value])})),this.dataValue.length>0?this.dataValue:t},setStyleBackgroud(e){let t={},s=this.selectedColor?this.selectedColor:"#2979ff";this.selectedColor&&("list"!==this.mode&&(t["border-color"]=e.selected?s:"#DCDFE6"),"tag"===this.mode&&(t["background-color"]=e.selected?s:"#f5f5f5"));let l="";for(let a in t)l+=`${a}:${t[a]};`;return l},setStyleIcon(e){let t={},s="";if(this.selectedColor){let s=this.selectedColor?this.selectedColor:"#2979ff";t["background-color"]=e.selected?s:"#fff",t["border-color"]=e.selected?s:"#DCDFE6",!e.selected&&e.disabled&&(t["background-color"]="#F2F6FC",t["border-color"]=e.selected?s:"#DCDFE6")}for(let l in t)s+=`${l}:${t[l]};`;return s},setStyleIconText(e){let t={},s="";if(this.selectedColor){let s=this.selectedColor?this.selectedColor:"#2979ff";"tag"===this.mode?t.color=e.selected?this.selectedTextColor?this.selectedTextColor:"#fff":"#666":t.color=e.selected?this.selectedTextColor?this.selectedTextColor:s:"#666",!e.selected&&e.disabled&&(t.color="#999")}for(let l in t)s+=`${l}:${t[l]};`;return s},setStyleRightIcon(e){let t={},s="";"list"===this.mode&&(t["border-color"]=e.selected?this.styles.selectedColor:"#DCDFE6");for(let l in t)s+=`${l}:${t[l]};`;return s}}},[["render",function(e,D,C,S,L,T){const I=_(s("uni-load-more"),t),$=g,V=p,E=y,B=x,F=b,R=k,w=v;return l(),a(V,{class:"uni-data-checklist",style:u({"margin-top":L.isTop+"px"})},{default:i((()=>[L.isLocal?(l(),c(n,{key:1},[C.multiple?(l(),a(F,{key:0,class:r(["checklist-group",{"is-list":"list"===C.mode||C.wrap}]),onChange:T.change},{default:i((()=>[(l(!0),c(n,null,h(L.dataList,((e,t)=>(l(),a(B,{class:r(["checklist-box",["is--"+C.mode,e.selected?"is-checked":"",C.disabled||e.disabled?"is-disable":"",0!==t&&"list"===C.mode?"is-list-border":""]]),style:u(e.styleBackgroud),key:t},{default:i((()=>[m(E,{class:"hidden",hidden:"",disabled:C.disabled||!!e.disabled,value:e[C.map.value]+"",checked:e.selected},null,8,["disabled","value","checked"]),"tag"!==C.mode&&"list"!==C.mode||"list"===C.mode&&"left"===C.icon?(l(),a(V,{key:0,class:"checkbox__inner",style:u(e.styleIcon)},{default:i((()=>[m(V,{class:"checkbox__inner-icon"})])),_:2},1032,["style"])):f("",!0),m(V,{class:r(["checklist-content",{"list-content":"list"===C.mode&&"left"===C.icon}])},{default:i((()=>[m($,{class:"checklist-text",style:u(e.styleIconText)},{default:i((()=>[o(d(e[C.map.text]),1)])),_:2},1032,["style"]),"list"===C.mode&&"right"===C.icon?(l(),a(V,{key:0,class:"checkobx__list",style:u(e.styleBackgroud)},null,8,["style"])):f("",!0)])),_:2},1032,["class"])])),_:2},1032,["class","style"])))),128))])),_:1},8,["class","onChange"])):(l(),a(w,{key:1,class:r(["checklist-group",{"is-list":"list"===C.mode,"is-wrap":C.wrap}]),onChange:T.change},{default:i((()=>[(l(!0),c(n,null,h(L.dataList,((e,t)=>(l(),a(B,{class:r(["checklist-box",["is--"+C.mode,e.selected?"is-checked":"",C.disabled||e.disabled?"is-disable":"",0!==t&&"list"===C.mode?"is-list-border":""]]),style:u(e.styleBackgroud),key:t},{default:i((()=>[m(R,{class:"hidden",hidden:"",disabled:C.disabled||e.disabled,value:e[C.map.value]+"",checked:e.selected},null,8,["disabled","value","checked"]),"tag"!==C.mode&&"list"!==C.mode||"list"===C.mode&&"left"===C.icon?(l(),a(V,{key:0,class:"radio__inner",style:u(e.styleBackgroud)},{default:i((()=>[m(V,{class:"radio__inner-icon",style:u(e.styleIcon)},null,8,["style"])])),_:2},1032,["style"])):f("",!0),m(V,{class:r(["checklist-content",{"list-content":"list"===C.mode&&"left"===C.icon}])},{default:i((()=>[m($,{class:"checklist-text",style:u(e.styleIconText)},{default:i((()=>[o(d(e[C.map.text]),1)])),_:2},1032,["style"]),"list"===C.mode&&"right"===C.icon?(l(),a(V,{key:0,style:u(e.styleRightIcon),class:"checkobx__list"},null,8,["style"])):f("",!0)])),_:2},1032,["class"])])),_:2},1032,["class","style"])))),128))])),_:1},8,["class","onChange"]))],64)):(l(),a(V,{key:0,class:"uni-data-loading"},{default:i((()=>[e.mixinDatacomErrorMessage?(l(),a($,{key:1},{default:i((()=>[o(d(e.mixinDatacomErrorMessage),1)])),_:1})):(l(),a(I,{key:0,status:"loading",iconType:"snow",iconSize:18,"content-text":L.contentText},null,8,["content-text"]))])),_:1}))])),_:1},8,["style"])}],["__scopeId","data-v-67e12ca1"]]);export{C as _};
