import{l as t,Y as a,U as e,Z as s,a6 as i,a7 as o,$ as l,z as n,c as d,w as p,a4 as r,i as u,o as c,f as h,B as f,g as m,t as g,a5 as v}from"./index-D8Q9TKMD.js";import{_ as w}from"./uni-popup.D6FGSq45.js";import{r as _}from"./uni-app.es.KQfvbE5W.js";import{v as $}from"./webviewUrl.CKZhzb7_.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.CNGNZNaw.js";const k=x({data:()=>({url:"",popupText:"",options:null}),onLoad(a){this.showLoading(),this.tokenData=t("token"),this.options=a,this.Download(a)},onHide(t){a(),e({delta:1})},methods:{showLoading(){s({title:"文件下载中"})},closeView(){this.$refs.popup.close(),e({delta:1})},tryAgin(){this.$refs.popup.close(),this.showLoading(),this.Download(this.options)},Download(t){let a=decodeURI(null==t?void 0:t.id).split("_"),e=`${$}showPdf`;this.url="true"===(null==t?void 0:t.isEditor)?`${e}?token=${this.tokenData}&id=${a[0]}&isEditor=${null==t?void 0:t.isEditor}`:`${e}?token=${this.tokenData}&id=${a[0]}`;let s=a[1],o=null==t?void 0:t.time;i({url:`${r}/pdf/render-url`,data:{url:this.url,id:a[0],time:o,filename:s+".pdf",json:!0,options:{screen:!0,page:{format:"A4",landscape:!1,height:null,width:null,printBackground:!0,displayHeaderFooter:!1,margin:{top:"45px",bottom:"45px"}}}},header:{},method:"POST",success:t=>{var a,e;500===(null==t?void 0:t.statusCode)||""===(null==(a=null==t?void 0:t.data)?void 0:a.content)||"systemBusy"===(null==(e=null==t?void 0:t.data)?void 0:e.status)?this.setFailState():this.onPreviewImage(t.data,s)},fail:t=>{this.setFailState()}})},setFailState(){a(),this.popupText="文件下载失败，请再试一次",setTimeout((()=>{this.$refs.popup.open()}),500)},loadSuccess(){a()},jumpToChat(){let t=`${$}showPdf`;this.url=`${t}?token=${this.tokenData}`},handlePostMessage(t){var a,e,s,i;this.onPreviewImage(null==(e=null==(a=t.detail)?void 0:a.data[0])?void 0:e.imageData,null==(i=null==(s=t.detail)?void 0:s.data[0])?void 0:i.imageName)},onPreviewImage(t,e){let s=null==t?void 0:t.content;var i=wx.env.USER_DATA_PATH+`/${e}.pdf`,n=o(s.replace("data:application/pdf;filename=generated.pdf;base64,",""));wx.getFileSystemManager().writeFile({filePath:i,data:n,success:t=>{l({filePath:i,showMenu:!0,success:function(t){a()},fail(t){a()}})}})}}},[["render",function(t,a,e,s,i,o){const l=u,r=v,$=_(n("uni-popup"),w);return c(),d(l,{class:"page-section page-section-gap"},{default:p((()=>[h(l,{class:"pdgBg"},{default:p((()=>[f("img",{class:"headportrait",src:"https://static.idicc.cn/cdn/aiChat/applet/viewBg.png",alt:""})])),_:1}),h($,{class:"pops",ref:"popup",type:"center",animation:!1,"border-radius":"10px 10px 0 0"},{default:p((()=>[h(l,{class:"popView"},{default:p((()=>[h(l,{class:"tipsTitle"},{default:p((()=>[h(l,{class:"tipsText"},{default:p((()=>[m("提示")])),_:1}),h(l,{class:"close",onClick:o.closeView},{default:p((()=>[m("X")])),_:1},8,["onClick"])])),_:1}),h(l,{class:"errorText"},{default:p((()=>[m(g(i.popupText),1)])),_:1}),h(r,{onClick:o.tryAgin,class:"closeBtn"},{default:p((()=>[m("再试一次")])),_:1},8,["onClick"])])),_:1})])),_:1},512)])),_:1})}],["__scopeId","data-v-69f9a50c"]]);export{k as default};
