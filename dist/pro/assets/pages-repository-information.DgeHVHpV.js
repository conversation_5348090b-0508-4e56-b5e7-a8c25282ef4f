import{z as a,o as t,c as s,w as e,f as o,B as i,a as c,t as l,C as r,i as n,T as d,p,g as u,l as m,U as h,m as f,q as g,A as D,n as b,F as y,b as v,d as A,D as k}from"./index-D8Q9TKMD.js";import{_ as F}from"./page-meta.CQ11A97m.js";import{r as w}from"./uni-app.es.KQfvbE5W.js";import{_ as C}from"./uni-easyinput.Kc2lvObb.js";import{t as x}from"./index.DW56S9T8.js";import{_ as P,a as U}from"./uni-forms.4s1Yg_LJ.js";import{_ as L}from"./uni-popup.D6FGSq45.js";import{_ as B}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as I}from"./returnFn.BYkANsDr.js";import"./uni-icons.HwX4OE83.js";import"./uni-transition.CNGNZNaw.js";const M="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAbFBMVEUAAAD/uAD/uAD/rwD/uAD/uAD/twD/nQD/uAD/twD/tgD/uAD/twD/twD/uAD/uAD/tQD/twD/twD/uAD/uAD/uAD/twD/uAD/uAD/uQD/uAD/uAD/uQD/uQD/swD/uAD/uAD/sQD/twD/uAADUf57AAAAI3RSTlMAv68P96BHB/M6MJbsh7pPKhfn4dPPqZuNfW9pW0EU23ckILaNzGgAAACuSURBVCjP1ZFJDsMgDEUdAqGBTM08d/D971hwkyoRYtVu+jYfeMIGGX7KKITQ0iNvaFAeGc7cL+MLDz5SOi4asNhc1czy7OA57Ccdx/K+Ht2Ra5og7wvH7W3HGpEtm1N5Hp0fkmeIDbkJDRmcUYJTzRYtIRBRgsgnak592VFmdp0A4ciANuaeZqxdPFJQBY98519L+l8NoG1WAL3NchvLmgqhHwChTQUgTXYxfMMLCJUe09P0m6wAAAAASUVORK5CYII=";const $=B({components:{tabBar:x,submitPop:B({data:()=>({FormData:{contact:"",contactMobile:""},porpsData:"",iconurl:"",rules:{contact:{rules:[{required:!0,errorMessage:"请输入姓名"}]},contactMobile:{rules:[{required:!0,errorMessage:"请输入联系方式"},{pattern:/^1[3-9]\d{9}$/,errorMessage:"请输入合法手机号"}]},submitLoading:!1,type:"productData "}}),methods:{applyaffirm(){this.$refs.apply.close()},opens(a,t,s){this.iconurl=s,this.porpsData=a,this.type=t,this.$refs.enterforPopup.open("bottom")},changepop(a){this.$emit("updatapop",a),a.show||(this.$refs.valiForm.clearValidate(),this.FormData={contact:"",contactMobile:""},this.iconurl="")},canle(a){this.$refs.enterforPopup.close(),this.$refs.valiForm.clearValidate(),this.iconurl="",this.FormData={contact:"",contactMobile:""}},add(){if(!this.submitLoading)try{this.submitLoading=!0,this.$refs.valiForm.validate().then((a=>{let t={contact:this.FormData.contact,contactMobile:this.FormData.contactMobile,type:this.type,className:this.porpsData};this.$api.submitProductContact({data:t,method:"post"}).then((a=>{this.submitLoading=!1,this.$refs.apply.open(),this.canle()})).catch((a=>{this.submitLoading=!1}))})).catch((a=>{this.submitLoading=!1}))}catch(a){this.submitLoading=!1}}}},[["render",function(d,p,u,m,h,f){const g=r,D=w(a("uni-easyinput"),C),b=w(a("uni-forms-item"),P),y=w(a("uni-forms"),U),v=w(a("uni-popup"),L),A=n;return t(),s(A,null,{default:e((()=>[o(v,{onChange:f.changepop,"is-mask-click":!0,"border-radius":"10px 10px 0 0",ref:"enterforPopup","background-color":"#fff"},{default:e((()=>[i("div",{class:"messageSubmitBox"},[i("div",{class:"messageSubmitHead"}," 信息提交 "),i("div",{class:"namebox"},[h.iconurl?(t(),s(g,{key:0,src:h.iconurl,class:"icon"},null,8,["src"])):(t(),c("div",{key:1,class:"iconbox"},[o(g,{src:M,class:"icons"})])),i("div",{class:"rightConst"},[i("div",{class:"name"},l(h.porpsData),1),i("div",{class:"operator"}," 运营人：艾瑞数云 ")])]),i("div",{class:"attractForm"},[o(y,{ref:"valiForm",rules:h.rules,modelValue:h.FormData,"label-position":"top","label-width":"100"},{default:e((()=>[o(b,{label:"姓名",name:"contact"},{default:e((()=>[o(D,{trim:"all",clearable:!1,modelValue:h.FormData.contact,"onUpdate:modelValue":p[0]||(p[0]=a=>h.FormData.contact=a),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),o(b,{label:"联系方式",name:"contactMobile"},{default:e((()=>[o(D,{trim:"all",clearable:!1,modelValue:h.FormData.contactMobile,"onUpdate:modelValue":p[1]||(p[1]=a=>h.FormData.contactMobile=a),placeholder:"请输入"},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","modelValue"])]),i("div",{class:"btnsbox"},[i("div",{onClick:p[2]||(p[2]=a=>f.canle()),class:"canle"}," 取消 "),i("div",{onClick:p[3]||(p[3]=(...a)=>f.add&&f.add(...a)),class:"affirm"}," 确认 ")])])])),_:1},8,["onChange"]),o(v,{ref:"apply","is-mask-click":!1,type:"dialog",style:{"z-index":"99"}},{default:e((()=>[i("div",{class:"applyaffirm"},[i("div",{class:"p1"},"提交成功"),i("div",{class:"p2"},"客服将会尽快联系您，请保持电话畅通"),i("div",{onClick:p[4]||(p[4]=(...a)=>f.applyaffirm&&f.applyaffirm(...a)),class:"affirm"}," 确认 ")])])),_:1},512)])),_:1})}],["__scopeId","data-v-3c908d80"]]),buyPop:B({data:()=>({porpsData:""}),methods:{applyaffirm(){this.$refs.apply.close()},opens(a){this.porpsData=a,this.$refs.enterforPopup.open("bottom")},changepop(a){this.$emit("updatapop",a),a.show||(this.FormData={contact:"",contactMobile:""},this.iconurl="")},canle(a){this.$refs.enterforPopup.close(),this.iconurl="",this.FormData={contact:"",contactMobile:""}},purchase(){if(!this.submitLoading)try{this.submitLoading=!0;let a=this;d({success(t){a.$api.billingSubmit({method:"post",data:{jsCode:t.code,businessCode:"dataProduct",item:{dataProductId:a.porpsData.id},method:1}}).then((t=>{a.submitLoading=!1,wx.requestPayment({timeStamp:t.result.timeStamp,nonceStr:t.result.nonceStr,package:t.result.packageStr,signType:"RSA",paySign:t.result.paySign,success(t){p({title:"支付成功",icon:"none"}),a.canle(),a.$emit("updataData")},fail(t){a.submitLoading=!1,p({title:"您已取消支付",icon:"none"})}})}))}})}catch(a){this.submitLoading=!1}}}},[["render",function(d,p,m,h,f,g){const D=r,b=w(a("uni-popup"),L),y=n;return t(),s(y,null,{default:e((()=>[o(b,{onChange:g.changepop,"is-mask-click":!0,"border-radius":"10px 10px 0 0",ref:"enterforPopup","background-color":"#fff"},{default:e((()=>[i("div",{class:"messageSubmitBox"},[i("div",{class:"enterforHead"},[i("div",{onClick:p[0]||(p[0]=(...a)=>g.canle&&g.canle(...a)),class:"canle"},"取消"),i("div",{class:"title"},"购买"),i("div",{class:"canle"})]),i("div",{class:"namebox"},[f.porpsData.logoUrl?(t(),s(D,{key:0,src:f.porpsData.logoUrl,class:"icon"},null,8,["src"])):(t(),c("div",{key:1,class:"iconbox"},[o(D,{src:M,class:"icons"})])),i("div",{class:"rightConst"},[i("div",{class:"name"},l(f.porpsData.productName),1),i("div",{class:"operator"}," 运营人："+l(f.porpsData.operator),1)])]),i("div",{class:"attractForm"},[i("div",{class:"top"},"价格明细"),i("div",{class:"bto"},[i("div",null,"商品总价"),i("span",{class:"total"},"￥"+l(f.porpsData.price),1)])]),i("div",{class:"btnsbox"},[i("div",{class:"left"},[i("div",{class:"summation"},"合计:"),i("div",{class:"price"},[i("span",{class:"currency"},"￥"),u(l(f.porpsData.price),1)])]),i("div",{onClick:p[1]||(p[1]=(...a)=>g.purchase&&g.purchase(...a)),class:"affirm"}," 立即购买 ")])])])),_:1},8,["onChange"])])),_:1})}],["__scopeId","data-v-a59f30a3"]])},data:()=>({showNodata:!1,productlist:[],Oldproductlist:[],title:"哒达助招",bottomHeight:0,token:"",checked:"",keyword:"",statusBarHeight:m("statusBarHeight"),whetherIos:!1}),onLoad(a){this.whetherIos=m("whetherIos"),this.token=m("token"),this.getList()},onShow(){wx.hideHomeButton()},onUnload(){},methods:{goBack(){h({delta:1})},bindingId(){let a=this;d({success(t){a.$api.syncUnion({data:{code:t.code},method:"post"}).then((a=>{})).catch((a=>{}))}})},updataData(){this.getList()},previewPDF(a){if(!this.token)return f({url:"/pages/login/index"});1==a.type?g({url:`/pages/repository/informationDel?detail=${a.detail}&imageUrl=${encodeURIComponent(a.imageUrl)}`}):g({url:`/pages/repository/PdfLoading?id=${a.id}&isBuy=${a.isBuy}`})},changekeyword(a){this.productlist=a?this.Oldproductlist.filter((t=>t.productName.includes(a))):[...this.Oldproductlist]},getrepository(a){if(!this.token)return f({url:"/pages/login/index"});if(this.whetherIos){let a=this;return void d({success(t){a.$api.syncUnion({data:{code:t.code},method:"post"}).then((a=>{g({url:"/components/Goofficial"})})).catch((a=>{}))}})}let t=a;this.$refs.buyPop.opens(t)},getList(){this.$api.productList({data:{type:-1},method:"get"}).then((a=>{this.productlist=a.result.records,this.Oldproductlist=a.result.records,this.showNodata=!0})).catch((a=>{this.showNodata=!0}))}}},[["render",function(d,p,m,h,f,g){const x=w(a("page-meta"),F),P=r,U=n,L=w(a("uni-easyinput"),C),B=D("submitPop"),$=D("buyPop");return t(),c(y,null,[o(x,{"page-style":"background-color: #FAFCFF"}),i("div",{style:{height:"168rpx","background-color":"#dde6ff"}}),i("div",{class:"topImg",style:b(`top: ${f.statusBarHeight}px`)},[o(U,{class:"black",onClick:p[0]||(p[0]=a=>g.goBack())},{default:e((()=>[o(P,{class:"returnFn",src:I})])),_:1}),u(" 哒达助招 ")],4),i("div",{class:"headimg"}),i("div",{class:"box"},[i("div",{style:{padding:"0 30rpx","margin-top":"18rpx"}},[i("div",{class:"seinput"},[o(L,{trim:"all",prefixIcon:"search",clearable:!1,modelValue:f.keyword,"onUpdate:modelValue":p[1]||(p[1]=a=>f.keyword=a),onInput:g.changekeyword,placeholder:"请输入关键词",placeholderStyle:"font-size:26rpx"},null,8,["modelValue","onInput"])])]),i("div",{class:"repository"},[i("div",{class:"repositoryList"},[(t(!0),c(y,null,v(f.productlist,((a,e)=>(t(),c("div",{key:a.id},[i("div",{style:b({background:f.checked===a.id?1==a.type?"linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #FFFFFF;":"linear-gradient(164deg, #FFDB5C 0%, #FF9900 100%), #FFFFFF;":"#fff"}),class:"itemrepository2",onClick:t=>g.previewPDF(a)},[i("div",{class:"itemtop"},[a.logoUrl?(t(),s(P,{key:0,src:a.logoUrl,class:"icons"},null,8,["src"])):(t(),c("div",{key:1,class:"iconPaths"},[o(P,{src:M,class:"icon"})])),i("span",{style:b({color:f.checked===a.id?"#fff":"#000000"}),class:"name"},l(a.productName),5)]),i("div",{style:b({color:f.checked===a.id?"#fff":"#86909C"}),class:"itembtm"}," 运营人："+l(a.operator),5),a.isBuy?(t(),c("div",{key:0,class:"havePurchased"},l(f.whetherIos?"已解锁":"已购买"),1)):A("",!0),a.isBuy||0!=a.type?A("",!0):(t(),c("div",{key:1,onClick:k((t=>g.getrepository(a)),["stop"]),class:"Purchased"},l(f.whetherIos?"解锁":"购买"),9,["onClick"]))],12,["onClick"])])))),128))]),0==f.productlist.length&&f.showNodata?(t(),c("div",{key:0,class:"nodatabox"},[o(P,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),i("span",{class:"span"},"暂无内容")])):A("",!0)]),i("div",{class:"popInput"},[o(B,{ref:"submitPops"},null,512),o($,{onUpdataData:g.updataData,ref:"buyPop"},null,8,["onUpdataData"])])])],64)}],["__scopeId","data-v-22789cab"]]);export{$ as default};
