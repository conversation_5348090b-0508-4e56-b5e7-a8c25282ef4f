import{l as e,p as s,z as t,A as i,a,f as n,B as r,D as o,g as l,t as c,w as h,F as p,b as d,d as u,C as y,o as g,e as m,i as A}from"./index-D8Q9TKMD.js";import{_ as L}from"./page-meta.CQ11A97m.js";import{r as I}from"./uni-app.es.KQfvbE5W.js";import{_ as k}from"./uni-easyinput.Kc2lvObb.js";import{_ as w}from"./uni-transition.CNGNZNaw.js";import{e as T}from"./enterpriseList.CHrsyWKd.js";import{t as f}from"./index.DW56S9T8.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.HwX4OE83.js";import"./utils.CQ5ImuOB.js";import"./returnFn.BYkANsDr.js";const b=C({components:{enterpriseList:T,tabBar:f},data:()=>({statusBarHeight:0,keyword:"",time:null,searchResultList:[],calling:!1,chainId:"",HistoryList:[],screenType:1,showPop:!1}),created(){this.statusBarHeight=e("statusBarHeight")},onLoad(e){this.chainId=e.showChain,this.getSearchHistoryList()},watch:{searchResultList(e){this.searchResultList.forEach((e=>{let s=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let t=e.enterpriseLabelNames[0];t?s.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:t.includes("独角兽")?e.enterpriseIconLabelId=2:t.includes("专精特新")?e.enterpriseIconLabelId=3:t.includes("隐形冠军")?e.enterpriseIconLabelId=4:t.includes("瞪羚")?e.enterpriseIconLabelId=5:t.includes("创新")?e.enterpriseIconLabelId=6:t.includes("技术先进")?e.enterpriseIconLabelId=7:t.includes("科技")?e.enterpriseIconLabelId=8:t.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}},methods:{uncheckPop(){this.showPop=!1},changeTypeFn(){this.showPop=!this.showPop},changeListType(e){this.screenType!=e&&(this.screenType=e,this.keyword="",this.takeEffect=!1,this.getSearchHistoryList(),this.searchResultList=[])},getSearchHistoryList(){this.$api.getSearchHistory({data:{type:this.screenType}}).then((e=>{this.HistoryList=e.result}))},delHistoryList(){if(0==this.HistoryList.length)return s({title:"暂无历史记录",icon:"none"});this.$api.deleteSearchHistory({data:{type:this.screenType},method:"POST"}).then((e=>{this.getSearchHistoryList(),setTimeout((()=>{s({title:"已清空历史记录",icon:"none"})}),1e3)}))},goBack(){var e;null==(e=null==window?void 0:window.ReactNativeWebView)||e.postMessage(JSON.stringify({type:"changePath",value:"",path:"goBack"}))},automaticSearch(e){this.keyword=e,this.getList(e)},searchFn(e){clearTimeout(this.time),e?this.getList(e):this.searchResultList=[]},changekeyword(e){e||this.getSearchHistoryList(),this.keyword=e,this.calling=!0,null!=this.time&&clearTimeout(this.time),this.time=setTimeout((async()=>{e?this.getList(e):this.searchResultList=[]}),3e3)},getList(e){let s={pageNum:1,pageSize:20,chainId:this.chainId};1==this.screenType?s.keyword=e:s.productName=e,this.$api.pageByChainNode({data:s,method:"post"}).then((e=>{this.searchResultList=e.result.records})).finally((()=>{this.calling=!1}))}}},[["render",function(e,s,T,f,C,b){const H=I(t("page-meta"),L),B=i("tabBar"),j=I(t("uni-easyinput"),k),v=A,S=I(t("uni-transition"),w),R=y,F=i("enterpriseList");return g(),a(p,null,[n(H,{"page-style":"background-color: #FFFFFF"}),n(B,{title:"产业360"}),r("div",{onClick:s[5]||(s[5]=e=>b.uncheckPop()),class:"box"},[r("div",{class:"searchTop"},[r("div",{class:"seinput"},[n(j,{focus:!0,trim:"all",modelValue:C.keyword,"onUpdate:modelValue":s[0]||(s[0]=e=>C.keyword=e),onConfirm:b.searchFn,onInput:b.changekeyword,"adjust-position":!1,placeholder:"请输入关键词",confirmType:"search",placeholderStyle:"font-size:26rpx"},null,8,["modelValue","onConfirm","onInput"]),r("div",{onClick:s[3]||(s[3]=o((e=>b.changeTypeFn()),["stop"])),class:"changeType"},[l(c(1==C.screenType?"按企业":"按产品")+" ",1),r("div",{class:"InvertedTriangle"}),r("div",{class:"longString"}),n(S,{style:{"z-index":"99"},ref:"ani",show:C.showPop},{default:h((()=>[n(v,{class:"userPop"},{default:h((()=>[n(v,{onClick:s[1]||(s[1]=e=>b.changeListType(1)),class:m(1==C.screenType?"popItems":" popItem")},{default:h((()=>[l(" 按企业 ")])),_:1},8,["class"]),n(v,{onClick:s[2]||(s[2]=e=>b.changeListType(2)),class:m(2==C.screenType?"popItems":" popItem")},{default:h((()=>[l(" 按产品 ")])),_:1},8,["class"])])),_:1})])),_:1},8,["show"])]),r("div",{onClick:s[4]||(s[4]=e=>b.getList(C.keyword)),class:"searchBtn"},"搜索")])]),0!=C.searchResultList.length||C.keyword?u("",!0):(g(),a("div",{key:0,class:"searchHistory"},[r("div",{class:"HistoryTitle"},[r("div",{class:"HistoryTitleText"},"搜索历史"),n(R,{onClick:b.delHistoryList,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgBAMAAACBVGfHAAAAG1BMVEUAAACGj5yFkJuGkJyFj5uAjJmAjJmIj5yGkJwoW6//AAAACHRSTlMAy/Wy6ygUS64l1CIAAABWSURBVCjPYwCBkg4wcGeAAQ+IQAtcoANKkSsg0YECGjEFEFohTBoKMAqAMUKAowGEB14A4jBq+lYjAcZnawJTFmYwgeRmMBWEiARVsACrEYyvHMDAAABZtUdTAlNznwAAAABJRU5ErkJggg==",class:"HistoryTitleImg"},null,8,["onClick"])]),r("div",{class:"HistoryBox"},[(g(!0),a(p,null,d(C.HistoryList,((e,s)=>(g(),a("div",{class:"HistoryItem",onClick:s=>b.automaticSearch(e),key:s},c(e),9,["onClick"])))),128))])])),r("div",{class:"searchResult"},[n(F,{showChain:C.chainId,Jurisdiction:!0,enterpriseList:C.searchResultList},null,8,["showChain","enterpriseList"])]),0==C.searchResultList.length&&C.keyword&&!C.calling?(g(),a("div",{key:1,class:"nodatabox"},[n(R,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),r("span",{class:"span"},"暂无内容")])):u("",!0)])],64)}],["__scopeId","data-v-a2ea62e0"]]);export{b as default};
