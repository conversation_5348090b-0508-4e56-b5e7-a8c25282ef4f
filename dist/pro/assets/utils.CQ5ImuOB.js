const n=function(n){let i=window.location.href,c="";i.split("?").length>1&&(c=i.split("?")[1]);let t=c.split("&"),a={};return t.forEach((function(n){let i=n.split("=");i.length>1&&(a[decodeURIComponent(i[0])]=decodeURIComponent(i[1]||""))})),a.hasOwnProperty(n)?a[n]:null},i=[{name:"其他",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/0.png"},{name:"上市",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png"},{name:"独角兽",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/2.png"},{name:"专精特新",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png"},{name:"隐形冠军",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/4.png"},{name:"瞪羚",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/5.png"},{name:"创新",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/6.png"},{name:"技术先进",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/7.png"},{name:"科技",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/8.png"},{name:"雏鹰",icon:"https://static.idicc.cn/cdn/aiChat/NewVersionicon/9.png"}];export{n as g,i as l};
