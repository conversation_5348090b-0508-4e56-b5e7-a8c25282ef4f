import{l as e,s as t,q as s,m as a,A as i,z as n,a as o,f as r,w as l,F as c,i as p,o as d,n as m,g as h,t as u,B as f,e as g,c as y,b as I,d as L,C as A,S as v}from"./index-D8Q9TKMD.js";import{_ as b}from"./uni-popup.D6FGSq45.js";import{r as N}from"./uni-app.es.KQfvbE5W.js";import{m as S}from"./mapList.C3TkrYrq.js";import{m as C}from"./moreScreen.CVMruDSN.js";import{l as w,g as k}from"./utils.CQ5ImuOB.js";import{_ as P}from"./right2.DB4H0AJ0.js";import{_ as F}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.CNGNZNaw.js";const B=F({components:{mapList2:S,moreScreen:C},watch:{enterpriseList(e){this.enterpriseList.forEach((e=>{let t=["主板","创业板","科创板","北交所","港股","中概股","新三板"];if(null===e.enterpriseIconLabelId&&(e.enterpriseIconLabelId=0),e.enterpriseLabelNames){let s=e.enterpriseLabelNames[0];s?t.includes(e.enterpriseLabelNames[0])?e.enterpriseIconLabelId=1:s.includes("独角兽")?e.enterpriseIconLabelId=2:s.includes("专精特新")?e.enterpriseIconLabelId=3:s.includes("隐形冠军")?e.enterpriseIconLabelId=4:s.includes("瞪羚")?e.enterpriseIconLabelId=5:s.includes("创新")?e.enterpriseIconLabelId=6:s.includes("技术先进")?e.enterpriseIconLabelId=7:s.includes("科技")?e.enterpriseIconLabelId=8:s.includes("雏鹰")?e.enterpriseIconLabelId=9:e.enterpriseIconLabelId=0:e.enterpriseIconLabelId=0}else e.enterpriseIconLabelId=0}))}},data:()=>({statusBarHeight:e("statusBarHeight"),searchForm:{model:[],strategy:[],code:""},cityName:"",listicon:w,pageNum:1,pages:0,baseInfo:{},clueModelList:[{paramName:"线索模型",paramValueList:["亲缘招商","链式招商","舆情招商","资本招商","人才招商"],paramKey:"clueModel",unlimited:!0}],inStrategyList:[{paramName:"招商策略",paramValueList:["资本招商","场景招商","亲缘招商"],paramKey:"inStrategy",unlimited:!0}],enterpriseList:[],total:0,token:"",showNodata:!1,isLoading:!1,dataInfo:{}}),onLoad(s){this.token=e("token");let a=(null==s?void 0:s.id)||"";k("token")&&(this.token=k("token"),t("token",this.token)),k("token")&&(a=k("id")),this.getDel(a),this.token?this.getList():this.showNodata=!0},methods:{oneClickExportFn(){s({url:"/pages/deriveExcel/index?id=47"})},getDel(e){let t={id:e};this.$api.capabilityPoolDetailAPI({data:t}).then((e=>{this.dataInfo=e.result}))},getList(){if(this.isLoading)return;this.isLoading=!0;let e={pageSize:10,pageNum:this.pageNum,code:this.searchForm.code||"",modelTypes:this.getCodeByName(this.searchForm.model),strategyTypes:this.getCodeByName2(this.searchForm.strategy)};this.$api.reportListAPI({data:e,method:"post"}).then((e=>{this.enterpriseList=this.enterpriseList.concat(e.result.records),this.pages=e.result.pages,this.total=e.result.total})).finally((()=>{this.isLoading=!1,this.showNodata=!0}))},scrolltolowerFn(){this.isLoading||this.pages>this.pageNum&&(this.pageNum++,this.getList())},detailpage(e){var t;null==(t=null==window?void 0:window.ReactNativeWebView)||t.postMessage(JSON.stringify({type:"changePath",value:{id:e.enterpriseId||e.id,iconTypeid:e.enterpriseIconLabelId,enterpriseName:e.enterpriseName,enterpriseLabelIds:JSON.stringify(this.enterpriseLabelIds||[])},path:"industryDetail"}))},getCodeByName(e){const t={"亲缘招商":1,"链式招商":3,"舆情招商":5,"人才招商":7,"资本招商":8};return e.map((e=>t[e]||null))||[]},getCodeByName2(e){const t={"亲缘招商":1,"资本招商":2,"场景招商":3};return e.map((e=>t[e]||null))||[]},goBack(){},goDetail(e){let t={enterpriseName:e.enterpriseName,uniCode:e.uniCode,enterpriseId:e.enterpriseId,recommendRegionCode:e.recommendRegionCode||""};this.$api.reportGenerateAPI({data:t,method:"post"}).then((t=>{var s;null==(s=null==window?void 0:window.ReactNativeWebView)||s.postMessage(JSON.stringify({type:"changePath",value:{url:"previewPdf",name:"报告预览",params:{reportId:t.result,type:"pdf",enterpriseName:e.enterpriseName+"招商策略报告"}},path:"webViewPage"}))}))},regionFn(){var e,t;let s="全国";return null!=(null==(e=this.baseInfo)?void 0:e.area)&&0!=(null==(t=this.baseInfo)?void 0:t.area.length)?s=this.baseInfo.area[0]:this.baseInfo.city?s=this.baseInfo.city:this.baseInfo.province&&(s=this.baseInfo.province),s.length>4&&(s=s.substring(0,3)+"..."),s},iconColour:e=>e?"/static/AboutAi/blackArrows.png":"/static/AboutAi/blueArrows.png",screenFn(e){this.token||a({url:"/pages/login/index"}),1==e?(this.$refs.popup2.open("bottom"),this.$nextTick((()=>{this.$refs.mapList2?this.$refs.mapList2.init():console.warn("mapList2组件未挂载打开时")}))):2==e?this.$refs.clueModelPop.opens():3==e&&this.$refs.inStrategyPop.opens()},affirm1(e){this.searchForm.model=e?e.clueModel:[],this.enterpriseList=[],this.showNodata=!1,this.pageNum=1,this.pages=1,this.total=0,this.getList()},affirm2(e){this.searchForm.code=e.area.code?e.area.code:e.citys.code?e.citys.code:e.province.code,this.$refs.popup2.close(),this.baseInfo.province=e.province.name,this.baseInfo.city=e.citys.name,this.baseInfo.area=[],e.area.name&&(this.baseInfo.area[0]=e.area.name);let t=e.province.name?e.province.name+"/":"",s=e.citys.name?e.citys.name+"/":"",a=e.area.name?e.area.name+"/":"";this.cityName=t+s+a,console.log(this.cityName,"this.cityName "),this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList()},affirm3(e){this.searchForm.strategy=e?e.inStrategy:[],this.enterpriseList=[],this.showNodata=!1,this.pages=1,this.pageNum=1,this.total=0,this.getList()},isEmptyValue(e){return"string"==typeof e?""===e.trim():"number"==typeof e?0===e:"boolean"==typeof e?!1===e:Array.isArray(e)?e.every(this.isEmptyValue):null!==e&&"object"==typeof e&&Object.values(e).every(this.isEmptyValue)}}},[["render",function(e,t,s,a,S,C){const w=A,k=p,F=v,B=i("moreScreen"),E=i("mapList2"),M=N(n("uni-popup"),b);return d(),o(c,null,[r(k,null,{default:l((()=>[r(k,{class:"topHeader",style:m(`top: ${S.statusBarHeight}px`)},{default:l((()=>[r(k,{class:"header"},{default:l((()=>[r(w,{src:S.dataInfo.iconPath,class:"icon"},null,8,["src"]),r(k,{class:"mgl-20"},{default:l((()=>[r(k,{class:"span1"},{default:l((()=>[h(u(S.dataInfo.name),1)])),_:1}),r(k,{class:"span2"},{default:l((()=>[h("来自：艾瑞数云")])),_:1})])),_:1}),r(k,{onClick:C.oneClickExportFn,class:"oneClickExport"},{default:l((()=>[r(w,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAABAlBMVEUAAAA/SVg/Sllmk/9slv99o/9+pf9nlP+Jq/9rl/97o/9AQFWAgP+Lrf9znf96ov8/SVmKrP92n/97ov9xmv94oP90nv9plf+Bp/8+SVh5of6EqP+Fqf+Hqv93nv90mP9AgP+EqP9xmv5jkv9tmP+Gqv58ov+Cp/9rl/8/SlmJrP+CqP92nv9ok/9vmf+Fqf91nf99o/9xmv+Hqf9vmv+Apf92oP8+SlmKrf98o/98ov96of9vmP9rmP+Gq/89SVlxnf9AR1WAqv8/Sll+pP90nf+Fqf98o/+Hqv56of94oP9xm/9mk/+Cp/6KrP+Apv9umf9qlv+Bpv92n/92n/52nv62er/dAAAAQ3RSTlMA858IGvrKnpZXJAwE+e3p39W/qZ2Xi4l5b1tZQzcqFQT7+/fz8+/r6+vj29PTz8O3r6ejl5OTh4N/c29cT0w/NCQMiMQDoQAAAR9JREFUOMvl1NdugzAUgGEbCCFkp9m7e++9B3VdRltG+/6vUuMKCRDHzVVu8ktIB+mTz41ltMgVJWzwth8RTyvXr0YZUDKiLhGv/Mna0YZyCuLI1Z4R76H+zls/HbzEISMold49fOMtayLIG+5+hG3+Aweq7/tfrHMhHKmvYTn2jUWwywStXuvHlKoIhqUlSunGjYwmOdPsA5A70zQbCptuLasqw7BlWVarFE573z9tBMJ+EAQXfHoihIxBqFQIOZrysU3sBgJhk5DK5G88se0CCPUV276LDm92piA8c90DFAuC8prrJtb1ar1MeO95W3LyBuJMmPe8PLAs+bfvOIWZoOM4ykyws8o3C2HUfCE2BOEYlERQSj0pQFgqooXtF/7eVcUkio+WAAAAAElFTkSuQmCC"}),h(" 一键导出 ")])),_:1},8,["onClick"])])),_:1}),r(k,{class:"headertext"},{default:l((()=>[h(u(S.dataInfo.content),1)])),_:1}),r(k,{class:"optionBox"},{default:l((()=>[f("span",{class:g([C.isEmptyValue(S.baseInfo)?"unselected":"pitchOn","optionItem"]),onClick:t[0]||(t[0]=e=>C.screenFn(1))},[h(u(C.regionFn())+" ",1),r(w,{src:C.iconColour(C.isEmptyValue(S.baseInfo)),class:"Arrows"},null,8,["src"])],2),f("span",{class:g([""==S.searchForm.model?"unselected":"pitchOn","optionItem"]),onClick:t[1]||(t[1]=e=>C.screenFn(2))},[h(" 线索模型 "),r(w,{src:C.iconColour(S.searchForm.model.length<=0),class:"Arrows"},null,8,["src"])],2),f("span",{class:g([""==S.searchForm.strategy?"unselected":"pitchOn","optionItem"]),onClick:t[2]||(t[2]=e=>C.screenFn(3))},[h(" 招商策略 "),r(w,{src:C.iconColour(S.searchForm.strategy.length<=0),class:"Arrows"},null,8,["src"])],2)])),_:1}),r(k,{class:"total"},{default:l((()=>[h(" 共 "),f("span",{class:"totalSpan2"},u(S.total),1),h(" 家企业 ")])),_:1})])),_:1},8,["style"]),0!=S.enterpriseList.length?(d(),y(F,{key:0,class:"listBox",onScrolltolower:C.scrolltolowerFn,onScroll:e.scroll,style:m(`top: ${S.statusBarHeight}px`),"scroll-y":"true"},{default:l((()=>[r(k,null,{default:l((()=>[(d(!0),o(c,null,I(S.enterpriseList,(e=>(d(),y(k,{class:"card",key:e},{default:l((()=>[r(k,{class:"cadHeader"},{default:l((()=>[r(w,{src:null==(null==e?void 0:e.enterpriseIconLabelId)?S.listicon[0].icon:S.listicon[e.enterpriseIconLabelId].icon,class:"icon2"},null,8,["src"]),f("span",{onClick:t=>C.detailpage(e),class:"mgl-10"},u(e.enterpriseName),9,["onClick"])])),_:2},1024),r(k,{class:"contentBox mgt-24"},{default:l((()=>[f("p",{class:"contentSpan"},"线索模型："+u(e.clueTypeName),1),f("p",{class:"contentSpan"},"招商策略："+u(e.strategyTypeName),1),f("p",{class:"contentSpan"},"推荐时间："+u(e.recommendDate),1),f("p",{class:"contentSpan"},"企业地址："+u(e.enterpriseAddress),1),f("p",{class:"contentSpan"},"推送地区："+u(e.recommendRegionName),1)])),_:2},1024),r(k,{class:"detail",onClick:t=>C.goDetail(e)},{default:l((()=>[h(" 一企一策 "),r(w,{src:P,class:"righticon"})])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["onScrolltolower","onScroll","style"])):L("",!0),0==S.enterpriseList.length&&S.showNodata?(d(),o("div",{key:1,class:"nodatabox"},[r(w,{class:"nodata",src:"https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"}),f("span",{class:"span"},"暂无内容")])):L("",!0)])),_:1}),r(B,{ref:"clueModelPop",title:"请选择",safearea:!0,moreScreenList:S.clueModelList,onUpdatamoreScreen:C.affirm1},null,8,["moreScreenList","onUpdatamoreScreen"]),r(M,{ref:"popup2","background-color":"#fff"},{default:l((()=>[r(E,{ref:"mapList2",cityName:S.cityName,unrestricted:!0,onAffirm:C.affirm2},null,8,["cityName","onAffirm"])])),_:1},512),r(B,{ref:"inStrategyPop",title:"请选择",safearea:!0,moreScreenList:S.inStrategyList,onUpdatamoreScreen:C.affirm3},null,8,["moreScreenList","onUpdatamoreScreen"])],64)}],["__scopeId","data-v-4fc1ced5"]]);export{B as default};
