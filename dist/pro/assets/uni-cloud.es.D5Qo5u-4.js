import{al as e,am as t,W as n,av as s,an as o,Z as r,Y as i,p as a,af as c,aw as l,ax as u,a6 as d,a3 as h,s as g,l as f,R as p,ay as m,az as y,j as v,q as _,aA as T,u as w,m as S,aB as k,aC as I,aD as x,aE as P}from"./index-D8Q9TKMD.js";const A={pages:[{path:"pages/repository/capacity",style:{navigationBarTitleText:"首页",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/repository/property",style:{navigationBarTitleText:"产研",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/repository/capacitys",style:{navigationBarTitleText:"产发",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/information/index",style:{navigationBarTitleText:"消息",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/repository/information",style:{navigationBarTitleText:"数据产品",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/repository/informationDel",style:{navigationBarTitleText:"数据产品详情",navigationBarBackgroundColor:"#dde6ff",navigationStyle:"custom"}},{path:"pages/repository/attract",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/repository/enterpriseService",style:{navigationBarTitleText:"哒达企服",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/repository/PdfLoading",style:{navigationBarTitleText:"pdf预览",navigationBarBackgroundColor:"#FAFCFF"}},{path:"pages/newMapEnterprise/index2",style:{navigationBarTitleText:"360",navigationBarBackgroundColor:"#FAFCFF",navigationStyle:"custom"}},{path:"pages/newMapEnterprise/components/enterprise",style:{navigationBarTitleText:"企业详情",navigationBarBackgroundColor:"#FAFCFF",navigationStyle:"custom"}},{path:"pages/chat/index",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff"}},{path:"components/feedbackDel",style:{navigationBarTitleText:"反馈详情",navigationStyle:"custom"}},{path:"components/attractLiist/feedbackList",style:{navigationBarTitleText:"反馈列表",navigationStyle:"custom"}},{path:"pages/transition/index",style:{navigationBarTitleText:"欢迎页",navigationStyle:"custom"}},{path:"components/Goofficial",style:{navigationBarTitleText:"公众号",navigationStyle:"custom"}},{path:"components/memberarticle",style:{navigationBarTitleText:"会员中心",navigationStyle:"custom"}},{path:"pages/user/index",style:{navigationBarTitleText:"个人中心",navigationStyle:"custom"}},{path:"pages/repository/Coursedetails",style:{navigationBarTitleText:"课程详情",navigationStyle:"custom"}},{path:"components/attractDel",style:{navigationBarTitleText:"招商详情",navigationStyle:"custom"}},{path:"components/ApplicationDel",style:{navigationBarTitleText:"申请详情",navigationStyle:"custom"}},{path:"components/demandDel",style:{navigationBarTitleText:"需求详情",navigationStyle:"custom"}},{path:"components/WithdrawalRecord",style:{navigationBarTitleText:"提现记录",navigationStyle:"custom"}},{path:"components/commissionList",style:{navigationBarTitleText:"佣金明细",navigationStyle:"custom"}},{path:"components/withdraw",style:{navigationBarTitleText:"提现",navigationStyle:"custom"}},{path:"pages/history/index",style:{navigationBarTitleText:"历史记录",navigationBarBackgroundColor:"#dde6ff"}},{path:"pages/pdfShow/index",style:{navigationBarTitleText:"预览",navigationBarBackgroundColor:"#dde6ff"}},{path:"pages/user/userSet",style:{navigationBarTitleText:"设置",navigationStyle:"custom"}},{path:"pages/user/changename",style:{navigationBarTitleText:"编辑个人信息",navigationStyle:"custom"}},{path:"pages/user/perfectionInfo",style:{navigationBarTitleText:"信息完善",navigationStyle:"custom"}},{path:"pages/user/perfectionInfo2",style:{navigationBarTitleText:"信息完善",navigationStyle:"custom"}},{path:"pages/repository/BillingRule",style:{navigationBarTitleText:"计费规则",navigationStyle:"custom"}},{path:"pages/user/MyQRCode",style:{navigationBarTitleText:"我的二维码",navigationStyle:"custom"}},{path:"pages/user/MemberCenter",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#FFE6C0"}},{path:"pages/user/payment",style:{navigationBarTitleText:"会员中心支付",navigationBarBackgroundColor:"#f8e0be"}},{path:"pages/user/AboutAi",style:{navigationBarTitleText:"关于小AIR",navigationStyle:"custom"}},{path:"pages/login/index",style:{navigationBarTitleText:"登录",navigationBarBackgroundColor:"#FAFCFF"}},{path:"pages/login/components/passwordLogin",style:{navigationBarTitleText:"登录",navigationBarBackgroundColor:"#FAFCFF"}},{path:"pages/login/components/verificationcodeLogin",style:{navigationBarTitleText:"登录",navigationBarBackgroundColor:"#FAFCFF"}},{path:"pages/login/components/scanCodeLogin",style:{navigationBarTitleText:"登录",navigationStyle:"custom"}},{path:"pages/login/components/agreement",style:{navigationBarTitleText:"用户协议"}},{path:"pages/user/association",style:{navigationBarTitleText:"加入社群",navigationStyle:"custom"}},{path:"pages/excelShow/index",style:{navigationBarTitleText:"表格下载",navigationStyle:"custom"}},{path:"pages/excelView/index",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff"}},{path:"pages/user/team",style:{navigationBarTitleText:"团队管理",navigationBarBackgroundColor:"#dde6ff"}},{path:"pages/webView/identity",style:{navigationBarTitleText:"身份切换",navigationBarBackgroundColor:"#dde6ff"}},{path:"pages/webView/InformationDetails",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#FFFFFF"}},{path:"pages/search/index",style:{navigationBarTitleText:"搜索",navigationStyle:"custom"}},{path:"pages/strategy/index",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/deriveExcel/index",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"pages/user/IdentityStatement",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}}],subPackages:[{root:"attractionManage/",pages:[{path:"index",style:{navigationBarTitleText:"招商管理",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/addCompany",style:{navigationBarTitleText:"添加企业",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/targetDetail",style:{navigationBarTitleText:"企业详情",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/addWeekly",style:{navigationBarTitleText:"周报详情",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}}]},{root:"goToSea/",pages:[{path:"index",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"historyStrategy",style:{navigationBarTitleText:"历史报告",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/enterpriseDel",style:{navigationBarTitleText:"企业详情",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/gardenDel",style:{navigationBarTitleText:"园区详情",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/businessDirectory",style:{navigationBarTitleText:"企业园区名录",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"strategyPdf",style:{navigationBarTitleText:"生成报告",navigationBarBackgroundColor:"#dde6ff"}},{path:"askIndex",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff"}},{path:"serviceInfo",style:{navigationBarTitleText:"哒达招商",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"mineOrder",style:{navigationBarTitleText:"哒达出海",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"serviceList",style:{navigationBarTitleText:"服务方",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/orderDetail",style:{navigationBarTitleText:"订单详情",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/servicerDetail",style:{navigationBarTitleText:"服务商详情",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"elementPage/addService",style:{navigationBarTitleText:"新增服务",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}},{path:"agreement/index",style:{navigationBarTitleText:"新增服务",navigationBarBackgroundColor:"#dde6ff",disableScroll:!0,navigationStyle:"custom"}}]}],tabBar:{color:"#3F4A59",selectedColor:"#417FFF",backgroundColor:"#FFFFFF",list:[{pagePath:"pages/repository/capacity",text:"首页",iconPath:"static/navigation/home.png",selectedIconPath:"static/navigation/selected-home.png"},{pagePath:"pages/information/index",text:"消息",iconPath:"static/navigation/news.png",selectedIconPath:"static/navigation/selected-news.png"},{pagePath:"pages/user/index",text:"我的",iconPath:"static/navigation/my.png",selectedIconPath:"static/navigation/selected-my.png"}]},globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"}};function b(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var C=b((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},o=s.lib={},r=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},i=o.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,s=this.sigBytes,o=e.sigBytes;if(this.clamp(),s%4)for(var r=0;r<o;r++){var i=n[r>>>2]>>>24-r%4*8&255;t[s+r>>>2]|=i<<24-(s+r)%4*8}else for(r=0;r<o;r+=4)t[s+r>>>2]=n[r>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,s=[],o=function(t){var n=987654321,s=4294967295;return function(){var o=((n=36969*(65535&n)+(n>>16)&s)<<16)+(t=18e3*(65535&t)+(t>>16)&s)&s;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},r=0;r<t;r+=4){var a=o(4294967296*(n||e.random()));n=987654071*a(),s.push(4294967296*a()|0)}return new i.init(s,t)}}),a=s.enc={},c=a.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s+=2)n[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new i.init(n,t/2)}},l=a.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],o=0;o<n;o++){var r=t[o>>>2]>>>24-o%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s++)n[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new i.init(n,t)}},u=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},d=o.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new i.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,s=n.words,o=n.sigBytes,r=this.blockSize,a=o/(4*r),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*r,l=e.min(4*c,o);if(c){for(var u=0;u<c;u+=r)this._doProcessBlock(s,u);var d=s.splice(0,c);n.sigBytes-=l}return new i.init(d,l)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=d.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=s.algo={};return s}(Math),n)})),O=C,B=(b((function(e,t){var n;e.exports=(n=O,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,i=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=i.MD5=r.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var s=t+n,o=e[s];e[s]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var r=this._hash.words,i=e[t+0],c=e[t+1],g=e[t+2],f=e[t+3],p=e[t+4],m=e[t+5],y=e[t+6],v=e[t+7],_=e[t+8],T=e[t+9],w=e[t+10],S=e[t+11],k=e[t+12],I=e[t+13],x=e[t+14],P=e[t+15],A=r[0],b=r[1],C=r[2],O=r[3];A=l(A,b,C,O,i,7,a[0]),O=l(O,A,b,C,c,12,a[1]),C=l(C,O,A,b,g,17,a[2]),b=l(b,C,O,A,f,22,a[3]),A=l(A,b,C,O,p,7,a[4]),O=l(O,A,b,C,m,12,a[5]),C=l(C,O,A,b,y,17,a[6]),b=l(b,C,O,A,v,22,a[7]),A=l(A,b,C,O,_,7,a[8]),O=l(O,A,b,C,T,12,a[9]),C=l(C,O,A,b,w,17,a[10]),b=l(b,C,O,A,S,22,a[11]),A=l(A,b,C,O,k,7,a[12]),O=l(O,A,b,C,I,12,a[13]),C=l(C,O,A,b,x,17,a[14]),A=u(A,b=l(b,C,O,A,P,22,a[15]),C,O,c,5,a[16]),O=u(O,A,b,C,y,9,a[17]),C=u(C,O,A,b,S,14,a[18]),b=u(b,C,O,A,i,20,a[19]),A=u(A,b,C,O,m,5,a[20]),O=u(O,A,b,C,w,9,a[21]),C=u(C,O,A,b,P,14,a[22]),b=u(b,C,O,A,p,20,a[23]),A=u(A,b,C,O,T,5,a[24]),O=u(O,A,b,C,x,9,a[25]),C=u(C,O,A,b,f,14,a[26]),b=u(b,C,O,A,_,20,a[27]),A=u(A,b,C,O,I,5,a[28]),O=u(O,A,b,C,g,9,a[29]),C=u(C,O,A,b,v,14,a[30]),A=d(A,b=u(b,C,O,A,k,20,a[31]),C,O,m,4,a[32]),O=d(O,A,b,C,_,11,a[33]),C=d(C,O,A,b,S,16,a[34]),b=d(b,C,O,A,x,23,a[35]),A=d(A,b,C,O,c,4,a[36]),O=d(O,A,b,C,p,11,a[37]),C=d(C,O,A,b,v,16,a[38]),b=d(b,C,O,A,w,23,a[39]),A=d(A,b,C,O,I,4,a[40]),O=d(O,A,b,C,i,11,a[41]),C=d(C,O,A,b,f,16,a[42]),b=d(b,C,O,A,y,23,a[43]),A=d(A,b,C,O,T,4,a[44]),O=d(O,A,b,C,k,11,a[45]),C=d(C,O,A,b,P,16,a[46]),A=h(A,b=d(b,C,O,A,g,23,a[47]),C,O,i,6,a[48]),O=h(O,A,b,C,v,10,a[49]),C=h(C,O,A,b,x,15,a[50]),b=h(b,C,O,A,m,21,a[51]),A=h(A,b,C,O,k,6,a[52]),O=h(O,A,b,C,f,10,a[53]),C=h(C,O,A,b,w,15,a[54]),b=h(b,C,O,A,c,21,a[55]),A=h(A,b,C,O,_,6,a[56]),O=h(O,A,b,C,P,10,a[57]),C=h(C,O,A,b,y,15,a[58]),b=h(b,C,O,A,I,21,a[59]),A=h(A,b,C,O,p,6,a[60]),O=h(O,A,b,C,S,10,a[61]),C=h(C,O,A,b,g,15,a[62]),b=h(b,C,O,A,T,21,a[63]),r[0]=r[0]+A|0,r[1]=r[1]+b|0,r[2]=r[2]+C|0,r[3]=r[3]+O|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var r=e.floor(s/4294967296),i=s;n[15+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),n[14+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var u=c[l];c[l]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,n,s,o,r,i){var a=e+(t&n|~t&s)+o+i;return(a<<r|a>>>32-r)+t}function u(e,t,n,s,o,r,i){var a=e+(t&s|n&~s)+o+i;return(a<<r|a>>>32-r)+t}function d(e,t,n,s,o,r,i){var a=e+(t^n^s)+o+i;return(a<<r|a>>>32-r)+t}function h(e,t,n,s,o,r,i){var a=e+(n^(t|~s))+o+i;return(a<<r|a>>>32-r)+t}t.MD5=r._createHelper(c),t.HmacMD5=r._createHmacHelper(c)}(Math),n.MD5)})),b((function(e,t){var n,s,o;e.exports=(s=(n=O).lib.Base,o=n.enc.Utf8,void(n.algo.HMAC=s.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var n=e.blockSize,s=4*n;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var r=this._oKey=t.clone(),i=this._iKey=t.clone(),a=r.words,c=i.words,l=0;l<n;l++)a[l]^=1549556828,c[l]^=909522486;r.sigBytes=i.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),b((function(e,t){e.exports=O.HmacMD5}))),L=b((function(e,t){e.exports=O.enc.Utf8})),E=b((function(e,t){var n,s,o;e.exports=(o=(s=n=O).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,s=this._map;e.clamp();for(var o=[],r=0;r<n;r+=3)for(var i=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,a=0;a<4&&r+.75*a<n;a++)o.push(s.charAt(i>>>6*(3-a)&63));var c=s.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var r=0;r<n.length;r++)s[n.charCodeAt(r)]=r}var i=n.charAt(64);if(i){var a=e.indexOf(i);-1!==a&&(t=a)}return function(e,t,n){for(var s=[],r=0,i=0;i<t;i++)if(i%4){var a=n[e.charCodeAt(i-1)]<<i%4*2,c=n[e.charCodeAt(i)]>>>6-i%4*2;s[r>>>2]|=(a|c)<<24-r%4*8,r++}return o.create(s,r)}(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const R="FUNCTION",U="pending",F="rejected";function D(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function N(e){return"object"===D(e)}function M(e){return"function"==typeof e}function q(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const j="REJECTED",$="NOT_PENDING";class K{constructor({createPromise:e,retryRule:t=j}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case j:return this.status===F;case $:return this.status!==U}}exec(){return this.needRetry?(this.status=U,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=F,Promise.reject(e)))),this.promise):this.promise}}function H(e){return e&&"string"==typeof e?JSON.parse(e):e}const J=H([]);H("");const W=H("[]")||[];let V="";try{V="__UNI__8725B78"}catch(Ue){}let z={};function Q(e,t={}){var n,s;return n=z,s=e,Object.prototype.hasOwnProperty.call(n,s)||(z[e]=t),z[e]}const Y=["invoke","success","fail","complete"],G=Q("_globalUniCloudInterceptor");function X(e,t){G[e]||(G[e]={}),N(t)&&Object.keys(t).forEach((n=>{Y.indexOf(n)>-1&&function(e,t,n){let s=G[e][t];s||(s=G[e][t]=[]),-1===s.indexOf(n)&&M(n)&&s.push(n)}(e,n,t[n])}))}function Z(e,t){G[e]||(G[e]={}),N(t)?Object.keys(t).forEach((n=>{Y.indexOf(n)>-1&&function(e,t,n){const s=G[e][t];if(!s)return;const o=s.indexOf(n);o>-1&&s.splice(o,1)}(e,n,t[n])})):delete G[e]}function ee(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function te(e,t){return G[e]&&G[e][t]||[]}function ne(e){X("callObject",e)}const se=Q("_globalUniCloudListener"),oe="response",re="needLogin",ie="refreshToken",ae="clientdb",ce="cloudfunction",le="cloudobject";function ue(e){return se[e]||(se[e]=[]),se[e]}function de(e,t){const n=ue(e);n.includes(t)||n.push(t)}function he(e,t){const n=ue(e),s=n.indexOf(t);-1!==s&&n.splice(s,1)}function ge(e,t){const n=ue(e);for(let s=0;s<n.length;s++)(0,n[s])(t)}let fe,pe=!1;function me(){return fe||(fe=new Promise((e=>{pe&&e(),function t(){if("function"==typeof n){const t=n();t&&t[0]&&(pe=!0,e())}pe||setTimeout((()=>{t()}),30)}()})),fe)}function ye(e){const t={};for(const n in e){const s=e[n];M(s)&&(t[n]=q(s))}return t}class ve extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var _e={request:e=>d(e),uploadFile:e=>h(e),setStorageSync:(e,t)=>g(e,t),getStorageSync:e=>f(e),removeStorageSync:e=>p(e),clearStorageSync:()=>m(),connectSocket:e=>y(e)};function Te(e){return e&&Te(e.__v_raw)||e}function we(){return{token:_e.getStorageSync("uni_id_token")||_e.getStorageSync("uniIdToken"),tokenExpired:_e.getStorageSync("uni_id_token_expired")}}function Se({token:e,tokenExpired:t}={}){e&&_e.setStorageSync("uni_id_token",e),t&&_e.setStorageSync("uni_id_token_expired",t)}let ke,Ie;function xe(){return ke||(ke=v()),ke}function Pe(){let e,t;try{if(P){if(P.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:s}=P();e=s,t=n}}catch(n){}return{channel:e,scene:t}}let Ae={};function be(){const e=x&&x()||"en";if(Ie)return{...Ae,...Ie,locale:e,LOCALE:e};const t=xe(),{deviceId:n,osName:s,uniPlatform:o,appId:r}=t,i=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const a in t)Object.hasOwnProperty.call(t,a)&&-1===i.indexOf(a)&&delete t[a];return Ie={PLATFORM:o,OS:s,APPID:r,DEVICEID:n,...Pe(),...t},{...Ae,...Ie,locale:e,LOCALE:e}}var Ce=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),B(n,t).toString()},Oe=function(e,t){return new Promise(((n,s)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return s(new ve({code:n,message:o,requestId:t}))}const o=e.data;if(o.error)return s(new ve({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},Be=function(e){return E.stringify(L.parse(e))},Le={init(e){const t=new class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=_e,this._getAccessTokenPromiseHub=new K({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new ve({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:$})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return Oe(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=Ce(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),s={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,s["x-basement-token"]=this.accessToken),s["x-serverless-sign"]=Ce(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:s}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:s,fileType:o,onUploadProgress:r}){return new Promise(((i,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:s,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?i(e):a(new ve({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new ve({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:s=!1,onUploadProgress:o,config:r}){if("string"!==D(t))throw new ve({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ve({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ve({code:"INVALID_PARAM",message:"cloudPath不合法"});const i=r&&r.envType||this.config.envType;if(s&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new ve({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:i,filename:s?t.split("/").pop():t,fileId:s?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:l,accessKeyId:u,signature:d,host:h,ossPath:g,id:f,policy:p,ossCallbackUrl:m}=a,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:d,host:h,id:f,key:g,policy:p,success_action_status:200};if(l&&(y["x-oss-security-token"]=l),m){const e=JSON.stringify({callbackUrl:m,callbackBody:JSON.stringify({fileId:f,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=Be(e)}const v={url:"https://"+a.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},v,{onUploadProgress:o})),m)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:f})).success)return{success:!0,filePath:e,fileID:c};throw new ve({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new ve({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new ve({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}}(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const Ee="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Re,Ue;(Ue=Re||(Re={})).local="local",Ue.none="none",Ue.session="session";var Fe=function(){},De=b((function(e,t){var n;e.exports=(n=O,function(e){var t=n,s=t.lib,o=s.WordArray,r=s.Hasher,i=t.algo,a=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),s=2;s<=n;s++)if(!(t%s))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var s=2,o=0;o<64;)t(s)&&(o<8&&(a[o]=n(e.pow(s,.5))),c[o]=n(e.pow(s,1/3)),o++),s++}();var l=[],u=i.SHA256=r.extend({_doReset:function(){this._hash=new o.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,s=n[0],o=n[1],r=n[2],i=n[3],a=n[4],u=n[5],d=n[6],h=n[7],g=0;g<64;g++){if(g<16)l[g]=0|e[t+g];else{var f=l[g-15],p=(f<<25|f>>>7)^(f<<14|f>>>18)^f>>>3,m=l[g-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[g]=p+l[g-7]+y+l[g-16]}var v=s&o^s&r^o&r,_=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),T=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&d)+c[g]+l[g];h=d,d=u,u=a,a=i+T|0,i=r,r=o,o=s,s=T+(_+v)|0}n[0]=n[0]+s|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+i|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(s/4294967296),n[15+(o+64>>>9<<4)]=s,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(u),t.HmacSHA256=r._createHmacHelper(u)}(Math),n.SHA256)})),Ne=De,Me=b((function(e,t){e.exports=O.HmacSHA256}));const qe=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new ve({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,s)=>e?n(e):t(s)}));return e.promise=t,e};function je(e){return void 0===e}function $e(e){return"[object Null]"===Object.prototype.toString.call(e)}var Ke;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Ke||(Ke={}));const He={adapter:null,runtime:void 0},Je=["anonymousUuidKey"];class We extends Fe{constructor(){super(),He.adapter.root.tcbObject||(He.adapter.root.tcbObject={})}setItem(e,t){He.adapter.root.tcbObject[e]=t}getItem(e){return He.adapter.root.tcbObject[e]}removeItem(e){delete He.adapter.root.tcbObject[e]}clear(){delete He.adapter.root.tcbObject}}function Ve(e,t){switch(e){case"local":return t.localStorage||new We;case"none":return new We;default:return t.sessionStorage||new We}}class ze{constructor(e){if(!this._storage){this._persistence=He.adapter.primaryStorage||e.persistence,this._storage=Ve(this._persistence,He.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,s=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,r=`login_type_${e.env}`,i=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s,anonymousUuidKey:o,loginTypeKey:r,userInfoKey:i}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=Ve(e,He.adapter);for(const s in this.keys){const e=this.keys[s];if(t&&Je.includes(s))continue;const o=this._storage.getItem(e);je(o)||$e(o)||(n.setItem(e,o),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const s={version:n||"localCachev1",content:t},o=JSON.stringify(s);try{this._storage.setItem(e,o)}catch(r){throw r}}getStore(e,t){try{if(!this._storage)return}catch(s){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const Qe={},Ye={};function Ge(e){return Qe[e]}class Xe{constructor(e,t){this.data=t||null,this.name=e}}class Ze extends Xe{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const et=new class{constructor(){this._listeners={}}on(e,t){return n=e,s=t,(o=this._listeners)[n]=o[n]||[],o[n].push(s),this;var n,s,o}off(e,t){return function(e,t,n){if(n&&n[e]){const s=n[e].indexOf(t);-1!==s&&n[e].splice(s,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Ze)return console.error(e.error),this;const n="string"==typeof e?new Xe(e,t||{}):e,s=n.name;if(this._listens(s)){n.target=this;const e=this._listeners[s]?[...this._listeners[s]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function tt(e,t){et.on(e,t)}function nt(e,t={}){et.fire(e,t)}function st(e,t){et.off(e,t)}const ot="loginStateChanged",rt="loginStateExpire",it="loginTypeChanged",at="anonymousConverted",ct="refreshAccessToken";var lt;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(lt||(lt={}));const ut=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],dt={"X-SDK-Version":"1.3.5"};function ht(e,t,n){const s=e[t];e[t]=function(t){const o={},r={};n.forEach((n=>{const{data:s,headers:i}=n.call(e,t);Object.assign(o,s),Object.assign(r,i)}));const i=t.data;return i&&(()=>{var e;if(e=i,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...i,...o};else for(const t in o)i.append(t,o[t])})(),t.headers={...t.headers||{},...r},s.call(e,t)}}function gt(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...dt,"x-seqid":e}}}class ft{constructor(e={}){var t;this.config=e,this._reqClass=new He.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Ge(this.config.env),this._localCache=(t=this.config.env,Ye[t]),ht(this._reqClass,"post",[gt]),ht(this._reqClass,"upload",[gt]),ht(this._reqClass,"download",[gt])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:s,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let r=this._cache.getStore(n);if(!r)throw new ve({message:"未登录CloudBase"});const i={refresh_token:r},a=await this.request("auth.fetchAccessTokenWithRefreshToken",i);if(a.data.code){const{code:e}=a.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(s)===lt.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(n),s=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(s.refresh_token),this._refreshAccessToken()}nt(rt),this._cache.removeStore(n)}throw new ve({code:a.data.code,message:`刷新access token失败：${a.data.code}`})}if(a.data.access_token)return nt(ct),this._cache.setStore(e,a.data.access_token),this._cache.setStore(t,a.data.access_token_expire+Date.now()),{accessToken:a.data.access_token,accessTokenExpire:a.data.access_token_expire};a.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,a.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new ve({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(e),o=this._cache.getStore(t),r=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(s,o))&&(r=!1),(!s||!o||o<Date.now())&&r?this.refreshAccessToken():{accessToken:s,accessTokenExpire:o}}async request(e,t,n){const s=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const r={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};if(-1===ut.indexOf(e)){const{refreshTokenKey:e}=this._cache.keys;this._cache.getStore(e)&&(r.access_token=(await this.getAccessToken()).accessToken)}let i;if("storage.uploadFile"===e){i=new FormData;for(let e in i)i.hasOwnProperty(e)&&void 0!==i[e]&&i.append(e,r[e]);o="multipart/form-data"}else{o="application/json",i={};for(let e in r)void 0!==r[e]&&(i[e]=r[e])}let a={headers:{"content-type":o}};n&&n.timeout&&(a.timeout=n.timeout),n&&n.onUploadProgress&&(a.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(s);c&&(a.headers["X-TCB-Trace"]=c);const{parse:l,inQuery:u,search:d}=t;let h={env:this.config.env};l&&(h.parse=!0),u&&(h={...u,...h});let g=function(e,t,n={}){const s=/\?/.test(t);let o="";for(let r in n)""===o?!s&&(t+="?"):o+="&",o+=`${r}=${encodeURIComponent(n[r])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(Ee,"//tcb-api.tencentcloudapi.com/web",h);d&&(g+=d);const f=await this.post({url:g,data:i,...a}),p=f.header&&f.header["x-tcb-trace"];if(p&&this._localCache.setStore(s,p),200!==Number(f.status)&&200!==Number(f.statusCode)||!f.data)throw new ve({code:"NETWORK_ERROR",message:"network request error"});return f}async send(e,t={},n={}){const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if("ACCESS_TOKEN_EXPIRED"===s.data.code&&-1===ut.indexOf(e)){await this.refreshAccessToken();const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(s.data.code)throw new ve({code:s.data.code,message:s.data.message});return s.data}if(s.data.code)throw new ve({code:s.data.code,message:s.data.message});return s.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}}const pt={};function mt(e){return pt[e]}class yt{constructor(e){this.config=e,this._cache=Ge(e.env),this._request=mt(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(s,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class vt{constructor(e){if(!e)throw new ve({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=Ge(this._envId),this._request=mt(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new ve({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new ve({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:i}=e,{data:a}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:s,province:o,country:r,city:i});this.setLocalUserInfo(a)}async refresh(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class _t{constructor(e){if(!e)throw new ve({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Ge(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys,o=this._cache.getStore(t),r=this._cache.getStore(n),i=this._cache.getStore(s);this.credential={refreshToken:o,accessToken:r,accessTokenExpire:i},this.user=new vt(e)}get isAnonymousAuth(){return this.loginType===lt.ANONYMOUS}get isCustomAuth(){return this.loginType===lt.CUSTOM}get isWeixinAuth(){return this.loginType===lt.WECHAT||this.loginType===lt.WECHAT_OPEN||this.loginType===lt.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class Tt extends yt{async signIn(){this._cache.updatePersistence("local");const{anonymousUuidKey:e,refreshTokenKey:t}=this._cache.keys,n=this._cache.getStore(e)||void 0,s=this._cache.getStore(t)||void 0,o=await this._request.send("auth.signInAnonymously",{anonymous_uuid:n,refresh_token:s});if(o.uuid&&o.refresh_token){this._setAnonymousUUID(o.uuid),this.setRefreshToken(o.refresh_token),await this._request.refreshAccessToken(),nt(ot),nt(it,{env:this.config.env,loginType:lt.ANONYMOUS,persistence:"local"});const e=new _t(this.config.env);return await e.user.refresh(),e}throw new ve({message:"匿名登录失败"})}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,s=this._cache.getStore(t),o=this._cache.getStore(n),r=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:o,ticket:e});if(r.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(r.refresh_token),await this._request.refreshAccessToken(),nt(at,{env:this.config.env}),nt(it,{loginType:lt.CUSTOM,persistence:"local"}),{credential:{refreshToken:r.refresh_token}};throw new ve({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,lt.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class wt extends yt{async signIn(e){if("string"!=typeof e)throw new ve({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),nt(ot),nt(it,{env:this.config.env,loginType:lt.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new _t(this.config.env);throw new ve({message:"自定义登录失败"})}}class St extends yt{async signIn(e,t){if("string"!=typeof e)throw new ve({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token:r,access_token_expire:i}=s;if(o)return this.setRefreshToken(o),r&&i?this.setAccessToken(r,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),nt(ot),nt(it,{env:this.config.env,loginType:lt.EMAIL,persistence:this.config.persistence}),new _t(this.config.env);throw s.code?new ve({code:s.code,message:`邮箱登录失败: ${s.message}`}):new ve({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class kt extends yt{async signIn(e,t){if("string"!=typeof e)throw new ve({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:lt.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token_expire:r,access_token:i}=s;if(o)return this.setRefreshToken(o),i&&r?this.setAccessToken(i,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),nt(ot),nt(it,{env:this.config.env,loginType:lt.USERNAME,persistence:this.config.persistence}),new _t(this.config.env);throw s.code?new ve({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new ve({message:"用户名密码登录失败"})}}class It{constructor(e){this.config=e,this._cache=Ge(e.env),this._request=mt(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),tt(it,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new Tt(this.config)}customAuthProvider(){return new wt(this.config)}emailAuthProvider(){return new St(this.config)}usernameAuthProvider(){return new kt(this.config)}async signInAnonymously(){return new Tt(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new St(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new kt(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new Tt(this.config)),tt(at,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===lt.ANONYMOUS)throw new ve({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,s=this._cache.getStore(e);if(!s)return;const o=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),nt(ot),nt(it,{env:this.config.env,loginType:lt.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){tt(ot,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){tt(rt,e.bind(this))}onAccessTokenRefreshed(e){tt(ct,e.bind(this))}onAnonymousConverted(e){tt(at,e.bind(this))}onLoginTypeChanged(e){tt(it,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{refreshTokenKey:e}=this._cache.keys;return this._cache.getStore(e)?new _t(this.config.env):null}async isUsernameRegistered(e){if("string"!=typeof e)throw new ve({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new wt(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:s}=e.data;s===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const xt=function(e,t){t=t||qe();const n=mt(this.config.env),{cloudPath:s,filePath:o,onUploadProgress:r,fileType:i="image"}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{const{data:{url:a,authorization:c,token:l,fileId:u,cosFileId:d},requestId:h}=e,g={key:s,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:a,data:g,file:o,name:s,fileType:i,onUploadProgress:r}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:h}):t(new ve({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},Pt=function(e,t){t=t||qe();const n=mt(this.config.env),{cloudPath:s}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},At=function({fileList:e},t){if(t=t||qe(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let s of e)if(!s||"string"!=typeof s)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return mt(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},bt=function({fileList:e},t){t=t||qe(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?n.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const s={file_list:n};return mt(this.config.env).send("storage.batchGetDownloadUrl",s).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Ct=async function({fileID:e},t){const n=(await bt.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const s=mt(this.config.env);let o=n.download_url;if(o=encodeURI(o),!t)return s.download({url:o});t(await s.download({url:o}))},Ot=function({name:e,data:t,query:n,parse:s,search:o,timeout:r},i){const a=i||qe();let c;try{c=t?JSON.stringify(t):""}catch(u){return Promise.reject(u)}if(!e)return Promise.reject(new ve({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:s,search:o,function_name:e,request_data:c};return mt(this.config.env).send("functions.invokeFunction",l,{timeout:r}).then((e=>{if(e.code)a(null,e);else{let n=e.data.response_data;if(s)a(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),a(null,{result:n,requestId:e.requestId})}catch(t){a(new ve({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},Bt={timeout:15e3,persistence:"session"},Lt={};class Et{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(He.adapter||(this.requestClient=new He.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Bt,...e},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Et(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||He.adapter.primaryStorage||Bt.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Qe[t]=new ze(e),Ye[t]=new ze({...e,persistence:"local"})}(this.config),n=this.config,pt[n.env]=new ft(n),this.authObj=new It(this.config),this.authObj}on(e,t){return tt.apply(this,[e,t])}off(e,t){return st.apply(this,[e,t])}callFunction(e,t){return Ot.apply(this,[e,t])}deleteFile(e,t){return At.apply(this,[e,t])}getTempFileURL(e,t){return bt.apply(this,[e,t])}downloadFile(e,t){return Ct.apply(this,[e,t])}uploadFile(e,t){return xt.apply(this,[e,t])}getUploadMetadata(e,t){return Pt.apply(this,[e,t])}registerExtension(e){Lt[e.name]=e}async invokeExtension(e,t){const n=Lt[e];if(!n)throw new ve({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const s of t){const{isMatch:e,genAdapter:t,runtime:n}=s;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&(He.adapter=t),n&&(He.runtime=n)}}var Rt=new Et;function Ut(e,t,n){void 0===n&&(n={});var s=/\?/.test(t),o="";for(var r in n)""===o?!s&&(t+="?"):o+="&",o+=r+"="+encodeURIComponent(n[r]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class Ft{post(e){const{url:t,data:n,headers:s,timeout:o}=e;return new Promise(((e,r)=>{_e.request({url:Ut("https:",t),data:n,method:"POST",header:s,timeout:o,success(t){e(t)},fail(e){r(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:s,file:o,data:r,headers:i,fileType:a}=e,c=_e.uploadFile({url:Ut("https:",s),name:"file",formData:Object.assign({},r),filePath:o,fileType:a,header:i,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&r.success_action_status&&(n.statusCode=parseInt(r.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const Dt={setItem(e,t){_e.setStorageSync(e,t)},getItem:e=>_e.getStorageSync(e),removeItem(e){_e.removeStorageSync(e)},clear(){_e.clearStorageSync()}};var Nt={genAdapter:function(){return{root:{},reqClass:Ft,localStorage:Dt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Rt.useAdapters(Nt);const Mt=Rt,qt=Mt.init;Mt.init=function(e){e.env=e.spaceId;const t=qt.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:s,complete:o}=ye(e);if(!(t||s||o))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{s&&s(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var jt=Mt;async function $t(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(s={url:n,timeout:500},new Promise(((e,t)=>{_e.request({...s,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(o){return!1}var s}const Kt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var Ht={init(e){const t=new class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=_e}async request(e,t=!0){return e=this.setupRequest(e),Promise.resolve().then((()=>Oe(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",s=e.data&&e.data.message||"request:fail";return n(new ve({code:t,message:s}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=Ce(t,this.config.clientSecret);const s=be();n["x-client-info"]=encodeURIComponent(JSON.stringify(s));const{token:o}=we();return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=be(),{token:n}=we(),s=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:o,servePort:r}=this.__dev__&&this.__dev__.debugInfo||{},{address:i}=await async function(e,t){let n;for(let s=0;s<e.length;s++){const o=e[s];if(await $t(o,t)){n=o;break}}return{address:n,port:t}}(o,r);return{url:`http://${i}:${r}/${Kt[e.method]}`,method:"POST",data:s,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:s}){if(!t)throw new ve({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:r,formData:i,name:a}=t.result;return o=t.result.fileUrl,new Promise(((t,o)=>{const c=this.adapter.uploadFile({url:r,formData:i,name:a,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):o(new ve({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new ve({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,s)=>{t.success?n({success:!0,filePath:e,fileID:o}):s(new ve({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new ve({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new ve({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new ve({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Jt=b((function(e,t){e.exports=O.enc.Hex}));function Wt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Vt(e="",t={}){const{data:n,functionName:s,method:o,headers:r,signHeaderKeys:i=[],config:a}=t,c=Date.now(),l=Wt(),u=Object.assign({},r,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":s,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":l,"x-alipay-callid":l,"x-trace-id":l}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(i),[h="",g=""]=e.split("?")||[],f=function(e){const t=e.signedHeaders.join(";"),n=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),s=Ne(e.body).toString(Jt),o=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${n}\n${t}\n${s}\n`,r=Ne(o).toString(Jt),i=`HMAC-SHA256\n${e.timestamp}\n${r}\n`,a=Me(i,e.secretKey).toString(Jt);return`HMAC-SHA256 Credential=${e.secretId}, SignedHeaders=${t}, Signature=${a}`}({path:h,query:g,method:o,headers:u,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:d.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},u,{Authorization:f})}}function zt({url:e,data:t,method:n="POST",headers:s={},timeout:o}){return new Promise(((r,i)=>{_e.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:s,dataType:"json",timeout:o,complete:(e={})=>{const t=s["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:s,trace_id:o}=e.data||{};return i(new ve({code:"SYS_ERR",message:n||s||"request:fail",requestId:o||t}))}r({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Qt(e,t){const{path:n,data:s,method:o="GET"}=e,{url:r,headers:i}=Vt(n,{functionName:"",data:s,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return zt({url:r,data:s,method:o,headers:i}).then((e=>{const t=e.data||{};if(!t.success)throw new ve({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new ve({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Yt(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new ve({code:"INVALID_PARAM",message:"fileID不合法"});const s=t.substring(0,n),o=t.substring(n+1);return s!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function Gt(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class Xt{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Wt(),timestamp:""+Date.now()}),r=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),`host:${s}`].join("\n"),i=["HMAC-SHA256",Ne(r).toString(Jt)].join("\n"),a=Me(i,this.config.secretKey).toString(Jt),c=Object.keys(o).map((e=>`${e}=${encodeURIComponent(o[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${a}`}}var Zt={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Xt(this.config)}callFunction(e){return function(e,t){const{name:n,data:s,async:o=!1,timeout:r}=e,i="POST",a={"x-to-function-name":n};o&&(a["x-function-invoke-type"]="async");const{url:c,headers:l}=Vt("/functions/invokeFunction",{functionName:n,data:s,method:i,headers:a,signHeaderKeys:["x-to-function-name"],config:t});return zt({url:c,data:s,method:i,headers:l,timeout:r}).then((e=>{let t=0;if(o){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new ve({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new ve({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:s,onUploadProgress:o}){return new Promise(((r,i)=>{const a=_e.uploadFile({url:e,filePath:t,fileType:n,formData:s,name:"file",success(e){e&&e.statusCode<400?r(e):i(new ve({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){i(new ve({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:s}){if("string"!==D(t))throw new ve({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ve({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ve({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Qt({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:r,upload_url:i,form_data:a}=o,c=a&&a.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:i,filePath:e,fileType:n,formData:c,onUploadProgress:s}).then((()=>({fileID:r})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&n(new ve({errCode:"INVALID_PARAM",errMsg:"fileList不能为空数组"})),e.length>50&&n(new ve({errCode:"INVALID_PARAM",errMsg:"fileList数组长度不能超过50"}));const s=[];for(const o of e){"string"!==D(o)&&n(new ve({errCode:"INVALID_PARAM",errMsg:"fileList的元素必须是非空的字符串"}));const e=Yt.call(this,o);s.push({file_id:e,expire:600})}Qt({path:"/?download_url",data:{file_list:s},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Gt.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return _e.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function en({data:e}){let t;t=be();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=we();e&&(n.uniIdToken=e)}return n}const tn=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var nn=/[\\^$.*+?()[\]{}|]/g,sn=RegExp(nn.source);function on(e,t,n){return e.replace(new RegExp((s=t)&&sn.test(s)?s.replace(nn,"\\$&"):s,"g"),n);var s}const rn=2e4,an={code:20101,message:"Invalid client"};function cn(e){const{errSubject:t,subject:n,errCode:s,errMsg:o,code:r,message:i,cause:a}=e||{};return new ve({subject:t||n||"uni-secure-network",code:s||r||rn,message:o||i,cause:a})}let ln;function un({secretType:e}={}){return"request"===e||"response"===e||"both"===e}function dn({functionName:e,result:t,logPvd:n}){}function hn(e){const t=e.callFunction,n=function(n){const s=n.name;n.data=en.call(e,{data:n.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],r=un(n)||false;return t.call(this,n).then((e=>(e.errCode=0,!r&&dn.call(this,{functionName:s,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!r&&dn.call(this,{functionName:s,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let s=0;s<n.length;s++){const{rule:o,content:r,mode:i}=n[s],a=e.match(o);if(!a)continue;let c=r;for(let e=1;e<a.length;e++)c=on(c,`{$${e}}`,a[e]);for(const e in t)c=on(c,`{${e}}`,t[e]);return"replace"===i?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:tn,extraInfo:{functionName:s}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:s,spaceId:o}=e.config,r=t.name;let i,a;return t.data=t.data||{},i=n,i=i.bind(e),a=un(t)?new ln({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:s,uniPlatform:o,osName:r}=xe();let i=o;"app"===o&&(i=r);const a=function({provider:e,spaceId:t}={}){const n=J;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const s=n.find((n=>n.provider===e&&n.spaceId===t));return s&&s.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},l=Object.keys(c);if(0===l.length)return!0;const u=function(e,t){let n,s,o;for(let r=0;r<e.length;r++){const i=e[r];i!==t?"*"!==i?i.split(",").map((e=>e.trim())).indexOf(t)>-1&&(s=i):o=i:n=i}return n||s||o}(l,n);if(!u)return!1;if((c[u]||[]).find(((e={})=>e.appId===s&&(e.platform||"").toLowerCase()===i.toLowerCase())))return!0;throw console.error(`此应用[appId: ${s}, platform: ${i}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),cn(an)}({provider:s,spaceId:o,functionName:r})?new ln({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):i(t),Object.defineProperty(a,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),a.then((e=>("undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e)))}}ln=class{constructor(){throw cn({message:"Platform web is not supported by secure network"})}};const gn=Symbol("CLIENT_DB_INTERNAL");function fn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=gn,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,s){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,s)}})}function pn(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const s=e[t].indexOf(n);-1!==s&&e[t].splice(s,1)}}}const mn=["db.Geo","db.command","command.aggregate"];function yn(e,t){return mn.indexOf(`${e}.${t}`)>-1}function vn(e){switch(D(e=Te(e))){case"array":return e.map((e=>vn(e)));case"object":return e._internalType===gn||Object.keys(e).forEach((t=>{e[t]=vn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function _n(e){return e&&e.content&&e.content.$method}class Tn{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:vn(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=_n(e),n=_n(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===_n(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=_n(e),n=_n(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return wn({$method:e,$param:vn(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),s=this.getCommand();return s.$db.push({$method:e,$param:vn(t)}),this._database._callCloudFunction({action:n,command:s})}}function wn(e,t,n){return fn(new Tn(e,t,n),{get(e,t){let s="db";return e&&e.content&&(s=e.content.$method),yn(s,t)?wn({$method:t},e,n):function(){return wn({$method:t,$param:vn(Array.from(arguments))},e,n)}}})}function Sn({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function kn(e,t={}){return fn(new e(t),{get:(e,t)=>yn("db",t)?wn({$method:t},null,e):function(){return wn({$method:t,$param:vn(Array.from(arguments))},null,e)}})}class In extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=Q("_globalUniCloudDatabaseCallback")),t||(this.auth=pn(this._authCallBacks)),this._isJQL=t,Object.assign(this,pn(this._dbCallBacks)),this.env=fn({},{get:(e,t)=>({$env:t})}),this.Geo=fn({},{get:(e,t)=>Sn({path:["Geo"],method:t})}),this.serverDate=Sn({path:[],method:"serverDate"}),this.RegExp=Sn({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:s}){function o(e,t){if(n&&s)for(let n=0;n<s.length;n++){const o=s[n];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[n]))}}const r=this,i=this._isJQL?"databaseForJQL":"database";function a(e){return r._callback("error",[e]),ee(te(i,"fail"),e).then((()=>ee(te(i,"complete"),e))).then((()=>(o(null,e),ge(oe,{type:ae,content:e}),Promise.reject(e))))}const c=ee(te(i,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:"CLIENT_DB",data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:s,tokenExpired:c,systemInfo:l=[]}=e.result;if(l)for(let o=0;o<l.length;o++){const{level:e,message:t,detail:n}=l[o],s=console[e]||console.log;let r="[System Info]"+t;n&&(r=`${r}\n详细信息：${n}`),s(r)}if(t)return a(new ve({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&c&&(Se({token:s,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:s,tokenExpired:c}]),this._callback("refreshToken",[{token:s,tokenExpired:c}]),ge(ie,{token:s,tokenExpired:c}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<u.length;o++){const{prop:t,tips:n}=u[o];if(t in e.result){const s=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),s)})}}return d=e,ee(te(i,"success"),d).then((()=>ee(te(i,"complete"),d))).then((()=>{o(d,null);const e=r._parseResult(d);return ge(oe,{type:ae,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),a(new ve({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const xn="token无效，跳转登录页面",Pn="token过期，跳转登录页面",An={TOKEN_INVALID_TOKEN_EXPIRED:Pn,TOKEN_INVALID_INVALID_CLIENTID:xn,TOKEN_INVALID:xn,TOKEN_INVALID_WRONG_TOKEN:xn,TOKEN_INVALID_ANONYMOUS_USER:xn},bn={"uni-id-token-expired":Pn,"uni-id-check-token-failed":xn,"uni-id-token-not-exist":xn,"uni-id-check-device-feature-failed":xn};function Cn(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function On(e=[],t=""){const n=[],s=[];return e.forEach((e=>{!0===e.needLogin?n.push(Cn(t,e.path)):!1===e.needLogin&&s.push(Cn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:s}}function Bn(e){return e.split("?")[0].replace(/^\//,"")}function Ln(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=n();return e[e.length-1]}())}function En(){return Bn(Ln())}function Rn(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,s=Bn(e);return n.some((e=>e.pagePath===s))}const Un=!!A.uniIdRouter,{loginPage:Fn,routerNeedLogin:Dn,resToLogin:Nn,needLoginPage:Mn,notNeedLoginPage:qn,loginPageInTabBar:jn}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:s={}}=A){const{loginPage:o,needLogin:r=[],resToLogin:i=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=On(e),{needLoginPage:l,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:s,pages:o=[]}=e,{needLoginPage:r,notNeedLoginPage:i}=On(o,s);t.push(...r),n.push(...i)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:o,routerNeedLogin:r,resToLogin:i,needLoginPage:[...a,...l],notNeedLoginPage:[...c,...u],loginPageInTabBar:Rn(o,s)}}();if(Mn.indexOf(Fn)>-1)throw new Error(`Login page [${Fn}] should not be "needLogin", please check your pages.json`);function $n(e){const t=En();if("/"===e.charAt(0))return e;const[n,s]=e.split("?"),o=n.replace(/^\//,"").split("/"),r=t.split("/");r.pop();for(let i=0;i<o.length;i++){const e=o[i];".."===e?r.pop():"."!==e&&r.push(e)}return""===r[0]&&r.shift(),"/"+r.join("/")+(s?"?"+s:"")}function Kn({redirect:e}){const t=Bn(e),n=Bn(Fn);return En()!==n&&t!==n}function Hn({api:e,redirect:t}={}){if(!t||!Kn({redirect:t}))return;const n=(o=t,"/"!==(s=Fn).charAt(0)&&(s="/"+s),o?s.indexOf("?")>-1?s+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:s+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:s);var s,o;jn?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const r={navigateTo:_,redirectTo:T,switchTab:w,reLaunch:S};setTimeout((()=>{r[e]({url:n})}),0)}function Jn({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=we();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:bn[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:bn[e]}}return n}();if(function(e){const t=Bn($n(e));return!(qn.indexOf(t)>-1)&&(Mn.indexOf(t)>-1||Dn.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,ue(re).length>0)return setTimeout((()=>{ge(re,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Wn(){!function(){const e=Ln(),{abortLoginPageJump:t,autoToLoginPage:n}=Jn({url:e});t||n&&Hn({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];s(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:s}=Jn({url:e.url});return t?e:s?(Hn({api:n,redirect:$n(e.url)}),!1):e}})}}function Vn(){this.onResponse((e=>{const{type:t,content:n}=e;let s=!1;switch(t){case"cloudobject":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in bn}(n);break;case"clientdb":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in An}(n)}s&&function(e={}){const t=ue(re);me().then((()=>{const n=Ln();if(n&&Kn({redirect:n}))return t.length>0?ge(re,Object.assign({uniIdRedirectUrl:n},e)):void(Fn&&Hn({api:"navigateTo",redirect:n}))}))}(n)}))}function zn(e){var t;(t=e).onResponse=function(e){de(oe,e)},t.offResponse=function(e){he(oe,e)},function(e){e.onNeedLogin=function(e){de(re,e)},e.offNeedLogin=function(e){he(re,e)},Un&&(Q("_globalUniCloudStatus").needLoginInit||(Q("_globalUniCloudStatus").needLoginInit=!0,me().then((()=>{Wn.call(e)})),Nn&&Vn.call(e)))}(e),function(e){e.onRefreshToken=function(e){de(ie,e)},e.offRefreshToken=function(e){he(ie,e)}}(e)}let Qn;const Yn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Gn=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Xn(){const e=we().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((s=t[1],decodeURIComponent(Qn(s).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var s;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}Qn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Gn.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,s,o="",r=0;r<e.length;)t=Yn.indexOf(e.charAt(r++))<<18|Yn.indexOf(e.charAt(r++))<<12|(n=Yn.indexOf(e.charAt(r++)))<<6|(s=Yn.indexOf(e.charAt(r++))),o+=64===n?String.fromCharCode(t>>16&255):64===s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var Zn=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(b((function(n,s){Object.defineProperty(s,"__esModule",{value:!0});const r="chooseAndUploadFile:ok",i="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function c(e,t,{onChooseFile:n,onUploadProgress:s}){return t.then((e=>{if(n){const t=n(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:r,tempFilePaths:[],tempFiles:[]}:function(e,t,n=5,s){(t=Object.assign({},t)).errMsg=r;const o=t.tempFiles,i=o.length;let a=0;return new Promise((r=>{for(;a<n;)c();function c(){const n=a++;if(n>=i)return void(!o.find((e=>!e.url&&!e.errMsg))&&r(t));const l=o[n];e.uploadFile({provider:l.provider,filePath:l.path,cloudPath:l.cloudPath,fileType:l.fileType,cloudPathAsRealPath:l.cloudPathAsRealPath,onUploadProgress(e){e.index=n,e.tempFile=l,e.tempFilePath=l.path,s&&s(e)}}).then((e=>{l.url=e.fileID,n<i&&c()})).catch((e=>{l.errMsg=e.errMsg||e.message,n<i&&c()}))}}))}(e,t,5,s)))}s.initChooseAndUploadFile=function(n){return function(s={type:"all"}){return"image"===s.type?c(n,function(t){const{count:n,sizeType:s,sourceType:o=["album","camera"],extension:r}=t;return new Promise(((t,c)=>{e({count:n,sizeType:s,sourceType:o,extension:r,success(e){t(a(e,"image"))},fail(e){c({errMsg:e.errMsg.replace("chooseImage:fail",i)})}})}))}(s),s):"video"===s.type?c(n,function(e){const{camera:n,compressed:s,maxDuration:o,sourceType:r=["album","camera"],extension:c}=e;return new Promise(((e,l)=>{t({camera:n,compressed:s,maxDuration:o,sourceType:r,extension:c,success(t){const{tempFilePath:n,duration:s,size:o,height:r,width:i}=t;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:o,type:t.tempFile&&t.tempFile.type||"",width:i,height:r,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){l({errMsg:e.errMsg.replace("chooseVideo:fail",i)})}})}))}(s),s):c(n,function(e){const{count:t,extension:n}=e;return new Promise(((e,s)=>{let r=o;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(r=wx.chooseMessageFile),"function"!=typeof r)return s({errMsg:i+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});r({type:"all",count:t,extension:n,success(t){e(a(t))},fail(e){s({errMsg:e.errMsg.replace("chooseFile:fail",i)})}})}))}(s),s)}}})));function es(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if("manual"===this.loadtime)return;let n=!1;const s=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(s.push(e[o]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,s)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:s,count:o}=n.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=s.length<this.pageSize;const r=e?s.length?s[0]:void 0:s;this.mixinDatacomResData=r,t&&t(r)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const s=t.action||this.action;s&&(n=n.action(s));const o=t.collection||this.collection;n=Array.isArray(o)?n.collection(...o):n.collection(o);const r=t.where||this.where;r&&Object.keys(r).length&&(n=n.where(r));const i=t.field||this.field;i&&(n=n.field(i));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const l=t.groupField||this.groupField;l&&(n=n.groupField(l)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,g=void 0!==t.getcount?t.getcount:this.getcount,f=void 0!==t.gettree?t.gettree:this.gettree,p=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:g},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return f&&(m.getTree=y),p&&(m.getTreePath=y),n=n.skip(h*(d-1)).limit(h).get(m),n}}}}function ts(e){return Q("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function ns({openid:e,callLoginByWeixin:t=!1}={}){throw ts(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `web`")}async function ss(e){const t=ts(this);return t.initPromise||(t.initPromise=ns.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function os(e){Ae=e}function rs(e){const t={getSystemInfo:k,getPushClientId:I};return function(n){return new Promise(((s,o)=>{t[e]({...n,success(e){s(e)},fail(e){o(e)}})}))}}class is extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const s=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(s,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let s=0;s<n.length;s++)n[s](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([rs("getSystemInfo")(),rs("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:s,message:o}=t;this._payloadQueue.push({action:n,messageId:s,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:s}=e;"end"===t?this._end({messageId:n,message:s}):"message"===t&&this._appendMessage({messageId:n,message:s})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){l(this._uniPushMessageCallback)}_destroy(){u(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const as={tcb:jt,tencent:jt,aliyun:Le,private:Ht,dcloud:Ht,alipay:Zt};let cs=new class{init(e){let t={};const n=as[e.provider];if(!n)throw new Error("未提供正确的provider参数");var s;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new K({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),hn(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(s=t).database=function(e){if(e&&Object.keys(e).length>0)return s.init(e).database();if(this._database)return this._database;const t=kn(In,{uniClient:s});return this._database=t,t},s.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return s.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=kn(In,{uniClient:s,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=Xn,e.chooseAndUploadFile=Zn.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return es(e)}}),e.SSEChannel=is,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return ss.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=os,e.importObject=function(t){return function(n,s={}){s=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},s);const{customUI:o,loadingOptions:l,errorOptions:u,parseSystemError:d}=s,h=!o;return new Proxy({},{get(o,g){switch(g){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...s){const o=n?n({params:s}):{};let r,i;try{return await ee(te(t,"invoke"),{...o}),r=await e(...s),await ee(te(t,"success"),{...o,result:r}),r}catch(a){throw i=a,await ee(te(t,"fail"),{...o,error:i}),i}finally{await ee(te(t,"complete"),i?{...o,error:i}:{...o,result:r})}}}({fn:async function o(...f){let p;h&&r({title:l.title,mask:l.mask});const m={name:n,type:"OBJECT",data:{method:g,params:f}};"object"==typeof s.secretMethods&&function(e,t){const n=t.data.method,s=e.secretMethods||{},o=s[n]||s["*"];o&&(t.secretType=o)}(s,m);let y=!1;try{p=await t.callFunction(m)}catch(e){y=!0,p={result:new ve(e)}}const{errSubject:v,errCode:_,errMsg:T,newToken:w}=p.result||{};if(h&&i(),w&&w.token&&w.tokenExpired&&(Se(w),ge(ie,{...w})),_){let e=T;if(y&&d&&(e=(await d({objectName:n,methodName:g,params:f,errSubject:v,errCode:_,errMsg:T})).errMsg||T),h)if("toast"===u.type)a({title:e,icon:"none"});else{if("modal"!==u.type)throw new Error(`Invalid errorOptions.type: ${u.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:s,confirmText:o}={}){return new Promise(((r,i)=>{c({title:e,content:t,showCancel:n,cancelText:s,confirmText:o,success(e){r(e)},fail(){r({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:u.retry,cancelText:"取消",confirmText:u.retry?"重试":"确定"});if(u.retry&&t)return o(...f)}}const t=new ve({subject:v,code:_,message:T,requestId:p.requestId});throw t.detail=p.result,ge(oe,{type:le,content:t}),t}return ge(oe,{type:le,content:p.result}),p.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:g,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let s=!1;if("callFunction"===t){const e=n&&n.type||R;s=e!==R}const o="callFunction"===t&&!s,r=this._initPromiseHub.exec();n=n||{};const{success:i,fail:a,complete:c}=ye(n),l=r.then((()=>s?Promise.resolve():ee(te(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>s?Promise.resolve(e):ee(te(t,"success"),e).then((()=>ee(te(t,"complete"),e))).then((()=>(o&&ge(oe,{type:ce,content:e}),Promise.resolve(e))))),(e=>s?Promise.reject(e):ee(te(t,"fail"),e).then((()=>ee(te(t,"complete"),e))).then((()=>(ge(oe,{type:ce,content:e}),Promise.reject(e))))));if(!(i||a||c))return l;l.then((e=>{i&&i(e),c&&c(e),o&&ge(oe,{type:ce,content:e})}),(e=>{a&&a(e),c&&c(e),o&&ge(oe,{type:ce,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=W;let t={};if(e&&1===e.length)t=e[0],cs=cs.init(t),cs._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{cs[e]=function(){return console.error(n),Promise.reject(new ve({code:"SYS_ERR",message:n}))}}))}Object.assign(cs,{get mixinDatacom(){return es(cs)}}),zn(cs),cs.addInterceptor=X,cs.removeInterceptor=Z,cs.interceptObject=ne})();var ls=cs;export{ls as Y};
