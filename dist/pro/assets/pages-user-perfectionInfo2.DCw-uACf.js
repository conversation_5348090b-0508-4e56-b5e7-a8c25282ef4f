import{l as e,p as t,U as a,z as o,A as l,a as i,f as n,w as s,F as r,i as u,o as d,B as m,t as f,c as p,e as c,d as h}from"./index-D8Q9TKMD.js";import{_ as b}from"./page-meta.CQ11A97m.js";import{r as I}from"./uni-app.es.KQfvbE5W.js";import{_ as y}from"./uni-easyinput.Kc2lvObb.js";import{_ as v,a as g}from"./uni-forms.4s1Yg_LJ.js";import{_ as C}from"./uni-icons.HwX4OE83.js";import{_ as k}from"./uni-popup.D6FGSq45.js";import{t as _}from"./index.DW56S9T8.js";import{m as j}from"./mapList.C3TkrYrq.js";import{m as V}from"./mapList2.CVHjRncW.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-transition.CNGNZNaw.js";import"./returnFn.BYkANsDr.js";const N=x({components:{tabBar:_,mapList:j,mapList2:V},data:()=>({baseInfo:{userIdentity:5,focusIndustryIds:[],focusDirection:[],gender:"",realName:"",code:"",email:"",company:"",cityCode:"",cityName:"",advantageCity:"",resume:"",referrer:"",InvitationCode:""},regionName:"",sex:[{text:"男",value:1},{text:"女",value:0}],identityList:[{value:1,text:"政府机构工作人员"},{value:2,text:"企业工作人员"},{value:5,text:"产业顾问"}],industryList:[],directionList:[{value:1,text:"产业研究"},{value:2,text:"园区管理"},{value:3,text:"企业招商"},{value:4,text:"政府决策"}],DivisionList:[],title:"信息完善",showMap:!1,rules:{},phone:"",refresh:!1,gotoIdentity:5}),onLoad(e){"yes"==(null==e?void 0:e.refresh)&&(this.refresh=!0),e.identity&&(this.gotoIdentity=e.identity),this.echoInfo()},created(){this.token=e("token"),this.userId=e("userId"),this.phone=e("phone"),this.showMap=!0},methods:{echoInfo(){this.$api.userMessage().then((e=>{var t,a,o,l;this.baseInfo.realName=e.result.realName,5==this.gotoIdentity&&(this.baseInfo.gender=Number(null==(t=e.result.userCompletionInfoDO)?void 0:t.gender),this.baseInfo.email=null==(a=e.result.userCompletionInfoDO)?void 0:a.email,this.baseInfo.company=null==(o=e.result.userCompletionInfoDO)?void 0:o.company,this.baseInfo.jobTitle=null==(l=e.result.userCompletionInfoDO)?void 0:l.jobTitle)}))},affirm(e){var t,a,o;this.$refs.popup.close(),this.baseInfo.region=[],this.regionName=(null==(t=e.province)?void 0:t.name)+((null==(a=e.citys)?void 0:a.name)?"/"+e.citys.name:"")+((null==(o=e.area)?void 0:o.name)?"/"+e.area.name:"");let l=Object.keys(e).map((t=>e[t]&&e[t].code&&""!==e[t].code?e[t].code:null));this.baseInfo.region=l.filter((e=>null!==e))},affirm2(e){var t,a,o;this.$refs.popup2.close(),this.baseInfo.cityCode=[],this.baseInfo.cityName=(null==(t=e.province)?void 0:t.name)+((null==(a=e.citys)?void 0:a.name)?"/"+e.citys.name:"")+((null==(o=e.area)?void 0:o.name)?"/"+e.area.name:"");let l=Object.keys(e).map((t=>e[t]&&e[t].code&&""!==e[t].code?e[t].code:null));this.baseInfo.cityCode=l.filter((e=>null!==e))},pop(){this.$refs.popup.open("bottom")},pop2(){this.$refs.popup2.open("bottom")},presentFn(e){this.$api.updateHeadImg({data:e,method:"POST"}).then((e=>{"SUCCESS"==e.code&&(5==this.gotoIdentity&&this.baseInfo.InvitationCode?this.$api.workerapplyAPI({data:{invitationCode:this.baseInfo.InvitationCode},method:"post",error:"no"}).then((e=>{t({title:"切换身份成功，加入团队成功",icon:"none"}),setTimeout((()=>{this.refresh?a({delta:2}):a({delta:1})}),1e3)})).catch((e=>{t({title:"切换身份成功,"+(null==e?void 0:e.msg),icon:"none"}),setTimeout((()=>{this.refresh?a({delta:2}):a({delta:1})}),1e3)})):(t({title:"切换身份成功",icon:"none"}),setTimeout((()=>{this.refresh?a({delta:2}):a({delta:1})}),1e3)))}))},submit(e){this.$refs[e].validate().then((e=>{if(!this.baseInfo.realName)return t({title:"请输入姓名",icon:"none"});if(!this.baseInfo.company)return t({title:"请输入所在单位",icon:"none"});if(!this.baseInfo.jobTitle)return t({title:"请输入所在单位职务",icon:"none"});if(0==this.baseInfo.cityCode.length)return t({title:"请输入所在城市",icon:"none"});if(!this.baseInfo.advantageCity)return t({title:"请输入优势资源城市",icon:"none"});if(!this.baseInfo.resume)return t({title:"请输入优势资源简述",icon:"none"});let a={userId:this.userId,userIdentity:5,realName:this.baseInfo.realName,gender:this.baseInfo.gender,email:this.baseInfo.email,jobTitle:this.baseInfo.jobTitle,company:this.baseInfo.company,cityCode:this.baseInfo.cityCode[this.baseInfo.cityCode.length-1],cityName:this.baseInfo.cityName,advantageCity:this.baseInfo.advantageCity,resume:this.baseInfo.resume,referrer:this.baseInfo.referrer};this.presentFn(a)}))}}},[["render",function(e,t,a,_,j,V){const x=I(o("page-meta"),b),N=l("tabBar"),T=I(o("uni-easyinput"),y),L=I(o("uni-forms-item"),v),F=I(o("uni-icons"),C),U=I(o("uni-forms"),g),w=u,$=l("mapList"),q=I(o("uni-popup"),k),A=l("mapList2");return d(),i(r,null,[n(x,{"page-style":"background-color: #FAFCFF"}),n(w,{class:"container"},{default:s((()=>[n(N,{title:j.title,peInfo:!0},null,8,["title"]),n(w,{class:"example"},{default:s((()=>[5==j.baseInfo.userIdentity?(d(),i("div",{key:0,class:"ConsultantPerfection"},[m("span",{class:"TitleName"},"更多信息"),m("div",{class:"perfectBox"},[n(U,{ref:"valiForm",rules:j.rules,modelValue:j.baseInfo,"label-align":"left","label-width":"100"},{default:s((()=>[m("div",{class:"customizationInput"},[n(L,{label:"您的姓名",required:""},{default:s((()=>[n(T,{type:"nickname",trim:"all",clearable:!1,modelValue:j.baseInfo.realName,"onUpdate:modelValue":t[0]||(t[0]=e=>j.baseInfo.realName=e),placeholder:"请输入"},null,8,["modelValue"])])),_:1}),n(L,{label:"联系电话",required:""},{default:s((()=>[m("span",{class:"phone"},f(j.phone),1)])),_:1}),n(L,{label:"所在单位",required:""},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:j.baseInfo.company,"onUpdate:modelValue":t[1]||(t[1]=e=>j.baseInfo.company=e),placeholder:"请输入所在单位"},null,8,["modelValue"])])),_:1}),n(L,{label:"所在单位职务",required:""},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:j.baseInfo.jobTitle,"onUpdate:modelValue":t[2]||(t[2]=e=>j.baseInfo.jobTitle=e),placeholder:"请输入所在单位职务"},null,8,["modelValue"])])),_:1}),5==j.gotoIdentity?(d(),p(L,{key:0,label:"所在城市",required:""},{default:s((()=>{var e,a;return[m("div",{onClick:t[3]||(t[3]=e=>V.pop2()),class:"citySelect"},[m("span",{class:c(""==(null==(e=j.baseInfo)?void 0:e.cityName)?"":"citySelectText")},f(""==(null==(a=j.baseInfo)?void 0:a.cityName)?"请选择所在城市":this.baseInfo.cityName),3),n(F,{color:"#999",type:"down",size:"14"})])]})),_:1})):h("",!0),5==j.gotoIdentity?(d(),p(L,{key:1,label:"优势资源城市",required:""},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:j.baseInfo.advantageCity,"onUpdate:modelValue":t[4]||(t[4]=e=>j.baseInfo.advantageCity=e),placeholder:"请输入优势资源城市"},null,8,["modelValue"])])),_:1})):h("",!0)]),m("div",{class:"resume"},[5==j.gotoIdentity?(d(),p(L,{key:0,label:"优势资源简述",required:""},{default:s((()=>[n(T,{type:"textarea",trim:"all",clearable:!1,modelValue:j.baseInfo.resume,"onUpdate:modelValue":t[5]||(t[5]=e=>j.baseInfo.resume=e),placeholder:"请输入优势资源，包括但不限于 行业、地域等资源"},null,8,["modelValue"])])),_:1})):h("",!0)]),m("div",{class:"customizationInput"},[n(L,{label:"您的邮箱"},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:j.baseInfo.email,"onUpdate:modelValue":t[6]||(t[6]=e=>j.baseInfo.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})]),5==j.gotoIdentity?(d(),i("div",{key:0,class:"customizationInput"},[n(L,{label:"加入团队"},{default:s((()=>[n(T,{trim:"all",clearable:!1,modelValue:j.baseInfo.InvitationCode,"onUpdate:modelValue":t[7]||(t[7]=e=>j.baseInfo.InvitationCode=e),placeholder:"请输入团队邀请码"},null,8,["modelValue"])])),_:1})])):h("",!0)])),_:1},8,["rules","modelValue"])])])):h("",!0),m("div",{class:"experience",type:"primary",onClick:t[8]||(t[8]=e=>V.submit("valiForm"))}," 确定 ")])),_:1}),n(q,{ref:"popup","background-color":"#fff"},{default:s((()=>[j.showMap?(d(),p($,{key:0,token:e.token,onAffirm:V.affirm},null,8,["token","onAffirm"])):h("",!0)])),_:1},512),n(q,{ref:"popup2","background-color":"#fff"},{default:s((()=>[j.showMap?(d(),p(A,{key:0,token:e.token,onAffirm:V.affirm2},null,8,["token","onAffirm"])):h("",!0)])),_:1},512)])),_:1})],64)}],["__scopeId","data-v-322fac2a"]]);export{N as default};
