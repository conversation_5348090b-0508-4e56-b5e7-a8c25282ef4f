<!DOCTYPE html>
<html lang="en">

<head>
  <link rel="stylesheet" href="https://static.idicc.cn/static/wechatai/assets/uni.801347ed.css">

  <meta charset="UTF-8" />
  <script>
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
      CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') + '" />')

    window.callbacks = {
      callbacks: {},
      register: function (successCallback, errorCallback) {
        const callbackId = Date.now().toString();
        this.callbacks[callbackId] = {
          success: successCallback,
          error: errorCallback,
        };
        return callbackId;
      },
      execute: function (callbackId, type, data) {
        const callback = this.callbacks[callbackId];
        if (callback) {
          if (type === 'success') {
            callback.success && callback.success(data);
          } else {
            callback.error && callback.error(data);
          }
          delete this.callbacks[callbackId]; // 执行后删除回调
        }
      },
    };
  </script>
  <title>哒达招商</title>
  <!--preload-links-->
  <!--app-context-->
  <script type="module" crossorigin src="https://static.idicc.cn/static/wechatai/assets/index-D8Q9TKMD.js"></script>
  <link rel="stylesheet" crossorigin href="https://static.idicc.cn/static/wechatai/assets/index-RQ2IGHUs.css">
</head>

<body>
  <div id="app"><!--app-html--></div>
</body>

</html>