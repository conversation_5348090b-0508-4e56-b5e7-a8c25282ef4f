<template>
  <!-- #ifdef  MP-WEIXIN -->
  <page-meta page-style="background-color: #FAFCFF" />
  <!-- #endif -->
  <!-- #ifdef H5 -->
  <page-meta page-style="background-color: transparent" />
  <!-- #endif -->
  <view class="container">
    <!-- #ifdef  MP-WEIXIN -->
    <tabBar :title="title" :pe-info="true" />
    <!-- #endif -->
    <scroll-view class="example" scroll-y>
      <uni-forms ref="valiForm" :rules="rules" :model-value="baseInfo" label-position="top" label-width="100">
        <uni-forms-item required label="企业名称" name="enterpriseName">
          <div style="position: relative">
            <uni-easyinput v-model="baseInfo.enterpriseName" trim="all" :clearable="false" placeholder="请输入企业名称"
              @focus="enterpriseNameFocus" @blur="enterpriseNameBlur" @input="changeenterpriseName" />
            <view v-show="showAssociativeList" class="associativeList">
              <view class="enterpriseTotal">
                <view>
                  搜索到<span style="color: #3370ff">{{
                    enterpriseTotal
                  }}</span>条结果
                </view>
                <view class="enterpriseBack" @click="clearAssociativeList">
                  取消
                </view>
              </view>
              <scroll-view class="associativeList2" :scroll-y="true" @scrolltolower="scrolltolowerFn">
                <div v-for="(item, index) in AssociativeList" :key="index" class="associativeItem"
                  @click="SelectEn(item)">
                  <div class="name">{{ item.enterpriseName }}</div>
                  <div class="man">法定代表人：{{ item.legalPerson }}</div>
                  <div class="code">
                    统一社会信用代码：{{ item.unifiedSocialCreditCode }}
                  </div>
                </div>
              </scroll-view>
            </view>
          </div>
        </uni-forms-item>
        <uni-forms-item required label="统一社会信用代码" name="uniCode">
          <uni-easyinput v-if="!forbiddenCode" v-model="baseInfo.uniCode" trim="all" :clearable="false"
            placeholder="请输入" />
          <div v-else class="enterpriseUniCode">
            {{ baseInfo.uniCode }}
          </div>
        </uni-forms-item>
        <uni-forms-item label="企业简介" required name="introduction">
          <uni-easyinput v-model="baseInfo.introduction" type="textarea" trim="all" :clearable="false"
            placeholder="请输入企业简介" />
        </uni-forms-item>
        <uni-forms-item label="联系电话" required name="mobile">
          <uni-easyinput v-model="baseInfo.mobile" trim="all" :clearable="false" placeholder="请输入联系电话" />
        </uni-forms-item>
        <uni-forms-item label="注册地址" required name="enterpriseAddress">
          <uni-easyinput v-model="baseInfo.enterpriseAddress" trim="all" :clearable="false" placeholder="请输入注册地址" />
        </uni-forms-item>
        <uni-forms-item label="所属行业" required name="nationalStandardIndustry">
          <uni-easyinput v-model="baseInfo.nationalStandardIndustry" trim="all" :clearable="false"
            placeholder="请输入所属行业" />
        </uni-forms-item>
        <uni-forms-item label="经营范围" name="businessScope">
          <uni-easyinput v-model="baseInfo.businessScope" trim="all" :clearable="false" placeholder="请输入经营范围" />
        </uni-forms-item>
        <div style="min-height: 88px;" />
      </uni-forms>
      <div class="experience" type="primary" @click="submit('valiForm')">
        确定
      </div>
    </scroll-view>
    <alertPop ref="alertPop" />
  </view>
</template>
<script>
import tabBar from '../../components/tabBar/index.vue';
import alertPop from '../components/pop/alertPop.vue';
export default {
  components: {
    tabBar,
    alertPop
  },
  data() {
    return {
      baseInfo: {
        clueSource: '2',
        businessScope: '',
        nationalStandardIndustry: '',
        enterpriseAddress: '',
        mobile: '',
        introduction: '',
        enterpriseName: '',
        uniCode: ''
      },
      // 搜索企业
      AssociativeList: [],
      showAssociativeList: false,
      enterpriseTotal: 0,
      enterpriseId: '', //企业id
      forbiddenCode: false,
      enterpriseTime: null,

      regionName: '',
      title: '添加企业',
      showMap: false,
      delItem: {},
      // 校验规则
      rules: {
        mobile: {
          rules: [{
            required: true,
            errorMessage: '请输入联系方式'
          }
            // ,
            // {
            //   pattern: /^1[3-9]\d{9}$/,
            //   errorMessage: '请输入合法手机号'
            // }
          ]
        },
        enterpriseName: {
          rules: [{
            required: true,
            errorMessage: '请输入企业名称'
          }]
        },
        uniCode: {
          rules: [{
            required: true,
            errorMessage: '请输入统一信用代码'
          }]
        },
        introduction: {
          rules: [{
            required: true,
            errorMessage: '请输入企业简介'
          }]
        },
        enterpriseAddress: {
          rules: [{
            required: true,
            errorMessage: '请输入注册地址'
          }]
        },
        nationalStandardIndustry: {
          rules: [{
            required: true,
            errorMessage: '请输入所属行业'
          }]
        }
      }
    };
  },
  onLoad(op) {

    // #ifdef H5
    // #endif

  },
  methods: {
    //企业名称失去焦点
    enterpriseNameBlur() {
      if (this.AssociativeList.length > 0) {
        this.AssociativeList.forEach((it) => {
          if (it.enterpriseName === this.baseInfo.enterpriseName) {
            this.baseInfo = it;
            this.baseInfo.uniCode = it.enterpriseUniCode || it.unifiedSocialCreditCode || '';
            this.forbiddenCode = true;
            this.enterpriseId = it.id;
          }
        });
      } else {
        this.AssociativeList = [];
        this.showAssociativeList = false;
      }
    },
    scrolltolowerFn() {
      if (this.pages > this.pageNum) {
        this.pageNum++;
        this.requestSearch(this.keyText, this.pageNum);
      }
    },
    enterpriseNameFocus() {
      if (this.baseInfo.enterpriseName) {
        this.showAssociativeList = true;
      }
    },
    clearAssociativeList() {
      this.showAssociativeList = false;
    },
    //选中企业
    SelectEn(item) {
      this.showAssociativeList = false;
      this.forbiddenCode = true;
      this.baseInfo = item;
      this.baseInfo.uniCode = item.unifiedSocialCreditCode;
      this.enterpriseId = item.id;
    },
    //企业名称输入框变更
    changeenterpriseName(e) {
      this.enterpriseTotal = 0;
      this.forbiddenCode = false;
      this.baseInfo.uniCode = '';
      this.enterpriseId = '';
      if (this.enterpriseTime != null) {
        clearTimeout(this.enterpriseTime);
      }
      this.enterpriseTime = setTimeout(async () => {
        if (e) {
          this.keyText = e;
          this.pageNum = 1;
          this.pages = 0;
          this.requestSearch(e, 1);
        } else {
          this.pageNum = 1;
          this.pages = 0;
          this.AssociativeList = [];
          this.enterpriseTotal = 0;
        }
      }, 400);
    },
    requestSearch(e, pageNum) {
      this.$api
        .newEnterpriseSearchAPI({
          data: {
            keyword: e,
            pageNum: pageNum,
            pageSize: 10
          },
          method: 'POST'
        })
        .then((res) => {
          if (pageNum == 1) {
            this.enterpriseTotal = 0;
            this.AssociativeList = [];
          }
          this.AssociativeList = this.AssociativeList.concat(
            res.result.records
          );
          this.enterpriseTotal = res.result.total;
          this.pages = res.result.pages;
          if (this.baseInfo.enterpriseName) {
            this.showAssociativeList = true;
          } else {
            this.showAssociativeList = false;
          }
        });
    },

    submit(ref) {
      let that = this;
      this.$refs[ref].validate().then(() => {
        let data = that.baseInfo;
        data.clueSource = '2';
        that.$api.addEnterpriseAPI_investment({
          data,
          method: 'post'
        }).then(res => {
          if (res.code == 'SUCCESS') {
            if (res.result) {
              uni.showToast({
                title: '添加成功！系统已自动添加企业相关信息',
                icon: 'none',
              });
            } else {
              uni.showToast({
                title: '添加企业成功！',
                icon: 'none',
              });
            }
            setTimeout(() => {
              // #ifdef MP-WEIXIN
              uni.navigateBack({
                delta: 1
              });
              // #endif
              // #ifdef H5
              window?.ReactNativeWebView?.postMessage(
                JSON.stringify({
                  type: 'changePath',
                  value: {
                    type: 'refresh'
                  },
                  path: 'goBack'
                })
              );
              // #endif
            }, 1600);

          } else {
            that.$refs.alertPop.opens({
              title: res.msg
            });
          }
        });

      });
    }
  }
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
}

.associativeList {
  width: 100%;
  position: absolute;
  top: 88rpx;
  z-index: 90;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  max-height: 640rpx;

  .enterpriseTotal {
    width: 100%;
    color: #3f4a59;
    font-size: 30rpx;
    padding: 30rpx 0;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    .enterpriseBack {
      width: 60rpx;
      height: 40rpx;
      color: #3370ff;
      font-size: 28rpx;
      line-height: 40rpx;
    }
  }
}

.associativeList2 {
  width: 100%;
  max-height: 540rpx;
  // padding-top: 102rpx;

  .associativeItem {
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;

    .name {
      font-size: 28rpx;
      color: #333;
      line-height: 48rpx;
    }

    .man {
      font-size: 24rpx;
      color: #666;
      line-height: 40rpx;
    }

    .code {
      font-size: 24rpx;
      color: #666;
      line-height: 40rpx;
    }
  }
}


::v-deep {
  .uni-data-checklist .checklist-group {
    margin-left: 20rpx;
  }

  .resume {
    .uni-forms-item {
      margin-bottom: 0rpx;
      display: flex;
      //align-items: center;
      height: 160rpx;
      border-top: 2rpx solid #f6f6f6;
    }

    .uni-easyinput__content {
      width: 400rpx;
      height: 160rpx;
      border: transparent;
    }

    .uni-easyinput__content-textarea {
      width: 400rpx;
      height: 160rpx;
      //background: #F5F7FA;
      border: transparent;
      padding: 12rpx;
      box-sizing: border-box;
    }

    .uni-easyinput__placeholder-class {
      font-weight: 400;
      font-size: 28rpx;
      color: #b7bfc7;
    }

    .uni-forms-item__label {
      width: 220rpx;
      //margin-right: 44rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #86909c;
    }
  }

  .customizationInput {
    .uni-forms-item {
      margin-bottom: 0rpx;
      display: flex;
      align-items: center;
      height: 84rpx;
      border-top: 2rpx solid #f6f6f6;
    }

    .uni-forms-item__label {
      width: 220rpx;
      //margin-right: 44rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #86909c;
    }

    .uni-easyinput__content {
      width: 400rpx;
      height: 84rpx;
      border: transparent;
    }

    .uni-easyinput__content-input {
      width: 400rpx;
      height: 84rpx;
      border: transparent;
      font-weight: 400;
      font-size: 28rpx;
      color: #1d2129;
    }

    .uni-easyinput__placeholder-class {
      font-weight: 400;
      font-size: 28rpx;
      color: #b7bfc7;
    }
  }
}

.citySelect {
  font-weight: 400;
  font-size: 28rpx;
  color: #b7bfc7;
  width: 361rpx;
  height: 84rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  justify-content: space-between;

  .citySelectText {
    font-weight: 400;
    font-size: 28rpx;
    color: #1d2129;
  }
}

::v-deep {
  .uni-select {
    width: 690rpx !important;
    height: 80rpx !important;
    background: #ffffff !important;
    border-radius: 20rpx 20rpx 20rpx 20rpx !important;
    border: 2rpx solid #e6ebf0 !important;
  }

  .uni-select__input-placeholder {
    font-weight: 400 !important;
    font-size: 30rpx !important;
    color: #c8c9cc !important;
  }

  .uni-forms-item.is-direction-top .uni-forms-item__label {
    font-weight: 400;
    font-size: 30rpx;
    color: #1d2129;
  }
}

.simulation {
  width: 690rpx !important;
  height: 80rpx !important;
  box-sizing: border-box;
  background: #ffffff !important;
  border-radius: 20rpx 20rpx 20rpx 20rpx !important;
  border: 2rpx solid #e6ebf0 !important;
  display: flex;
  align-items: center;
  font-weight: 400 !important;
  font-size: 30rpx !important;
  justify-content: space-between;
  padding-left: 10px;
  padding-right: 5px;
  color: #c8c9cc !important;

  .textSpan {
    font-size: 14px;
    color: #333;
  }
}

.example {
  z-index: 20;
  position: relative;
  // #ifdef MP-WEIXIN
  top: 208rpx;
  height: calc(100vh - 208rpx);
  // #endif
  // #ifdef H5
  top: 22rpx;
  height: calc(100vh - 28rpx);
  // #endif
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  // overflow: hidden;

}

.experience {
  position: fixed;
  bottom: 60rpx;
  left: 30rpx;
  width: 690rpx;
  height: 88rpx;
  z-index: 60;
  background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
  box-shadow: 0rpx 8rpx 24rpx 0rpx #c2d9ff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

button[type="primary"] {
  background-color: $uni-color-primary !important;
}
</style>
