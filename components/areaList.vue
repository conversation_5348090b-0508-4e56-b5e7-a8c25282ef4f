<template>
	<view class="region">
		<view class="head">
			<span class="txt">请选择</span>
		</view>
		<view class="region-con">
			<view class="region-list province">
				<view class="item" :class="check.province.name == item.name ? 'on' : ''" @click="provinceEvent(item)"
					v-for="(item, i) in province" :key="i">
					<view class="txt">{{ item.name }}({{item.value}})</view>
				</view>
			</view>
			<view class="region-list citys" :class="check.province.name ? 'on' : ''">
				<view class="item" :class="check.citys.name == item.name ? 'on' : ''" @click="citysEvent(item)"
					v-for="(item, i) in citys" :key="i">
					<view class="txt">{{ item.name }}({{item.value}})</view>
				</view>
			</view>
		</view>
		<view class="btnbox">
			<view @click="clear" class="clear">重置</view>
			<view class="affirm" @click="affirm">确认</view>
		</view>
		<!-- #ifndef MP-WEIXIN -->
		<div v-if="BottomBlank" style="height: 160rpx;"></div>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		props: {
			active: {
				type: Number,
				default: null
			},
			token: {
				type: String,
				default: null
			},
			cityName: {
				type: String,
				default: null
			},
			BottomBlank: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {
				province: [],
				showChain: "",
				citys: [],
				area: [],
				type: 1, // 1省2市3县区
				check: {
					province: {
						name: '',

					},
					citys: {
						name: '',

					},
					area: {
						name: '',
					}
				},
				param: {}
			}
		},
		created() {
			if (this.province.length == 0) {
				// #ifndef H5
				//this.init();
				// #endif
			}
		},
		methods: {
			clear() {
				this.citys = [];
				this.area = [];
				this.check = {
					province: {
						name: "",
					},
					citys: {
						name: "",
					},
					area: {
						name: "",
					}
				}
			},

			affirm() {
				this.$emit("affirm", this.check)
			},
			init() {
				if (this.province.length == 0) {
					//this.mapList(1);
					this.mapInfo()
				}
			},
			mapInfo(showChain) {
				this.showChain = showChain
				this.$api.countByDivisionAPI({
					data: {
						chainId: this.showChain,
						divisionLevel: 1,
						divisionCode: ''
					},
					method: "GET"
				}).then((res) => {
					this.province = res.result;
				})
			},
			mapList(type, id) {
				this.$api.countByDivisionAPI({
					data: {
						chainId: this.showChain,
						divisionLevel: 2,
						divisionCode: this.check.province.code
					},
					method: "GET"
				}).then((res) => {
					this.citys = res.result;
				})
			},
			//点击省
			provinceEvent(item) {
				this.check.province.name = item.name;
				this.check.province.code = item.code;
				this.check.province.notAncestorCodes = item.notAncestorCodes;
				if (!item.notAncestorCodes) {
					this.mapList(2, item.id);
				} else {
					this.citys = []
				}
				this.area = []
				this.check.citys.name = '';
				this.check.citys.code = '';
				this.check.area.name = '';
				this.check.area.code = '';
			},
			//点击市
			citysEvent(item) {
				//this.mapList(3, item.id);
				this.check.citys.name = item.name;
				this.check.citys.code = item.code;
				this.check.citys.notAncestorCodes = item.notAncestorCodes;
			},
			//点击区
			// areaEvent(item) {
			// 	this.check.area.name = item.name;
			// 	this.check.area.code = item.code;
			// },
		},
	}
</script>

<style scoped lang="less">
	.btnbox {
		height: 120rpx;
		background: #FFFFFF;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		display: flex;
		padding-right: 30rpx;
		padding-left: 30rpx;

		.clear {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 212rpx;
			height: 80rpx;
			margin-top: 20rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 2rpx solid #C9CDD4;
		}

		.affirm {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 460rpx;
			margin-top: 20rpx;
			height: 80rpx;
			background: #3370FF;
			color: #FFF;
			margin-left: 20rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
		}
	}

	.head {
		display: flex;
		height: 88rpx;
		flex-direction: column;

		.txt {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}


	}

	.region {
		::-webkit-scrollbar {
			display: none;
			/* Chrome Safari */
		}

		&-con {
			display: flex;
			justify-content: space-between;
		}

		&-list {
			max-height: 50vh;
			overflow: auto;
			width: 50%;
			box-sizing: border-box;
			padding: 20rpx 0;
			padding-top: 0;

			.item {
				line-height: 80rpx;
				height: 80rpx;
				padding: 10rpx 0;
				font-size: 28rpx;
				text-align: center;
				color: rgb(54, 53, 53);
				display: flex;
				justify-content: center;
				align-items: center;

				.txt {
					width: 100%;
					line-height: 36rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					text-align: left;
					padding: 0 0 0 30rpx;
				}

				&.on {
					color: #417FFF;
					background: #fff;
				}
			}

			&.province {
				// padding-left: 40rpx;
				background-color: #f7f7f7;
				// border-right: 1rpx solid rgb(232, 232, 232);
			}

			&.citys {

				// padding-left: 30rpx;
				// border-right: 1rpx solid rgb(232, 232, 232);
				&.on {
					background: #fff;
				}
			}

			&.area {

				// padding-left: 20rpx;
				&.on {
					background: #fff;
				}

				.icon04 {
					display: none;
					width: 24rpx;
					height: 24rpx;
					margin-left: 10rpx;
				}

				.item {
					&.on {
						position: relative;

						.icon04 {
							position: absolute;
							right: 20rpx;
							display: block;
						}
					}
				}
			}
		}
	}
</style>