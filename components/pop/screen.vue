<template>
  <view>
    <!-- #ifdef H5 -->
    <Teleport to="body">
      <!-- #endif -->
      <uni-popup ref="follow" :safe-area='false' :is-mask-click="false" type="dialog" style="z-index: 99;"
        :show="false">
        <div class="applyaffirm">
          <uni-icons @click="entrustCloseFn" class="canleIcon" type="closeempty" color="#86909C" size="22"></uni-icons>
          <div class="head">筛选</div>
          <div class="filtrate">
            <div class="SelectTitle">订单状态</div>
            <div v-if="listType == 1" class="filtrateList">
              <div @click="changeState(item.value)" :class="form.state == item.value ? 'filtrateItems' : 'filtrateItem'"
                v-for="(item, index) in stateList" :key="index">
                {{ item.key }}
              </div>
            </div>
            <div v-if="listType == 2" class="filtrateList">
              <div @click="changeState(item.value)" :class="form.state == item.value ? 'filtrateItems' : 'filtrateItem'"
                v-for="(item, index) in stateList2" :key="index">
                {{ item.key }}
              </div>
            </div>
            <div v-if="listType == 1" class="SelectTitle">订单金额</div>
            <div v-if="listType == 1" class="filtrateList">
              <div @click="changeMoney(item.value)" :class="form.money == item.value ? 'filtrateItems' : 'filtrateItem'"
                v-for="(item, index) in moneyList" :key="index">
                {{ item.key }}
              </div>
            </div>
            <div class="SelectTitle">{{ listType == 1 ? '委托时间' : '需求发布时间' }}</div>
            <uni-datetime-picker v-model="form.entrustTime" type="daterange" />
          </div>
          <div class="btnBox">
            <div class="canle" @click="claimFn">重置</div>
            <div class="submit" @click="submit">确认</div>
          </div>
          <div :style="{ 'height': safeArea }"></div>
        </div>
      </uni-popup>
      <!-- #ifdef H5 -->
    </Teleport>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  props: {
    listType: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      porpsData: '',
      safeArea: uni.getStorageSync("safeAreaRpx"),
      form: {
        state: "",
        money: '',
        entrustTime: []
      },
      realName: '',
      submitLoading: false,
      operationTime: '',
      stateList: [{
        key: '待认领',
        value: "1",
      },
      {
        key: '已认领',
        value: "2",
      }
      ],
      stateList2: [{
        key: '待反馈',
        value: "1",
      },
      {
        key: '已反馈',
        value: "2",
      }
      ],
      moneyList: [{
        key: '全部',
        value: "",
      }, {
        key: '2k以下',
        value: "1",
      }, {
        key: '2-5k',
        value: "2",
      }, {
        key: '5-10k',
        value: "3",
      }, {
        key: '10k以上',
        value: "4",
      }],
      rules: {

      },
    }
  },
  methods: {
    changeMoney(value) {
      this.form.money = value
    },
    changeState(value) {
      this.form.state = value
    },
    opens(item) {
      this.$emit("changeBottomTab", false)
      this.$refs.follow.open("bottom")
    },
    entrustCloseFn() {
      this.$refs.follow.close()
      this.$emit("changeBottomTab", true)
      // 通知父组件关闭筛选弹窗
      this.$emit("closeScreenPopup")
    },
    claimFn() {
      this.form = {
        state: "",
        money: '',
        entrustTime: []
      }
    },
    submit() {
      this.$emit("updataentrustList", this.form)
      this.entrustCloseFn()
    },
  }
}
</script>

<style lang="scss" scoped>
.canleIcon {
  position: absolute;
  top: 28rpx;
  right: 40rpx;
  z-index: 4;
}

.text {
  font-weight: 400;
  font-size: 30rpx;
  color: #C8C9CC;
  margin-left: 8rpx;
}

.hintBox {
  width: 622rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  box-sizing: border-box;
  padding: 52rpx 48rpx 32rpx 48rpx;
  display: flex;
  align-items: center;
  flex-direction: column;

  .p1 {
    font-weight: 500;
    font-size: 32rpx;
    color: #323233;
    margin-bottom: 16rpx;

  }

  .p2 {
    font-weight: 400;
    font-size: 28rpx;
    color: #3F4A59;
    margin-bottom: 68rpx;
    text-align: center;
  }

  .popBtn {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .affirm {
      width: 251rpx;
      height: 72rpx;
      background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .canleBt {
      width: 251rpx;
      height: 72rpx;
      background: #FFFFFF;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      border: 2rpx solid #EBEDF0;
      font-weight: 400;
      font-size: 32rpx;
      color: #323233;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.applyaffirm {
  width: 750rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  box-sizing: border-box;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;

  .filtrate {
    width: 100%;
    margin-bottom: 64rpx;

    .SelectTitle {
      font-weight: 500;
      font-size: 30rpx;
      color: #000000;
      margin-bottom: 24rpx;
      margin-top: 6rpx;
    }

    .filtrateList {
      display: flex;
      flex-wrap: wrap;

      .filtrateItem {
        width: 160rpx;
        height: 60rpx;
        background: #F7F7F7;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 14rpx;
        margin-bottom: 20rpx;
      }

      .filtrateItems {
        width: 160rpx;
        height: 60rpx;
        background: #3370FF;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 14rpx;
        margin-bottom: 20rpx;
      }
    }
  }


  .formItem {
    width: 100%;
    height: auto;
    background: #FFFFFF;
    padding: 32rpx 30rpx;
    box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;

    .formKey {
      font-weight: 500;
      font-size: 30rpx;
      color: #1D2129;
      margin-bottom: 16rpx;
    }

    .remark {
      font-weight: 400;
      font-size: 28rpx;
      color: #86909C;
    }
  }

  .head {
    width: 750rpx;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 32rpx;
    margin-top: 24rpx;
    color: #323233;

  }

  .btnBox {
    width: 750rpx;
    height: 128rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -8rpx 8rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    padding: 24rpx 30rpx;
    display: flex;
    box-sizing: border-box;
    justify-content: space-between;

    .canle {
      width: 330rpx;
      height: 80rpx;
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      border: 2rpx solid #B7BFC7;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #3F4A59;
    }

    .submit {
      width: 330rpx;
      height: 80rpx;
      background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%);
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #FFFFFF;
    }
  }
}
</style>
