<template>
  <view>
    <!-- #ifdef H5 -->
    <Teleport to="body">
      <!-- #endif -->
      <uni-popup ref="claim" :safe-area='true' :is-mask-click="false" type="dialog" style="z-index: 99;">
        <div class="applyaffirm">
          <div class="p1">温馨提示</div>
          <div class="p2">认领后，若一周内或到了委托人预期的截止时间仍未有任何跟进信息，该订单将会被退回至待认领状态，供其他产业顾问认领。</div>
          <div class="popBtn">
            <div @click="claimCanle" class="canleBt">
              取消
            </div>
            <div @click="ConfirmationClaim" class="affirm">
              确认
            </div>
          </div>

        </div>
      </uni-popup>
      <!-- #ifdef H5 -->
    </Teleport>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      porpsData: '',
      loading: false,

    }
  },

  mounted() {
    // 确保弹窗默认关闭状态
    this.$nextTick(() => {
      if (this.$refs.claim) {
        this.$refs.claim.close()
      }
    })
  },
  methods: {
    opens(item) {
      this.porpsData = item
      this.$refs.claim.open()
    },
    //取消认领
    claimCanle() {
      this.$refs.claim.close()
      // 设置父级claimShow为false， 关闭认领弹窗为关闭状态
      this.$emit('closeClaimPopup')
    },
    //认领
    ConfirmationClaim() {
      if (this.loading) {
        return
      }
      this.loading = true
      this.$api.claim({
        data: {
          entrustId: this.porpsData.entrustId,
        },
        method: 'post'
      }).then(res => {
        this.$refs.claim.close()
        uni.showToast({
          title: '认领成功! 您现在可以在“我的订单”中进行后续跟进。',
          icon: "none",
          duration: 2000,
        });
        setTimeout(() => {
          this.$emit("claimupdata")
          this.loading = false
        }, 1000)

      }).catch(err => {
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.applyaffirm {
  width: 622rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  box-sizing: border-box;
  padding: 52rpx 48rpx 32rpx 48rpx;
  display: flex;
  align-items: center;
  flex-direction: column;


  .p1 {
    font-weight: 500;
    font-size: 32rpx;
    color: #323233;
    margin-bottom: 16rpx;

  }

  .p2 {
    font-weight: 400;
    font-size: 28rpx;
    color: #3F4A59;
    margin-bottom: 68rpx;
    text-align: center;
  }

  .affirm {
    width: 526rpx;
    height: 72rpx;
    background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .popBtn {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .affirm {
      width: 251rpx;
      height: 72rpx;
      background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .canleBt {
      width: 251rpx;
      height: 72rpx;
      background: #FFFFFF;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      border: 2rpx solid #EBEDF0;
      font-weight: 400;
      font-size: 32rpx;
      color: #323233;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
