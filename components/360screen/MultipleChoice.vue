<template>
	<view class="region">
		<view class="head">
			<span class="txt">请选择</span>
			<view class="affirm2" @click="affirm">确认</view>
		</view>
		<picker-view v-if="visible" :indicator-style="indicatorStyle" :value="value" @change="bindChange"
			class="picker-view" :immediate-change="true">
			<picker-view-column>
				<view class="item">不限</view>
				<view class="item" v-for="(item, index) in list" :key="index">{{ item }}</view>
			</picker-view-column>
		</picker-view>
		<!-- #ifndef MP-WEIXIN -->
		<div v-if="BottomBlank" style="height: 160rpx;"></div>
		<!-- #endif -->
		<!-- #ifndef H5 -->
		<div v-if="appletTab" style="height: 160rpx;"></div>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			},
			single: {
				type: Boolean,
				default: false
			},
			BottomBlank: {
				type: Boolean,
				default: false
			},

			introductionSelectIndex: {
				type: Number,
				default: 1
			},
			appletTab: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				value: [0],
				checkNames: [],
				selectIndex: 0,
				indicatorStyle: `height: 50px;`,
				visible: true,
			}
		},
		/* 		watch: {
					introductionSelectIndex(newValue, oldValue) {
						console.log('jiant1');
						// 当introductionSelectIndex的值发生变化时，执行这个函数
						this.selectIndex = newValue;
						this.value[0] = newValue;
					}
				}, */
		created() {
			this.selectIndex = this.introductionSelectIndex
			this.value[0] = this.introductionSelectIndex
		},
		methods: {
			init(introductionSelectIndex) {
				this.selectIndex = introductionSelectIndex
				this.value[0] = introductionSelectIndex
			},
			clear() {
				this.selectIndex == -1
				this.visible = false
				setTimeout(() => {
					this.visible = true
				}, 10)
			},
			bindChange(e) {
				this.selectIndex = e.detail.value[0]
			},
			affirm() {
				if (this.selectIndex == 0) {
					this.$emit("affirm", '')
				} else {
					this.$emit("affirm", this.list[this.selectIndex - 1])
				}

			},
		},
	}
</script>

<style scoped lang="less">
	.picker-view {
		width: 750rpx;
		height: 400rpx;
		margin-top: 20rpx;
	}

	.item {
		line-height: 100rpx;
		text-align: center;
		font-size: 32rpx
	}

	.icon04 {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx;
	}

	.btnbox {
		height: 120rpx;
		background: #FFFFFF;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		display: flex;
		padding-right: 30rpx;
		padding-left: 30rpx;

		.clear {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 212rpx;
			height: 80rpx;
			margin-top: 20rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 2rpx solid #C9CDD4;
		}

		.affirm {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 460rpx;
			margin-top: 20rpx;
			height: 80rpx;
			background: #3370FF;
			color: #FFF;
			margin-left: 20rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
		}
	}

	.head {
		display: flex;
		height: 88rpx;
		flex-direction: column;
		position: relative;
		border-bottom: 2rpx solid #E7E7E7;

		.txt {
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 500;
			font-size: 32rpx;
			color: rgba(0, 0, 0, 0.9);
		}

		.affirm2 {
			position: absolute;
			font-weight: 400;
			font-size: 32rpx;
			color: #3370FF;
			position: absolute;
			right: 32rpx;
			top: 50%;
			transform: translateY(-50%);
		}


	}

	.region {
		::-webkit-scrollbar {
			display: none;
			/* Chrome Safari */
		}

		&-con {
			display: flex;
			justify-content: space-between;
		}

		&-list {
			max-height: 50vh;
			overflow: auto;
			width: 100%;
			box-sizing: border-box;
			padding: 20rpx 0;
			padding-top: 0;

			.item {
				line-height: 80rpx;
				height: 80rpx;
				padding: 10rpx 0;
				font-size: 28rpx;
				text-align: center;
				color: rgb(54, 53, 53);
				display: flex;
				justify-content: center;
				align-items: center;

				.txt {
					width: 80%;
					line-height: 36rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
				}

				&.on {
					color: #417FFF;
					background: #fff;
				}
			}

			&.province {
				// padding-left: 40rpx;
				background-color: #f7f7f7;
				// border-right: 1rpx solid rgb(232, 232, 232);
			}

			&.citys {

				// padding-left: 30rpx;
				// border-right: 1rpx solid rgb(232, 232, 232);
				&.on {
					background: #fff;
				}
			}

			&.area {

				// padding-left: 20rpx;
				&.on {
					background: #fff;
				}

				.icon04 {
					display: none;
					width: 24rpx;
					height: 24rpx;
					margin-left: 10rpx;
				}

				.item {
					&.on {
						position: relative;

						.icon04 {
							position: absolute;
							right: 20rpx;
							display: block;
						}
					}
				}
			}
		}
	}
</style>