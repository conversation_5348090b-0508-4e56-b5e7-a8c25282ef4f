<template>
  <view>
    <uni-popup ref="follow" background-color="#fff" :safe-area='safearea' type="dialog" style="z-index: 99;">
      <div class="applyaffirm">
        <div class="head">{{ title }}</div>

        <span v-if="exchangeRate"> 参考汇率1人民币 = {{ exchangeRate }}</span>
        <div class="filtrate">
          <div v-for="(item, index) in moreScreenList" :key="index">
            <div v-if="item.paramValueList.length != 0" class="SelectTitle">{{ item.paramName }}</div>
            <div v-if="item.paramValueList.length != 0" class="filtrateList">
              <div v-if="item?.unlimited" @click="changeOption('', item.paramKey)"
                :class="form[item.paramKey]?.length == 0 ? 'filtrateItems' : 'filtrateItem'">
                不限
              </div>
              <div v-for="(it, ind) in item.paramValueList" :key="index" @click="changeOption(it, item.paramKey)"
                :class="!MultipleChoice ? (form[item.paramKey]?.includes(it) ? 'filtrateItems' : 'filtrateItem') : (form[item.paramKey] === it ? 'filtrateItems' : 'filtrateItem')">
                {{ it }}
              </div>
            </div>
          </div>
        </div>
        <div class="btnBox">
          <div class="canle" @click="claimFn">重置</div>
          <div class="submit" @click="submit">确认</div>
        </div>
        <!-- #ifndef MP-WEIXIN -->
        <div v-if="BottomBlank" style="height: 160rpx;"></div>
        <!-- #endif -->
        <!-- #ifndef H5 -->
        <div v-if="appletTab" style="height: 160rpx;"></div>
        <!-- #endif -->
      </div>
    </uni-popup>
  </view>
</template>

<script>
export default {
  props: {
    moreScreenList: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '筛选',
    },
    MultipleChoice: {
      type: Boolean,
      default: false,
    },
    safearea: {
      type: Boolean,
      default: false,
    },
    BottomBlank: {
      type: Boolean,
      default: false
    },
    appletTab: {
      type: Boolean,
      default: false
    },
    exchangeRate: {
      type: String,
      default: ''
    }

  },
  watch: {
    moreScreenList: {
      handler(params) {
        this.form = params.reduce((acc, curr) => {
          if (this.MultipleChoice) {
            acc[curr.paramKey] = ''
          } else {
            acc[curr.paramKey] = [];
          }
          return acc;
        }, {});
      },
      deep: true // 添加深度监听
    },
  },
  data() {
    return {
      oleForm: {},
      form: {
        technologicalInnovation: [],
        listedSector: [],
        financingRounds: [],
        finalExecution: [],
        establishment: [],
        enterpriseScale: [],
        clueModel: [],
        inStrategy: [],
      },
      submitLoading: false,
    }
  },
  methods: {
    changeOption(it, key) {
      if (this.MultipleChoice) {
        if (this.form[key] == it) {
          this.form[key] = ''
        } else {
          this.form[key] = it
        }
      } else {
        if (it == '') {
          this.form[key] = []
        } else {
          const index = this.form[key].indexOf(it);
          if (index > -1) {
            this.form[key].splice(index, 1);
          } else {
            this.form[key].push(it);
          }
        }
      }

    },
    opens() {
      // React Native WebView 兼容性处理
      // #ifdef H5
      this.disableBodyScroll();
      // #endif
      this.$refs.follow.open("bottom")
    },
    entrustCloseFn() {
      // React Native WebView 兼容性处理 - 恢复滚动
      // #ifdef H5
      this.enableBodyScroll();
      // #endif
      this.$refs.follow.close()
    },
    claimFn() {
      this.form = this.moreScreenList.reduce((acc, curr) => {
        if (this.MultipleChoice) {
          acc[curr.paramKey] = ''
        } else {
          acc[curr.paramKey] = [];
        }
        return acc;
      }, {});
    },
    submit() {
      this.$emit("updatamoreScreen", this.form)
      this.entrustCloseFn()
    },

    // React Native WebView 兼容性处理方法
    // #ifdef H5
    disableBodyScroll() {
      // 检测是否在React Native WebView环境中
      const isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
      if (isInApp) {
        // 禁用body滚动
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.height = '100%';

        // 向React Native发送消息，禁用WebView滚动
        window?.ReactNativeWebView?.postMessage(
          JSON.stringify({
            type: "disableScroll",
            value: true
          })
        );
      }
    },

    enableBodyScroll() {
      // 检测是否在React Native WebView环境中
      const isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
      if (isInApp) {
        // 恢复body滚动
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
        document.body.style.height = '';

        // 向React Native发送消息，启用WebView滚动
        window?.ReactNativeWebView?.postMessage(
          JSON.stringify({
            type: "enableScroll",
            value: true
          })
        );
      }
    }
    // #endif
  }
}
</script>

<style lang="scss" scoped>
.canleIcon {
  position: absolute;
  top: 28rpx;
  right: 40rpx;
  z-index: 4;
}

.text {
  font-weight: 400;
  font-size: 30rpx;
  color: #C8C9CC;
  margin-left: 8rpx;
}

.hintBox {
  width: 622rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  box-sizing: border-box;
  padding: 52rpx 48rpx 32rpx 48rpx;
  display: flex;
  align-items: center;
  flex-direction: column;

  .p1 {
    font-weight: 500;
    font-size: 32rpx;
    color: #323233;
    margin-bottom: 16rpx;

  }

  .p2 {
    font-weight: 400;
    font-size: 28rpx;
    color: #3F4A59;
    margin-bottom: 68rpx;
    text-align: center;
  }

  .popBtn {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .affirm {
      width: 251rpx;
      height: 72rpx;
      background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .canleBt {
      width: 251rpx;
      height: 72rpx;
      background: #FFFFFF;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      border: 2rpx solid #EBEDF0;
      font-weight: 400;
      font-size: 32rpx;
      color: #323233;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.applyaffirm {
  width: 750rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;

  .filtrate {
    width: 750rpx;
    overflow-y: scroll;
    max-height: 900rpx;
    padding-left: 30rpx;
    padding-bottom: 64rpx;
    box-sizing: border-box;
    /* React Native WebView 兼容性处理 */
    -webkit-overflow-scrolling: touch;

    .SelectTitle {
      font-weight: 500;
      font-size: 30rpx;
      color: #000000;
      margin-bottom: 24rpx;
      margin-top: 6rpx;
    }

    .filtrateList {
      display: flex;
      flex-wrap: wrap;

      .filtrateItem {
        width: auto;
        height: 60rpx;
        padding: 0 18rpx;
        background: #F7F7F7;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 14rpx;
        margin-bottom: 20rpx;
      }

      .filtrateItems {
        width: auto;
        height: 60rpx;
        padding: 0 18rpx;
        background: #3370FF;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 14rpx;
        margin-bottom: 20rpx;
      }
    }
  }


  .formItem {
    width: 100%;
    height: auto;
    background: #FFFFFF;
    padding: 32rpx 30rpx;
    box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;

    .formKey {
      font-weight: 500;
      font-size: 30rpx;
      color: #1D2129;
      margin-bottom: 16rpx;
    }

    .remark {
      font-weight: 400;
      font-size: 28rpx;
      color: #86909C;
    }
  }

  .head {
    width: 750rpx;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 32rpx;
    margin-top: 24rpx;
    color: #323233;

  }

  .btnBox {
    width: 750rpx;
    height: 128rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -8rpx 8rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    padding: 24rpx 30rpx;
    display: flex;
    box-sizing: border-box;
    justify-content: space-between;

    .canle {
      width: 330rpx;
      height: 80rpx;
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      border: 2rpx solid #B7BFC7;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #3F4A59;
    }

    .submit {
      width: 330rpx;
      height: 80rpx;
      background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%);
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #FFFFFF;
    }
  }
}
</style>
