<template>
	<view class="region">
		<view class="head">
			<span class="txt">请选择</span>
		</view>
		<view class="region-con">
			<view class="region-list province">
				<view class="item" :class="check.province.name == item.name ? 'on' : ''" @click="provinceEvent(item)"
					v-for="(item, i) in province" :key="i">
					<view class="txt">{{ item.name_new }}</view>
				</view>
			</view>
			<view class="region-list citys" :class="check.province.name ? 'on' : ''">
				<view class="item" :class="check.citys.name == item.name ? 'on' : ''" @click="citysEvent(item)"
					v-for="(item, i) in citys" :key="i">
					<view class="txt">{{ item.name }}</view>
				</view>
			</view>
			<view class="region-list area" :class="check.province.name ? 'on' : ''">
				<view class="item" :class="check.area.name == item.name ? 'on' : ''" @click="areaEvent(item)"
					v-for="(item, i) in area" :key="i">
					<view class="txt">{{ item.name }}</view>
				</view>
			</view>
		</view>
		<view class="btnbox">
			<view @click="clear" class="clear">重置</view>
			<view class="affirm" @click="affirm">确认</view>
		</view>
		<!-- #ifndef MP-WEIXIN -->
		<div v-if="BottomBlank" style="height: 160rpx;"></div>
		<!-- #endif -->
		<!-- #ifndef H5 -->
		<div v-if="appletTab" style="height: 160rpx;"></div>
		<!-- #endif -->
	</view>
</template>

<script>
export default {
	props: {
		active: {
			type: Number,
			default: null
		},
		token: {
			type: String,
			default: null
		},
		cityName: {
			type: String,
			default: null
		},
		unrestricted: {
			type: Boolean,
			default: false
		},
		BottomBlank: {
			type: Boolean,
			default: false
		},
		appletTab: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			province: [],
			citys: [],
			area: [],
			type: 1, // 1省2市3县区
			check: {
				province: {
					name: '',

				},
				citys: {
					name: '',

				},
				area: {
					name: '',
				}
			},
			param: {}
		}
	},
	methods: {
		clear() {
			this.citys = [];
			this.area = [];
			this.check = {
				province: {
					name: "",
				},
				citys: {
					name: "",
				},
				area: {
					name: "",
				}
			}
		},

		affirm() {
			//820000 澳门
			//810000 香港
			//710000 台湾
			if (this.unrestricted) {
				this.$emit("affirm", this.check)
			} else {
				if ((!this.check.citys.code && this.check.province.code) && (this.check.province.code != 820000 && this
					.check.province.code != 810000 && this.check.province.code != 710000)) {
					return uni.showToast({
						title: '请选择市级或区级',
						icon: 'none'
					})
				}
				this.$emit("affirm", this.check)
			}
		},
		init() {
			if (this.province.length == 0) {
				this.mapList(1);
			}
		},
		mapList(type, id) {
			let param = {};
			if (this.cityName) {
				let list = this.cityName.split('/')
				param.province = list[0]
				if (list.length > 1) {
					param.city = list[1]
				}
				if (list.length > 2) {
					param.area = list[2]
				}
			}
			let {
				province,
				city,
				area
			} = param;


			let data = {
				type
			}
			id && (data.parentId = id)
			this.$api.administrativeDivision({
				data,
				token: this.token
			}).then(res => {
				if (type == 1) {
					this.province = res.result;
					this.province.map(e => {
						e.name_new = e.name.replace("自治区", '')
						e.name_new = e.name_new.replace("壮族", '')
						e.name_new = e.name_new.replace("回族", '')
						e.name_new = e.name_new.replace("维吾尔", '')
						e.name_new = e.name_new.replace("特别行政区", '')
						if (province == e.name) {
							this.check.province.name = e.name
							this.check.province.code = e.code
							this.mapList(2, e.id);
						}
						return e;
					})
				} else if (type == 2) {
					res.result.map(e => {
						if (e.name == city) {
							this.check.citys.name = e.name;
							this.check.citys.code = e.code;
							this.mapList(3, e.id);
						}
					})
					this.citys = res.result;
				} else if (type == 3) {
					res.result.map(e => {
						if (e.name == area) {
							this.check.area.name = e.name;
							this.check.area.code = e.code;
						}
					})
					this.area = res.result;

				}

			})
		},
		//点击省
		provinceEvent(item) {
			this.mapList(2, item.id);
			this.check.province.name = item.name;
			this.check.province.code = item.code;
			this.area = []
			this.check.citys.name = '';
			this.check.citys.code = '';
			this.check.area.name = '';
			this.check.area.code = '';
		},
		//点击市
		citysEvent(item) {
			this.mapList(3, item.id);
			this.check.citys.name = item.name;
			this.check.citys.code = item.code;
			this.check.area.name = '';
			this.check.area.code = '';
		},
		//点击区
		areaEvent(item) {
			this.check.area.name = item.name;
			this.check.area.code = item.code;
		},
	},
	created() {
		if (this.province.length == 0) {
			this.init();
		}
	}
}
</script>

<style scoped lang="less">
.btnbox {
	height: 120rpx;
	background: #FFFFFF;
	border-radius: 0rpx 0rpx 0rpx 0rpx;
	display: flex;
	padding-right: 30rpx;
	padding-left: 30rpx;

	.clear {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 212rpx;
		height: 80rpx;
		margin-top: 20rpx;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		border: 2rpx solid #C9CDD4;
	}

	.affirm {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 460rpx;
		margin-top: 20rpx;
		height: 80rpx;
		background: #3370FF;
		color: #FFF;
		margin-left: 20rpx;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
	}
}

.head {
	display: flex;
	height: 88rpx;
	flex-direction: column;

	.txt {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}


}

.region {
	::-webkit-scrollbar {
		display: none;
		/* Chrome Safari */
	}

	&-con {
		display: flex;
		justify-content: space-between;
	}

	&-list {
		max-height: 50vh;
		overflow: auto;
		width: 33.333333333333333%;
		box-sizing: border-box;
		padding: 20rpx 0;
		padding-top: 0;
		/* React Native WebView 兼容性处理 */
		-webkit-overflow-scrolling: touch;

		.item {
			line-height: 80rpx;
			height: 80rpx;
			padding: 10rpx 0;
			font-size: 28rpx;
			text-align: center;
			color: rgb(54, 53, 53);
			display: flex;
			justify-content: center;
			align-items: center;

			.txt {
				width: 144rpx;
				line-height: 36rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}

			&.on {
				color: #417FFF;
				background: #fff;
			}
		}

		&.province {
			// padding-left: 40rpx;
			background-color: #f7f7f7;
			// border-right: 1rpx solid rgb(232, 232, 232);
		}

		&.citys {

			// padding-left: 30rpx;
			// border-right: 1rpx solid rgb(232, 232, 232);
			&.on {
				background: #fff;
			}
		}

		&.area {

			// padding-left: 20rpx;
			&.on {
				background: #fff;
			}

			.icon04 {
				display: none;
				width: 24rpx;
				height: 24rpx;
				margin-left: 10rpx;
			}

			.item {
				&.on {
					position: relative;

					.icon04 {
						position: absolute;
						right: 20rpx;
						display: block;
					}
				}
			}
		}
	}
}
</style>