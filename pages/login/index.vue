<template>
	<page-meta page-style="background-color: #FAFCFF"></page-meta>
	<view>
		<view class="box">
			<view class="t1">快捷登录</view>
			<view class="t2">未注册的手机号将自动注册并登录</view>
			<view class="t3">
				<uni-data-checkbox multiple v-model="checkbox" :localdata="hobby"></uni-data-checkbox>
				<view class="agreement">阅读并同意
					<!-- <span>《平台服务协议》</span>
				和 -->
					<span @click="go('agreement')">《用户协议》</span>
				</view>
			</view>
			<button v-if="this.checkbox.length == 0" class="ClickLogin" type="primary"
				@click="Unchecked">本机一键登录</button>
			<quickLogin :blue="true" v-else></quickLogin>
			<view v-if="showPassword || showPassword2" class="ClickLogin2">
				<span @click="go('passwordLogin')">使用账号密码登录</span>
				<!-- 			<span @click="go('verificationcodeLogin')">使用验证码登录/注册</span> -->
			</view>
			<view class="else">
				<span class="cancelLogin" @click="NoLogin()">取消登录</span>
				<!-- 			<span @click="go('verificationcodeLogin')">使用验证码登录/注册</span> -->
			</view>
		</view>
		<view class="rightClick" @click="changePassword">
			<uni-popup ref="PasswordDialog" type="dialog" :mask-click="false">
				<div class="Passwordbox">
					<h1 style="margin-top: 32rpx;margin-bottom: 32rpx;">是否展示账号密码登录</h1>
					<div @click="changeShowPassword(item.value)" class="ShowTimeItem" v-for="(item, index) in sex"
						:key="item.id">
						<div :class="showPassword2 == item.value ? 'circle-with-dot' : 'circle'"></div>
						{{ item.text }}
					</div>
					<button class="bs3" type="primary" @click="PasswordAffirm">确认</button>
				</div>
			</uni-popup>
		</view>
		<view class="clickArea" @click="changeEnv">
			<uni-popup ref="inputDialog" type="dialog" :mask-click="false">
				<div class="popbox">
					<h2 style="margin-top: 4rpx;">环境切换</h2>
					<view class="clickBox">
						<view class="tips">
							tips: 切换环境成功后，点击右上角的···，重新进入小程序即可生效
						</view>
						<view class="env">
							<view> 当前环境：</view>{{ env }}
						</view>
						<view class="clickBoxItem">
							点击切换环境
						</view>
						<uni-data-select style="width: 500rpx;margin-top:8%;" :clear="false" v-model="value"
							@change="selectEnv" :localdata="range"></uni-data-select>
					</view>
					<div class="popbtns">
						<button class="bs" type="default" @click="cancel">取消</button>
						<button class="bs2" type="primary" @click="save">确认</button>
					</div>
				</div>
			</uni-popup>
		</view>
	</view>
</template>

<script>
import quickLogin from '../login/components/quicklogin.vue'
export default {

	data() {
		return {
			checkbox: [],
			hobby: [{
				text: '',
				value: 0
			}],
			passwordNum: 0,
			sex: [{
				text: '是',
				value: true
			}, {
				text: '否',
				value: false
			},],
			range: [{
				value: 0,
				text: "开发环境"
			},
			{
				value: 1,
				text: "测试环境"
			},
			{
				value: 2,
				text: "预发环境"
			},
			{
				value: 3,
				text: "正式环境"
			},
			],
			value: 0,
			env: 'https://pangutest.idicc.cn',
			touchNum: 0,
			showPassword: false,
			showPassword2: false,

		}
	},
	components: {
		quickLogin,
	},
	/* onShow() {
	  wx.hideHomeButton()
	}, */
	onLoad(options) {
		if (options.teamCode) {
			uni.setStorageSync('teamCode', options.teamCode)
		}
		if (uni.getStorageSync('consent')) {
			this.checkbox = [0]
		}
		this.showPassword2 = uni.getStorageSync('showPasswordBtn') || false
		this.getIsShowPassword()
	},
	methods: {
		PasswordAffirm() {
			this.$refs.PasswordDialog.close()
		},
		changeShowPassword(value) {
			this.showPassword2 = value
			uni.setStorageSync('showPasswordBtn', value);
			uni.showToast({
				title: '设置成功！',
				icon: 'none'
			})
		},
		changePassword() {
			if (this.resetTimer) {
				clearTimeout(this.resetTimer)
			}
			this.passwordNum++
			if (this.passwordNum >= 5) {
				this.$refs.PasswordDialog.open()
				this.passwordNum = 0
			}
			this.resetTimer = setTimeout(() => {
				this.passwordNum = 0
			}, 1000)
		},
		getIsShowPassword() {
			this.$api.showPasswordAPI({
				method: 'get'
			}).then((res) => {
				if (res.code == "SUCCESS") {
					this.showPassword = res.result?.miniProgramPasswordLogin
					//this.showPassword = false
				}
			})
		},
		Unchecked() {
			uni.showToast({
				title: `请勾选用户协议`,
				icon: 'error'
			})
		},
		go(url) {
			if (url == "agreement") {
				uni.navigateTo({
					url: `/pages/login/components/${url}`
				})
			} else {
				uni.reLaunch({
					url: `/pages/login/components/${url}`
				})
			}
		},
		NoLogin() {
			uni.reLaunch({
				url: `/pages/repository/capacity`
			})
		},
		selectEnv(e) {
			if (this.value == 0) {
				uni.setStorageSync('portUrl', "https://pangudev.idicc.cn");
				uni.setStorageSync('h5Url', "https://staticdev.idicc.cn/static/ai/index.html#/");
			} else if (this.value == 1) {
				uni.setStorageSync('portUrl', "https://pangutest.idicc.cn");
				uni.setStorageSync('h5Url', "https://statictest.idicc.cn/static/ai/index.html#/");
			} else if (this.value == 2) {
				uni.setStorageSync('portUrl', "https://pangustg.idicc.cn");
				uni.setStorageSync('h5Url', "https://staticstg.idicc.cn/static/ai/index.html#/");
			} else {
				uni.setStorageSync('portUrl', "https://pangu.idicc.cn");
				uni.setStorageSync('h5Url', "https://static.idicc.cn/static/ai/index.html#/");
			}
			uni.setStorageSync('value', e);
			uni.showToast({
				title: '切换成功',
				icon: 'none'
			})
		},
		changeEnv() {
			//return
			this.value = uni.getStorageSync("value")
			this.env = uni.getStorageSync('portUrl')
			this.touchNum++
			setTimeout(() => {

				// if(this.touchNum == 1)
				// }
				if (this.touchNum >= 3) {
					this.$refs.inputDialog.open()
					this.touchNum = 0


				}


			}, 250)

			// uni.setStorageSync('env', 'test')
			// uni.showToast({
			// 	title: `切换环境成功`,
			// 	icon: 'success'
			// })
		},
		save() {
			this.$refs.inputDialog.close()

		},
		cancel() {
			this.$refs.inputDialog.close()
		},
	}
}
</script>

<style lang="scss" scoped>
.ShowTimeItem {
	width: 60%;
	//height: 70rpx;
	display: flex;
	align-items: center;
	//justify-content: space-between;
	margin-bottom: 24rpx;
	color: #3F4A59;
	font-size: 28rpx;
	font-weight: normal;
}

.circle {
	width: 32rpx;
	height: 32rpx;
	background-color: #ffffff;
	border: 2px solid #CED4DB;
	border-radius: 50%;
	box-sizing: border-box;
	margin-right: 16rpx;
	margin-top: 4rpx;
}

.circle-with-dot {
	position: relative;
	width: 32rpx;
	height: 32rpx;
	background-color: #ffffff;
	border: 2px solid #CED4DB;
	border-radius: 50%;
	box-sizing: border-box;
	margin-right: 16rpx;
	margin-top: 4rpx;
}

.circle-with-dot::after {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 8px;
	height: 8px;
	background-color: #3370FF;
	border-radius: 50%;
}

.checkboxs {
	width: 80%;
	margin-top: 60rpx;
	margin-bottom: 60rpx;
}

.Passwordbox {
	width: 500rpx;
	position: relative;
	padding: 24rpx;
	flex-direction: column;
	border-radius: 20rpx;
	background-color: #fff;
	box-sizing: border-box;
	height: auto;
	display: flex;
	align-items: center;
}

.rightClick {
	width: 100px;
	height: 100px;
	border-radius: 0 100px 0 0;
	position: fixed;
	z-index: 11;
	right: 0;
	bottom: 0;
}

.bs3 {
	width: 80%;
	height: 72rpx;
	margin-top: 12rpx;
	background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	font-weight: 400;
	font-size: 32rpx;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
}

.ClickLogin2 {
	width: 690rpx;
	height: 88rpx;
	box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	border: 2rpx solid #3370FF;
	background-color: #FFFFFF;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 30rpx;
	color: #3370FF;
	line-height: 40rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 32rpx;
	margin-left: 30rpx;
}

::v-deep {
	button:after {
		border: none !important;
	}
}

::v-deep {
	.uni-data-checklist .checklist-group .checklist-box {
		display: flex;
		flex-direction: row;
		align-items: center;
		position: relative;
		margin: 8rpx 0;
	}
}


.box {

	.t1 {
		margin-top: 64rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 64rpx;
		color: #1D2129;
		line-height: 75rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.t2 {
		margin-top: 20rpx;
		margin-bottom: 128rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 26rpx;
		color: #969799;
		line-height: 30rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.t3 {
		padding: 0 30rpx;
		display: flex;
		//margin-top: 160rpx;

		.agreement {
			//margin-left: 14rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #323233;
			line-height: 40rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-top: 3rpx;

			label {
				color: #165DFF;
			}
		}
	}

	.else {
		display: flex;
		//justify-content: space-between;
		justify-content: center;
		margin-top: 32rpx;
		padding: 0 80rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #B7BFC7;
		line-height: 40rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;

		.cancelLogin {
			color: #467fff;
		}

	}

	.ClickLogin {
		width: 690rpx;
		height: 88rpx;
		margin-top: 20rpx;
		background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
		box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		font-weight: 400;
		font-size: 30rpx;
		display: flex;
		align-items: center;
		background-color: #FFFFFF;
		justify-content: center;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}

.popbox {
	width: 80%;
	height: 550rpx;
	display: flex;
	align-items: center;
	//justify-content: center;
	flex-direction: column;
	background-color: #fff;
	position: relative;
	padding: 24rpx;
	border-radius: 20rpx;
	margin-left: 7%;

	h2 {
		font-size: 32rpx;
		color: #000000;

	}

	.popbtns {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 60rpx;
		position: absolute;
		bottom: 24rpx;

		.bs {
			width: 40%;
			height: 72rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 2rpx solid #EBEDF0;
			font-weight: 400;
			font-size: 32rpx;
			color: #323233;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.bs2 {
			width: 40%;
			height: 72rpx;
			background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			font-weight: 400;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

.clickArea {
	width: 100px;
	height: 100px;
	border-radius: 0 100px 0 0;
	// background: #ff6816;
	position: fixed;
	z-index: 11;
	left: 0;
	bottom: 0;
}

.clickBox {
	width: 100%;
	height: 250rpx;
	// display: flex;
	// flex-wrap: wrap;
	// justify-content: center;
	// align-items: center;
	// border: 1px solid #c5c5c5;
	// padding: 15px;

}

.tipBox {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;

}

.tips {
	width: 100%;
	font-size: 15px;
	color: #ff4646;
	padding-bottom: 20rpx;
	padding-top: 15px;
}

.env {
	width: 100%;
	color: #000000;
	padding-bottom: 20rpx;

}

.clickBoxItem {
	padding: 20px 0 10px;
	width: 100%;
	color: #000000;

}
</style>