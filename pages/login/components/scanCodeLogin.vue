<template>
	<page-meta page-style="background-color: #FAFAFA"></page-meta>
	<view class="bar-con">
		<view class="tabBarCon">
			<view id="tabBar" class="tabBar">
				<view class="nav" :style="`
					    margin-top: ${statusBarHeight}px;
					`">
					<view class="black" @click="goBack()">
						<image class="returnFn" src="../../../static/AboutAi/returnFn.png"></image>
					</view>
					<view class="nav-title">登录</view>
				</view>
			</view>

		</view>
	</view>
	<div v-if="logintype==1" class="box">
		<image class="arsy" src="https://static.idicc.cn/cdn/aiChat/applet/arsy.png"></image>
		<div class="p1">哒达招商</div>
		<div class="p2">即将在电脑上登录哒达招商，确认是否本人操作</div>
		<button class="greenBtn" open-type="getPhoneNumber" type="primary" @getphonenumber="getcode">本机一键登录</button>
	</div>
	<div v-if="logintype==2" class="box">
		<image class="landsuccessfully" src="https://static.idicc.cn/cdn/aiChat/applet/landsuccessfully.png">
		</image>
		<div class="p1">登录成功</div>
		<div class="p2">您可以在电脑端操作啦</div>
		<button class="greenBtn" @click="goBack()" type="primary">完成</button>
	</div>
	<div v-if="logintype==3" class="box">
		<image class="landsuccessfully" src="https://static.idicc.cn/cdn/aiChat/applet/error.png"></image>
		<div class="p3">二维码已失效，请重新扫码登录</div>
		<button class="greenBtn" @click="goBack()" type="primary">退出</button>
	</div>
	<div v-if="logintype==4" class="box" style="margin-top: 306rpx;">
		<image style="margin-bottom: 4rpx;" class="landsuccessfully" src="/static/user/errorIcon.png"></image>
		<div class="p4">登陆失败</div>
		<div class="p5">该微信号与选择手机号不匹配，请重新尝试<br>有疑问请联系小艾客服</div>
		<div class="p6">—— 扫码添加小艾客服 ——</div>
		<image src="https://static.idicc.cn/cdn/aiChat/applet/serviceCode2.png" show-menu-by-longpress="true"
			class="serviceImg"></image>
		<button style="margin-top: 100rpx;;" class="greenBtn" @click="goBack()" type="primary">返回</button>
	</div>
</template>

<script>
	export default {
		props: {
			blue: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				// 状态栏高度
				statusBarHeight: 0,
				logintype: 1,
				loginId: '',
			}
		},
		created() {
			this.statusBarHeight = uni.getWindowInfo().statusBarHeight
		},
		onLoad(options) {
			console.log(options, 'options');
			let str = decodeURIComponent(options.scene);
			console.log(str);
			//const match = str.match(/uniqueKey=(\d+)/);
			//this.loginId = match ? match[1] : null;
			this.loginId = str
			console.log(this.loginId);
			this.getStatusLogin()
		},
		methods: {
			goBack() {
				wx.exitMiniProgram()
			},
			getStatusLogin() {
				this.$api.getStatusLoginAPI({
					method: 'get',
					data: {
						uniqueKey: this.loginId,
					},
					token: '',
				}).then(res => {
					if (res.result.status == "SCAN_EXPIRE" || res.result.status == "AUTHORIZE_EXPIRE" || res.result
						.status == "AUTHORIZE_EXIT" || res.result.status == "TOKEN_COMPLETE") {
						this.logintype = 3
					} else {
						this.updataStatus()
					}
				})
			},
			updataStatus() {
				this.$api.updateStatusLoginAPI({
					method: 'POST',
					data: {
						uniqueKey: this.loginId,
						status: 1,
					},
					token: '',
				}).then(res => {})
			},
			getcode(e) {
				const that = this

				if (e.detail.errMsg == "getPhoneNumber:ok") {
					uni.showToast({
						title: '授权成功，正在登录...',
						icon: 'none'
					})
					wx.login({
						success(res2) {
							that.$api.updateStatusLoginAPI({
								method: 'POST',
								data: {
									phoneCode: e?.detail.code,
									uniqueKey: that.loginId,
									jsCode: res2.code,
									status: 3,
								},
								token: '',
							}).then(res => {
								if (res.code == 'SUCCESS') {
									that.logintype = 2
								}
							}).catch(err => {
								if (err.code == '41005') {
									setTimeout(() => {
										that.logintype = 4
									}, 500)
								}
							})
						}
					});
				} else {
					return uni.showToast({
						title: '获取授权失败',
						icon: 'none'
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		margin-top: 380rpx;
		width: 750rpx;
		display: flex;
		align-items: center;
		flex-direction: column;

		.arsy {
			width: 160rpx;
			height: 160rpx;
			margin-bottom: 32rpx;
		}

		.landsuccessfully {
			width: 260rpx;
			height: 260rpx;
			margin-bottom: 16rpx;
		}

		.p1 {
			font-size: 36rpx;
			font-weight: 600;
			color: rgba(0, 0, 0, 0.9);
		}

		.p2 {
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.6);
			font-weight: normal;
			margin-top: 8rpx;
		}

		.p3 {
			font-size: 36rpx;
			font-weight: 600;
			color: rgba(0, 0, 0, 0.6);
		}

		.p4 {
			font-size: 32rpx;
			font-weight: 600;
			color: #EA595D;
		}

		.p5 {
			font-size: 24rpx;
			font-weight: normal;
			color: rgba(0, 0, 0, 0.6);
			margin-top: 8rpx;
			margin-bottom: 80rpx;
			width: 100%;
			text-align: center;
			line-height: 40rpx;
		}

		.p6 {
			font-size: 24rpx;
			font-weight: normal;
			color: rgba(0, 0, 0, 0.6);
			margin-bottom: 18rpx;
		}

		.serviceImg {
			width: 280rpx;
			height: 280rpx;
		}
	}

	.greenBtn {
		width: 580rpx;
		height: 100rpx;
		border-radius: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: normal;
		margin-top: 356rpx;
		background: #10C673;
	}

	.returnFn {
		width: 48rpx;
		height: 48rpx;
	}

	.headimg {
		width: 750rpx;
		height: 1634rpx;
		position: absolute;
		z-index: 7;
	}

	.bar-con {
		width: 100vw;
		//background: linear-gradient(180deg, #3548B6 0%, #417FFF 100%);
	}

	.tabBarCon {
		height: 200rpx;
		width: 750rpx;
		//height: 100%;
		box-sizing: border-box;
		padding-bottom: 22rpx;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 101;
		//background: linear-gradient(180deg, #3548B6 0%, #417FFF 100%);
	}

	.tabBar {

		.nav {
			height: 88rpx;
			line-height: 88rpx;
			width: 100%;
			display: flex;
			position: relative;

			.black {
				margin-left: 32rpx;
				position: absolute;
				height: 88rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				//z-index: 999;
				// width: 48rpx;
				// height: 48rpx;
			}

			&-title {
				//position: absolute;
				width: 100%;
				text-align: center;
				font-size: 34rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				color: #000000;
				background-color: transparent;
			}
		}
	}

	.contrastImg {
		width: 662rpx;
		height: 832rpx;
		position: relative;
	}

	.close {
		width: 80rpx;
		height: 80rpx;
		position: absolute;
		top: 30rpx;
		right: 0;
	}

	::v-deep {
		button:after {
			border: none !important;
		}
	}


	.ClickLogin {
		width: 690rpx;
		height: 88rpx;
		margin-top: 20rpx;
		background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #3370FF;
		box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		font-weight: 400;
		font-size: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.ClickLogins {
		width: 690rpx;
		height: 88rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		border: 2rpx solid #3370FF;
		background-color: #FFFFFF;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 30rpx;
		color: #3370FF;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 32rpx;
	}
</style>