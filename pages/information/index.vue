<template>
	<page-meta page-style="background-color: #F3F5F9"></page-meta>
	<div class="box" :style="`padding-top: ${statusBarHeight}px;`">
		<image class="messageTop" src="https://static.idicc.cn/cdn/aiChat/applet/home/<USER>"></image>
		<div class="tabList">
			<image @click="oneRead" src="https://static.idicc.cn/cdn/aiChat/applet/home/<USER>" class="read">
			</image>
			<div @click="changeTab(1)" :class="tabId == 1 ? 'tabItems' : 'tabItem'">
				服务消息<div v-if="unSvcMsgCount != 0" class="UnreadQuantity">
					{{ unSvcMsgCount > 99 ? '99+' : unSvcMsgCount }}</div>
				<div v-if="tabId == 1" class="pitchOn"></div>
			</div>
			<div @click="changeTab(2)" :class="tabId == 2 ? 'tabItems' : 'tabItem'">
				订单消息<div v-if="unOrderMsgCount != 0" class="UnreadQuantity">
					{{ unOrderMsgCount > 99 ? '99+' : unOrderMsgCount }}
				</div>
				<div v-if="tabId == 2" class="pitchOn"></div>
			</div>
		</div>
		<div v-if="token" class="contentBox">
			<ServiceMessage v-if="tabId == 1" @RefreshDot='RefreshDot' ref="ServiceMessage"></ServiceMessage>
			<informationServe v-if="tabId == 2" @RefreshDot='RefreshDot' ref="informationServe"></informationServe>
		</div>
		<div v-else class="contentBox">
			<div class="nodatabox">
				<image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
				<span class="span">暂无内容</span>
			</div>
		</div>
	</div>
</template>

<script>
	import informationServe from './components/informationServe.vue'
	import ServiceMessage from './components/ServiceMessage.vue'
	export default {
		components: {
			informationServe,
			ServiceMessage
		},
		data() {
			return {
				statusBarHeight: 0,
				tabId: 1,
				indentLsit: [],
				identity: 1,
				unOrderMsgCount: 0,
				unSvcMsgCount: 0,
				token: '',
			}
		},
		onLoad(options) {
			this.token = uni.getStorageSync('token')
			this.statusBarHeight = uni.getStorageSync("statusBarHeight")
			if (uni.getStorageSync('identity')) {
				this.identity = uni.getStorageSync('identity')
				if (this.identity == 2) {
					this.tabId = 2
				}
			}
		},
		onShow() {
			const params = uni.getStorageSync('tabParams');
			if (params) {
				this.tabId = params
				uni.removeStorageSync('tabParams');
			}
			this.token = uni.getStorageSync('token')
			if (this.token) {
				this.RefreshDot()
				this.$nextTick(() => {
					if (this.tabId == 1) {
						this.$refs.ServiceMessage.RefreshList(true)
					} else {
						this.$refs.informationServe.RefreshList(true)
					}
				})
			}
		},
		methods: {
			RefreshDot() {
				let data = {
					msgType: '',
					pageSize: 10,
					msgReceive: {
						userId: uni.getStorageSync('userId'),
						userIdentity: uni.getStorageSync('userIdentity'),
					}
				}
				this.$api.messagePageAPI({
					data,
					method: "POST",
					loading: 'no',
				}).then((res) => {
					if (res.result.records.length != 0) {
						this.unOrderMsgCount = res.result.records[0]?.unOrderMsgCount
						this.unSvcMsgCount = res.result.records[0]?.unSvcMsgCount
						if (this.unOrderMsgCount == 0 && this.unSvcMsgCount == 0) {
							uni.hideTabBarRedDot({
								index: 1
							})
						} else {
							uni.showTabBarRedDot({
								index: 1
							})
						}
					}
				})
			},
			oneRead() {
				let data = {
					msgIds: [],
					userId: uni.getStorageSync('userId')
				}
				this.$api.receiveAPI({
					data,
					method: "POST",
				}).then((res) => {
					uni.showToast({
						title: "所有消息已全部标记已读",
						icon: "none",
					});
					setTimeout(() => {
						this.$nextTick(() => {
							this.RefreshDot()
							if (this.tabId == 1) {
								this.$refs.ServiceMessage.updata()
							} else {
								this.$refs.informationServe.updata()
							}
						})
					}, 800)
				})
			},
			changeTab(id) {
				if (this.tabId != id) {
					this.tabId = id
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.nodatabox {
		display: flex;
		align-items: center;
		flex-direction: column;
		font-weight: 400;
		font-size: 30rpx;
		color: #3f4a59;
		margin-top: 100rpx;
		position: relative;
		//box-sizing: border-box;

		.nodata {
			width: 750rpx;
			height: 634rpx;
			min-width: 750rpx;
			min-height: 634rpx;
			//margin-left: -30rpx;
		}

		.span {
			position: absolute;
			top: 472rpx;
			//margin-left: -30rpx;
		}
	}

	.box {
		width: 750rpx;
		height: 100vh;
		position: relative;

		.contentBox {
			width: 750rpx;
			height: calc(100vh - 160rpx);
			//overflow: scroll;
			background: #F3F5F9;
			border-radius: 32rpx 32rpx 0rpx 0rpx;
			position: relative;
			z-index: 3;
			margin-top: 8rpx;
			padding-top: 6rpx;
			display: flex;
			justify-content: center;
		}

		.messageTop {
			position: absolute;
			top: 0;
			right: 0;
			height: 230rpx;
			width: 750rpx;
			z-index: 1;
		}

		.tabList {
			width: 750rpx;
			height: 88rpx;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			z-index: 2;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;

			.read {
				position: absolute;
				width: 48rpx;
				height: 48rpx;
				min-width: 48rpx;
				min-height: 48rpx;
				left: 30rpx;
			}

			.tabItem {
				width: 200rpx;
				height: 88rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 400;
				font-size: 32rpx;
				color: rgba(255, 255, 255, 0.9);
				position: relative;

				.UnreadQuantity {
					min-width: 32rpx;
					height: 32rpx;
					background: #EE0A24;
					border-radius: 16rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-weight: 500;
					font-size: 22rpx;
					color: #FFFFFF;
					position: absolute;
					right: 12rpx;
					top: 12rpx;
					padding-left: 4rpx;
					padding-right: 4rpx;
					box-sizing: border-box;
				}
			}

			.tabItems {
				width: 200rpx;
				height: 88rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 500;
				font-size: 36rpx;
				color: #FFFFFF;
				position: relative;

				.UnreadQuantity {
					min-width: 32rpx;
					height: 32rpx;
					background: #EE0A24;
					border-radius: 16rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-weight: 500;
					font-size: 22rpx;
					color: #FFFFFF;
					position: absolute;
					right: 12rpx;
					top: 12rpx;
					padding-left: 4rpx;
					padding-right: 4rpx;
					box-sizing: border-box;
				}

				.pitchOn {
					width: 36rpx;
					height: 6rpx;
					background: #FFFFFF;
					border-radius: 4rpx 4rpx 4rpx 4rpx;
					position: absolute;
					left: 50%;
					bottom: 12rpx;
					transform: translateX(-50%);
				}
			}
		}
	}
</style>