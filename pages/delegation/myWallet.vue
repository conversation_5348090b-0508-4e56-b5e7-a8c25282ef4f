<template>
  <div class="myWallet">
    <!-- 我的钱包页面内容 -->
    <page-meta page-style="background-color: #FAFCFF"></page-meta>
    <div style="height: 168rpx;background-color: #dde6ff;"> </div>
    <tabBar title="我的钱包" />
    <div class="headimg" />
    <div class="box">
      <wallet ref="wallet"></wallet>
      <!-- 钱包组件 -->

      <!-- <div class="walletContainer">
       
      </div> -->
    </div>
  </div>

</template>

<script>
import wallet from '@/components/wallet.vue'
import tabBar from '@/components/tabBar/index3.vue'

export default {
  name: 'MyWallet',
  components: {
    wallet,
    tabBar
  },
  data() {
    return {
      // 钱包相关数据
    }
  },
  onLoad() {
    // 页面加载时初始化钱包数据
    this.initWallet()
  },
  methods: {
    onScroll() {
      // 处理滚动事件
    },
    // 初始化钱包
    initWallet() {
      if (this.$refs.wallet && this.$refs.wallet.init) {
        this.$refs.wallet.init()
      }
    },

    // 刷新钱包数据
    refreshWallet() {
      this.initWallet()
    }
  }
}
</script>

<style lang="scss" scoped>
// @import './styles/common.scss';

.myWallet {
  min-height: 100vh;
  background-color: #FAFCFF;
}

.walletContainer {
  padding: 0;
  width: 100%;
  height: 100%;
}
</style>