<template>
  <div class="delegationCreate">
    <!-- 委托发起页面内容 -->
    <page-meta page-style="background-color: #FAFCFF"></page-meta>

    <!-- 头部装饰图 -->
    <div class="headimg" />

    <scroll-view scroll-y="true" class="repository" @scroll="onScroll()">
      <div>
        <image src="https://static.idicc.cn/cdn/aiChat/applet/attract3.png" class="attractImg"></image>

        <div class="orderTicket">
          <!-- 表单头部 -->
          <div class="attractFormHead">
            <image v-if="DelegateType == 1" src="https://static.idicc.cn/cdn/aiChat/applet/attractFormHead0.png"
              class="btnBgImg">
            </image>
            <image v-else src="https://static.idicc.cn/cdn/aiChat/applet/attractFormHead1.png" class="btnBgImg">
            </image>
          </div>

          <!-- 切换标签 -->
          <div class="formtitle">
            <div v-if="DelegateType == 1" class="pitchonOne" @click="changType(1)">
              企业对接需求
              <div class="line"></div>
            </div>
            <div @click="changType(1)" v-else class="topBtm">
              企业对接需求
            </div>

            <div class="pitchonIntelligence" v-if="DelegateType == 2" @click="changType(2)">
              招商需求发布
              <div class="line"></div>
            </div>
            <div @click="changType(2)" v-else class="topBtm2">
              招商需求发布
            </div>
          </div>

          <!-- 招商需求发布表单 -->
          <div class="attractForm" v-show="DelegateType == 2">
            <div class="attractFormInput2">
              <uni-forms ref="demandvaliForm" :modelValue="demandForm" label-position="left" label-width="100">
                <uni-forms-item required label="招商需求方">
                  <uni-easyinput trim="all" maxlength="50" :clearable="false" v-model="demandForm.demander"
                    placeholder="请输入">
                  </uni-easyinput>
                </uni-forms-item>

                <uni-forms-item label="招商需求企业所属产业链">
                  <div v-if="demandForm.chainNames.length == 0" class="unselected" @click="SelectIndustryChain">
                    请选择
                  </div>
                  <div v-else class="selected" @click="SelectIndustryChain">
                    <span class="text">{{ demandForm.chainNames.join('、') }}</span>
                  </div>
                </uni-forms-item>

                <uni-forms-item label="招商需求企业所属区域">
                  <div v-if="demandForm.addressName.length == 0" class="unselected" @click="SelectCity">
                    请选择
                  </div>
                  <div v-else class="selected" @click="SelectCity">
                    <span class="text">{{ demandForm.addressName.join('、') }}</span>
                  </div>
                </uni-forms-item>

                <uni-forms-item label="招商需求企业类型">
                  <div v-if="demandForm.enterpriseType.length == 0" class="unselected" @click="SelectEnterpriseType">
                    请选择
                  </div>
                  <div v-else class="selected" @click="SelectEnterpriseType">
                    <span class="text">{{ demandForm.enterpriseType.join('、') }}</span>
                  </div>
                </uni-forms-item>

                <uni-forms-item label="招商需求企业融资阶段">
                  <div v-if="demandForm.enterpriseFinance.length == 0" class="unselected" @click="SelectFinance">
                    请选择
                  </div>
                  <div v-else class="selected" @click="SelectFinance">
                    <span class="text">{{ demandForm.enterpriseFinance.join('、') }}</span>
                  </div>
                </uni-forms-item>

                <uni-forms-item required label="招商需求描述"></uni-forms-item>
                <div class="multirowInput">
                  <uni-easyinput :cursorSpacing="150" type="textarea" trim="all" maxlength="300" :clearable="false"
                    v-model="demandForm.demandDescribe" placeholder="请输入">
                  </uni-easyinput>
                </div>
              </uni-forms>
            </div>

            <div class="submitBtnContainer">
              <div @click="RequirementPublishing()" class="entrust">发布需求</div>
            </div>
          </div>

          <!-- 企业对接需求表单 -->
          <div v-show="DelegateType == 1" class="attractForm">
            <div class="attractFormInput">
              <uni-forms ref="valiForm" :modelValue="messageForm" label-position="left" label-width="100">
                <uni-forms-item required label="意向企业" name="enterpriseName">
                  <div style="position: relative;">
                    <uni-easyinput trim="all" @focus="enterpriseNameFocus" @blur="enterpriseNameBlur"
                      @input="changeenterpriseName" :clearable="false" v-model="messageForm.enterpriseName"
                      placeholder="请输入">
                    </uni-easyinput>

                    <!-- 联想列表 -->
                    <div v-show="showAssociativeList" class="AssociativeList">
                      <div @click="SelectEn(item)" class="AssociativeItem" v-for="(item, index) in AssociativeList"
                        :key="index">
                        {{ item.enterprise }}
                      </div>
                    </div>
                  </div>
                </uni-forms-item>

                <uni-forms-item required label="统一社会信用代码">
                  <uni-easyinput v-if="!forbiddenCode" trim="all" @input="changeUniCode" :clearable="false"
                    v-model="enterpriseUniCode" placeholder="请输入">
                  </uni-easyinput>
                  <div class="enterpriseUniCode" v-else>
                    {{ enterpriseUniCode }}
                  </div>
                </uni-forms-item>

                <uni-forms-item required label="期望对接时间" name="time">
                  <div class="unselected2">
                    <div @click="showDateSelection">
                      <uni-icons class="calendaricons" color="c8c9cc" type="calendar" size="22"></uni-icons>
                      {{ showTimetext ? showTimetext : '选择日期' }}
                    </div>
                    <uni-icons v-if="showTimetext" @click.stop="emptyTime" class="clearIcon" color="c8c9cc" type="clear"
                      size="22">
                    </uni-icons>
                  </div>
                </uni-forms-item>

                <uni-forms-item label="招商需求">
                  <uni-easyinput trim="all" maxlength="200" :clearable="false" v-model="messageForm.remark"
                    placeholder="请输入">
                  </uni-easyinput>
                </uni-forms-item>

                <uni-forms-item required label="招商对接人" name="name">
                  <uni-easyinput trim="all" maxlength="10" :clearable="false" v-model="messageForm.name"
                    placeholder="请输入">
                  </uni-easyinput>
                </uni-forms-item>

                <uni-forms-item required label="联系方式" name="phone">
                  <uni-easyinput trim="all" :clearable="false" v-model="messageForm.phone" placeholder="请输入">
                  </uni-easyinput>
                </uni-forms-item>
              </uni-forms>

              <!-- 委托费用和提交按钮 -->
              <div class="entrustBox">
                <div class="priceBox">
                  <div class="price">
                    <span class="symbol">￥</span>{{ priceText.amount }}
                    <span v-if="priceText.name" class="type">| {{ priceText.name }}</span>
                  </div>
                  <div @click="BillingRuleFn()" class="BillingRule">
                    计费规则 >
                  </div>
                </div>
                <div @click="submit" class="entrust">一键委托</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日期选择器 -->
      <div style="margin-left: 100000rpx;">
        <uni-datetime-picker ref="atetimepicker" type="date" return-type="timestamp" :value="messageForm.time"
          @change="timeChange" @show="timeShow" @maskClick="timeCanle" :start="timeData" />
      </div>

      <div style="height: 120rpx;"></div>
    </scroll-view>

    <!-- 弹窗组件 -->
    <uni-popup ref="industryChainPop" background-color="#fff">
      <industryChain ref="empty" :industryList="industryList" @affirm="affirm"></industryChain>
    </uni-popup>

    <uni-popup ref="cityPop" background-color="#fff">
      <city ref="empty0" :list="cityList" @affirm="affirm0"></city>
    </uni-popup>

    <uni-popup ref="EnterpriseTypePop" background-color="#fff">
      <SingleLineSelection ref="empty1" :list="enterpriseTypeList" @affirm="affirm1"></SingleLineSelection>
    </uni-popup>

    <uni-popup ref="SelectFinancePop" background-color="#fff">
      <SingleLineSelection ref="empty2" :list="enterpriseFinanceList" @affirm="affirm2"></SingleLineSelection>
    </uni-popup>

    <!-- 委托弹窗 -->
    <entrust @ClearOrder="ClearOrder" @applySucceedFn="applySucceedFn" ref="entrust"></entrust>
  </div>
</template>

<script>
import treeSelect from '@/components/treeSelect/treeSelect.vue'
import entrustPopup from '@/components/pop/entrust.vue'

export default {
  name: 'DelegationCreate',
  components: {
    treeSelect,
    entrustPopup
  },
  data() {
    return {
      submitLoading: false,
      delegationType: 1, // 1: 委托需求, 2: 发布需求

      // 委托表单
      messageForm: {
        enterpriseName: '',
        time: '',
        name: '',
        phone: '',
        remark: ''
      },
      enterpriseUniCode: '',
      enterpriseId: '',

      // 需求发布表单
      demandForm: {
        demander: "",
        demandDescribe: "",
        chainIds: [],
        chainNames: [],
        addressCode: [],
        addressName: [],
        enterpriseType: [],
        enterpriseFinance: []
      },

      // 基础数据
      industryList: [],
      cityList: [],
      enterpriseAmountTypeList: [],
      token: uni.getStorageSync('token') || ''
    }
  },

  onLoad() {
    this.getEnumList()
  },

  methods: {
    // 获取枚举列表 - 使用原始的方法
    getEnumList() {
      this.$api.enumListAPI({}).then(res => {
        this.enterpriseAmountTypeList = res.result.enterpriseAmountType || []
      }).catch(e => {
        console.error('获取枚举列表失败:', e)
      })
    },

    // 需求发布 - 使用原始的RequirementPublishing方法
    RequirementPublishing() {
      if (this.submitLoading) {
        return
      }
      if (!this.demandForm.demander) {
        return uni.showToast({
          title: '请输入招商需求方',
          icon: "none",
          duration: 2000,
        });
      } else if (!this.demandForm.demandDescribe) {
        return uni.showToast({
          title: '请输入招商需求描述',
          icon: "none",
          duration: 2000,
        });
      }

      try {
        this.submitLoading = true
        this.$refs.demandvaliForm.validate().then(res => {
          let data = {}
          data = this.demandForm
          this.$api.demandSubmit({
            data,
            method: 'post'
          }).then(res => {
            uni.showToast({
              title: '需求发布成功!',
              icon: 'none'
            })
            setTimeout(() => {
              // 返回上一页或跳转到指定页面
              uni.navigateBack()
            }, 1000)

            // 重置表单
            this.demandForm = {
              demander: "",
              demandDescribe: "",
              chainIds: [],
              chainNames: [],
              addressCode: [],
              addressName: [],
              enterpriseType: [],
              enterpriseFinance: []
            }
            this.clearFormRefs()
            this.submitLoading = false
          }).catch(e => {
            this.submitLoading = false
          })
        }).catch(err => {
          this.submitLoading = false
        })
      } catch (e) {
        this.submitLoading = false
      }
    },

    // 一键委托 - 使用原始的submit方法
    submit(form) {
      if (!this.token) {
        return uni.reLaunch({
          url: `/pages/login/index`
        });
      }
      if (this.submitLoading) {
        return
      }

      let sevenDays = 6 * 24 * 60 * 60 * 1000;
      let XtimeData = new Date().getTime() + sevenDays

      if (!this.messageForm.enterpriseName) {
        return uni.showToast({
          title: '请输入意向企业名称',
          icon: "none",
          duration: 2000,
        });
      } else if (!this.enterpriseUniCode) {
        return uni.showToast({
          title: '请输入社会统一社会信用代码',
          icon: "none",
          duration: 2000,
        });
      } else if (this.messageForm.time < XtimeData) {
        return uni.showToast({
          title: '期望对接时间须为委托日期七日后',
          icon: "none",
          duration: 2000,
        });
      } else if (!this.messageForm.time) {
        return uni.showToast({
          title: '请选择期望对接时间',
          icon: "none",
          duration: 2000,
        });
      } else if (!this.messageForm.name) {
        return uni.showToast({
          title: '请输入招商对接人姓名',
          icon: "none",
          duration: 2000,
        });
      } else if (!this.messageForm.phone) {
        return uni.showToast({
          title: '请输入联系方式',
          icon: "none",
          duration: 2000,
        });
      } else if (!this.isValidPhoneNumber(this.messageForm.phone)) {
        return uni.showToast({
          title: '请输入合法的手机号',
          icon: "none",
          duration: 2000,
        });
      }

      try {
        this.submitLoading = true
        this.$refs.valiForm.validate().then(res => {
          let data = {
            enterprise: this.messageForm.enterpriseName,
            exceptedDatetime: this.messageForm.time,
            enterpriseUniCode: this.enterpriseUniCode,
            enterpriseId: this.enterpriseId,
            contact: this.messageForm.name,
            contactPhone: this.messageForm.phone,
            note: this.messageForm.remark,
          }
          this.$refs.entrust.opens(data, this.enterpriseAmountTypeList)
          this.submitLoading = false
        }).catch(err => {
          this.submitLoading = false
        })
      } catch (e) {
        this.submitLoading = false
      }
    },

    // 合法手机号校验 - 使用原始方法
    isValidPhoneNumber(phone) {
      const pattern = /^1[3-9]\d{9}$/
      return pattern.test(phone);
    },

    // 委托类型切换
    changeDelegationType(type) {
      this.delegationType = type
      if (type == 2 && this.industryList.length == 0) {
        this.getindustrychainList()
        this.getCityList()
      }
    },

    // 获取产业链列表
    getindustrychainList() {
      // 实现产业链数据获取逻辑
    },

    // 获取城市列表
    getCityList() {
      // 实现城市数据获取逻辑
    },

    // 清空表单引用
    clearFormRefs() {
      if (this.$refs.empty) this.$refs.empty.clear()
      if (this.$refs.empty0) this.$refs.empty0.clear()
      if (this.$refs.empty1) this.$refs.empty1.clear()
      if (this.$refs.empty2) this.$refs.empty2.clear()
    },

    // 表单验证
    validateForm() {
      return true // 基础验证
    },

    // 重置表单
    resetForm() {
      this.messageForm = {
        enterpriseName: '',
        time: '',
        name: '',
        phone: '',
        remark: ''
      }
      this.demandForm = {
        demander: "",
        demandDescribe: "",
        chainIds: [],
        chainNames: [],
        addressCode: [],
        addressName: [],
        enterpriseType: [],
        enterpriseFinance: []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// @import './styles/common.scss';
@import '@/static/css/AttractInvestment.scss';

.delegationCreate {
  min-height: 100vh;
  background-color: #FAFCFF;
}

.headimg {
  width: 100%;
  height: 168rpx;
  background-color: #dde6ff;
}

.repository {
  height: calc(100vh - 168rpx);
}

.attractImg {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.orderTicket {
  background-color: #fff;
  margin: 0 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(191, 195, 203, 0.1);
}

.attractFormHead {
  position: relative;
  height: 120rpx;

  .btnBgImg {
    width: 100%;
    height: 100%;
  }
}

.formtitle {
  display: flex;
  padding: 0 30rpx;

  .pitchonOne,
  .pitchonIntelligence,
  .topBtm,
  .topBtm2 {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 28rpx;
    position: relative;
  }

  .pitchonOne,
  .pitchonIntelligence {
    color: #3370FF;
    font-weight: 500;

    .line {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #3370FF;
      border-radius: 2rpx;
    }
  }

  .topBtm,
  .topBtm2 {
    color: #86909C;
  }
}

.attractForm {
  padding: 0 30rpx 30rpx;
}

.attractFormInput,
.attractFormInput2 {
  margin-bottom: 30rpx;
}

.multirowInput {
  margin-top: 20rpx;
}

.unselected,
.selected,
.unselected2 {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #86909C;
  border-bottom: 1rpx solid #f0f0f0;

  &.selected {
    color: #1D2129;
  }
}

.unselected2 {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .calendaricons {
    margin-right: 10rpx;
  }
}

.AssociativeList {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1rpx solid #e5e6eb;
  border-radius: 8rpx;
  max-height: 300rpx;
  overflow-y: auto;
  z-index: 10;

  .AssociativeItem {
    padding: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
    font-size: 28rpx;
    color: #1D2129;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f7f8fa;
    }
  }
}

.entrustBox {
  margin-top: 40rpx;

  .priceBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .price {
      font-size: 32rpx;
      color: #FF6B35;
      font-weight: bold;

      .symbol {
        font-size: 24rpx;
      }

      .type {
        font-size: 24rpx;
        color: #86909C;
        margin-left: 10rpx;
      }
    }

    .BillingRule {
      font-size: 24rpx;
      color: #3370FF;
    }
  }
}

.entrust {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(270deg, #3370FF 0%, #45B8FF 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.submitBtnContainer {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 52rpx;
}

.enterpriseUniCode {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #1D2129;
  border-bottom: 1rpx solid #f0f0f0;
}
</style>
