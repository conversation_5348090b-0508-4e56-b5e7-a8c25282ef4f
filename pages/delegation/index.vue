<template>
  <div class="delegationIndex">
    <!-- 顶部导航栏 -->
    <tabBar title="哒达助招" />

    <!-- 头部装饰 -->
    <div style="height: 168rpx;background-color: #dde6ff;"></div>
    <div class="headimg" />

    <!-- 内容区域 -->
    <div @click="ClickonScroll()" class="box">
      <!-- 委托人视图 -->
      <template v-if="identity == 1">
        <!-- 发起委托 -->
        <DelegationCreate v-if="pitch == 1" />

        <!-- 我的委托 -->
        <MyOrders v-if="pitch == 2" />

        <!-- 我的需求 -->
        <div v-if="pitch == 3">
          <view class="demandInput">
            <uni-easyinput trim="all" prefixIcon="search" :clearable="false" v-model="demandkeyword"
              @input="demandkeywordChange" placeholder="请输入关键词" placeholderStyle="font-size:26rpx">
            </uni-easyinput>
          </view>

          <scroll-view scroll-y="true" class="repository" @scroll="onScroll()">
            <div class="myList">
              <demandList @changeBottomTab="changeBottomTab" @updataList="updatademandList" :demandList="demandList">
              </demandList>

              <div v-if="demandList.length == 0 && showNodata2" class="nodatabox">
                <image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
                <span class="span">暂无内容</span>
              </div>
            </div>
            <div style="height: 200rpx;"></div>
          </scroll-view>
        </div>

        <!-- 我的申请 -->
        <div v-if="pitch == 4">
          <scroll-view scroll-y="true" class="repository" @scroll="onScroll()">
            <div class="myList">
              <view class="total">
                共 <span class="totalSpan2">{{ ApplicationTotal }}</span> 条申请记录
              </view>
              <ApplicationList :ApplicationList="ApplicationList"></ApplicationList>

              <div v-if="ApplicationList.length == 0 && showNodata3" class="nodatabox">
                <image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
                <span class="span">暂无内容</span>
              </div>
            </div>
            <div style="height: 200rpx;"></div>
          </scroll-view>
        </div>
      </template>

      <!-- 招商经理视图 -->
      <template v-if="identity == 2">
        <!-- 招商委托大厅 -->
        <DelegationHall v-if="pitch == 1" />

        <!-- 我的订单 -->
        <MyOrders v-if="pitch == 2" :identity="identity" />

        <!-- 我的钱包 -->
        <MyWallet v-if="pitch == 3" />

        <!-- 我的反馈 -->
        <MyFeedback v-if="pitch == 4" />
      </template>

      <!-- 底部导航栏 - 委托人 -->
      <div v-if="identity == 1 && showBtns" class="bottomTab">
        <div @click="changtab(1)" class="singleTabs" :style="{ color: pitch == 1 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 1" class="my" src="/static/Attract/sponsors.png"></image>
          <image v-else class="my" src="/static/Attract/sponsor.png"></image>
          发起委托
        </div>

        <div v-if="!isDefault && orgName" @click="changtab(4)" class="singleTabs"
          :style="{ color: pitch == 4 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 4" class="my" src="/static/Attract/Myapprovals.png"></image>
          <image v-else class="my" src="/static/Attract/Myapproval.png"></image>
          我的申请
        </div>

        <div @click="changtab(3)" class="singleTabs" :style="{ color: pitch == 3 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 3" class="my" src="/static/Attract/demands.png"></image>
          <image v-else class="my" src="/static/Attract/demand.png"></image>
          我的需求
        </div>

        <div @click="changtab(2)" class="singleTabs" :style="{ color: pitch == 2 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 2" class="my" src="/static/Attract/mys.png"></image>
          <image v-else class="my" src="/static/Attract/my.png"></image>
          我的委托
        </div>
      </div>

      <!-- 底部导航栏 - 招商经理 -->
      <div v-if="identity == 2 && showBtns" class="bottomTab">
        <div @click="changtabs(1)" class="single" :style="{ color: pitch == 1 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 1" class="my" src="/static/Attract/halls.png"></image>
          <image v-else class="my" src="/static/Attract/hall.png"></image>
          招商委托大厅
        </div>

        <div @click="changtabs(2)" class="single" :style="{ color: pitch == 2 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 2" class="my" src="/static/Attract/orderforms.png"></image>
          <image v-else class="my" src="/static/Attract/orderform.png"></image>
          我的订单
        </div>

        <div @click="changtabs(4)" class="single" :style="{ color: pitch == 4 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 4" class="my" src="/static/Attract/feedbacks.png"></image>
          <image v-else class="my" src="/static/Attract/feedback.png"></image>
          我的反馈
        </div>

        <div @click="changtabs(3)" class="single" :style="{ color: pitch == 3 ? '#3370FF' : '#86909C' }">
          <image v-if="pitch == 3" class="my" src="/static/Attract/mywallets.png"></image>
          <image v-else class="my" src="/static/Attract/mywallet.png"></image>
          我的钱包
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tabBar from '@/components/tabBar/index3.vue'
import DelegationHall from './delegationHall.vue'
import MyOrders from './myOrders.vue'
import MyFeedback from './myFeedback.vue'
import MyWallet from './myWallet.vue'
import DelegationCreate from './delegationCreate.vue'
import demandList from '@/components/attractLiist/demandList.vue'
import ApplicationList from '@/components/attractLiist/ApplicationList.vue'

export default {
  name: 'DelegationIndex',
  components: {
    tabBar,
    DelegationHall,
    MyOrders,
    MyFeedback,
    MyWallet,
    DelegationCreate,
    demandList,
    ApplicationList
  },
  data() {
    return {
      identity: 1, // 1-委托人，2-招商经理
      pitch: 1, // 当前选中的tab
      showBtns: true,
      orgName: uni.getStorageSync('orgName'),
      isDefault: uni.getStorageSync('isDefault'),
      demandkeyword: '',
      demandList: [],
      ApplicationList: [],
      ApplicationTotal: 0,
      showNodata2: false,
      showNodata3: false
    }
  },
  onLoad(options) {
    this.initData(options)
  },
  onShow() {
    // #ifdef MP-WEIXIN
    wx.hideHomeButton()
    // #endif

    if (uni.getStorageSync('identity')) {
      this.identity = uni.getStorageSync('identity')
    }
  },
  methods: {
    // 初始化数据
    initData(options) {
      this.token = uni.getStorageSync('token')
      let IdentityType = uni.getStorageSync("userIdentityType")

      if (this.token && (IdentityType == 3 || IdentityType == 4)) {
        this.identity = 2
      }

      if (uni.getStorageSync('identity')) {
        this.identity = uni.getStorageSync('identity')
      }

      // 根据传入参数决定默认显示的tab
      if (options && options.tabId) {
        this.pitch = parseInt(options.tabId)
      } else if (this.identity == 2) {
        this.pitch = 1
      } else if (this.identity == 1) {
        this.pitch = 1
      }
    },

    // 委托人tab切换
    changtab(id) {
      this.pitch = id
      if (id === 3) {
        this.getDemandList()
      } else if (id === 4) {
        this.getApplicationList()
      }
    },

    // 招商经理tab切换
    changtabs(id) {
      this.pitch = id
    },

    // 需求关键词搜索
    demandkeywordChange(e) {
      clearTimeout(this.demandTimer)
      this.demandTimer = setTimeout(() => {
        this.demandkeyword = e
        this.getDemandList()
      }, 500)
    },

    // 获取需求列表
    async getDemandList() {
      try {
        // 实现获取需求列表的API调用
        this.showNodata2 = true
      } catch (error) {
        console.error('获取需求列表失败:', error)
        this.showNodata2 = true
      }
    },

    // 获取申请列表
    async getApplicationList() {
      try {
        // 实现获取申请列表的API调用
        this.showNodata3 = true
      } catch (error) {
        console.error('获取申请列表失败:', error)
        this.showNodata3 = true
      }
    },

    // 更新需求列表
    updatademandList() {
      this.getDemandList()
    },

    // 底部tab切换回调
    changeBottomTab() {
      // 处理底部tab切换
    },

    // 点击滚动
    ClickonScroll() {
      // 处理点击滚动
    },

    // 滚动事件
    onScroll() {
      // 处理滚动事件
    }
  }
}
</script>

<style lang="scss" scoped>
// @import './styles/common.scss';
@import '@/static/css/AttractInvestment.scss';

.delegationIndex {
  min-height: 100vh;
  background-color: #FAFCFF;
}

.headimg {
  width: 100%;
  height: 0;
}

.box {
  position: relative;
}

.demandInput {
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.repository {
  height: calc(100vh - 400rpx);
}

.myList {
  padding: 0 30rpx;
}

.total {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #86909C;
  text-align: center;

  .totalSpan2 {
    color: #3370FF;
    font-weight: 500;
  }
}

.nodatabox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .nodata {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  .span {
    font-size: 28rpx;
    color: #86909C;
  }
}

.bottomTab {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;

  .singleTabs,
  .single {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    transition: color 0.3s ease;

    .my {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: 8rpx;
    }
  }
}
</style>
