# 委托模块拆分说明

原始的 `pages/repository/attract.vue` 文件过于冗余，现已拆分为以下五个独立模块：

## 模块结构

### 1. 招商委托大厅 (`delegationHall.vue`)

- **功能**: 招商经理查看和认领委托单、需求单
- **组件名**: `DelegationHall`
- **主要内容**:
  - 委托单/需求单切换
  - 关键词搜索和筛选功能
  - 委托单认领功能
  - 需求单反馈功能

### 2. 我的订单 (`myOrders.vue`)

- **功能**: 展示用户的订单状态统计和列表
- **组件名**: `MyOrders`
- **主要内容**:
  - 订单状态标签栏（待认领、跟进中、待审核、已完结）
  - 订单列表展示
  - 订单数量统计

### 3. 我的反馈 (`myFeedback.vue`)

- **功能**: 招商经理的反馈管理
- **组件名**: `MyFeedback`
- **主要内容**:
  - 反馈关键词搜索
  - 反馈列表展示
  - 反馈操作功能

### 4. 我的钱包 (`myWallet.vue`)

- **功能**: 钱包功能模块
- **组件名**: `MyWallet`
- **主要内容**:
  - 钱包余额展示
  - 提现功能
  - 收益统计

### 5. 委托发起页面 (`delegationCreate.vue`)

- **功能**: 委托人发起委托和发布需求
- **组件名**: `DelegationCreate`
- **主要内容**:
  - 企业对接需求表单
  - 招商需求发布表单
  - 表单验证和提交

## 文件结构

```
pages/delegation/
├── index.vue              # 主页面，集成所有模块和导航
├── delegationHall.vue     # 招商委托大厅模块
├── myOrders.vue           # 我的订单模块
├── myFeedback.vue         # 我的反馈模块
├── myWallet.vue           # 我的钱包模块
├── delegationCreate.vue   # 委托发起页面模块
└── styles/
    └── common.scss        # 公共样式文件
```

## 功能分配

### 委托人（identity = 1）功能：

1. **发起委托** - `delegationCreate.vue`
2. **我的委托** - `myOrders.vue`
3. **我的需求** - 在主页面中处理
4. **我的申请** - 在主页面中处理

### 招商经理（identity = 2）功能：

1. **招商委托大厅** - `delegationHall.vue`
2. **我的订单** - `myOrders.vue`
3. **我的钱包** - `myWallet.vue`
4. **我的反馈** - `myFeedback.vue`

## 使用方式

### 1. 单独使用模块

```vue
<template>
  <div>
    <DelegationHall />
  </div>
</template>

<script>
import DelegationHall from './delegationHall.vue';

export default {
  components: {
    DelegationHall
  }
};
</script>
```

### 2. 使用集成页面

直接引用 `index.vue` 即可使用所有模块，页面会根据用户身份和选中的 tab 动态显示对应模块

## 主要改进

1. **模块化拆分**: 每个功能模块独立，便于维护和复用
2. **身份识别**: 根据用户身份（委托人/招商经理）显示不同的功能模块
3. **导航集成**: 在主页面统一管理底部导航和 tab 切换
4. **代码复用**: 相同的组件在不同模块中复用
5. **样式统一**: 公共样式文件统一管理

## 样式说明

- 所有模块共享 `styles/common.scss` 公共样式
- 每个模块都有自己的特定样式
- 保持了原有的视觉效果和交互功能
- 引用了原始的 `AttractInvestment.scss` 样式文件

## 数据流和 API

- 各模块独立管理自己的数据和 API 调用
- 通过事件回调实现模块间的数据通信
- 保持了原有的数据结构和 API 接口

## 注意事项

1. 所有模块都保持了原有的功能逻辑
2. 数据结构和 API 调用保持不变
3. 样式文件进行了合理的拆分和复用
4. 可根据需要单独引用任何模块
5. 主页面负责身份判断和导航管理

## 维护建议

- 新增功能时，根据功能归属添加到对应模块
- 公共样式统一在 `common.scss` 中维护
- 保持各模块的独立性，减少耦合度
- 对于跨模块的数据通信，通过事件或全局状态管理
