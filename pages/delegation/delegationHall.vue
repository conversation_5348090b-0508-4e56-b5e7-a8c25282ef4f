<template>
  <div class="delegationHall">
    <!-- 招商委托大厅页面内容 -->
    <page-meta page-style=" background-color: #FAFCFF"></page-meta>
    <div style="height: 168rpx;background-color: #dde6ff;"> </div>
    <tabBar title="委托大厅" />
    <div class="headimg" />
    <div class="box">
      <!-- 搜索和筛选区域 -->
      <view class="seinput">
        <uni-easyinput trim="all" prefixIcon="search" :clearable="false" v-model="keyword" @input="changekeyword"
          placeholder="请输入关键词" placeholderStyle="font-size:26rpx">
        </uni-easyinput>
        <div @click.stop="changeTypeFn()" class="changeType">{{ listType == 1 ? '委托单' : '需求单' }}
          <div class="InvertedTriangle"></div>
          <uni-transition style="z-index: 99;" ref="ani" :show="showPop">
            <view class="userPop">
              <view @click="changeListType(1)" :class="listType == 1 ? 'popItems' : ' popItem'">
                委托单
              </view>
              <view @click="changeListType(2)" :class="listType == 2 ? 'popItems' : ' popItem'">
                需求单
              </view>
            </view>
          </uni-transition>


        </div>
        <div @click="screenFn" class="screenBox">
          <span :style="{ 'color': takeEffect ? '#3370FF' : '#3F4A59' }">筛选</span>
          <image class="screenImg"
            :src="takeEffect ? '../../static/dialog/screening.png' : '../../static/dialog/screen.png'"></image>
        </div>
      </view>
      <scroll-view :refresher-enabled='true' @refresherrefresh="refreshMyentrustList()" :refresher-triggered='triggered'
        scroll-y="true" class="repository" @scroll="onScroll()">
        <div v-if="listType == 1" class="myList">
          <div class="myentrustListClass" v-for="(item, index) in entrustList" :key="item.entrustId">
            <div class="t1">
              <div class="startTime"> 委托时间：{{ item.startDatetime }}</div>
              <div class="myListstate">{{ item.state == 1 ? '待认领' : '已认领' }}</div>
            </div>
            <div class="t2">
              <div class="enterprise">
                <image class="enterpriseIcon" src="../../static/AboutAi/enterprise.png"></image>
                <span class="enterpriseText"> {{ item.enterprise }}</span>
              </div>
              <div>
                <span class="currency">￥</span>
                <span class="amount">{{ item.receiveAmount }}</span>
              </div>
            </div>
            <div class="t3">
              <div class="line">统一社会信用代码：{{ item.enterpriseUniCode }}</div>
              <div class="line">期望对接时间：{{ item.exceptedDatetime }}</div>
              <div class="line">招商要求：{{ item.note }}</div>
            </div>
            <div v-if="item.state == 1" @click="addclaim(item)" class="T4Btn">
              认领
            </div>
          </div>
          <div v-if="entrustList.length == 0 && showNodata" class="nodatabox">
            <image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
            <span class="span">暂无内容</span>
          </div>
        </div>
        <div v-if="listType == 2" class="myList">
          <demandList @updataList="gethalldemandList" @changeBottomTab="changeBottomTab" :demandList="halldemandList"
            @openfeedback="openfeedback" :counselor="true" :outersphere='true'>
          </demandList>
          <div v-if="halldemandList.length == 0 && showNodata" class="nodatabox">
            <image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
            <span class="span">暂无内容</span>
          </div>
        </div>
        <div style="height: 200rpx;">
        </div>
      </scroll-view>
      <!-- v-if="claimShow" -->
      <claim ref="claim" @claimupdata="claimupdata" @closeClaimPopup="closeClaimPopup"></claim>
      <screen v-if="screenShow" @changeBottomTab="changeBottomTab" @updataentrustList="updataentrustList"
        :listType="listType" ref="screen">
      </screen>
    </div>
  </div>
</template>

<script>
import tabBar from '@/components/tabBar/index3.vue'
import demandList from '@/components/attractLiist/demandList.vue'
import screen from '@/components/pop/screen.vue'
import claim from '@/components/pop/claim.vue'

export default {
  name: 'DelegationHall',
  components: {
    demandList,
    screen,
    claim,
    tabBar
  },
  data() {
    return {
      keyword: '',
      listType: 1, // 1-委托单，2-需求单
      showPop: false,
      takeEffect: false,
      triggered: false,
      showNodata: false,
      entrustList: [],
      halldemandList: [],
      HallScreening: {
        state: "",
        money: '',
        entrustTime: []
      },
      time2: null,
      claimShow: false,
      screenShow: false
    }
  },
  onLoad() {
    this.entrustFn()
    this.gethalldemandList()
  },

  onReady() {
    // 确保弹窗组件默认关闭状态
    this.$nextTick(() => {
      this.ensurePopupsAreClosed()
    })
  },

  onShow() {
    // 每次页面显示时确保弹窗关闭
    this.$nextTick(() => {
      this.ensurePopupsAreClosed()
    })
  },
  methods: {
    // 切换委托单/需求单类型
    changeTypeFn() {
      this.showPop = !this.showPop
    },

    // 切换列表类型
    changeListType(type) {
      if (this.listType != type) {
        this.listType = type
        this.keyword = ''
        this.takeEffect = false
        this.HallScreening = {
          state: "",
          money: '',
          entrustTime: []
        }
        this.showPop = false
        if (type == 1) {
          this.$refs.screen.claimFn()
          this.entrustFn()
        } else {
          this.gethalldemandList()
        }
      }
    },

    // 关键词搜索
    changekeyword(e) {
      this.keyword = e
      if (this.time2 != null) {
        clearTimeout(this.time2);
      }
      this.time2 = setTimeout(async () => {
        if (this.listType == 1) {
          this.entrustFn()
        } else {
          this.gethalldemandList(e)
        }
      }, 400);
    },

    // 打开筛选
    screenFn() {
      this.screenShow = true
      this.$refs.screen.opens()
    },

    // 筛选确认
    updataentrustList(data) {
      if (!data.entrustTime) {
        return
      }
      this.HallScreening = data
      if (data.entrustTime.length == 0 && !data.state && !data.money) {
        this.takeEffect = false
      } else {
        this.takeEffect = true
      }
      if (this.listType == 1) {
        this.changekeyword(this.keyword)
      } else {
        this.gethalldemandList(this.keyword)
      }
    },

    // 刷新列表
    refreshMyentrustList() {
      this.triggered = true;
      if (this.listType == 1) {
        this.entrustFn()
      } else {
        this.gethalldemandList()
      }
    },

    // 认领
    addclaim(item) {
      this.claimShow = true
      this.$refs.claim.opens(item)
    },
    closeClaimPopup() {
      console.log('ee')
      this.$refs.claim.claimCanle()
      this.claimShow = false
    },
    // 认领成功回调
    claimupdata() {
      this.entrustFn(this.keyword)
    },

    // 获取委托单列表 - 使用原始的entrustFn方法
    entrustFn(e) {
      this.showNodata = false
      let data = {
        state: this.HallScreening.state,
        key: e || this.keyword || '',
        pageSize: 1000,
        pageNum: 1,
      }
      if (this.HallScreening.entrustTime.length > 1) {
        data.minStartDatetime = this.HallScreening.entrustTime[0] + ' 00:00:00'
        data.maxStartDatetime = this.HallScreening.entrustTime[1] + ' 23:59:59'
      }
      if (this.HallScreening.money == 1) {
        data.maxAmount = 2000
      } else if (this.HallScreening.money == 2) {
        data.minAmount = 2000
        data.maxAmount = 5000
      } else if (this.HallScreening.money == 3) {
        data.minAmount = 5000
        data.maxAmount = 10000
      } else if (this.HallScreening.money == 4) {
        data.minAmount = 10000
      }
      this.$api.workerList({
        data,
        method: 'get'
      }).then(res => {
        this.showNodata = true
        this.triggered = false
        this.entrustList = res.result.records
      }).catch(e => {
        this.showNodata = true
        this.triggered = false
      })
    },

    // 获取需求单列表 - 使用原始的gethalldemandList方法
    gethalldemandList(e) {
      this.showNodata = false
      let data = {
        state: this.HallScreening.state,
        demander: e || this.keyword || '',
        pageSize: 1000,
        pageNum: 1,
      }
      if (this.HallScreening.entrustTime.length > 1) {
        data.minStartDatetime = this.HallScreening.entrustTime[0] + ' 00:00:00'
        data.maxStartDatetime = this.HallScreening.entrustTime[1] + ' 23:59:59'
      }
      this.$api.workerdemandListAPI({
        data,
        method: 'get'
      }).then(res => {
        this.showNodata = true
        this.triggered = false
        this.halldemandList = res.result.records
      }).catch(e => {
        this.showNodata = true
        this.triggered = false
      })
    },

    // 反馈回调
    openfeedback(item) {
      // #ifdef H5
      const callbackId = window.callbacks.register(
        (data) => {
          this.gethalldemandList();
        }
      );
      window?.ReactNativeWebView?.postMessage(
        JSON.stringify({
          callbackId,
          type: "showAlert",
          value: {
            type: 'FeedbackModal',
            data: item,
          },
          module: "Investment",
        })
      );
      // #endif
      // #ifdef MP-WEIXIN
      this.$refs.feedback2.opens(item)
      // #endif
    },

    // 底部tab切换回调
    changeBottomTab(isShow) {
      // 处理底部tab切换
    },

    // 滚动事件
    onScroll() {
      // 处理滚动事件
    },

    // 确保弹窗关闭
    ensurePopupsAreClosed() {
      try {
        if (this.$refs.claim && this.$refs.claim.$refs.claim) {
          this.$refs.claim.$refs.claim.close()
        }
      } catch (e) {
        // 忽略错误，可能组件还未完全加载
      }

      try {
        if (this.$refs.screen && this.$refs.screen.$refs.follow) {
          this.$refs.screen.$refs.follow.close()
        }
      } catch (e) {
        // 忽略错误，可能组件还未完全加载
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// @import './styles/common.scss';
@import '@/static/css/AttractInvestment.scss';

// .delegationHall {
//   min-height: 100vh;
//   background-color: #FAFCFF;
// }

// .seinput {
//   display: flex;
//   align-items: center;
//   padding: 20rpx 30rpx;
//   background-color: #fff;
//   margin-bottom: 20rpx;

//   .changeType {
//     margin-left: 20rpx;
//     display: flex;
//     align-items: center;
//     padding: 0 20rpx;
//     height: 60rpx;
//     background-color: #f5f6fa;
//     border-radius: 8rpx;
//     font-size: 28rpx;
//     color: #3F4A59;
//     position: relative;

//     .InvertedTriangle {
//       width: 0;
//       height: 0;
//       border-left: 6rpx solid transparent;
//       border-right: 6rpx solid transparent;
//       border-top: 6rpx solid #86909C;
//       margin-left: 8rpx;
//     }
//   }

//   .screenBox {
//     margin-left: 20rpx;
//     display: flex;
//     align-items: center;
//     padding: 0 20rpx;
//     height: 60rpx;

//     .screenImg {
//       width: 32rpx;
//       height: 32rpx;
//       margin-left: 8rpx;
//     }
//   }
// }

// .userPop {
//   position: absolute;
//   top: 70rpx;
//   left: 0;
//   right: 0;
//   background-color: #fff;
//   border-radius: 8rpx;
//   box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

//   .popItem,
//   .popItems {
//     padding: 20rpx;
//     text-align: center;
//     font-size: 28rpx;
//     border-bottom: 1rpx solid #f0f0f0;

//     &:last-child {
//       border-bottom: none;
//     }
//   }

//   .popItems {
//     color: #3370FF;
//     background-color: #f0f7ff;
//   }
// }

// .repository {
//   height: calc(100vh - 200rpx);
// }

// .myList {
//   padding: 0 30rpx;
// }

// .myentrustListClass {
//   background-color: #fff;
//   border-radius: 12rpx;
//   padding: 30rpx;
//   margin-bottom: 20rpx;
//   box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

//   .t1 {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     margin-bottom: 20rpx;

//     .startTime {
//       font-size: 24rpx;
//       color: #86909C;
//     }

//     .myListstate {
//       font-size: 24rpx;
//       color: #3370FF;
//       background-color: #f0f7ff;
//       padding: 8rpx 16rpx;
//       border-radius: 16rpx;
//     }
//   }

//   .t2 {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     margin-bottom: 20rpx;

//     .enterprise {
//       display: flex;
//       align-items: center;

//       .enterpriseIcon {
//         width: 32rpx;
//         height: 32rpx;
//         margin-right: 8rpx;
//       }

//       .enterpriseText {
//         font-size: 32rpx;
//         font-weight: 500;
//         color: #1D2129;
//       }
//     }

//     .currency {
//       font-size: 24rpx;
//       color: #FF6B35;
//     }

//     .amount {
//       font-size: 36rpx;
//       font-weight: bold;
//       color: #FF6B35;
//     }
//   }

//   .t3 {
//     .line {
//       font-size: 26rpx;
//       color: #4E5969;
//       margin-bottom: 8rpx;

//       &:last-child {
//         margin-bottom: 0;
//       }
//     }
//   }

//   .T4Btn {
//     margin-top: 30rpx;
//     text-align: center;
//     background: linear-gradient(270deg, #3370FF 0%, #45B8FF 100%);
//     color: #fff;
//     padding: 20rpx;
//     border-radius: 8rpx;
//     font-size: 28rpx;
//     font-weight: 500;
//   }
// }

// .nodatabox {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;
//   padding: 100rpx 0;

//   .nodata {
//     width: 200rpx;
//     height: 200rpx;
//     margin-bottom: 20rpx;
//   }

//   .span {
//     font-size: 28rpx;
//     color: #86909C;
//   }
// }</style>