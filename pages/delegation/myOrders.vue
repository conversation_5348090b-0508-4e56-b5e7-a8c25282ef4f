<template>
  <div class="myOrders">
    <!-- 我的订单页面内容 -->
    <page-meta page-style="background-color: #FAFCFF"></page-meta>
    <div style="height: 168rpx;background-color: #dde6ff;"> </div>
    <tabBar title="我的订单" />
    <div class="headimg" />
    <div class="box">

      <!-- 状态标签栏 -->
      <div class="myList">
        <div class="stateTab">
          <div @click="changestateId(item.id)" :class="stateId == item.id ? 'statepitch' : 'stateClass'"
            v-for="(item, index) in stateTabList" :key="item.id">
            {{ item.name }}
            <span v-if="item.num != 0 && index == 3">({{ item.num }})</span>
            <div class="numShow" v-if="item.num != 0 && index != 3">{{ item.num }}</div>
          </div>
        </div>

        <!-- 订单列表 -->
        <consignorList @changeBottomTab="changeBottomTab" @updataList="updataList" :identity="identity"
          :myentrustList="myentrustList">
        </consignorList>

        <!-- 无数据提示 -->
        <div v-if="myentrustList.length == 0 && showNodata" class="nodatabox">
          <image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
          <span class="span">暂无内容</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import consignorList from '@/components/attractLiist/consignorList.vue'
import tabBar from '@/components/tabBar/index3.vue'

export default {
  name: 'MyOrders',
  components: {
    consignorList,
    tabBar
  },
  props: {
    identity: {
      type: Number,
      default: 1 // 1-委托人，2-招商经理
    }
  },
  data() {
    return {
      stateId: 1,
      stateId2: 2, // 招商经理用的状态ID
      showNodata: false,
      myentrustList: [],
      managerentrustList: [], // 招商经理的订单列表
      stateTabList: [
        {
          name: "待认领",
          num: 0,
          id: 1
        },
        {
          name: "跟进中",
          num: 0,
          id: 3
        },
        {
          name: "待审核",
          num: 0,
          id: 6
        },
        {
          name: "已完结",
          num: 0,
          id: 7
        }
      ],
      counselorTabList: [
        {
          name: "待跟进",
          num: 0,
          id: 2
        },
        {
          name: "跟进中",
          num: 0,
          id: 3
        },
        {
          name: "申诉中",
          num: 0,
          id: 10
        },
        {
          name: "已完结",
          num: 0,
          id: 7
        }
      ]
    }
  },
  computed: {
    // 根据身份返回对应的状态标签列表
    currentStateTabList() {
      return this.identity === 1 ? this.stateTabList : this.counselorTabList
    },
    // 根据身份返回对应的状态ID
    currentStateId() {
      return this.identity === 1 ? this.stateId : this.stateId2
    },
    // 根据身份返回对应的订单列表
    currentOrderList() {
      return this.identity === 1 ? this.myentrustList : this.managerentrustList
    }
  },
  onLoad() {
    if (this.identity === 1) {
      this.getNum()
      this.changestateId(this.stateId, true)
    } else {
      this.getCounselorNum()
      this.MyentrustFn(this.stateId2, true)
    }
  },
  methods: {
    // 切换状态标签
    changestateId(id, one) {
      if (this.identity === 1) {
        if (this.stateId !== id || one) {
          this.stateId = id
          this.showNodata = false
          this.getNum()
          this.$api.billingList({
            data: {
              state: this.stateId,
              pageSize: 1000,
              pageNum: 1,
            },
            method: 'get'
          }).then(res => {
            this.showNodata = true
            this.myentrustList = res.result.records
          }).catch(e => {
            this.showNodata = true
          })
        }
      } else {
        // 招商经理的订单切换
        this.MyentrustFn(id, true)
      }
    },

    // 招商经理订单列表切换
    MyentrustFn(id, one) {
      if (this.stateId2 !== id || one) {
        this.stateId2 = id
        this.showNodata = false
        this.getCounselorNum()
        this.$api.claimList({
          data: {
            pageSize: 1000,
            pageNum: 1,
            state: this.stateId2,
          },
          method: 'get'
        }).then(res => {
          this.showNodata = true
          this.managerentrustList = res.result.records
        }).catch(e => {
          this.showNodata = true
        })
      }
    },

    // 获取委托人订单数量
    getNum() {
      this.$api.billingCount({
        method: 'get'
      }).then(res => {
        if (res.result) {
          this.stateTabList[0].num = res.result.waitClaimCount || 0
          this.stateTabList[1].num = res.result.followingCount || 0
          this.stateTabList[2].num = res.result.waitCheckCount || 0
          this.stateTabList[3].num = res.result.completedCount || 0
        }
      })
    },

    // 获取产业顾问订单数量
    getCounselorNum() {
      this.$api.countAPI({
        method: 'get'
      }).then(res => {
        if (res.result) {
          this.counselorTabList[0].num = res.result.waitFollowCount || 0
          this.counselorTabList[1].num = res.result.followingCount || 0
          this.counselorTabList[2].num = res.result.complainCount || 0
          this.counselorTabList[3].num = res.result.completedCount || 0
        }
      })
    },

    // 更新列表
    updataList() {
      //判断委托人还是顾问
      if (this.identity == 1) {
        this.changestateId(this.stateId, true)
      } else {
        this.MyentrustFn(this.stateId2, true)
      }
    },

    // 底部tab切换回调
    changeBottomTab(isShow) {
      // 处理底部tab切换
    }
  }
}
</script>

<style lang="scss" scoped>
// @import './styles/common.scss';
@import '@/static/css/AttractInvestment.scss';

.myOrders {
  min-height: 100vh;
  background-color: #FAFCFF;
}

.myList {
  padding: 0 30rpx;
}

.stateTab {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .stateClass,
  .statepitch {
    flex: 1;
    text-align: center;
    padding: 24rpx 0;
    font-size: 28rpx;
    position: relative;
    transition: all 0.3s ease;
  }

  .stateClass {
    color: #86909C;
    background-color: #fff;
  }

  .statepitch {
    color: #3370FF;
    background-color: #f0f7ff;
    font-weight: 500;
  }

  .numShow {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    background-color: #FF6B35;
    color: #fff;
    font-size: 20rpx;
    min-width: 32rpx;
    height: 32rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8rpx;
    box-sizing: border-box;
  }
}

.nodatabox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .nodata {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  .span {
    font-size: 28rpx;
    color: #86909C;
  }
}
</style>