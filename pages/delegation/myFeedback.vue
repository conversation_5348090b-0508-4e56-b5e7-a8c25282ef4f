<template>
  <div class="myFeedback">
    <!-- 我的反馈页面内容 -->
    <page-meta page-style="background-color: #FAFCFF"></page-meta>
    <div style="height: 168rpx;background-color: #dde6ff;"> </div>
    <tabBar title="我的反馈" />
    <div class="headimg" />
    <div class="box">

      <!-- 搜索区域 -->
      <view class="demandInput">
        <uni-easyinput trim="all" prefixIcon="search" :clearable="false" v-model="feedbackKeyword"
          @input="feedbackKeywordChange" placeholder="请输入关键词" placeholderStyle="font-size:26rpx">
        </uni-easyinput>
      </view>

      <!-- 反馈列表 -->
      <scroll-view scroll-y="true" class="repository" @scroll="onScroll()">
        <div class="myList">
          <demandList @updataList="gethalldemandList" @changeBottomTab="changeBottomTab" :demandList="MyfeedbackList"
            :showenterpriseNote="true" :counselor="true">
          </demandList>

          <!-- 无数据提示 -->
          <div v-if="MyfeedbackList.length == 0 && showNodata" class="nodatabox">
            <image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
            <span class="span">暂无内容</span>
          </div>
        </div>

        <div style="height: 200rpx;"></div>
      </scroll-view>

      <!-- 反馈弹窗 -->
      <feedback @updataList="gethalldemandList" @updataTab="changeBottomTab" ref="feedback2">
      </feedback>
    </div>
  </div>
</template>

<script>
import tabBar from '@/components/tabBar/index3.vue'
import demandList from '@/components/attractLiist/demandList.vue'
import feedback from '@/components/pop/feedback.vue'

export default {
  name: 'MyFeedback',
  components: {
    demandList,
    feedback,
    tabBar
  },
  data() {
    return {
      feedbackKeyword: '',
      showNodata: false,
      MyfeedbackList: [],
      time2: null
    }
  },
  onLoad() {
    this.getMyfeedback()
  },
  methods: {
    // 关键词搜索
    feedbackKeywordChange(e) {
      if (this.time2 != null) {
        clearTimeout(this.time2);
      }
      this.time2 = setTimeout(async () => {
        this.getMyfeedback(e)
      }, 400);
    },

    // 获取我的反馈列表 - 使用原始的getMyfeedback方法
    getMyfeedback(e) {
      let data = {
        demander: e || this.feedbackKeyword || '',
        pageSize: 1000,
        pageNum: 1,
      }
      this.$api.demandmineAPI({
        data,
        method: 'get'
      }).then(res => {
        this.MyfeedbackList = res.result.records
        this.showNodata = true
      }).catch(e => {
        this.showNodata = true
      })
    },

    // 更新列表
    gethalldemandList() {
      this.getMyfeedback()
    },

    // 底部tab切换回调
    changeBottomTab(isShow) {
      // 处理底部tab切换
    },

    // 滚动事件
    onScroll() {
      // 处理滚动事件，如加载更多
    }
  }
}
</script>

<style lang="scss" scoped>
// @import './styles/common.scss';
@import '@/static/css/AttractInvestment.scss';

.myFeedback {
  min-height: 100vh;
  background-color: #FAFCFF;
}

.demandInput {
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.repository {
  height: calc(100vh - 200rpx);
}

.myList {
  padding: 0 30rpx;
}

.nodatabox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .nodata {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  .span {
    font-size: 28rpx;
    color: #86909C;
  }
}
</style>