<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<image src="https://static.idicc.cn/cdn/aiChat/applet/bg.png" class="homeBgc"></image>
		<!-- #endif -->
		<view class="topHeader" :style="`top: ${statusBarHeight}px`">
			<!-- #ifndef H5 -->
			<view class="topImg">
				<view class="black" @click="goBack()">
					<image class="returnFn" src="~@/static/AboutAi/returnFn.png"></image>
				</view>
				{{ dataInfo.name }}
			</view>
			<!-- #endif -->
			<view class="header">
				<image :src="dataInfo.iconPath" class="icon"></image>
				<view class="mgl-20">
					<view class="span1"> {{ dataInfo.name }}</view>
					<view class="span2">来自：艾瑞数云</view>
				</view>
				<view @click="oneClickExportFn" class="oneClickExport">
					<image src="/static/AboutAi/derive.png"></image>
					一键导出
				</view>
			</view>
			<view class="headertext">
				{{ dataInfo.content }}
			</view>
			<view class="optionBox">
				<span :class="(isEmptyValue(baseInfo) ? 'unselected' : 'pitchOn')" class="optionItem" @click="screenFn(1)">
					{{ regionFn() }}
					<image :src="iconColour(isEmptyValue(baseInfo))" class="Arrows"></image>
				</span>
				<span :class="(searchForm.model == '' ? 'unselected' : 'pitchOn')" class="optionItem" @click="screenFn(2)">
					线索模型
					<image :src="iconColour(searchForm.model.length <= 0)" class="Arrows"></image>
				</span>
				<span :class="(searchForm.strategy == '' ? 'unselected' : 'pitchOn')" class="optionItem" @click="screenFn(3)">
					招商策略
					<image :src="iconColour(searchForm.strategy.length <= 0)" class="Arrows"></image>
				</span>
			</view>
			<view class="total">
				共
				<span class="totalSpan2">{{ total }}</span>
				家企业
			</view>
		</view>
		<scroll-view v-if="enterpriseList.length != 0" class="listBox" @scrolltolower="scrolltolowerFn" @scroll="scroll"
			:style="`top: ${statusBarHeight}px`" scroll-y="true">
			<view>
				<view class="card" v-for="i in enterpriseList" :key="i">
					<view class="cadHeader">
						<image :src="i?.enterpriseIconLabelId == null
							? listicon[0].icon
							: listicon[i.enterpriseIconLabelId].icon" class="icon2"></image>
						<span @click="detailpage(i)" class="mgl-10">{{ i.enterpriseName }}</span>
					</view>
					<view class="contentBox mgt-24">
						<p class="contentSpan">线索模型：{{ i.clueTypeName }}</p>
						<p class="contentSpan">招商策略：{{ i.strategyTypeName }}</p>
						<p class="contentSpan">推荐时间：{{ i.recommendDate }}</p>
						<p class="contentSpan">企业地址：{{ i.enterpriseAddress }}</p>
						<p class="contentSpan">推送地区：{{ i.recommendRegionName }}</p>
					</view>
					<view class="detail" @click="goDetail(i)">
						一企一策
						<image src="~@/static/AboutAi/right2.png" class="righticon"></image>
					</view>
				</view>
			</view>
		</scroll-view>
		<div v-if="enterpriseList.length == 0 && showNodata" class="nodatabox">
			<image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
			<span class="span">暂无内容</span>
		</div>
	</view>
	<moreScreen ref="clueModelPop" title="请选择" :safearea="true" :moreScreenList="clueModelList"
		@updatamoreScreen="affirm1">
	</moreScreen>
	<uni-popup ref="popup2" background-color="#fff">
		<mapList2 ref="mapList2" :cityName="cityName" :unrestricted="true" @affirm="affirm2">
		</mapList2>
	</uni-popup>
	<moreScreen ref="inStrategyPop" title="请选择" :safearea="true" :moreScreenList="inStrategyList"
		@updatamoreScreen="affirm3">
	</moreScreen>
</template>

<script>
import mapList2 from '../../components/mapList.vue'
import moreScreen from '../../components/360screen/moreScreen.vue'
import {
	listicon
} from '../../utils/utils.js'
import {
	getQueryParam
} from '@/utils/utils.js'
export default {
	components: {
		mapList2,
		moreScreen
	},
	watch: {
		enterpriseList(value) {
			this.enterpriseList.forEach(it => {
				let arr = ['主板', '创业板', '科创板', '北交所', '港股', '中概股', '新三板'] //上市
				if (it.enterpriseIconLabelId === null) {
					it.enterpriseIconLabelId = 0
				}
				if (it.enterpriseLabelNames) {
					let Names = it.enterpriseLabelNames[0]
					if (!Names) {
						it.enterpriseIconLabelId = 0
					} else if (arr.includes(it.enterpriseLabelNames[0])) {
						it.enterpriseIconLabelId = 1
					} else if (Names.includes('独角兽')) {
						it.enterpriseIconLabelId = 2
					} else if (Names.includes('专精特新')) {
						it.enterpriseIconLabelId = 3
					} else if (Names.includes('隐形冠军')) {
						it.enterpriseIconLabelId = 4
					} else if (Names.includes('瞪羚')) {
						it.enterpriseIconLabelId = 5
					} else if (Names.includes('创新')) {
						it.enterpriseIconLabelId = 6
					} else if (Names.includes('技术先进')) {
						it.enterpriseIconLabelId = 7
					} else if (Names.includes('科技')) {
						it.enterpriseIconLabelId = 8
					} else if (Names.includes('雏鹰')) {
						it.enterpriseIconLabelId = 9
					} else {
						it.enterpriseIconLabelId = 0
					}
				} else {
					it.enterpriseIconLabelId = 0
				}
			})
		}
	},
	data() {
		return {
			statusBarHeight: wx.getStorageSync("statusBarHeight"),
			searchForm: {
				model: [],
				strategy: [],
				code: '',
			},
			cityName: '',
			listicon,
			pageNum: 1,
			pages: 0,
			baseInfo: {},
			clueModelList: [{
				paramName: '线索模型',
				paramValueList: ['亲缘招商', '链式招商', '舆情招商', '资本招商', '人才招商'],
				paramKey: 'clueModel',
				unlimited: true,
			}],
			inStrategyList: [{
				paramName: '招商策略',
				paramValueList: ['资本招商', '场景招商', '亲缘招商'],
				paramKey: 'inStrategy',
				unlimited: true,
			}],
			enterpriseList: [],
			total: 0,
			token: '',
			showNodata: false,
			isLoading: false,
			dataInfo: {}
		}
	},
	onLoad(options) {
		this.token = uni.getStorageSync('token')
		let id = options?.id || ''
		// #ifdef H5
		if (getQueryParam("token")) {
			this.token = getQueryParam("token");
			uni.setStorageSync("token", this.token);
		}
		if (getQueryParam("id")) {
			id = getQueryParam("id");
		}
		// #endif
		this.getDel(id)
		if (this.token) {
			this.getList()
		} else {
			this.showNodata = true
		}
	},
	methods: {
		oneClickExportFn() {
			// #ifdef MP-WEIXIN
			uni.navigateTo({
				url: `/pages/deriveExcel/index?id=47`
			});
			// #endif
			// #ifdef H5
			window?.ReactNativeWebView?.postMessage(
				JSON.stringify({
				type: "changePath",
				value: {
					id: 47
				},
				path: "deriveExcel",
				})
			);
			// #endif

		},
		getDel(id) {
			let data = {
				id,
			}
			this.$api.capabilityPoolDetailAPI({
				data,
			}).then(res => {
				this.dataInfo = res.result
			})
		},
		getList() {
			if (this.isLoading) {
				return;
			}
			this.isLoading = true;
			let data = {
				pageSize: 10,
				pageNum: this.pageNum,
				code: this.searchForm.code || '',
				modelTypes: this.getCodeByName(this.searchForm.model),
				strategyTypes: this.getCodeByName2(this.searchForm.strategy),
			}
			this.$api.reportListAPI({
				data,
				method: "post"
			}).then(res => {
				this.enterpriseList = this.enterpriseList.concat(res.result.records);
				this.pages = res.result.pages
				this.total = res.result.total
			}).finally(() => {
				this.isLoading = false;
				this.showNodata = true
			})
		},
		scrolltolowerFn() {
			if (this.isLoading) {
				return;
			}
			if (this.pages > this.pageNum) {
				this.pageNum++;
				this.getList()
			}
		},
		detailpage(item) {
			// #ifndef MP-WEIXIN
			window?.ReactNativeWebView?.postMessage(
				JSON.stringify({
					type: "changePath",
					value: {
						id: item.enterpriseId || item.id,
						iconTypeid: item.enterpriseIconLabelId,
						enterpriseName: item.enterpriseName,
						enterpriseLabelIds: JSON.stringify(this.enterpriseLabelIds || []),
					},
					path: "industryDetail",
				})
			);
			// #endif
			// #ifndef H5
			uni.navigateTo({
				url: `/pages/newMapEnterprise/components/enterprise?id=${item.enterpriseId || item.id
					}&iconTypeid=${item.enterpriseIconLabelId}&showChain=${this.showChain
					}&enterpriseName=${item.enterpriseName
					}&enterpriseLabelIds=${JSON.stringify(this.enterpriseLabelIds || [])}`,
			});
			// #endif
		},
		getCodeByName(namesArray) {
			const investmentTypes = {
				亲缘招商: 1,
				链式招商: 3,
				舆情招商: 5,
				人才招商: 7,
				资本招商: 8
			};
			const codes = namesArray.map(name => investmentTypes[name] || null);
			return codes || []
		},
		getCodeByName2(namesArray) {
			const investmentTypes = {
				亲缘招商: 1,
				资本招商: 2,
				场景招商: 3,
			};
			const codes = namesArray.map(name => investmentTypes[name] || null);
			return codes || []
		},
		goBack() {
			// #ifndef MP-WEIXIN
			// window?.ReactNativeWebView?.postMessage(
			// 	JSON.stringify({
			// 		type: "changePath",
			// 		value: '',
			// 		path: "goBack",
			// 	})
			// );
			// #endif
			// #ifndef H5
			uni.navigateBack({
				delta: 1
			});
			// #endif
		},
		goDetail(item) {
			let data = {
				enterpriseName: item.enterpriseName,
				uniCode: item.uniCode,
				enterpriseId: item.enterpriseId,
				recommendRegionCode: item.recommendRegionCode || '',
			}
			this.$api.reportGenerateAPI({
				data,
				method: "post"
			}).then(res => {
				// #ifndef MP-WEIXIN
				window?.ReactNativeWebView?.postMessage(
					JSON.stringify({
						type: "changePath",
						value: {
							url: 'previewPdf',
							name: '报告预览',
							params: {
								reportId: res.result,
								type: 'pdf',
								enterpriseName: item.enterpriseName + '招商策略报告'
							}
						},
						path: "webViewPage",
					})
				);
				// #endif
				// #ifndef H5
				uni.navigateTo({
					url: `/pages/excelView/index?id=${res.result}&type=pdf&enterpriseName=${item.enterpriseName + '招商策略报告'}`,
				});
				// #endif
			})
		},
		//省市区筛选展示
		regionFn() {
			let region = '全国';
			if (this.baseInfo?.area != null && this.baseInfo?.area.length != 0) {
				region = this.baseInfo.area[0];
			} else if (this.baseInfo.city) {
				region = this.baseInfo.city;
			} else if (this.baseInfo.province) {
				region = this.baseInfo.province;
			}
			if (region.length > 4) {
				region = region.substring(0, 3) + '...';
			}
			return region;
		},
		//是否选中
		iconColour(data) {
			if (data) {
				return '/static/AboutAi/blackArrows.png'
			} else {
				return '/static/AboutAi/blueArrows.png'
			}
		},
		screenFn(type) {
			if (!this.token) {
				uni.reLaunch({
					url: `/pages/login/index`
				});
			}
			if (type == 1) {
				this.$refs.popup2.open('bottom')
				// #ifndef MP-WEIXIN
				this.$nextTick(() => {
					if (this.$refs.mapList2) {
						this.$refs.mapList2.init()
					} else {
						console.warn('mapList2组件未挂载打开时')
					}
				})
				// #endif
			} else if (type == 2) {
				this.$refs.clueModelPop.opens()
			} else if (type == 3) {
				this.$refs.inStrategyPop.opens()
			}
		},
		affirm1(data) {
			if (!data) {
				this.searchForm.model = []
			} else {
				this.searchForm.model = data.clueModel
			}
			this.enterpriseList = []
			this.showNodata = false
			this.pageNum = 1
			this.pages = 1
			this.total = 0
			this.getList()
		},
		//省市区确认
		affirm2(data) {
			this.searchForm.code = data.area.code ? data.area.code : ((data.citys.code ? data.citys.code : data
				.province.code))
			this.$refs.popup2.close()
			this.baseInfo.province = data.province.name
			this.baseInfo.city = data.citys.name
			this.baseInfo.area = []
			if (data.area.name) {
				this.baseInfo.area[0] = data.area.name
			}
			// #ifndef MP-WEIXIN
			let province = data.province.name ? data.province.name + '/' : ''
			let citys = data.citys.name ? data.citys.name + '/' : ''
			let area = data.area.name ? data.area.name + '/' : ''
			this.cityName = province + citys + area
			console.log(this.cityName, 'this.cityName ');
			// #endif
			this.enterpriseList = []
			this.showNodata = false
			this.pages = 1
			this.pageNum = 1
			this.total = 0
			this.getList()
		},
		affirm3(data) {
			// this.$refs.inStrategyPop.close()
			if (!data) {
				this.searchForm.strategy = []
			} else {
				this.searchForm.strategy = data.inStrategy
			}
			this.enterpriseList = []
			this.showNodata = false
			this.pages = 1
			this.pageNum = 1
			this.total = 0
			this.getList()
		},
		//判断是否完全为空
		isEmptyValue(value) {
			if (typeof value === 'string') {
				return value.trim() === '';
			}
			if (typeof value === 'number') {
				return value === 0;
			}
			if (typeof value === 'boolean') {
				return value === false;
			}
			if (Array.isArray(value)) {
				return value.every(this.isEmptyValue);
			}
			if (value !== null && typeof value === 'object') {
				return Object.values(value).every(this.isEmptyValue);
			}
			return false;
		},
	}
}
</script>

<style lang="scss" scoped>
.nodatabox {
	display: flex;
	align-items: center;
	flex-direction: column;
	font-weight: 400;
	font-size: 30rpx;
	color: #3f4a59;
	top: 520rpx;
	//margin-top: 520rpx;
	position: absolute;
	//box-sizing: border-box;

	.nodata {
		width: 750rpx;
		height: 634rpx;
		min-width: 750rpx;
		min-height: 634rpx;
		//margin-left: -30rpx;
	}

	.span {
		position: absolute;
		top: 472rpx;
		//margin-left: -30rpx;
	}
}

@import "@/static/css/mixin.scss";

.homeBgc {
	width: 100vw;
	height: 100vh;
	top: 0;
	right: 0;
	position: absolute;
	z-index: -1 !important;
}

.topHeader {
	position: fixed;
}

.topImg {
	// position: fixed;
	height: 88rpx;
	width: 750rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 34rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 500;
	color: #000000;
	z-index: 996;

	.black {
		position: absolute;
		left: 32rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.returnFn {
		width: 48rpx;
		height: 48rpx;
	}
}

.header {
	padding: 16rpx 45rpx 0px 45rpx;
	display: flex;
	align-items: center;
	position: relative;

	.oneClickExport {
		position: absolute;
		top: 25rpx;
		right: 40rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		font-size: 28rpx;
		font-weight: normal;
		color: #3F4A59;

		image {
			width: 40rpx;
			height: 40rpx;
			margin-right: 6rpx;
		}
	}

	.iconBg {
		width: 56rpx;
		height: 56rpx;
		background: #FFFFFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.icon {
		width: 56rpx;
		height: 56rpx;
	}

	.span1 {
		font-size: 26rpx;
		font-weight: 500;
		color: #333333;
	}

	.span2 {
		font-size: 22rpx;
		font-weight: 500;
		color: #3F4A59;
	}
}

.headertext {
	padding: 24rpx 45rpx 0px 45rpx;
	font-size: 24rpx;
	color: #455487;
}

.optionBox {
	padding: 40rpx 36rpx 0px 36rpx;
	display: flex;
	justify-content: space-between;

	.optionItem {
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 400;
		font-size: 28rpx;
		width: 218rpx;
		height: 72rpx;
		border-radius: 8rpx;
		box-shadow: 0px 8rpx 24rpx 0px #EEF1F8;
		background: rgba(255, 255, 255, 0.6);
	}

	.Arrows {
		width: 22rpx;
		height: 22rpx;
		min-width: 22rpx;
		min-height: 22rpx;
		margin-left: 18rpx;
	}

	.pitchOn {
		color: #3370FF;
	}

	.noAccess {
		color: #86909C;
	}

	.unselected {
		color: #3F4A59;
	}
}

.total {
	padding: 30rpx 36rpx 30px 36rpx;
	font-size: 28rpx;
	color: #86909C;

	.totalSpan2 {
		color: #417FFF;
	}
}

.listBox {
	// #ifdef MP-WEIXIN
	height: calc(100vh - 560rpx);
	margin-top: 440rpx;
	// #endif
	// #ifdef H5
	height: calc(100vh - 400rpx);
	margin-top: 400rpx;
	// #endif

	width: calc(100vw - 72rpx);
	position: absolute;
	padding: 0rpx 36rpx 30px 36rpx;
	//overflow: auto;

	.card {
		width: calc(100% - 64rpx);
		// height: 210px;
		border-radius: 12rpx;
		background: #FFFFFF;
		box-shadow: 0px 8rpx 24rpx 0px #EEF1F8;
		margin-bottom: 24rpx;
		padding: 32rpx;
		padding-bottom: 100rpx;
		position: relative;

		.cadHeader {
			font-size: 16px;
			display: flex;
			align-items: center;

			.icon2 {
				height: 40rpx;
				width: 40rpx;
			}
		}

		.contentBox {
			width: calc(100% - 48rpx);
			border-radius: 8rpx;
			background: #FAFAFA;
			padding: 24rpx;

			.contentSpan {
				font-size: 28rpx;
				color: #555555 !important;
			}

			.contentSpan+.contentSpan {
				margin-top: 16rpx;
			}
		}

		.detail {
			position: absolute;
			right: 32rpx;
			bottom: 24rpx;
			font-size: 28rpx;
			color: #3370FF;
			display: flex;
			align-items: center;

			.righticon {
				width: 32rpx;
				height: 32rpx;
				margin-left: 10rpx;
			}
		}
	}
}
</style>