.homeBgc {
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  position: absolute;
  z-index: -1 !important;
}

.topHeader {
  position: fixed;
}

.topImg {
  // position: fixed;
  height: 88rpx;
  width: 750rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  color: #000000;
  z-index: 996;

  .black {
    position: absolute;
    left: 32rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .returnFn {
    width: 48rpx;
    height: 48rpx;
  }
}

.header {
  padding: 16rpx 45rpx 0px 45rpx;
  display: flex;
  align-items: center;
  position: relative;

  .oneClickExport {
    position: absolute;
    top: 25rpx;
    right: 40rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: normal;
    color: #3f4a59;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 6rpx;
    }
  }

  .iconBg {
    width: 56rpx;
    height: 56rpx;
    background: #ffffff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .icon {
    width: 56rpx;
    height: 56rpx;
  }
  .airui{
    margin-left: 20rpx;
  }

  .span1 {
    font-size: 26rpx;
    font-weight: 500;
    color: #333333;
  }

  .span2 {
    font-size: 22rpx;
    font-weight: 500;
    color: #3f4a59;
  }
}

.headertext {
  padding: 24rpx 45rpx 0px 45rpx;
  font-size: 24rpx;
  color: #455487;
}
