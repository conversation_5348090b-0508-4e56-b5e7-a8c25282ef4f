<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<image src="https://static.idicc.cn/cdn/aiChat/applet/bg.png" class="homeBgc"></image>
		<!-- #endif -->
		<view class="topHeader" :style="`top: ${statusBarHeight}px`">
			<!-- #ifndef H5 -->
			<view class="topImg">
				<view class="black" @click="goBack()">
					<image class="returnFn" src="~@/static/AboutAi/returnFn.png"></image>
				</view>
				{{ dataInfo.name }}
			</view>
			<!-- #endif -->
			<view class="header">
				<image :src="dataInfo.iconPath" class="icon"></image>
				<view class="airui">
					<view class="span1"> {{ dataInfo.name }}</view>
					<view class="span2">来自：艾瑞数云</view>
				</view>
			</view>
			<view class="headertext">
				{{ dataInfo.content }}
			</view>
			<view class="optionBox">
				<span :class="(parameterList[0] ? 'unselected' : 'pitchOn')" class="optionItem" @click="screenFn(1)">
					高管籍贯
					<!-- {{ regionFn() }} -->
					<image :src="iconColour(parameterList[0])" class="Arrows"></image>
				</span>
				<span :class="(parameterList[1] ? 'unselected' : 'pitchOn')" class="optionItem" @click="screenFn(2)">
					企业注册地
					<image :src="iconColour(parameterList[1])" class="Arrows"></image>
				</span>
				<span :class="(parameterList[2] ? 'unselected' : 'pitchOn')" class="optionItem" @click="screenFn(3)">
					更多查询
					<image :src="iconColour(parameterList[2])" class="Arrows"></image>
				</span>
			</view>
			<view class="total">
				共
				<span class="totalSpan2">{{ total }}</span>
				家企业
			</view>
		</view>
		<scroll-view class="listBox" @scrolltolower="scrolltolowerFn" @scroll="scroll"
			:style="`top: ${statusBarHeight}px`" scroll-y="true">
			<view>
				<view class="card" v-for="(i,index) in enterpriseList" :key="index">
					<view class="cadHeader">
						<image :src="i?.enterpriseIconLabelId == null
						          ? listicon[0].icon
						          : listicon[i.enterpriseIconLabelId].icon
						          " class="icon2"></image>
						<span @click="detailpage(i)" class="enterpriseName">{{ i?.enterpriseName || '-' }}
							<label v-if="isWithinLastWeek(i?.enterpriseUpdateDate)" class="new">新</label>
						</span>
					</view>
					<view class="contentBox">
						<view v-show="i?.refs" style="margin-bottom: 16rpx;" v-for="(detail, detailIndex) in i?.refs"
							:key="detailIndex">
							<p v-if="detail.relateName && detail.relateName!='-'" class="contentSpan">
								{{ '关联人' + ( i.refs.length==1 ? '' :`${detailIndex + 1}`) + `：` }}
								<label class="listValue">
									{{ detail?.relateName }}
								</label>
							</p>
							<p v-if="detail.positions && detail.positions.length!=0" class="contentSpan">职务： <label
									class="listValue">
									{{ detail?.positions.join('、')  }}</label>
							</p>
							<p v-if="detail.ancestorHome && detail.ancestorHome!='-'" class="contentSpan">籍贯： <label
									class="listValue">
									{{ detail?.ancestorHome }}</label>
							</p>
							<p v-if="detail.commerceNames && detail.commerceNames.length!=0" class="contentSpan">所属商会：
								<label class="listValue">
									{{ detail?.commerceNames.join('、') }}</label>
							</p>
						</view>
						<p v-if="i.enterpriseAddress && i.enterpriseAddress!='-'" class="contentSpan">注册地址：<label
								class="listValue">
								{{ i?.enterpriseAddress}}</label>
						</p>
						<p v-if="i.enterpriseContact && i.enterpriseContact!='-'" class="contentSpan">联系方式： <label
								class="listValue">
								{{ i?.enterpriseContact}}</label><label class="copy"
								@click="copyPhone(i?.enterpriseContact)">复制</label></p>
						<p v-if="i.enterpriseChain && i.enterpriseChain!='-'" class="contentSpan">所属产业链： <label
								class="listValue">
								{{ i?.enterpriseChain}}</label>
						</p>
						<!-- 	<p v-if="i.enterpriseName && i.enterpriseName!='-'" class="contentSpan">所属商会： <label
								class="listValue">
								{{ i?.enterpriseName}}</label>
						</p>
								<p v-if="i.enterpriseContact" class="contentSpan">所属商会联系方式： <label class="listValue">
								{{ i?.enterpriseContact}}<label class="copy"
									@click="copyPhone(i?.enterpriseContact)">复制</label></label>
						</p> -->
					</view>
				</view>
				<view style="height: 40px;"></view>
			</view>
		</scroll-view>
		<div v-if="enterpriseList.length == 0 && showNodata" class="nodatabox">
			<image class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata2.png"></image>
			<span class="span">暂无内容</span>
		</div>
	</view>
	<uni-popup ref="clueModelPop" background-color="#fff">
		<mapList ref="mapList" :cityName="cityName" :unrestricted="true" @affirm="affirm2">
		</mapList>
	</uni-popup>
	<!--高管籍贯 pop  -->
	<uni-popup ref="popup2" background-color="#fff">
		<mapList2 ref="mapList2" :showChain="showChain" :divisionCode="divisionCode" :cityName="cityName2"
			:unrestricted="true" @affirm="affirm1">
		</mapList2>
	</uni-popup>
	<moreScreen ref="inStrategyPop" :BottomBlank="bottomBlank" title="更多查询" :moreScreenList="inStrategyList"
		@updatamoreScreen="affirm3">
	</moreScreen>
</template>

<script>
	import mapList2 from '../../components/areaList.vue'
	import mapList from '../../components/mapList.vue'
	import moreScreen from '../../components/360screen/moreScreen.vue'
	import {
		getQueryParam,
		listicon,
		isWithinLastWeek,
	} from '@/utils/utils.js'
	export default {
		components: {
			mapList,
			mapList2,
			moreScreen
		},
		watch: {
			enterpriseList(value) {
				this.enterpriseList.forEach(it => {
					let arr = ['主板', '创业板', '科创板', '北交所', '港股', '中概股', '新三板'] //上市
					if (it.enterpriseIconLabelId === null) {
						it.enterpriseIconLabelId = 0
					}
					if (it.enterpriseLabelNames) {
						let Names = it.enterpriseLabelNames[0]
						if (!Names) {
							it.enterpriseIconLabelId = 0
						} else if (arr.includes(it.enterpriseLabelNames[0])) {
							it.enterpriseIconLabelId = 1
						} else if (Names.includes('独角兽')) {
							it.enterpriseIconLabelId = 2
						} else if (Names.includes('专精特新')) {
							it.enterpriseIconLabelId = 3
						} else if (Names.includes('隐形冠军')) {
							it.enterpriseIconLabelId = 4
						} else if (Names.includes('瞪羚')) {
							it.enterpriseIconLabelId = 5
						} else if (Names.includes('创新')) {
							it.enterpriseIconLabelId = 6
						} else if (Names.includes('技术先进')) {
							it.enterpriseIconLabelId = 7
						} else if (Names.includes('科技')) {
							it.enterpriseIconLabelId = 8
						} else if (Names.includes('雏鹰')) {
							it.enterpriseIconLabelId = 9
						} else {
							it.enterpriseIconLabelId = 0
						}
					} else {
						it.enterpriseIconLabelId = 0
					}
				})
			}
		},
		data() {
			return {
				statusBarHeight: wx.getStorageSync("statusBarHeight"),
				searchForm: {
					model: [],
					strategy: [],
					code: '',
				},
				cityName: '',
				cityName2: '',
				listicon,
				pageNum: 1,
				pages: 0,
				baseInfo: {},
				inStrategyList: [],
				enterpriseList: [],
				total: 0,
				token: '',
				showNodata: false,
				isLoading: false,
				dataInfo: {},
				bottomBlank: false,
				moreData: {},
				showChain: '',
				parameterList: [true, true, true],
				enterpriseRegionCode: "",
				ancestorRegionCode: '',
				divisionCode: '',
				notAncestorCodes: [],
			}
		},
		onLoad(options) {
			this.token = uni.getStorageSync('token')
			let id = options?.id || ''
			// #ifdef H5
			if (getQueryParam("token")) {
				this.token = getQueryParam("token");
				uni.setStorageSync("token", this.token);
			}
			if (getQueryParam("id")) {
				id = getQueryParam("id");
			}
			// #endif
			this.getDel(id)
			if (this.token) {
				this.getSearch()
				this.getChainId()
				//this.getList()
			} else {
				this.showNodata = true
			}
		},
		methods: {
			isWithinLastWeek,
			getChainId() {
				this.$api.choosechainAPI({
					method: 'get'
				}).then((obj) => {
					this.showChain = obj.result.chain2Id;
					this.getList()
					this.$nextTick(() => {
						this.$refs.mapList2.mapInfo(this.showChain)
					})

				});
			},
			getDel(id) {
				let data = {
					id,
				}
				this.$api.capabilityPoolDetailAPI({
					data,
				}).then(res => {
					this.dataInfo = res.result
				})
			},
			getSearch() {
				this.$api.getSearchParamAPI({
					method: "GET"
				}).then((res) => {
					this.inStrategyList = res.result
				})
			},
			getList() {
				if (this.isLoading) {
					return;
				}
				this.isLoading = true;
				let data = {
					pageSize: 10,
					pageNum: this.pageNum,
					chainId: this.showChain,
					...this.moreData,
				}
				if (this.enterpriseRegionCode) {
					data.enterpriseRegionCode = this.enterpriseRegionCode
				}
				if (this.ancestorRegionCode) {
					data.ancestorRegionCode = this.ancestorRegionCode
				}
				if (this.notAncestorCodes) {
					data.notAncestorCodes = this.notAncestorCodes
				}
				this.$api.ancestorListAPI({
					data,
					method: "post"
				}).then(res => {
					this.enterpriseList = this.enterpriseList.concat(res.result.data || []);
					this.pages = Math.ceil(res.result.total / 10);
					this.total = res.result.total
				}).finally(() => {
					this.isLoading = false;
					this.showNodata = true
				})
			},
			scrolltolowerFn() {
				if (this.isLoading) {
					return;
				}
				if (this.pages > this.pageNum) {
					this.pageNum++;
					this.getList()
				}
			},
			detailpage(item) {
				// #ifndef MP-WEIXIN
				window?.ReactNativeWebView?.postMessage(
					JSON.stringify({
						type: "changePath",
						value: {
							id: item.enterpriseId || item.id,
							iconTypeid: item.enterpriseIconLabelId,
							enterpriseName: item.enterpriseName,
							enterpriseLabelIds: JSON.stringify(this.enterpriseLabelIds || []),
						},
						path: "industryDetail",
					})
				);
				// #endif
				// #ifndef H5
				uni.navigateTo({
					url: `/pages/newMapEnterprise/components/enterprise?id=${item.enterpriseId || item.id
          }&iconTypeid=${item.enterpriseIconLabelId}&showChain=${this.showChain
          }&enterpriseName=${item.enterpriseName
          }`,
				});
				// #endif
			},
			goBack() {
				// #ifndef MP-WEIXIN
				// window?.ReactNativeWebView?.postMessage(
				// 	JSON.stringify({
				// 		type: "changePath",
				// 		value: '',
				// 		path: "goBack",
				// 	})
				// );
				// #endif
				// #ifndef H5
				uni.navigateBack({
					delta: 1
				});
				// #endif
			},
			goDetail(item) {
				let data = {
					enterpriseName: item.enterpriseName,
					uniCode: item.uniCode,
					enterpriseId: item.enterpriseId,
					recommendRegionCode: item.recommendRegionCode || '',
				}
				this.$api.reportGenerateAPI({
					data,
					method: "post"
				}).then(res => {
					// #ifndef MP-WEIXIN
					window?.ReactNativeWebView?.postMessage(
						JSON.stringify({
							type: "changePath",
							value: {
								url: 'previewPdf',
								name: '报告预览',
								params: {
									reportId: res.result,
									type: 'pdf',
									enterpriseName: item.enterpriseName + '招商策略报告'
								}
							},
							path: "webViewPage",
						})
					);
					// #endif
					// #ifndef H5
					uni.navigateTo({
						url: `/pages/excelView/index?id=${res.result}&type=pdf&enterpriseName=${item.enterpriseName + '招商策略报告'}`,
					});
					// #endif
				})
			},
			//省市区筛选展示
			regionFn() {
				let region = '全国';
				if (this.baseInfo?.area != null && this.baseInfo?.area.length != 0) {
					region = this.baseInfo.area[0];
				} else if (this.baseInfo.city) {
					region = this.baseInfo.city;
				} else if (this.baseInfo.province) {
					region = this.baseInfo.province;
				}
				if (region.length > 4) {
					region = region.substring(0, 3) + '...';
				}
				return region;
			},
			//是否选中
			iconColour(data) {
				if (data) {
					return '/static/AboutAi/blackArrows.png'
				} else {
					return '/static/AboutAi/blueArrows.png'
				}
			},
			screenFn(type) {
				if (!this.token) {
					uni.reLaunch({
						url: `/pages/login/index`
					});
				}
				if (type == 1) {
					this.$refs.popup2.open('bottom')
					// #ifndef MP-WEIXIN
					this.$nextTick(() => {
						if (this.$refs.mapList2) {
							this.$refs.mapList2.init()
						} else {
							console.warn('mapList2组件未挂载打开时')
						}
					})
					// #endif
				} else if (type == 2) {
					this.$refs.clueModelPop.open('bottom')
				} else if (type == 3) {
					this.$refs.inStrategyPop.opens()
				}
			},
			affirm1(data) {
				this.ancestorRegionCode = data.area.code ? data.area.code : ((data.citys.code ? data.citys.code : data
					.province.code || ''))
				this.notAncestorCodes = data.area.notAncestorCodes ? data.area.notAncestorCodes : ((data.citys
					.notAncestorCodes ? data.citys.notAncestorCodes : data
					.province.notAncestorCodes || []))
				this.$refs.popup2.close()
				// #ifndef MP-WEIXIN
				let province = data.province.name ? data.province.name + '/' : ''
				let citys = data.citys.name ? data.citys.name + '/' : ''
				let area = data.area.name ? data.area.name + '/' : ''
				this.cityName2 = province + citys + area
				// #endif
				this.enterpriseList = []
				this.showNodata = false
				this.pages = 1
				this.pageNum = 1
				this.total = 0
				this.getList(this.showChain);
				this.changeSelectType()
			},
			//省市区确认
			affirm2(data) {
				this.enterpriseRegionCode = data.area.code ? data.area.code : ((data.citys.code ? data.citys.code : data
					.province.code || ''))
				this.$refs.clueModelPop.close()
				// #ifndef MP-WEIXIN
				let province = data.province.name ? data.province.name + '/' : ''
				let citys = data.citys.name ? data.citys.name + '/' : ''
				let area = data.area.name ? data.area.name + '/' : ''
				this.cityName = province + citys + area
				// #endif
				this.enterpriseList = []
				this.showNodata = false
				this.pages = 1
				this.pageNum = 1
				this.total = 0
				this.getList(this.showChain);
				this.changeSelectType()
			},
			affirm3(data) {
				this.moreData = data
				this.enterpriseList = []
				this.showNodata = false
				this.pages = 1
				this.pageNum = 1
				this.total = 0
				this.getList(this.showChain);
				this.changeSelectType()
			},
			changeSelectType() {
				this.parameterList[0] = this.isEmptyValue(this.ancestorRegionCode)
				this.parameterList[1] = this.isEmptyValue(this.enterpriseRegionCode)
				this.parameterList[2] = this.isEmptyValue(this.moreData)
			},
			//判断是否完全为空
			isEmptyValue(value) {
				if (typeof value === 'string') {
					return value.trim() === '';
				}
				if (typeof value === 'number') {
					return value === 0;
				}
				if (typeof value === 'boolean') {
					return value === false;
				}
				if (Array.isArray(value)) {
					return value.every(this.isEmptyValue);
				}
				if (value !== null && typeof value === 'object') {
					return Object.values(value).every(this.isEmptyValue);
				}
				return false;
			},
			// 复制
			copyPhone(phone) {
				uni.setClipboardData({
					data: phone,
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						});
					}
				});
			}

		}
	}
</script>

<style lang="scss" scoped>
	.nodatabox {
		display: flex;
		align-items: center;
		flex-direction: column;
		font-weight: 400;
		font-size: 30rpx;
		color: #3f4a59;
		top: 520rpx;
		//margin-top: 520rpx;
		position: absolute;
		//box-sizing: border-box;

		.nodata {
			width: 750rpx;
			height: 634rpx;
			min-width: 750rpx;
			min-height: 634rpx;
			//margin-left: -30rpx;
		}

		.span {
			position: absolute;
			top: 472rpx;
			//margin-left: -30rpx;
		}
	}

	@import './xiangxian.scss';

	.optionBox {
		padding: 40rpx 36rpx 0px 36rpx;
		display: flex;
		justify-content: space-between;

		.optionItem {
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 28rpx;
			width: 218rpx;
			height: 72rpx;
			border-radius: 8rpx;
			box-shadow: 0px 8rpx 24rpx 0px #EEF1F8;
			background: rgba(255, 255, 255, 0.6);
		}

		.Arrows {
			width: 22rpx;
			height: 22rpx;
			min-width: 22rpx;
			min-height: 22rpx;
			margin-left: 18rpx;
		}

		.pitchOn {
			color: #3370FF;
		}

		.noAccess {
			color: #86909C;
		}

		.unselected {
			color: #3F4A59;
		}
	}

	.total {
		padding: 30rpx 36rpx 30px 36rpx;
		font-size: 28rpx;
		color: #86909C;

		.totalSpan2 {
			color: #417FFF;
		}
	}

	.listBox {
		// #ifdef MP-WEIXIN
		height: calc(100vh - 560rpx);
		margin-top: 515rpx;
		// #endif
		// #ifdef H5
		height: calc(100vh - 400rpx);
		margin-top: 460rpx;
		// #endif

		width: calc(100vw - 72rpx);
		position: absolute;
		padding: 0rpx 36rpx 30px 36rpx;
		//overflow: auto;

		.card {
			width: calc(100% - 64rpx);
			// height: 210px;
			border-radius: 12rpx;
			background: #FFFFFF;
			box-shadow: 0px 8rpx 24rpx 0px #EEF1F8;
			margin-bottom: 24rpx;
			padding: 32rpx;
			position: relative;

			.cadHeader {
				font-size: 16px;
				display: flex;
				align-items: center;

				.enterpriseName {
					margin-left: 10rpx;
				}

				.icon2 {
					height: 40rpx;
					width: 40rpx;
					min-width: 40rpx;
					min-height: 40rpx;
				}

				.new {
					padding: 2rpx 16rpx;
					background: #EE0A24;
					height: 29rpx;
					border-radius: 24rpx;
					margin-left: 10rpx;
					color: #fff;
					font-size: 20rpx;
				}
			}

			.contentBox {
				width: calc(100% - 48rpx);
				border-radius: 8rpx;
				background: #FAFAFA;
				padding: 24rpx;
				margin-top: 24rpx;

				.contentSpan {
					font-size: 24rpx;
					color: #767676 !important;
				}

				.listValue {
					font-size: 24rpx;
					color: #1A1A1A;
					white-space: pre-wrap;
					word-break: break-all;
				}

				.copy {
					color: #3370FF;
					margin-left: 10rpx;
				}

				.contentSpan+.contentSpan {
					margin-top: 16rpx;
				}
			}

			.detail {
				position: absolute;
				right: 32rpx;
				bottom: 24rpx;
				font-size: 28rpx;
				color: #3370FF;
				display: flex;
				align-items: center;

				.righticon {
					width: 32rpx;
					height: 32rpx;
					margin-left: 10rpx;
				}
			}
		}
	}
</style>