<template>
	<view>
		<image src="https://static.idicc.cn/cdn/aiChat/applet/bg.png" class="homeBgc"></image>
		<view class="topHeader" :style="`top: ${statusBarHeight}px`">
			<!-- #ifndef H5 -->
			<view class="topImg">
				<view class="black" @click="goBack()">
					<image class="returnFn" src="~@/static/AboutAi/returnFn.png"></image>
				</view>
				{{dataInfo.name}}
			</view>
			<!-- #endif -->
			<view class="header">
				<image :src="dataInfo.iconPath" class="icon"></image>
				<view class="mgl-20">
					<view class="span1"> {{dataInfo.name}}</view>
					<view class="span2">来自：艾瑞数云</view>
				</view>
				<div v-if="token" @click="go()" class="historyBox">
					<image class="icon2" src="../../static/dialog/history.png"></image>
					我的报告
				</div>
			</view>
			<view class="headertext">
				{{dataInfo.content}}
			</view>
		</view>
		<view class="timeDimensionBox">
			<div class="timeTitle">请选择时间维度</div>
			<div @click="changeDimension(item.id)" class="timeItem" v-for="(item,index) in timeDimension"
				:key="item.id">
				{{item.name}}
				<div :class="timeDimensionId==item.id ? 'circle-with-dot' : 'circle'"></div>
			</div>
		</view>
		<div @click="DownloadFn" class="deriveBtn">导出生成</div>
	</view>
</template>

<script>
	import {
		getQueryParam
	} from '@/utils/utils.js'
	export default {
		data() {
			return {
				statusBarHeight: wx.getStorageSync("statusBarHeight"),
				token: '',
				dataInfo: {},
				timeDimensionId: 1,
				timeDimension: [{
					name: '今日',
					id: 1,
				}, {
					name: '近一周',
					id: 7,
				}, {
					name: '近一个月',
					id: 30,
				}, {
					name: '近三个月',
					id: 90,
				}, {
					name: '全部',
					id: 0,
				}]
			}
		},
		onLoad(options) {
			this.token = uni.getStorageSync('token')
			let id = options?.id || ''
			// #ifdef H5
			if (getQueryParam("token")) {
				this.token = getQueryParam("token");
				uni.setStorageSync("token", this.token);
			}
			if (getQueryParam("token")) {
				id = getQueryParam("id");
			}
			// #endif
			this.getDel(id)
		},
		methods: {
			go() {
				let token = uni.getStorageSync("token");
				if (!token) {
					return uni.reLaunch({
						url: `/pages/login/index`,
					});
				}
				// #ifndef MP-WEIXIN
				window?.ReactNativeWebView?.postMessage(
					JSON.stringify({
						type: "changePath",
						value: {
							type: 'deriveExcel',
						},
						path: "history",
					})
				);
				// #endif
				// #ifndef H5
				uni.navigateTo({
					url: `/pages/history/index?isEditor=1&type=deriveExcel`,
				});
				// #endif
			},
			changeDimension(id) {
				let token = uni.getStorageSync("token");
				if (!token) {
					return uni.reLaunch({
						url: `/pages/login/index`,
					});
				}
				this.timeDimensionId = id
			},
			getDel(id) {
				let data = {
					id,
				}
				this.$api.capabilityPoolDetailAPI({
					data,
				}).then(res => {
					this.dataInfo = res.result
				})
			},
			goBack() {
				// #ifndef MP-WEIXIN
				// window?.ReactNativeWebView?.postMessage(
				// 	JSON.stringify({
				// 		type: "changePath",
				// 		value: '',
				// 		path: "goBack",
				// 	})
				// );
				// #endif
				// #ifndef H5
				uni.navigateBack({
					delta: 1
				});
				// #endif
			},
			//直接传入路径
			DownloadFn() {
				let token = uni.getStorageSync("token");
				if (!token) {
					return uni.reLaunch({
						url: `/pages/login/index`,
					});
				}
				this.$api.enterpriseExportAPI({
					data: {
						days: this.timeDimensionId
					},
					method: 'post'
				}).then((res) => {
					if (res.code == "SUCCESS" && res.result?.ossUrl) {
						;
						// #ifdef H5
						window?.ReactNativeWebView?.postMessage(
							JSON.stringify({
								type: "exportFile",
								value: {
									name: res.result.reportName,
									fileType: "xlsx",
									url: res.result.ossUrl
								},
							})
						);
						// #endif
						// #ifdef MP-WEIXIN
						uni.showLoading({
							title: '加载中'
						});
						let options = {
							DownloadName: res.result.reportName,
							DownloadPath: res.result.ossUrl
						}
						var tablePath = wx.env.USER_DATA_PATH + `/${options?.DownloadName}` + ".xlsx"; //微信内部虚拟路径
						uni.downloadFile({
							method: "get",
							url: decodeURIComponent(options?.DownloadPath),
							timeout: 500000,
							filePath: tablePath,
							success: (data) => {
								if (data.statusCode === 200) {
									uni.hideLoading();
									uni.openDocument({
										filePath: tablePath,
										showMenu: true,
										success: function(res) {},
									});

								}
							},
							fail: (err) => {
								uni.showToast({
									icon: "none",
									mask: true,
									title: "失败请重新下载",
									duration: 3000,
								});
							},
						});
						// #endif
					}

				})
			},
			generateExcel(item) {
				let data = {
					enterpriseName: item.enterpriseName,
					uniCode: item.uniCode,
					enterpriseId: item.enterpriseId,
					recommendRegionCode: item.recommendRegionCode || '',
				}
				this.$api.reportGenerateAPI({
					data,
					method: "post"
				}).then(res => {
					// #ifndef MP-WEIXIN
					window?.ReactNativeWebView?.postMessage(
						JSON.stringify({
							type: "changePath",
							value: {
								url: 'previewPdf',
								name: '报告预览',
								params: {
									reportId: res.result,
									type: 'pdf',
									enterpriseName: item.enterpriseName
								}
							},
							path: "webViewPage",
						})
					);
					// #endif
					// #ifndef H5
					uni.navigateTo({
						url: `/pages/excelView/index?id=${res.result}&type=pdf&enterpriseName=${item.enterpriseName}`,
					});
					// #endif
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.historyBox {
		height: 60rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #3F4A59;
		display: flex;
		align-items: center;
		position: absolute;
		top: 14rpx;
		right: 40rpx;

		.icon2 {
			width: 40rpx;
			height: 40rpx;
			min-width: 40rpx;
			min-height: 40rpx;
			margin-right: 8rpx;
		}
	}

	.deriveBtn {
		position: fixed;
		left: 32rpx;
		bottom: 100rpx;
		width: 686rpx;
		height: 88rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(141deg, #AEC6FF 7%, #1F61FF 71%), #3370FF;
		font-size: 30rpx;
		font-weight: 500;
		color: #FFFFFF;
		border-radius: 20rpx;
	}

	.timeDimensionBox {
		// #ifdef MP-WEIXIN
		margin-top: 372rpx;
		// #endif
		// #ifdef H5
		margin-top: 230rpx;
		// #endif
		position: absolute;
		padding: 0 45rpx;
		width: 100%;
		box-sizing: border-box;

		.circle {
			width: 32rpx;
			height: 32rpx;
			background-color: #ffffff;
			border: 2px solid #CED4DB;
			border-radius: 50%;
			box-sizing: border-box;
		}

		.circle-with-dot {
			position: relative;
			width: 32rpx;
			height: 32rpx;
			background-color: #ffffff;
			border: 2px solid #CED4DB;
			border-radius: 50%;
			box-sizing: border-box;
		}

		.circle-with-dot::after {
			content: "";
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 8px;
			height: 8px;
			background-color: #3370FF;
			border-radius: 50%;
		}

		.timeTitle {
			font-weight: 500;
			font-size: 26rpx;
			color: #000;
			margin-bottom: 22rpx;
		}

		.timeItem {
			width: 100%;
			height: 70rpx;
			border-radius: 12rpx;
			opacity: 1;
			background: #FFFFFF;
			box-shadow: 0px 4px 12px 0px #EEF1F8;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 28rpx;
			box-sizing: border-box;
			margin-bottom: 24rpx;
			color: #3F4A59;
			font-size: 28rpx;
			font-weight: normal;
		}
	}

	.homeBgc {
		width: 100vw;
		height: 100vh;
		top: 0;
		right: 0;
		position: absolute;
		z-index: -1 !important;
	}

	.topHeader {
		position: fixed;
	}

	.topImg {
		// position: fixed;
		height: 88rpx;
		width: 750rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 34rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		color: #000000;
		z-index: 996;

		.black {
			position: absolute;
			left: 32rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.returnFn {
			width: 48rpx;
			height: 48rpx;
		}
	}

	.header {
		padding: 16rpx 45rpx 0px 45rpx;
		display: flex;
		align-items: center;
		position: relative;

		.icon {
			width: 56rpx;
			height: 56rpx;
		}

		.span1 {
			font-size: 26rpx;
			font-weight: 500;
			color: #333333;
		}

		.span2 {
			font-size: 22rpx;
			font-weight: 500;
			color: #3F4A59;
		}
	}

	.headertext {
		padding: 24rpx 45rpx 0px 45rpx;
		font-size: 24rpx;
		color: #455487;
	}
</style>