<template>
	<page-meta page-style="background-color: #eff3fa"></page-meta>
	<!-- :style="`padding-top: ${statusBarHeight}px;`" -->
	<image src="https://static.idicc.cn/cdn/aiChat/applet/newHome/homeBgc2.png" class="homeBgc"></image>
	<div class="box">
		<div v-if="!showTop" class="homeTop2">
			<image class="homeTop2Img" src="https://static.idicc.cn/cdn/aiChat/applet/newHome/homeTop.png"></image>
			<div class="homeTop2Const">
				<image class="AIR" src="https://static.idicc.cn/cdn/aiChat/applet/newHome/dada.png"></image>
				<view class="TopOrgName" v-if="!isDefault && orgName != ''" :style="{
					fontSize: `${orgName.length > 10 ? '24rpx' : '34rpx'}`,
				}">{{ orgName }}</view>
			</div>
		</div>
		<div v-if="!showTop" class="gotopBox">
			<image @click="goTopFn()" class="gotopImg" src="/static/AboutAi/gotop.png"></image>
		</div>
		<scroll-view :scroll-top="distanceTop" style="height: 100vh;" @scroll="scrollFn" scroll-y="true"
			@scrolltolower="scrolltolowerFn">
			<div :style="`margin-top: ${statusBarHeight}px;`" class="homeTop">
				<div class="writer">
					<div class="airBox">
						<image class="AIR" src="https://static.idicc.cn/cdn/aiChat/applet/newHome/dada.png"></image>
						<view class="orgName" v-if="!isDefault && orgName != ''" :style="{
							fontSize: `${orgName.length > 10 ? '24rpx' : '34rpx'}`,
						}">{{ orgName }}</view>
					</div>

					<span class="AIRText">研·判·招·管·服 — 产业AI赋能平台</span>
					<image class="robot" src="https://static.idicc.cn/cdn/aiChat/applet/newHome/robot2.png"></image>
				</div>
			</div>
			<!-- 下拉 -->
			<div class="seinput">
				<tree-select ref="treeSelect" class="changeIndustry" select-tile="切换产业链" options-key="value"
					:value="defaultValue" :initial-value="initialValue" primary="value" :options="list"
					@confirm="onConfirm" :noLimit='true'>
					<div class="selectBox" style="display: flex;align-items: center;">
						<span style="white-space: nowrap;">{{ title ? (title.length > 5 ? title.substring(0, 4) + '...'
							: title) :
							'低空经济' }}</span>
						<image src="/static/AboutAi/Polygon.png" class="Polygon"></image>
					</div>
				</tree-select>
			</div>
			<!-- 最新消息 -->
			<div @click="GotoMessage()" v-if="informationList && informationList?.length !== 0" class="information">
				<div class="informationTitle" style="margin-bottom: 4px;">
					<div class="text">
						<div class="textTitle">最新消息</div>
						<image class="adornBgc" src="/static/AboutAi/adornBgc.png"></image>
					</div>
					<div v-if="informationList?.length != 0" style="display: flex;align-items: center;">
						<div v-if="messagesNumber != 0" class="newMessage">
							您有新消息未读 <div class="quantity">
								{{ messagesNumber > 99 ? '99+' : messagesNumber }}
							</div>
						</div>
						<image src="../../static/user/right.png" class="righticon"></image>
					</div>
				</div>
				<div @click.stop="GotoMessage(item.type)" v-for="(item, index) in informationList" :key="index"
					class="informationItem">
					<span class="informationWriter">
						<image :src="showTypeIcon(item.bizType)" class="informationIcon">
						</image>
						{{ showTypeText(item.bizType) }}
						<div v-if="item.schemaType == 20102" class="typetext">
							您的委托单已支付成功，支付{{ item?.contentJson?.entrustMsgContent?.amount }}元，请到我的委托中查看
						</div>
						<div v-else class="typetext">{{ item.content }}</div>
					</span>
					<span class="time">
						{{ informationFormatDate(item.gmtCreate) }}
					</span>
				</div>
			</div>
			<!--  产业洞察-->
			<div v-if="dataBoardList.length != 0" class=" information">
				<div class="informationTitle" style="margin-bottom: 4px;">
					<div class="text">
						<div class="textTitle">产业洞察</div>
						<image class="adornBgc" src="/static/AboutAi/adornBgc.png"></image>

					</div>
				</div>
				<div class="dataPresentationBox">
					<div @click="skip(item, 1)" class="dataPresentationItem" v-for="(item, index) in dataBoardList"
						:key="index">
						<view class="icons">
							<image class="iconImg" :src="item.icon"></image>
							<image v-if="isWithinLastWeek(item.lastUpdateDate)" class="upBgc"
								src="/static/AboutAi/up.png"></image>
						</view>
						<div class="count">
							{{ item.count || '-' }}
						</div>
						<div class="desc">
							{{ item.desc }}
						</div>
					</div>
				</div>
			</div>
			<!-- 回归工程 -->
			<div v-if="dataBoardList.length != 0" class=" information">
				<div class="informationTitle" style="margin-bottom: 4px;">
					<div class="text">
						<div class="textTitle">{{ shortProvinceName }}商回归</div>
						<image class="adornBgc" src="/static/AboutAi/adornBgc.png"></image>
					</div>
				</div>
				<div class="dataPresentationBox2">
					<div @click="skip(item, 2)" class="dataProjectItem" v-for="(item, index) in dataProjectList"
						:key="index">
						<div class="leftBox">
							<div class="desc">
								{{ item.desc }}
							</div>
							<div class="count">
								<!-- <span class="text"> {{ item.count || '-' }}</span>/{{ item.count || '-' }} -->
								{{ item.count || '-' }}
							</div>
						</div>
						<image :src="item.icon"></image>
					</div>
				</div>
			</div>
			<div class="swiper">
				<div class="switem" @click="go(item)" v-for="(item, index) in swiperList" :key="item.name">
					<image class="switemImg" :src="item.icon"></image>
					<div v-if="item.id === 2 && Number(historyListTotal) > 0" class="newNumber"> {{ historyListTotal }}家
					</div>
					<span class="name"> {{ item.name }}</span>
				</div>
			</div>
			<!--产业报告速编|产业图谱绘制  -->
			<div v-if="anthologyId || atlasId" class="Library">
				<div @click="goChat(anthologyId, '产业报告速编')" v-if="anthologyId" class="leftLibrary">
					<image src="https://static.idicc.cn/cdn/aiChat/applet/newHome/leftBox.png"></image>
					<span class="CapabilityName">产业报告速编</span>
				</div>
				<div @click="goChat(atlasId, '产业图谱绘制')" v-if="atlasId" class="rightLibrary">
					<image src="https://static.idicc.cn/cdn/aiChat/applet/newHome/rightBox.png"></image>
					<span class="CapabilityName">产业图谱绘制</span>
				</div>
			</div>

			<div v-if="posterList.length != 0" class="poster">
				<uni-swiper-dot :info="urlList" :current="current" :dots-styles="dotsStyles" field="content" mode="dot">
					<view class="uni-margin-wrap">
						<swiper @change="change" :circular='true' easing-function="easeOutCubic" class="swiperposter"
							:autoplay="autoplay" :interval="interval" :duration="duration">
							<swiper-item @click="goDetails(item.id, 2)" v-for="(item, index) in posterList"
								:key="index">
								<image class="bgc" :src="item.imageUrl"></image>
							</swiper-item>
						</swiper>
					</view>
				</uni-swiper-dot>
			</div>
			<div v-if="messageList.length !== 0" class="information">
				<div class="informationTitle">
					<div class="text">
						<div class="textTitle">即时资讯
						</div>
						<image class="adornBgc" src="/static/AboutAi/adornBgc.png"></image>
					</div>
				</div>
				<div @click="goDetails(item.id, 1)" v-for="(item, index) in messageList" :key="index"
					:class="index != messageList.length - 1 ? 'messageItem' : 'messageItems'">
					<div class="messageContent" :style="{ width: item.imageUrl ? 'calc(100% - 140rpx)' : '100%' }">
						<div class="messageTitle">
							<image v-if="item.isTop" src="/static/AboutAi/placeTop.png" class="placeTop"></image>
							{{ item.title }}
						</div>
						<div class="messagebtm">
							<div class="messageTime">{{ InterceptTime(item.publishDate) }}</div>
							<div class="source">来源：{{ item.accordingTo }}</div>
						</div>
					</div>
					<image v-if="item.imageUrl" :src="item.imageUrl" class="messageImg"></image>
				</div>
			</div>
			<div class="showTextClass" v-if="messageList.length !== 0 && showText && messageList.length > 5">
				— 我也是有底线的 —</div>
			<div style="height: 100rpx;">
			</div>
			<uni-popup ref="chargeback" :is-mask-click="false" type="dialog">
				<div class="applyaffirm">
					<div v-if="!failtoApply" class="p1">您即将加入{{ realName }}的团队</div>
					<div v-if="!failtoApply" class="p2">您只能加入一个产业顾问团队，请仔细斟酌。
					</div>
					<div v-if="failtoApply" class="p2">
						{{ failtoApplyText }}
					</div>
					<div v-if="!failtoApply" class="popBtn">
						<div @click="claimFn" class="canleBt">
							取消
						</div>
						<div @click="retreat" class="affirm">
							加入团队
						</div>
					</div>
					<div v-if="failtoApply" @click="claimFn" class="entrustBtn">确认</div>
				</div>
			</uni-popup>
		</scroll-view>
	</div>

</template>

<script>
	import treeSelect from '../../components/treeSelect/index2.vue';
	import {
		isWithinLastWeek
	} from '@/utils/utils.js'
	export default {
		components: {
			treeSelect
		},
		data() {
			return {
				dataBoardList: [],
				dataProjectList: [],
				presentationList: [{
						name: "产业洞察",
						type: 1,
					},
					{
						name: "回归工程",
						type: 2,
					},
				],
				presentationId: 1,
				token: '',
				title: '',
				current: 0,
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,
				distanceTop: 0,
				defaultValue: [],
				initialValue: [],
				list: [],
				dotsStyles: {
					//bottom: 48,
					backgroundColor: '#a8c4fd',
					border: '#a8c4fd',
					selectedBackgroundColor: '#FFFFFF',
					selectedBorder: '#FFFFFF'
				},
				posterList: [],
				urlList: [],
				swiperList: [
					// {
					// name: "产业360",
					// icon: 'https://static.idicc.cn/cdn/aiChat/applet/newHome/360.png',
					// url: "/pages/newMapEnterprise/index2",
					// id: 1
					// },
					{
						name: "历史智推",
						icon: 'https://static.idicc.cn/cdn/aiChat/applet/newHome/yiqiyice.png',
						url: "/pages/strategy/index?id=5",
						id: 2
					}, {
						name: "哒达助招",
						icon: 'https://static.idicc.cn/cdn/aiChat/applet/newHome/zhuzhao.png',
						url: "/pages/repository/attract",
						id: 3
					}, {
						name: "招商管理",
						icon: 'https://static.idicc.cn/cdn/aiChat/applet/newHome/zhaoshang.png',
						url: "/attractionManage/index",
						id: 4
					}
				],
				statusBarHeight: 0,
				informationList: [],
				messageList: [],
				addshow: false,
				failtoApply: false,
				failtoApplyText: '',
				realName: '',
				invitationCode: '',
				showTop: true,
				AI2jurisdiction: {},
				messagesNumber: 0,
				orgName: '',
				pageNum: 1,
				pages: 0,
				isLoading: false,
				showText: false,
				Jurisdiction: false,
				isDefault: '',
				onShowUpdata: false,
				cutLoading: false,
				atlasId: '',
				anthologyId: "",
				tuPoisOld: false,
				historyListTotal: 0,
				shortProvinceName: '浙'
			}

		},
		onLoad(options) {
			//console.log(options, 'options');
			//状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			const safeArea = systemInfo.screenHeight - systemInfo.safeArea.bottom;
			const safeAreaRpx = safeArea + 6 + 'rpx'
			uni.setStorageSync('safeAreaRpx', safeAreaRpx);
			this.statusBarHeight = uni.getWindowInfo().statusBarHeight //状态栏高度
			uni.setStorageSync('statusBarHeight', this.statusBarHeight);
			//是否第一次
			let newUser = ''
			newUser = wx.getStorageSync('newUser')

			if (newUser == '' && newUser !== false) {
				if (options.scene) {
					return uni.reLaunch({
						url: `/pages/transition/index?teamCode=${options.scene}`
					})

				} else {
					return uni.reLaunch({
						url: '/pages/transition/index'
					})
				}
			}
			this.info()
			this.getList()
			let teamCode = uni.getStorageSync("teamCode")
			this.orgName = uni.getStorageSync('orgName')
			this.isDefault = uni.getStorageSync('isDefault')

			if (options.scene) {
				this.jointheteam(options.scene)
				wx.setStorageSync('teamCode', options.scene)
			} else if (teamCode) {
				this.jointheteam(teamCode)
			}
			setTimeout(() => {
				this.addshow = true
			}, 500)
			this.getInformationList()

		},
		onShow() {
			this.getindustryList()
			wx.hideHomeButton()
			if (this.addshow) {
				let teamCode = uni.getStorageSync("teamCode")
				if (teamCode) {
					this.jointheteam(teamCode)
				}
			}
			//this.getInformationList()
			if (this.onShowUpdata) {
				this.updataMessageList()
				this.updataList()
				if (this.token) {
					this.whereTogo()
				}
			}

			this.getInformation()
		},
		methods: {
			isWithinLastWeek,
			getListByYiqiyice(id) {
				let data = {
					pageSize: 5,
					pageNum: 1,
					code: '',
					modelTypes: [],
					strategyTypes: [],
					chainId: this.AI2jurisdiction.chain2Id,
				}
				this.$api.reportListAPI({
					data,
					loading: 'no',
					method: "post"
				}).then(res => {
					this.historyListTotal = res.result.total
				}).finally(() => {
					// this.isLoading = false;
					// this.showNodata = true
				})
			},
			skip(data, type) {
				if (this.cutLoading) {
					console.log('拦截！');
					return
				}
				if (!this.token) {
					return uni.reLaunch({
						url: `/pages/login/index`
					})
				}
				if (!this.Jurisdiction && type == 1 && !!data.count) {
					return uni.showToast({
						title: '该产业链未开通会员，请在我的-会员中心开通后查看',
						icon: "none",
						duration: 2000,
					});
				}
				if (!data.count) {
					return uni.showToast({
						title: '敬请期待',
						icon: "none",
						duration: 2000,
					});
				}
				let id = data.abilityId;
				let showChain = this.AI2jurisdiction.chain2Id;
				let title = this.AI2jurisdiction.knowType2Name;
				let knowId = this.AI2jurisdiction.knowType2Id;
				if (id == 0) {

					uni.navigateTo({
						url: `/pages/newMapEnterprise/index2?showChain=${showChain}&title=${title}&knowId=${knowId}`
					});
					return
				} else if (id === '7' && data.desc === '乡贤企业') {
					uni.navigateTo({
						url: `/pages/industry/xiangxian?showChain=${showChain}&title=${title}&knowId=${knowId}&id=${id}`
					});
					return
				}
				uni.navigateTo({
					url: `/pages/chat/index?abilityId=${id}&isOld=${false}&question=${data.suggestQuestion}`,
				});
			},
			changeTabs(type) {
				if (this.presentationId == type) return
				this.presentationId = type
			},
			getHomeDataModule() {
				this.$api.homeDataModuleAPI({
						data: {
							type: 1,
							chainId: this.AI2jurisdiction.chain2Id,
						},
						loading: 'no',
					}).then(res => {
						this.dataBoardList = res.result
					})
					.finally(() => {
						this.cutLoading = false
					})
			},
			getHomeDataModule2() {
				this.$api.homeDataModuleAPI({
					data: {
						type: 2,
						chainId: this.AI2jurisdiction.chain2Id,
					},
					loading: 'no',
				}).then(res => {
					this.dataProjectList = res.result
				})
			},
			goChat(id, name) {
				if (!this.token) {
					return uni.reLaunch({
						url: `/pages/login/index`
					})
				}
				if (!this.Jurisdiction && name == "产业报告速编") {
					return uni.showToast({
						title: '该产业链未开通会员，请在我的-会员中心开通后查看',
						icon: "none",
						duration: 2000,
					});
				}
				uni.navigateTo({
					url: `/pages/chat/index?abilityId=${id}&abilityName=${name}&isOld=${this.tuPoisOld}&showInput=${true}&showIntroduce=${true}`
				});
			},
			getList() {
				this.$api.queryCapabilityListWithoutPermission({
					data: {
						type: 2
					},
					method: 'post'
				}).then(res => {
					res.result.forEach(it => {
						if (it.name == "产业报告速编") {
							this.anthologyId = it.id
						}
						if (it.name == "产业图谱绘制") {
							this.atlasId = it.id
							this.tuPoisOld = it.isOld
						}
					})
				})
			},
			quickSelection(e, oneEcho, twoEcho) {
				this.$api
					.updatechoosechainAPI({
						data: {
							capType: 2,
							knowId: e.knowId
						},
						method: 'get'
					})
					.then((res) => {
						this.defaultValue = [oneEcho, twoEcho];
						this.initialValue = [oneEcho, twoEcho];
						this.title = e.knowledgeName;
						this.$api.choosechainAPI({
								method: 'get'
							})
							.then((res) => {
								if (!res.result.knowType2Name) {
									return;
								}
								this.Jurisdiction = true
								this.AI2jurisdiction = res.result;
								this.getHomeDataModule()
								this.getListByYiqiyice(this.defaultValue[1])
							});
					});
			},
			onConfirm(e, fast, oneEcho, twoEcho) {
				this.cutLoading = true
				if (fast) {
					this.quickSelection(e, oneEcho, twoEcho)
					return
				}
				let {
					selected,
					values
				} = e;
				if (selected[1]?.categoryBuyStatus != undefined) {
					if (this.token) {
						this.$api
							.updatechoosechainAPI({
								data: {
									capType: 2,
									knowId: values[1]
								},
								method: 'get'
							})
							.then((res) => {
								this.defaultValue = [values[0], values[1]];
								this.initialValue = [values[0], values[1]];
								this.title = selected[1]?.chainName;

								this.$api.choosechainAPI({
										method: 'get'
									})
									.then((res) => {
										if (!res.result.knowType2Name) {
											return;
										}
										this.AI2jurisdiction = res.result;
										this.$api.checkByKnowIdAPI({
											data: {
												knowId: this.AI2jurisdiction.knowType2Id
											},
											method: "GET"
										}).then((res2) => {
											this.Jurisdiction = res2.result
										})
										this.getHomeDataModule()
										this.getListByYiqiyice(values[1])
									});
							});
					} else {
						this.Jurisdiction = false
						this.defaultValue = [values[0], values[1]];
						this.initialValue = [values[0], values[1]];
						this.title = selected[1]?.chainName;
						this.AI2jurisdiction = {
							category2Id: values[0],
							knowType2Id: values[1],
							knowType2Name: selected[1]?.chainName,
							chain2Id: selected[1]?.chainId,
						}
						this.getHomeDataModule()
						this.getListByYiqiyice(values[1])
					}

				} else {
					return;
				}
			},
			echoChain() {
				//debugger
				this.$api.choosechainAPI({
						method: 'get'
					})
					.then((res) => {
						if (!res.result.knowType2Name) {
							return;
						}
						this.AI2jurisdiction = res.result;
						this.title = res.result.knowType2Name;
						this.defaultValue = [
							res.result.category2Id, res.result.knowType2Id
						];
						this.initialValue = [
							res.result.category2Id, res.result.knowType2Id
						];
						this.$api.checkByKnowIdAPI({
							data: {
								knowId: res.result.knowType2Id
							},
							method: "GET"
						}).then((res) => {
							this.Jurisdiction = res.result
						})
						this.getHomeDataModule()
						this.getHomeDataModule2()
						this.getListByYiqiyice(this.defaultValue[1])
						//回显
						setTimeout(() => {
							let Arr = this.list.map(it => {
								if (it.value == this.initialValue[0]) {
									this.$refs.treeSelect.echo(this.list, it.children);
								}
							});
						}, 1000);
					});
			},
			updataList() {
				this.$api
					.industrychainList({
						data: {
							capType: 2
						},
						loading: 'no',
						method: 'get'
					}).then(res => {
						let industryChainList = res.result.map((item) => {
							let newChainList = item.chainList.map((childrenItem) => {
								return {
									...childrenItem,
									label: childrenItem.chainName,
									value: childrenItem.knowId,
									categoryBuyStatus: childrenItem.buyStatus
								};
							});
							let {
								chainList,
								...rest
							} = item;
							return {
								...rest,
								label: item.categoryName,
								value: item.categoryId,
								children: newChainList
							};
						});
						this.list = industryChainList
						this.$refs.treeSelect.updateData(this.list)
						this.$refs.treeSelect.getViP()
					})
			},
			getindustryList() {
				this.$api
					.industrychainList({
						data: {
							capType: 2
						},
						loading: 'no',
						method: 'get'
					})
					.then((res) => {
						let industryChainList = res.result.map((item) => {
							let newChainList = item.chainList.map((childrenItem) => {
								return {
									...childrenItem,
									label: childrenItem.chainName,
									value: childrenItem.knowId,
									categoryBuyStatus: childrenItem.buyStatus
								};
							});
							let {
								chainList,
								...rest
							} = item;
							return {
								...rest,
								label: item.categoryName,
								value: item.categoryId,
								children: newChainList
							};
						});
						this.list = industryChainList
						//调接口获取已经选中的
						if (this.token) {
							this.echoChain();
						} else {
							let arr = this.list.flatMap(item =>
								item.children
								.filter(it => it.chainName === '低空经济')
								.map(it => [item.categoryId, it.knowId, it.chainName, it.chainId])
							);
							arr = arr.length == 0 ? [this.list[0].categoryId, this.list[0].children[0].knowId, this
								.list[0].children[0].chainName, this.list[0].children[0].chainId
							] : arr
							this.AI2jurisdiction = {
								category2Id: arr[0][0],
								knowType2Id: arr[0][1],
								knowType2Name: arr[0][2],
								chain2Id: arr[0][3],
							}
							this.title = this.AI2jurisdiction.knowType2Name;
							this.defaultValue = [this.AI2jurisdiction.category2Id, this.AI2jurisdiction.knowType2Id]
							this.initialValue = [this.AI2jurisdiction.category2Id, this.AI2jurisdiction.knowType2Id]
							this.getHomeDataModule()
							this.getHomeDataModule2()
							this.getListByYiqiyice(this.defaultValue[1])
							//回显
							setTimeout(() => {
								let Arr = this.list.map(it => {
									if (it.value == this.initialValue[0]) {
										this.$refs.treeSelect.echo(this.list, it.children);
									}
								});
							}, 1000);
						}

					});
			},
			updataMessageList() {
				let data = {
					pageNum: 1,
					pageSize: this.pageNum * 5,
				}
				this.$api.newsAdminPage({
					data,
					method: "get",
				}).then(res => {
					//this.messageList = this.messageList.concat(res.result.records);
					this.messageList = res.result.records
					this.pages = Math.ceil(Number(res.result.total) / 5);
				})
			},
			InterceptTime(dateStr) {
				let strArr = dateStr.split(':')
				if (strArr.length === 3) {
					dateStr = strArr[0] + ':' + strArr[1]
				}
				return dateStr
			},
			/**
			 * 时间格式化
			 * @param inputDateStr 时间字符串 列如："2024-05-16 22:28:15"
			 * @return 两天内返回 昨天 22:28 今天：22:28，三天以上七天以内返回：周(1,2,3,4,5,6,7) 22:28 ，如果是更久以前则返回 05-16 22:28
			 */
			informationFormatDate(inputDateStr) {
				// 解析输入的日期字符串
				inputDateStr = new Date((inputDateStr).replace(/-/g, '/'))
				const inputDate = new Date(inputDateStr);
				if (isNaN(inputDate.getTime())) {
					throw new Error('Invalid date string');
				}
				// 获取当前日期和时间
				const now = new Date();
				// 去掉时间部分，只保留日期
				const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				const inputDay = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());
				const inputMonth = inputDate.getMonth() + 1;
				const inputDayOfMonth = inputDate.getDate();
				// 计算时间差（以毫秒为单位）
				const timeDiff = today.getTime() - inputDay.getTime();
				const dayDiff = timeDiff / (1000 * 60 * 60 * 24);
				// 获取输入日期的小时和分钟
				const hours = inputDate.getHours().toString().padStart(2, '0');
				const minutes = inputDate.getMinutes().toString().padStart(2, '0');
				const timePart = `${hours}:${minutes}`;
				if (dayDiff < 0) {
					return `${timePart}`;
				} else if (dayDiff === 0) {
					// 今天
					return `今天 ${timePart}`;
				} else if (dayDiff === 1) {
					// 昨天
					return `昨天 ${timePart}`;
				} else if (dayDiff <= 7) {
					// 一周内
					const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
					const dayOfWeek = weekDays[inputDay.getDay()];
					return `${dayOfWeek} ${timePart}`;
				} else {
					return `${inputMonth > 9 ? inputMonth : ('0' + inputMonth)}-${inputDayOfMonth > 9 ? inputDayOfMonth : ('0' + inputDayOfMonth)} ${timePart}`;
				}
			},
			GotoMessage(type) {
				if (type) {
					uni.setStorageSync('tabParams', type);
				}
				uni.switchTab({
					url: '/pages/information/index'
				});
			},

			goTopFn() {
				this.distanceTop = 0.01;
				this.$nextTick(() => {
					this.distanceTop = 0; // 强制触发滚动
				});
			},
			scrollFn(e) {
				if (e.detail.scrollTop > 100) {
					this.showTop = false
				} else {
					this.showTop = true
				}
			},
			showTypeText(type) {
				if (type == 11) {
					return '数字产研'
				} else if (type == 12 || type == 14) {
					return '哒达招商'
				} else if (type == 13) {
					return '出海智策'
				} else if (type == 20) {
					return '哒达助招'
				}
			},
			showTypeIcon(type) {
				if (type == 11) {
					return 'https://static.idicc.cn/cdn/aiChat/applet/home/<USER>'
				} else if (type == 12 || type == 14) {
					return 'https://static.idicc.cn/cdn/aiChat/applet/home/<USER>'
				} else if (type == 13) {
					return 'https://static.idicc.cn/cdn/aiChat/applet/home/<USER>'
				} else if (type == 20) {
					return 'https://static.idicc.cn/cdn/aiChat/applet/home/<USER>'
				} else if (type == 21) {
					return 'https://static.idicc.cn/cdn/aiChat/applet/home/<USER>'
				}
			},
			focusFn() {
				if (this.token) {
					uni.navigateTo({
						url: '/pages/search/index'
					});
				} else {
					uni.reLaunch({
						url: `/pages/login/index`
					})
				}

			},
			go(item) {

				if (!this.token) {
					return uni.reLaunch({
						url: `/pages/login/index`
					})
				} else if ((item.id == 2) && !this.Jurisdiction) {
					return uni.showToast({
						title: '该产业链未开通会员，请在我的-会员中心开通后查看',
						icon: "none",
						duration: 2000,
					});
				} else {
					let IdentityType = uni.getStorageSync('userIdentityType');
					let orgName = uni.getStorageSync('orgName');
					let isDefault = uni.getStorageSync('isDefault');
					if ((IdentityType != 1 || !orgName || isDefault) && item.name == "招商管理") {
						return uni.showToast({
							title: '暂无权限，请联系客服添加',
							icon: "none",
							duration: 2000,
						});
					}
					if (item.name == "产业360") {
						let showChain = this.AI2jurisdiction.chain2Id;
						let title = this.AI2jurisdiction.knowType2Name;
						let knowId = this.AI2jurisdiction.knowType2Id;
						uni.navigateTo({
							url: `${item.url}?showChain=${showChain}&title=${title}&knowId=${knowId}`
						});
					} else {
						uni.navigateTo({
							url: item.url
						});
					}
				}
			},
			goDetails(id, type) {
				uni.navigateTo({
					url: `/pages/webView/InformationDetails?type=${type}&id=${id}`
				});
			},
			change(e) {
				this.current = e.detail.current;
			},
			scrolltolowerFn() {
				if (this.isLoading) {
					return
				}
				if (this.pages > this.pageNum) {
					this.pageNum++;
					this.getInformationList()
				} else {
					this.showText = true
				}
			},
			getInformationList() {
				if (this.isLoading) {
					return;
				}
				this.isLoading = true;
				let data = {
					pageNum: this.pageNum,
					pageSize: 5,
				}
				this.$api.newsAdminPage({
						data,
						method: "get",
					}).then(res => {
						this.messageList = this.messageList.concat(res.result.records);
						//this.messageList = res.result.records
						this.pages = res.result.pages
						this.onShowUpdata = true
					})
					.finally(() => {
						this.isLoading = false;
					})
			},
			getInformation() {
				this.$api.homeAPI({
					loading: 'no'
				}).then(res => {
					this.informationList = res.result.msgList
					//this.messageList = res.result.newsList
					this.posterList = res.result.advertList
					this.urlList = res.result.advertList.map(it => it.imageUrl)
					if (this.informationList?.length != 0) {
						this.messagesNumber = Number(this.informationList?.[0].unOrderMsgCount || 0) + Number(this
							.informationList?.[0].unSvcMsgCount || 0)
						if (this.messagesNumber == 0) {
							uni.hideTabBarRedDot({
								index: 1
							})
						} else {
							uni.showTabBarRedDot({
								index: 1
							})
						}
					}
				})
				if (this.informationList) {
					this.informationList.forEach(it => {
						it.times = Math.floor(new Date(it.time).getTime());
					})
				}
			},
			info() {
				this.token = uni.getStorageSync('token')
				//是否ios
				wx.getSystemInfoAsync({
					success(res) {
						if (res.system.indexOf('iOS') > -1) {
							uni.setStorageSync('whetherIos', true);
						} else {
							uni.setStorageSync('whetherIos', false);
						}
					}
				})
				if (this.token) {
					this.whereTogo()
				}
			},
			whereTogo() {
				this.$api.userMessage({
					loading: 'no',
				}).then((res) => {
					if (res.code == 'SUCCESS') {
						this.shortProvinceName = res.result.shortProvinceName || '浙'
						wx.setStorageSync('userIdentityType', res.result.userCompletionInfoDO?.userIdentity)
						wx.setStorageSync('userIdentity', res.result.userCompletionInfoDO?.userIdentity)
						if (res.result.userCompletionInfoDO?.userIdentity == 5) {
							wx.setStorageSync('identity', 2)
						} else(
							wx.setStorageSync('identity', 1)
						)
					}
				})
			},
			jointheteam(code) {
				//未登录，存本地  锁死顾问身份登录后到这个页面
				//登录后但是为用户时提示完善信息存本地 跳转到完善信息页面
				//登录后为产业顾问时 调申请接口
				if (!this.token) {
					wx.setStorageSync('teamCode', code)
					setTimeout(() => {
						uni.reLaunch({
							url: `/pages/login/index?teamCode=${code}`
						})
					}, 1000)
				} else {
					let userIdentityType = uni.getStorageSync("userIdentityType")
					if (userIdentityType == 3 || userIdentityType == 4) {
						return uni.removeStorage({
							key: "teamCode",
						});
					}
					this.$api.userMessage().then((res) => {
						if (!res.result.userCompletionInfoDO.cityName) {
							uni.showToast({
								title: '成为产业顾问需要先完善信息',
								icon: "none",
								duration: 2000,
							});
							wx.setStorageSync('teamCode', code)
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/user/perfectionInfo2'
								})
							}, 1000)
						} else if (res.result.userCompletionInfoDO.userIdentity != 5) {
							let userId = uni.getStorageSync('userId')
							let data = {
								userId,
								userIdentity: 5,
							}
							this.$api.updateHeadImg({
								data,
								method: "POST",
							}).then((res) => {
								wx.setStorageSync('userIdentityType', 5)
								wx.setStorageSync('identity', 2)
								this.$api.getTeamByInvitationCodeAPI({
									data: {
										invitationCode: code,
									}
								}).then((res) => {
									this.invitationCode = code
									uni.removeStorage({
										key: "teamCode"
									});
									this.realName = res.result.realName
									this.$refs.chargeback.open()
								}).catch(err => {
									uni.removeStorage({
										key: "teamCode",
									});
								})
							})
						} else {
							this.$api.getTeamByInvitationCodeAPI({
								data: {
									invitationCode: code,
								}
							}).then((res) => {
								this.invitationCode = code
								uni.removeStorage({
									key: "teamCode",
								});
								this.realName = res.result.realName
								this.$refs.chargeback.open()
							}).catch(err => {
								uni.removeStorage({
									key: "teamCode",
								});
							})
						}
					})
				}

			},

			claimFn() {
				this.$refs.chargeback.close()
			},

			retreat() {
				this.$api.workerapplyAPI({
					data: {
						invitationCode: this.invitationCode
					},
					error: 'no',
					method: 'post'
				}).then(res => {
					uni.showToast({
						title: '加入成功！',
						icon: 'none'
					})
					this.claimFn()
				}).catch(error => {
					this.failtoApply = true
					this.failtoApplyText = error.msg
				});
			},
		}
	}
</script>
<style lang="scss" scoped>
	.Library {
		width: 690rpx;
		margin-left: 30rpx;
		margin-top: 32rpx;
		display: flex;
		justify-content: space-between;

		.leftLibrary {
			width: 350rpx;
			height: 134rpx;
			position: relative;

			.CapabilityName {
				position: absolute;
				top: 15rpx;
				left: 28rpx;
				font-size: 26rpx;
				font-weight: 500;
				color: #1d2129;
			}
		}

		.rightLibrary {
			width: 350rpx;
			height: 134rpx;
			position: relative;

			.CapabilityName {
				position: absolute;
				top: 15rpx;
				left: 40rpx;
				font-size: 26rpx;
				font-weight: 500;
				color: #1d2129;
			}
		}

		image {
			width: 350rpx;
			height: 134rpx;
		}
	}

	.Polygon {
		margin-left: 16rpx;
		width: 22rpx;
		height: 12rpx;
	}

	.InvertedTriangle {
		width: 0;
		height: 0;
		border-top: 12rpx solid #1d2129;
		margin-left: 8rpx;
		border-right: 8rpx solid transparent;
		border-left: 8rpx solid transparent;
		border-bottom: 0 solid transparent;
	}

	.selectBox {
		background-color: rgba(255, 255, 255, 0.6);
		padding: 0rpx 40rpx;
		height: 64rpx;
		// width: 200rpx;
		// background: #FFFFFF;
		box-shadow: 0rpx 2rpx 5rpx 0rpx rgba(191, 195, 203, 0.1);
		border-radius: 35rpx 35rpx 35rpx 35rpx;
		display: flex;
		justify-content: center;
	}

	::v-deep {
		.seinput {
			padding: 0 30rpx;
			box-sizing: border-box;
			width: 100%;
			height: 64rpx;
			margin-bottom: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			position: relative;

			.historyBox {
				height: 60rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #3f4a59;
				display: flex;
				align-items: center;
				position: absolute;
				top: 0;
				right: 30rpx;

				.icon {
					width: 40rpx;
					height: 40rpx;
					min-width: 40rpx;
					min-height: 40rpx;
					margin-right: 8rpx;
				}
			}

			.content-clear-icon {
				padding-left: 30rpx;
			}

			.uni-easyinput__content {
				border: 0rpx solid #e6ebf0 !important;
			}

			.uni-easyinput__content {
				/* #ifndef H5 */
				padding-left: 140rpx;
				/* #endif */
				/* #ifndef MP-WEIXIN */
				padding-left: 160rpx;
				/* #endif */
				border-radius: 40rpx;
			}

			.uni-easyinput {
				flex: 1 !important;
			}
		}
	}

	.gotopBox {
		position: absolute;
		right: 38rpx;
		bottom: 52rpx;
		z-index: 80;

		.gotopImg {
			width: 80rpx;
			height: 80rpx;
		}
	}

	.showTextClass {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 12rpx;
		font-weight: 400;
		font-size: 24rpx;
		color: #b7bfc7;
	}

	.airBox {
		display: flex;
		align-items: center;
		padding-top: 46rpx;
	}

	.orgName {
		//padding: 8rpx 15rpx;
		display: inline-block;
		margin-left: 12rpx;
		//position: absolute;
		//top: 48rpx;
		//left: 216rpx;
		font-weight: 600;
		font-size: 44rpx;
		color: #3370ff;
		//border-radius: 8rpx;
	}

	.TopOrgName {
		padding: 8rpx 15rpx;
		display: inline-block;
		font-weight: 600;
		font-size: 32rpx;
		color: #3370ff;
	}

	.fakeInput {
		width: 690rpx;
		height: 60rpx;
		font-size: 14px;
		background-color: #fff;
		border-radius: 100rpx;
		display: flex;
		align-items: center;
		padding-left: 30rpx;
		box-sizing: border-box;
		color: #a8a8a8;
		z-index: 18;

		.text {
			margin-left: 12rpx;
		}
	}

	.loginBox {
		position: absolute;
		width: 750rpx;
		height: 200rpx;
		bottom: 0;
		left: 0;
		z-index: 30;
		display: flex;
		justify-content: center;

		.loginBoxBgc {
			position: absolute;
			top: 0;
			left: 0;
			width: 750rpx;
			height: 200rpx;
		}

		.logBtn {
			width: 336rpx;
			height: 80rpx;
			background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), linear-gradient(90deg, #ff9e45 0%, #ff5c00 100%),
				linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #ffffff;
			position: relative;
			margin-top: 88rpx;
		}
	}

	.applyaffirm {
		width: 622rpx;
		height: auto;
		background: #ffffff;
		border-radius: 32rpx 32rpx 32rpx 32rpx;
		box-sizing: border-box;
		padding: 52rpx 48rpx 32rpx 48rpx;
		display: flex;
		align-items: center;
		flex-direction: column;

		.p1 {
			font-weight: 500;
			font-size: 32rpx;
			color: #323233;
			margin-bottom: 16rpx;
		}

		.p2 {
			font-weight: 400;
			font-size: 28rpx;
			color: #3f4a59;
			margin-bottom: 68rpx;
			text-align: center;
		}

		.affirm {
			width: 526rpx;
			height: 72rpx;
			background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			font-weight: 400;
			font-size: 32rpx;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.popBtn {
			display: flex;
			width: 100%;
			justify-content: space-between;

			.affirm {
				width: 251rpx;
				height: 72rpx;
				background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-weight: 400;
				font-size: 32rpx;
				color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.canleBt {
				width: 251rpx;
				height: 72rpx;
				background: #ffffff;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 2rpx solid #ebedf0;
				font-weight: 400;
				font-size: 32rpx;
				color: #323233;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	//输入框
	::v-deep {
		.content-clear-icon {
			padding-left: 30rpx;
		}

		.uni-easyinput__content {
			border: 0rpx solid #e6ebf0 !important;
		}

		.uni-easyinput__content {
			border-radius: 100rpx;
		}

		.uni-easyinput {
			flex: 0 !important;
		}
	}

	.box {
		position: relative;
		height: 100vh;

		//overflow: scroll;
		.homeTop2 {
			position: absolute;
			z-index: 30;
			top: 0;
			width: 750rpx;
			height: 188rpx;

			.homeTop2Img {
				position: absolute;
				top: 0;
				left: 0;
				width: 750rpx;
				height: 188rpx;
			}

			.homeTop2Const {
				display: flex;
				position: absolute;
				left: 0;
				bottom: 6rpx;
				height: 100rpx;
				align-items: center;
				width: 680rpx;
				padding-left: 44rpx;
				box-sizing: border-box;

				::v-deep {
					.uni-easyinput__content {
						border-radius: 100rpx;
						width: 400rpx;
					}
				}

				.fakeInput2 {
					width: 400rpx;
					height: 60rpx;
					font-size: 14px;
					background-color: #fff;
					border-radius: 100rpx;
					display: flex;
					align-items: center;
					padding-left: 30rpx;
					box-sizing: border-box;
					color: #a8a8a8;
					z-index: 18;

					.text {
						margin-left: 12rpx;
					}
				}

				.AIR {
					width: 140rpx;
					height: 36rpx;
					min-width: 140rpx;
					min-height: 36rpx;
					margin-right: 6rpx;
					padding: 8rpx 0;
				}
			}
		}

		.messageContent {
			position: relative;
			height: 100%;
			width: calc(100% - 160rpx);

			.messageTitle {
				font-weight: 400;
				width: 100%;
				font-size: 28rpx;
				color: #1d2129;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				line-height: 40rpx;

				.placeTop {
					width: 68rpx;
					height: 40rpx;
					margin-right: 8rpx;
					transform: translateY(10rpx);
				}
			}

			.messagebtm {
				display: flex;
				//justify-content: space-between;
				font-weight: 400;
				font-size: 24rpx;
				color: #86909c;
				margin-top: 20rpx;

				.source {
					margin-left: 112rpx;
				}
			}
		}

		.messageImg {
			width: 120rpx;
			height: 120rpx;
			border-radius: 4rpx;
		}

		.messageItem {
			width: 630rpx;
			box-sizing: border-box;
			height: auto;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			border-bottom: 2rpx solid #e6ebf0;
			padding: 24rpx 0;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.messageItems {
			width: 630rpx;
			box-sizing: border-box;
			height: auto;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			border-bottom: 2rpx solid #ffffff;
			padding-top: 24rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.poster {
			width: 690rpx;
			margin-left: 30rpx;
			height: 200rpx;
			position: relative;
			z-index: 20;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-top: 28rpx;

			.bgc {
				width: 690rpx;
				height: 200rpx;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
			}

			.swiperposter {
				width: 690rpx;
				height: 200rpx;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
			}
		}

		.information {
			width: 690rpx;
			margin-left: 30rpx;
			margin-top: 28rpx;
			height: auto;
			//background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #ffffff 97%);
			background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #FFFFFF 97%);
			backdrop-filter: blur(30px);
			box-shadow: inset 0px 2px 4px 0px rgba(255, 255, 255, 0.8);
			z-index: 20;
			position: relative;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			padding: 0 30rpx;
			box-sizing: border-box;
			padding-bottom: 28rpx;

			.informationItem {
				width: 630rpx;
				padding-right: 16rpx;
				margin-bottom: 10rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.informationWriter {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 26rpx;
					color: #1d2129;

					.informationIcon {
						width: 48rpx;
						height: 48rpx;
						min-width: 48rpx;
						min-height: 48rpx;
						margin-right: 4rpx;
					}

					.typetext {
						font-weight: 400;
						flex: 1;
						font-size: 24rpx;
						margin-left: 12rpx;
						white-space: nowrap;
						display: block;
						overflow: hidden;
						text-overflow: ellipsis;
						color: #3f4a59;
						max-width: 366rpx;
					}
				}

				.time {
					margin-right: 12rpx;
					width: 130rpx;
					font-weight: 400;
					font-size: 20rpx;
					color: #86909c;
					margin-left: 24rpx;
					text-align: right;
				}
			}

			.informationTitle {
				height: 80rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.newMessage {
					display: flex;
					font-weight: 400;
					font-size: 22rpx;
					color: #86909c;

					.quantity {
						min-width: 28rpx;
						height: 28rpx;
						background: #ee0a24;
						border-radius: 40rpx 40rpx 40rpx 40rpx;
						border-radius: 14rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-weight: 500;
						font-size: 22rpx;
						color: #ffffff;
						margin-left: 18rpx;
						box-sizing: border-box;
						padding-left: 6rpx;
						padding-right: 6rpx;
						line-height: 28rpx;
					}
				}



				.righticon {
					width: 28rpx;
					height: 28rpx;
				}
			}
		}

		.homeTop {
			width: 690rpx;
			height: auto;
			position: relative;
			margin-left: 30rpx;
			z-index: 12;

			.writer {
				height: 208rpx;
				display: flex;
				flex-direction: column;

				.AIR {
					width: 218rpx;
					height: 44rpx;
					//margin-top: 64rpx;
				}

				.robot {
					position: absolute;
					width: 276rpx;
					height: 252rpx;
					right: 6rpx;
					top: 48rpx;
					z-index: -1;
				}

				.AIRText {
					font-weight: 400;
					font-size: 28rpx;
					color: #455469;
					margin-top: 24rpx;
				}
			}
		}
	}

	.homeBgc {
		width: 750rpx;
		height: 1336rpx;
		top: 0;
		right: 0;
		position: absolute;
		z-index: -1 !important;
	}

	// .dataPresentation {
	// position: relative;
	// z-index: 20 !important;
	// width: 690rpx;
	// height: auto;
	// margin-left: 30rpx;
	// margin-bottom: 32rpx;
	// opacity: 1;
	// box-sizing: border-box;

	.dataPresentationBox2 {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		//margin: 0 24rpx;
		box-sizing: border-box;

		.dataProjectItem {
			width: 310rpx;
			height: 98rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;
			border-radius: 10rpx;
			padding: 9rpx 13rpx 0 13rpx;
			position: relative;
			background: linear-gradient(284deg, rgba(156, 243, 234, 0.1) 0%, rgba(176, 206, 255, 0.26) 92%);

			.leftBox {
				position: absolute;
				width: 100%;
				z-index: 2;
				width: 288rpx;

				.desc {
					// 超过一行。。。
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
				}
			}

			image {
				width: 80rpx;
				height: 80rpx;
				position: absolute;
				right: 13rpx;
				top: 9rpx;
			}

			.count {
				font-size: 32rpx;
				font-weight: bold;
				line-height: normal;
				color: #3d3d3d;


				.text {
					color: #3370ff
				}
			}

			.desc {
				font-size: 24rpx;
				font-weight: normal;
				line-height: normal;
				color: rgba(61, 61, 61, 0.6);
				margin-bottom: 4rpx;
			}
		}
	}

	.dataPresentationBox {
		display: flex;
		flex-wrap: wrap;

		.dataPresentationItem {
			flex: 1;
			min-width: 25%;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			flex-direction: column;
			margin-bottom: 40rpx;

			.iconImg {
				width: 40rpx;
				height: 40rpx;
				margin-bottom: 8rpx;
			}

			.count {
				font-size: 32rpx;
				font-weight: bold;
				line-height: normal;
				color: #3d3d3d;
				margin-bottom: 8rpx;
			}

			.desc {
				font-size: 26rpx;
				font-weight: normal;
				line-height: normal;
				color: rgba(61, 61, 61, 0.6);
			}
		}
	}

	.homeBox {
		width: 690rpx;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
	}

	.tabsBox {
		display: flex;
		margin-bottom: 50rpx;

		.adornBgc {
			position: absolute;
			bottom: -6rpx;
			right: -12rpx;
			width: 76rpx;
			height: 36rpx;
			z-index: 4;
		}

		.tabsName {
			position: relative;
			z-index: 10;
		}

		.tabsItem {
			//width: 140rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 36rpx;
			padding-top: 25rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: rgba(29, 33, 41, 0.6);
			position: relative;
		}

		.tabsItems {
			//width: 140rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 36rpx;
			padding-top: 25rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #1d2129;
			position: relative;
		}
	}

	// }

	.swiper {
		position: relative;
		z-index: 20 !important;
		width: 690rpx;
		margin-left: 30rpx;
		margin-top: 32rpx;
		padding: 0 12rpx;
		height: 216rpx;
		background: #ffff;
		border-radius: 20rpx;
		box-shadow: 0px 4px 12px 0px rgba(191, 195, 203, 0.1);
		//background: #FFFFFF;
		//box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(191, 195, 203, 0.1);
		//border-radius: 20rpx 20rpx 20rpx 20rpx;
		opacity: 1;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: space-around;
		//padding-top: 28rpx;
		box-sizing: border-box;

		//justify-content: space-around;
		.homeBox {
			width: 690rpx;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
		}

		.switem {
			//width: 25%;
			display: flex;
			flex-direction: column;
			align-items: center;
			//margin-bottom: 28rpx;
			justify-content: center;
			position: relative;

			.switemImg {
				width: 140rpx;
				height: 164rpx;
			}

			.name {
				margin-top: 4rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #3f4a59;
				position: absolute;
				white-space: nowrap;
				bottom: 20rpx;
				left: 50%;
				transform: translateX(-50%);
			}

			.newNumber {
				font-weight: 500;
				font-size: 20rpx;
				color: #fff;
				background: linear-gradient(270deg, #ff782f, #ffd09e 96%);
				position: absolute;
				white-space: nowrap;
				top: 0;
				right: 0;
				border-radius: 30rpx;
				transform: translate(50%);
				padding: 3px 8px;
			}
		}
	}

	.text {
		font-weight: 500;
		font-size: 32rpx;
		color: #1d2129;
		position: relative;

		.textTitle {
			z-index: 5;
			position: relative;
		}

		.adornBgc {
			position: absolute;
			bottom: -6rpx;
			right: -12rpx;
			width: 76rpx;
			height: 36rpx;
			z-index: 4;
		}
	}

	.icons {
		position: relative;




		.upBgc {
			width: 24rpx !important;
			height: 24rpx !important;
			position: absolute;
			top: -6px;
			right: -10px;
		}
	}
</style>