.information {
  width: 690rpx;
  margin-left: 30rpx;
  margin-top: 32rpx;
  height: auto;
  // background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #ffffff 97%);
  background: linear-gradient(201deg, #d6ecff 19%, #ffffff 65%), #ffffff;
  z-index: 20;
  position: relative;
  box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(191, 195, 203, 0.1);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: 28rpx;
  .informationTitle {
    height: 80rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .newMessage {
      display: flex;
      font-weight: 400;
      font-size: 22rpx;
      color: #86909c;

      .quantity {
        min-width: 28rpx;
        height: 28rpx;
        background: #ee0a24;
        border-radius: 40rpx 40rpx 40rpx 40rpx;
        border-radius: 14rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 22rpx;
        color: #ffffff;
        margin-left: 18rpx;
        box-sizing: border-box;
        padding-left: 6rpx;
        padding-right: 6rpx;
        line-height: 28rpx;
      }
    }

    .righticon {
      width: 28rpx;
      height: 28rpx;
    }
  }
}
.text {
  font-weight: 500;
  font-size: 32rpx;
  color: #1d2129;
  position: relative;

  .textTitle {
    z-index: 5;
    position: relative;
  }

  .adornBgc {
    position: absolute;
    bottom: -6rpx;
    right: -12rpx;
    width: 76rpx;
    height: 36rpx;
    z-index: 4;
  }
}
.dataPresentationBox2 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  // margin: 0 24rpx;
  box-sizing: border-box;

  .dataProjectItem {
    width: 310rpx;
    height: 98rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    border-radius: 10rpx;
    padding: 9rpx 13rpx 0 13rpx;
    position: relative;
    background: linear-gradient(284deg, rgba(156, 243, 234, 0.1) 0%, rgba(176, 206, 255, 0.26) 92%);

    .leftBox {
      position: absolute;
      width: 100%;
      z-index: 2;
      width: 288rpx;

      .desc {
        // 超过一行。。。
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    .rightImg {
      width: 80rpx;
      height: 80rpx;
      position: absolute;
      right: 13rpx;
      top: 9rpx;
    }

    .count {
      font-size: 32rpx;
      font-weight: bold;
      line-height: normal;
      color: #3d3d3d;

      .text {
        color: #3370ff;
      }
    }

    .desc {
      font-size: 24rpx;
      font-weight: normal;
      line-height: normal;
      color: rgba(61, 61, 61, 0.6);
      margin-bottom: 4rpx;
    }
  }
}

.dataPresentationBox {
  display: flex;
  flex-wrap: wrap;

  .dataPresentationItem {
    flex: 1;
    min-width: 25%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 40rpx;

    .iconImg {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 8rpx;
    }

    .count {
      font-size: 32rpx;
      font-weight: bold;
      line-height: normal;
      color: #3d3d3d;
      margin-bottom: 8rpx;
    }

    .desc {
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      color: rgba(61, 61, 61, 0.6);
    }
  }
}
