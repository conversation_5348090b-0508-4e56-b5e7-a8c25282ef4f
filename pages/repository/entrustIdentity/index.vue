<template>
  <div class="entrustIdentity">
    <!-- 招商委托大厅 -->
    <div v-if="dataProjectList?.length != 0" class="information">
      <image class="cardBg" src="https://static.idicc.cn/cdn/zhaoShang/delegation.webp"></image>
      <div class="cardList">
        <div class="informationTitle">
          <div class="text">
            <div class="textTitle">招商委托大厅</div>
            <image class="adornBgc" src="/static/AboutAi/adornBgc.png"></image>
          </div>
        </div>
        <div class="dataPresentationBox2">
          <div @click="skip(item, 2)" class="dataProjectItem" v-for="(item, index) in dataProjectList" :key="index">
            <div class="leftBox">
              <div class="desc">
                {{ item.desc }}
              </div>
              <div class="count">
                {{ item.count || '-' }}
              </div>
            </div>
            <image class="rightImg" :src="item.icon"></image>
          </div>
        </div>
      </div>
    </div>
    <!-- 我的订单 -->
    <div v-if="myOrderList?.length != 0" class="information">
      <image class="cardBg" src="https://static.idicc.cn/cdn/zhaoShang/orderBg.webp"></image>
      <div class="cardList">
        <div class="informationTitle" style="margin-bottom: 4px;">
          <div class="text">
            <div class="textTitle">我的订单</div>
            <image class="adornBgc" src="/static/AboutAi/adornBgc.png"></image>
          </div>
        </div>
        <div class="dataPresentationBox">
          <div @click="skip(item, 1)" class="dataPresentationItem" v-for="(item, index) in myOrderList" :key="index">
            <view class="icons">
              <image class="iconImg" :src="item.icon"></image>
              <!-- <image v-if="isWithinLastWeek(item.lastUpdateDate)" class="upBgc" src="/static/AboutAi/up.png"></image> -->
            </view>
            <div class="count">
              {{ item.count || '-' }}
            </div>
            <div class="desc">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 我的反馈 -->
    <div class="feedback">
      <div class="informationTitle">
        <div class="dataPresentationBox2">
          <div @click="skip(item, 2)" class="dataProjectItem" v-for="(item, index) in feedbackList" :key="index">
            <image class="feedbackImg" :src="item.icon"></image>
            <div class="feedbackBtn">
              <div class="desc">
                {{ item.desc }}
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

</template>


<script>

export default {
  data() {
    return {
      myOrderList: [
        {
          desc: '待跟进',
          count: 23440,
          icon: 'https://static.idicc.cn/cdn/zhaoShang/待跟进.png',
          url: '/pages/delegation/myOrders'
        },
        {
          desc: '跟进中',
          count: 1110,
          icon: 'https://static.idicc.cn/cdn/zhaoShang/跟进中.png',
          url: '/pages/delegation/myOrders'
        },
        {
          desc: '申诉中',
          count: 1110,
          icon: 'https://static.idicc.cn/cdn/zhaoShang/申诉中.png',
          url: '/pages/delegation/myOrders'
        },
        {
          desc: '已完成',
          count: 1110,
          icon: 'https://static.idicc.cn/cdn/zhaoShang/已完成.png',
          url: "/pages/repository/attract",
          // '/pages/delegation/myOrders'
        }
      ],
      dataProjectList: [
        {
          desc: '委托单',
          count: 23440,
          url: '/pages/delegation/delegationHall',
          icon: 'https://static.idicc.cn/cdn/zhaoShang/委托单.png',

        },
        {
          desc: '需求单',
          count: 1110,
          url: '/pages/delegation/delegationHall',
          icon: 'https://static.idicc.cn/cdn/zhaoShang/需求单.webp',
        }
      ],
      feedbackList: [
        {
          desc: '去看看',
          url: '/pages/delegation/myFeedback',
          icon: 'https://static.idicc.cn/cdn/zhaoShang/反馈.png',
        }, {
          desc: '去提现',
          url: '/pages/delegation/myWallet',
          icon: 'https://static.idicc.cn/cdn/zhaoShang/钱包.png',
        }
      ]
    }
  },
  methods: {
    skip(item, type) {
      if (item.count == 0) {
        return uni.showToast({
          title: '敬请期待',
          icon: "none",
          duration: 2000,
        });
      }
      uni.navigateTo({
        url: item.url
      });

    },
  }

}
</script>

<style lang="scss" scoped>
@import './listScss.scss';

.cardBg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.cardList {
  position: relative;
  z-index: 2;

  .dataPresentationBox {
    padding-top: 40rpx;
  }

  .iconImg {
    width: 56rpx !important;
    height: 56rpx !important;
  }
}

.entrustIdentity {
  .information {
    background: linear-gradient(180deg, rgba(199, 220, 255, 0.6) 0%, rgba(210, 243, 255, 0.06) 46%);
  }
}

.feedbackImg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.feedback {
  width: 690rpx;
  margin: 32rpx auto;
  height: auto;
  z-index: 20;
  position: relative;
  box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(191, 195, 203, 0.1);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  // padding: 0 30rpx;
  box-sizing: border-box;
  // padding-bottom: 28rpx;

  .dataPresentationBox2 {
    .dataProjectItem {
      width: 330rpx;
      height: 160rpx;
      margin-bottom: 0;
    }

    .feedbackBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(270deg, #0066FF 0%, #45B8FF 97%);
      position: absolute;
      z-index: 22;
      border-radius: 20rpx;
      margin: 50rpx 0 0 18rpx;

      .desc {

        // font-family: Alibaba PuHuiTi 2.0;
        font-size: 20rpx;
        font-weight: 500;
        padding: 4rpx 20rpx;
        color: #FFFFFF;

      }
    }
  }
}
</style>