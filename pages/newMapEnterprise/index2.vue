<template>
	<page-meta :page-style="'overflow:' + (popupShow ? 'hidden' : 'visible')"></page-meta>
	<!-- #ifndef H5 -->
	<tabBar title="产业360"></tabBar>
	<!-- #endif -->
	<!-- #ifdef H5 -->
	<view v-if="showPage" class="box"
		:style="{ height: initShowChain ? 'calc(100vh - 20rpx)' : 'calc(100vh - 208rpx)' }">
	<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<view v-if="showPage" class="box">
		<!-- #endif -->
			<view class="titleBox">
				<!-- #ifdef H5 -->
				<tree-select v-if="!initShowChain" selectTile="切换产业链" optionsKey="value" :value="defaultValue"
					:initialValue="initialValue" primary="value" ref="treeSelect" :options="list" @confirm="onConfirm"
					:noLimit='true'>
					<view style="display: flex; align-items: center">
						<img src="https://static.idicc.cn/cdn/aiChat/applet/cut.png" class="cutIcon" /> {{ title }}产业链图谱
					</view>
				</tree-select>
				<view v-if="initShowChain" style="display: flex; align-items: center">
					{{ title }}产业链图谱
				</view>
				<!-- #endif -->
				<!-- #ifdef MP-WEIXIN -->
				<view style="display: flex; align-items: center">
					{{ title }}产业链图谱
				</view>
				<!-- #endif -->
				<image @click="goSearch" class="searchIcon" src="/static/AboutAi/searchIcon.png"></image>
			</view>
			<view class="contentBox">
				<div class="optionBox">
					<span :class="Jurisdiction ? (parameterList[0] ? 'unselected' : 'pitchOn') : 'noAccess'"
						class="optionItem" @click="screenFn(1)">
						{{ regionFn() }}
						<image :src="iconColour(parameterList[0])" class="Arrows"></image>
					</span>
					<span :class="Jurisdiction ? (parameterList[1] ? 'unselected' : 'pitchOn') : 'noAccess'"
						class="optionItem" @click="screenFn(2)">
						亲缘查询<image :src="iconColour(parameterList[1])" class="Arrows"></image>
					</span>
					<span :class="Jurisdiction ? (parameterList[2] ? 'unselected' : 'pitchOn') : 'noAccess'"
						class="optionItem" @click="screenFn(3)">
						更多查询 <image :src="iconColour(parameterList[2])" class="Arrows"></image>
					</span>
					<span :class="Jurisdiction ? (parameterList[3] ? 'unselected' : 'pitchOn') : 'noAccess'"
						class="optionItem" @click="screenFn(4)">
						指数排序 <image :src="iconColour(parameterList[3])" class="Arrows"></image>
					</span>
				</div>
				<scroll-view scroll-y="true" v-if="List.length != 0" class="nodeBox">
					<div v-for="(item, index) in List" :key="index" class="nodeItem">
						<view class="stairNode">
							<span class="stairNodeText" @click="recommendQ(item)">{{ item.nodeName }}
								(<span style="text-align: center;" v-html="digitalfiltering(item)"></span>
								)
								<image v-if="isWithinLastWeek(item.lastModifyDate)" class="upBgc"
									src="/static/AboutAi/up.png"></image>
							</span>
							<div v-if="item.children.length >= 1" @click="isshow(index)" class="OperationExpansion">
								<img src="../../static/user/right.png"
									:class="item.show ? 'unfoldIcon' : 'upackIcon'"></img>
							</div>
						</view>
						<div class="secondLevelBox" v-if="item.children && item.show">
							<div v-for="(it, ind) in item.children" :key="ind">
								<div class="secondLevel">
									<span class="secondLevelText" @click="recommendQ(it)">
										<div class="dot"></div>
										{{ it.nodeName }} (<span style="text-align: center;"
											v-html="digitalfiltering(it)"></span>)
										<image v-if="isWithinLastWeek(it.lastModifyDate)" class="upBgc"
											src="/static/AboutAi/up.png"></image>
									</span>
									<div style="OperationExpansion" v-if="it.children.length >= 1"
										@click="isshow(index, ind)">
										<img src="../../static/user/right.png"
											:class="it.show ? 'unfoldIcon' : 'upackIcon'"></img>
									</div>
								</div>

								<div v-if="it.children && it.show" class="threeLevelBox">
									<div v-for="(i, g) in it.children" :key="g" class="threeLevel">
										<span class="threeLevelText" @click="recommendQ(i)">
											{{ i.nodeName }} (<span style="text-align: center;"
												v-html="digitalfiltering(i)"></span>)

										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div style="padding-bottom: 180rpx;"></div>
				</scroll-view>
			</view>
		</view>
		<uni-popup ref="popup" :safe-area="false" background-color="rgba(0,0,0,0)" @change="changeState">
			<view class="uni-popups">
				<view class="popup-content">
					<span class="nodeName">
						{{ isshownodeName.nodeName }}({{ total }})</span>
				</view>
				<scroll-view style="height: 1100rpx" scroll-y="true" @scrolltolower="loadMoreData">
					<view class="qyList">
						<enterpriseList :Jurisdiction="Jurisdiction" :showChain="showChain" :knowId="knowId"
							:enterpriseList="keyenterprise" :enterpriseLabelIds="pitchtag"
							@openJurisdictionPop="openJurisdictionPop">
						</enterpriseList>
					</view>
					<view v-if="total < 1" class="noemphasis">
						<img src="https://static.idicc.cn/cdn/aiChat/applet/nozdqy.png" alt="" />
						<span class="nozdqy">暂无企业</span>
					</view>
					<view v-if="mosj && total != 0" class="noemphasis2">
						<span class="total">没有更多啦~</span>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
		<uni-popup :safe-area='false' ref="popup2" background-color="#fff">
			<mapList2 :BottomBlank="bottomBlank" ref="mapList2" :cityName="cityName" :unrestricted="true"
				@affirm="affirm2">
			</mapList2>
		</uni-popup>
		<moreScreen :BottomBlank="bottomBlank" ref="moreScreen" title="更多查询" :moreScreenList="moreScreenList"
			@updatamoreScreen="updatamoreScreen">
		</moreScreen>
		<moreScreen :BottomBlank="bottomBlank" ref="moreScreen2" title="亲缘查询" :MultipleChoice="true"
			:moreScreenList="moreScreenList2" @updatamoreScreen="updatamoreScreen2"></moreScreen>
		<noAccess ref="noAccess"></noAccess>
		<uni-popup :safe-area='false' ref="EnterpriseTypePop" background-color="#fff">
			<MultipleChoice :introductionSelectIndex="introductionSelectIndex" :BottomBlank="false" ref="MultipleChoice"
				:list="enterpriseTypeList" @affirm="affirm1">
			</MultipleChoice>
		</uni-popup>
</template>

<script>
	import enterpriseList from "./components/enterpriseList.vue";
	import treeSelect from "../../components/treeSelect/index.vue";
	import tabBar from "../../components/tabBar/index.vue";
	import mapList2 from '../../components/mapList.vue'
	import moreScreen from '../../components/360screen/moreScreen.vue'
	import SingleLineSelection from '../../components/attractSelect/SingleLineSelection.vue'
	import noAccess from '../../components/360screen/noAccess.vue'
	import MultipleChoice from '../../components/360screen/MultipleChoice.vue'
	import {
		getQueryParam,
		isWithinLastWeek
	} from '@/utils/utils.js'
	export default {
		components: {
			tabBar,
			enterpriseList,
			treeSelect,
			mapList2,
			moreScreen,
			SingleLineSelection,
			noAccess,
			MultipleChoice
		},
		data() {
			return {
				bottomBlank: false,
				title: "",
				showChain: "",
				initShowChain: "",
				industryChainList: [],
				nodeName: "",
				List: [],
				keyenterprise: [], //重点企业
				isshownodeName: "",
				showPopup: false,
				tagList: [],
				pitchtag: [],
				pageNum: 1,
				pageSize: 10,
				total: 0,
				size: 0,
				mosj: false,
				amount: 0,
				coilHeight: "0rpx", // 初始高度
				nodeHeight: 0,
				waibianjv: 0,
				popupShow: false,
				oletagList: [],
				statusBarHeight: 0,
				showTree: false,
				defaultValue: [],
				initialValue: [],
				list: [],
				selectType: 2,
				knowId: '',
				//行政区划筛选
				baseInfo: {},
				//更多筛选
				moreData: {},
				//亲缘查询
				affinityData: {},
				//指数排序
				sortData: {},
				Jurisdiction: false,
				token: '',
				moreScreenList: [], //更多查询的参数
				moreScreenList2: [], //亲缘查询的参数
				enterpriseTypeList: [
					'扩张意愿指数', '快速成长指数'
				],
				parameterList: [true, true, true, true],
				cityName: '',
				introductionSelectIndex: 0,
				showPage: true
			};
		},
		onLoad(options) {
			this.statusBarHeight = uni.getStorageSync("statusBarHeight")
			this.token = uni.getStorageSync("token")
			// #ifdef MP-WEIXIN
			this.ecoIndustryChain(options)
			// #endif
			// #ifdef H5
			if (getQueryParam("bottom")) {
				let safeAreaRpx = getQueryParam("bottom") + 'px';
				uni.setStorageSync("safeAreaRpx", safeAreaRpx);
			}
			if (getQueryParam("token")) {
				this.token = getQueryParam("token");
				uni.setStorageSync("token", this.token);
				this.requestOrgCode();
			}
			if (getQueryParam("identity")) {
				uni.setStorageSync("userIdentityType", getQueryParam("identity"));
			}
			if (getQueryParam("showChain")) {
				this.initShowChain = getQueryParam("showChain");
				this.showChain = getQueryParam("showChain");
			}
			if (getQueryParam("title")) {
				this.title = getQueryParam("title");
			}
			if (getQueryParam("knowId")) {
				this.knowId = getQueryParam("knowId");
			}
			if (this.initShowChain) {
				this.Jurisdiction = true
				this.gettree(this.initShowChain);
			} else {
				this.bottomBlank = true;
				this.getindustryList();
			}
			// #endif
			this.getScreen()
		},
		mounted() {},
		methods: {
			isWithinLastWeek,
			requestOrgCode() {
				this.$api.ownList({}).then((res) => {
					// this.orgName = res.result.selected?.orgName;
					// this.isDefault = res.result.selected?.isDefault;
					uni.setStorageSync('orgCode', res.result.selected?.orgCode)
					uni.setStorageSync('orgName', res.result.selected?.orgName)
					uni.setStorageSync('isDefault', res.result.selected?.isDefault)
				})
			},
			isGoSea() {
				let IdentityType = uni.getStorageSync("userIdentityType")
				if (IdentityType == 3 || IdentityType == 4) {
					uni.reLaunch({
						url: "/goToSea/index?pitch=4",
					});
				} else {
					this.showPage = true
				}
			},
			//省市区筛选展示
			regionFn() {
				let region = '全国';
				if (this.baseInfo?.area != null && this.baseInfo?.area.length != 0) {
					region = this.baseInfo.area[0];
				} else if (this.baseInfo.city) {
					region = this.baseInfo.city;
				} else if (this.baseInfo.province) {
					region = this.baseInfo.province;
				}
				if (region.length > 4) {
					region = region.substring(0, 3) + '...';
				}
				return region;
			},
			//清空筛选
			ClearFilter() {
				this.moreData = {}
				this.affinityData = {}
				this.sortData = {}
				this.introductionSelectIndex = 0
				this.baseInfo = {}
				this.$nextTick(() => {
					this.$refs.mapList2.clear()
					this.$refs.moreScreen.claimFn()
					this.$refs.moreScreen2.claimFn()
					this.$refs.MultipleChoice.clear()
				})
				this.changeSelectType()
			},
			//更多筛选确认
			updatamoreScreen(data) {
				this.moreData = data
				this.gettree(this.showChain);
				this.changeSelectType()
			},
			//亲缘筛选确认
			updatamoreScreen2(data) {
				this.affinityData = data
				this.gettree(this.showChain);
				this.changeSelectType()
			},
			//指数排序确认
			affirm1(data) {
				this.$refs.EnterpriseTypePop.close()
				if (!data) {
					this.sortData = {}
					this.introductionSelectIndex = 0
				} else {
					this.sortData.orderByField = data == '扩张意愿指数' ? 'expansionIndex' : 'growthIndex'
					this.introductionSelectIndex = data == '扩张意愿指数' ? 1 : 2
				}
				this.gettree(this.showChain);
				this.changeSelectType()
			},
			//省市区确认
			affirm2(data) {
				this.$refs.popup2.close()
				this.baseInfo.province = data.province.name
				this.baseInfo.city = data.citys.name
				this.baseInfo.area = []
				if (data.area.name) {
					this.baseInfo.area[0] = data.area.name
				}
				// #ifndef MP-WEIXIN
				let province = data.province.name ? data.province.name + '/' : ''
				let citys = data.citys.name ? data.citys.name + '/' : ''
				let area = data.area.name ? data.area.name + '/' : ''
				this.cityName = province + citys + area
				// #endif
				this.gettree(this.showChain);
				this.changeSelectType()
			},
			//是否选中
			iconColour(data) {
				if (!this.Jurisdiction) {
					return '/static/AboutAi/grayArrows.png'
				} else {
					if (data) {
						return '/static/AboutAi/blackArrows.png'
					} else {
						return '/static/AboutAi/blueArrows.png'
					}
				}
			},
			//判断是否完全为空
			isEmptyValue(value) {
				if (typeof value === 'string') {
					return value.trim() === '';
				}
				if (typeof value === 'number') {
					return value === 0;
				}
				if (typeof value === 'boolean') {
					return value === false;
				}
				if (Array.isArray(value)) {
					return value.every(this.isEmptyValue);
				}
				if (value !== null && typeof value === 'object') {
					return Object.values(value).every(this.isEmptyValue);
				}
				return false;
			},
			//筛选高亮
			changeSelectType() {
				this.parameterList[0] = this.isEmptyValue(this.baseInfo)
				this.parameterList[1] = this.isEmptyValue(this.affinityData)
				this.parameterList[2] = this.isEmptyValue(this.moreData)
				this.parameterList[3] = this.isEmptyValue(this.sortData)
			},
			//获取是否有权限
			getJurisdiction() {
				if (!this.token) {
					return
				}
				this.$api.checkByKnowIdAPI({
					data: {
						knowId: this.knowId
					},
					method: "GET"
				}).then((res) => {
					this.Jurisdiction = res.result
				})
			},
			//筛选项
			getScreen() {
				if (!this.token) {
					return
				}
				this.$api.getSearchParamAPI({
					method: "GET"
				}).then((res) => {
					this.moreScreenList = res.result
				})
				this.$api.getKinshipParamAPI({
					method: "GET"
				}).then((res) => {
					this.moreScreenList2 = res.result
				})
			},
			//暂无权限弹窗
			openJurisdictionPop() {
				this.$refs.noAccess.opens()
			},
			//打开筛选弹层
			screenFn(type) {
				if (!this.token) {
					let isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
					if (isInApp) {
						window?.ReactNativeWebView?.postMessage(
							JSON.stringify({
								type: "changePath",
								value: "",
								path: "login",
							}));
					} else {
						uni.reLaunch({
							url: `/pages/login/index`
						});
					}
					return
				}
				if (!this.Jurisdiction) {
					this.$refs.noAccess.opens()
					return
				}
				if (type == 1) {
					this.$refs.popup2.open('bottom')
					// #ifndef MP-WEIXIN
					this.$nextTick(() => {
						if (this.$refs.mapList2) {
							this.$refs.mapList2.init()
						} else {
							console.warn('mapList2组件未挂载打开时')
						}
					})
					// #endif
				} else if (type == 2) {
					this.$refs.moreScreen2.opens()
				} else if (type == 3) {
					this.$refs.moreScreen.opens()
				} else if (type == 4) {
					this.$refs.EnterpriseTypePop.open('bottom')
					// #ifndef MP-WEIXIN
					this.$nextTick(() => {
						if (this.$refs.MultipleChoice) {
							this.$refs.MultipleChoice.init(this.introductionSelectIndex)
						} else {
							console.warn('MultipleChoice')
						}
					})
					// #endif
				}
			},
			//去搜索页
			goSearch() {
				if (!this.token) {
					let isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
					if (isInApp) {
						window?.ReactNativeWebView?.postMessage(
							JSON.stringify({
								type: "changePath",
								value: "",
								path: "login",
							}));
					} else {
						uni.reLaunch({
							url: `/pages/login/index`
						});
					}
					return
				}
				if (!this.Jurisdiction) {
					this.$refs.noAccess.opens()
					return
				}
				// #ifndef MP-WEIXIN
				window?.ReactNativeWebView?.postMessage(
					JSON.stringify({
						type: "changePath",
						value: {
							showChain: this.showChain,
							knowId: this.knowId
						},
						path: "industrySearch",
					})
				);
				// #endif
				// #ifndef H5
				uni.navigateTo({
					url: `/pages/search/index?showChain=${this.showChain}&knowId=${this.knowId}`
				});
				// #endif
			},
			//h5取token
			init() {

				// this.popupShow=false
				// this.$refs.popup.open()
				let isInApp = navigator.userAgent.indexOf("airApp/1.0.0") > -1;
				// alert(isInApp?'app':'no')
				if (isInApp) {
					let data = {
						token: "",
						id: "",
					};
					let url = window.location.href;
					let tokenAndId = url.split("?token=")?.[1];
					let values = tokenAndId?.split("&id=");
					data.token = values?.[0]?.split("&abilityName=")?.[0];
					data.id = values?.[1];
					uni.setStorageSync("token", data.token);
				}
			},
			//切换产业链
			onConfirm(e) {
				uni.setStorageSync("Selectedindustrychain", e);
				let {
					selected,
					values
				} = e;
				if (values[1] != undefined) {
					//清空原有的筛选Clear filter
					this.ClearFilter()
					this.List = [];
					this.amount = 0;
					this.showChain = selected[1]?.chainId;
					this.defaultValue = [values[0], values[1]];
					this.initialValue = [values[0], values[1]];
					this.title = selected[1]?.chainName;
					this.knowId = selected[1]?.knowId;
					this.gettree(values[1]);
					this.getJurisdiction();
				} else {
					//console.log('没选中二级');
				}
			},
			//数量展示
			digitalfiltering(item) {
				if (!item.sumEnterpriseCount) {
					if (this.parameterList[0] && this.parameterList[1] && this.parameterList[2] && this
						.parameterList[3]) {
						return `0`
					}
					return `<span style="color: #3370FF;">0</span>/0`;
				} else {
					if (!item.searchEnterpriseCount) {
						item.searchEnterpriseCount = 0
					}
					let searchEnterpriseCount = parseFloat(item.searchEnterpriseCount).toString();
					let sumEnterpriseCount = parseFloat(item.sumEnterpriseCount).toString();
					if (this.parameterList[0] && this.parameterList[1] && this.parameterList[2] && this
						.parameterList[3]) {
						return `${sumEnterpriseCount}`
					}
					return `<span style="color: #3370FF">${searchEnterpriseCount}</span>/${sumEnterpriseCount}`;
				}
			},
			changeState(e) {
				this.popupShow = e.show;
			},
			changeState2(e) {
				this.popupShow = e;
			},
			// 下拉滚动
			loadMoreData() {
				if (this.pageNum > this.size) {
					this.mosj = true;
					return;
				}
				if (this.isLoading) {
					return;
				}
				this.isLoading = true;
				let data = {
					nodeId: this.isshownodeName.nodeId,
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					chainId: this.showChain,
					//isImportantEnterprise: true, //重点企业
					//importantEnterprise: true,
					enterpriseLabelIds: this.pitchtag,
					...this.baseInfo,
					...this.moreData,
					...this.affinityData,
					...this.sortData,
				};
				this.$api
					.pageByChainNode({
						data,
						method: "POST",
					})
					.then((res) => {
						this.mosj = false;
						this.total = res.result.total;
						this.size = Math.ceil(this.total / 10);
						this.pageNum++;
						this.keyenterprise = this.keyenterprise.concat(res.result.records);
						this.isLoading = false;
					})
					.catch((err) => {
						this.isLoading = false;
					});
			},
			ecoIndustryChain(options) {
				this.showChain = options.showChain
				this.title = options.title
				this.knowId = options.knowId
				this.Jurisdiction = true
				this.gettree(this.showChain);
			},
			// 产业链列表
			getindustryList() {
				this.$api
					.industrychainList({
						data: {
							capType: this.selectType,
						},
						method: "get",
					})
					.then((res) => {
						this.industryChainList = res.result.map((item) => {
							let newChainList = item.chainList.map((childrenItem) => {
								return {
									...childrenItem,
									label: childrenItem.chainName,
									value: childrenItem.chainId,
									categoryBuyStatus: childrenItem.buyStatus,
								};
							});
							let {
								chainList,
								...rest
							} = item;
							return {
								...rest,
								label: item.categoryName,
								value: item.categoryId,
								children: newChainList,
							};
						});
						let Selectedindustrychain = uni.getStorageSync("Selectedindustrychain")
						this.list = this.industryChainList;
						if (Selectedindustrychain) {
							this.showChain = Selectedindustrychain.selected[1].chainId
							this.title = Selectedindustrychain.selected[1].chainName
							this.knowId = Selectedindustrychain.selected[1].knowId
							this.defaultValue = [
								Selectedindustrychain.selected[0].categoryId,
								this.showChain,
							];
							this.initialValue = [
								Selectedindustrychain.selected[0].categoryId,
								this.showChain,
							];
							this.gettree(this.showChain);
							this.getJurisdiction();
							setTimeout(() => {
								let Arr = this.list.map(it => {
									if (it.value == this.initialValue[0]) {
										this.$refs.treeSelect.echo(this.list, it.children)
									}
								})
							}, 1000)
						} else {
							this.showChain = res.result[0].chainList[0]?.chainId;
							this.title = res.result[0].chainList[0]?.chainName;
							this.knowId = res.result[0].chainList[0]?.knowId
							this.defaultValue = [
								this.industryChainList[0].categoryId,
								this.showChain,
							];
							this.initialValue = [
								this.industryChainList[0].categoryId,
								this.showChain,
							];
							this.gettree(this.showChain);
							this.getJurisdiction();
						}

					});
			},
			//获取产业链节点树
			gettree(id) {
				uni.setStorageSync("chainId", id);
				let data = {
					chainId: id,
					//countEnterpriseNum: 0
					...this.baseInfo,
					...this.moreData,
					...this.affinityData,
					...this.sortData,
				};
				this.showTree = false;
				this.$api
					.nodeTreeListAPI({
						data,
						method: "POST",
					})
					.then((res) => {
						this.List = res.result;
						this.List.forEach((item, index) => {
							if (index == 0 && item.children.length != 0) {
								item.show = true;
							} else {
								item.show = false;
							}
							item.children.forEach((it) => {
								it.show = false;
								if (it.children) {
									it.children.forEach((i) => {
										i.show = false;
									});
								}
							});
						});
						this.showTree = true;
					})
					.catch((err) => {
						this.showTree = true;
					})
			},
			// 节点的展开和关闭
			isshow(index, ind) {
				if (ind == undefined) {
					this.List[index].show = !this.List[index].show;
				} else {
					this.List[index].children[ind].show = !this.List[index].children[ind].show;
				}
			},
			// 点击产业链节点
			recommendQ(item) {
				if (!this.token) {
					let isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
					if (isInApp) {
						window?.ReactNativeWebView?.postMessage(
							JSON.stringify({
								type: "changePath",
								value: "",
								path: "login",
							}));
					} else {
						uni.reLaunch({
							url: `/pages/login/index`
						});
					}
					return
				}
				this.isshownodeName = item;
				(this.pageNum = 1), (this.mosj = false), (this.keyenterprise = []);
				this.pitchtag = [];
				let data = {
					nodeId: item.nodeId,
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					chainId: this.showChain,
					...this.baseInfo,
					...this.moreData,
					...this.affinityData,
					...this.sortData,
				};
				this.$api
					.pageByChainNode({
						data,
						method: "POST",
					})
					.then((res) => {
						this.total = res.result.total;
						this.size = Math.ceil(this.total / 10);
						this.keyenterprise = this.keyenterprise.concat(res.result.records);
						this.pageNum++;
						this.$refs.popup.open("bottom");
					});
			},
		},
		watch: {
			keyenterprise(value) {
				this.keyenterprise.forEach(it => {
					let arr = ['主板', '创业板', '科创板', '北交所', '港股', '中概股', '新三板'] //上市
					if (it.enterpriseIconLabelId === null) {
						it.enterpriseIconLabelId = 0
					}
					if (it.enterpriseLabelNames) {
						let Names = it.enterpriseLabelNames[0]
						if (!Names) {
							it.enterpriseIconLabelId = 0
						} else if (arr.includes(it.enterpriseLabelNames[0])) {
							it.enterpriseIconLabelId = 1
						} else if (Names.includes('独角兽')) {
							it.enterpriseIconLabelId = 2
						} else if (Names.includes('专精特新')) {
							it.enterpriseIconLabelId = 3
						} else if (Names.includes('隐形冠军')) {
							it.enterpriseIconLabelId = 4
						} else if (Names.includes('瞪羚')) {
							it.enterpriseIconLabelId = 5
						} else if (Names.includes('创新')) {
							it.enterpriseIconLabelId = 6
						} else if (Names.includes('技术先进')) {
							it.enterpriseIconLabelId = 7
						} else if (Names.includes('科技')) {
							it.enterpriseIconLabelId = 8
						} else if (Names.includes('雏鹰')) {
							it.enterpriseIconLabelId = 9
						} else {
							it.enterpriseIconLabelId = 0
						}
					} else {
						it.enterpriseIconLabelId = 0
					}
				})
			}
		},
	};
</script>

<style lang="scss" scoped>
	.box {
		width: 750rpx;
		position: absolute;
		overflow: hidden;
		box-sizing: border-box;
		z-index: 66;
		/* #ifndef MP-WEIXIN */
		top: 10rpx;
		height: calc(100vh - 10rpx);
		/* #endif */
		/* #ifndef H5 */
		top: 208rpx;
		height: calc(100vh - 208rpx);

		/* #endif */
		.messageTop {
			position: absolute;
			top: 0;
			right: 0;
			height: 230rpx;
			width: 750rpx;
			z-index: 1;
		}

		.titleBox {
			width: 750rpx;
			height: 88rpx;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			z-index: 2;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 40rpx;
			box-sizing: border-box;
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
			z-index: 68;

			.cutIcon {
				width: 32rpx;
				height: 32rpx;
				min-width: 32rpx;
				min-height: 32rpx;
				margin-right: 8rpx;
				margin-top: 2rpx;
			}
		}

		.contentBox {
			width: 750rpx;
			height: calc(100vh - 248rpx);
			/* #ifndef MP-WEIXIN */
			height: calc(100vh - 48rpx);
			/* #endif */

			//background: rgba(243, 245, 249, 0.9);
			background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, #F3F5F9 100%);
			//background: linear-gradient(180deg, #EEF2FD 0%, #F3F5F9 100%);
			//background: linear-gradient(180deg, rgba(255, 255, 255, 0.42) 0%, rgba(255, 255, 255, 0) 100%);
			border-radius: 32rpx 32rpx 0rpx 0rpx;
			position: relative;
			z-index: 3;
			margin-top: 8rpx;
			padding-top: 6rpx;
			display: flex;
			flex-direction: column;

			.nodeBox {
				::-webkit-scrollbar {
					display: none;
				}

				padding: 0 32rpx;
				width: 100%;
				height: 100%;
				box-sizing: border-box;

				.OperationExpansion {
					width: 50rpx;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}

				.nodeItem {
					width: 100%;
					height: auto;
					padding: 32rpx;
					box-sizing: border-box;
					background: #FFFFFF;
					box-shadow: 0px 4px 12px 0px rgba(191, 195, 203, 0.1);
					margin-bottom: 24rpx;
					border-radius: 12rpx;

					.unfoldIcon {
						width: 32rpx;
						height: 32rpx;
						min-width: 32rpx;
						min-height: 32rpx;
						transform: rotate(-90deg);
					}

					.upackIcon {
						width: 32rpx;
						height: 32rpx;
						min-width: 32rpx;
						min-height: 32rpx;
						transform: rotate(90deg);
					}

					.stairNode {
						display: flex;
						align-items: center;
						justify-content: space-between;
						white-space: nowrap;
						height: 48rpx;
						line-height: 48rpx;
						color: #333333;
						font-size: 32rpx;
						font-weight: 600;

						.stairNodeText {
							display: flex;
							align-items: center;
							white-space: nowrap;
						}
					}

					.secondLevelBox {
						border-top: 2rpx solid #F5F5F5;
						margin-top: 24rpx;
						padding-top: 32rpx;
						display: flex;
						flex-direction: column;

						.secondLevel {
							display: flex;
							align-items: center;
							justify-content: space-between;
							margin-bottom: 24rpx;
							height: 48rpx;
							line-height: 48rpx;
							color: #333333;
							font-size: 28rpx;
							font-weight: 600;

							.secondLevelText {
								display: flex;
								align-items: center;
								white-space: nowrap;

								.dot {
									width: 12rpx;
									height: 12rpx;
									min-width: 12rpx;
									min-height: 12rpx;
									border-radius: 50%;
									margin-right: 16rpx;
									background: rgba(51, 112, 255, 0.2);
								}
							}

						}
					}

					.threeLevelBox {
						margin-left: 32rpx;

						.threeLevel {
							display: flex;
							align-items: center;
							justify-content: space-between;
							margin-bottom: 24rpx;
							height: 44rpx;
							color: #333333;
							font-size: 28rpx;
							line-height: 44rpx;
							font-weight: normal;
						}

						.threeLevelText {
							display: flex;
							width: 100%;
							align-items: center;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}


	/* #ifndef MP-WEIXIN */
	body,
	uni-page-body {
		background-color: transparent !important;
	}

	/* #endif */
	.searchIcon {
		width: 36rpx;
		height: 36rpx;
		min-width: 36rpx;
		min-height: 36rpx;
	}

	.optionBox {
		width: 100%;
		height: 88rpx;
		min-height: 88rpx;
		display: flex;

		.optionItems {
			width: 19%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 28rpx;
		}

		.optionItem {
			width: 25%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 28rpx;
		}

		.Arrows {
			width: 22rpx;
			height: 22rpx;
			min-width: 22rpx;
			min-height: 22rpx;
			margin-left: 8rpx;
		}
	}

	.pitchOn {
		color: #3370FF;
	}

	.noAccess {
		color: #86909C;
	}

	.unselected {
		color: #3F4A59;
	}

	.noemphasis {
		width: 90%;
		height: 400rpx;
		margin-left: 5%;
		margin-top: 20rpx;
		background-color: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: start;

		/* #ifndef MP-WEIXIN */
		img {
			width: 390rpx;
			height: 300rpx;
		}

		/* #endif */
		image {
			width: 390rpx;
			height: 300rpx;
		}

		.nozdqy {
			text-align: center;
			margin-top: -50rpx;
		}
	}

	.noemphasis2 {
		display: flex;
		align-items: center;
		justify-content: center;
		//  #ifndef MP-WEIXIN
		padding-bottom: 30%;
		//  #endif
	}

	.qyList {
		width: 92%;
		margin-left: 4%;
	}

	.uni-popups {
		height: 1200rpx;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		overflow-x: hidden;
		background-color: #f7f7f7;

		.popup-content {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 20rpx;

			.more {
				margin-top: 8rpx;
				font-size: 28rpx;
				color: #4e5969;
			}

			.nodeName {
				font-size: 36rpx;
				font-weight: 500;
				color: #1d2129;
			}
		}
	}

	page {
		background-color: rgb(247, 247, 247);
	}

	.uni-drawer__content--visible {
		background-color: #f7f7f7 !important;
	}

	.upBgc {
		width: 24rpx !important;
		height: 24rpx !important;
		margin-left: 10rpx;
		// position: absolute;
		// top: -6px;
		// right: -10px;
	}
</style>