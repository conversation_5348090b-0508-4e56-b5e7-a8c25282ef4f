<template>
	<page-meta :page-style="'overflow:' + (popupShow ? 'hidden' : 'visible')" />
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<tabBar title="企业详情" />
		<view v-if="showHeader" class="headerBack">
			<view class="back" @click="goBack" />
			<view class="text">企业详情 </view>
		</view>

		<!-- #endif -->
		<!-- #ifdef H5  -->
		<div v-if="clientVersion && clientVersion > 250">
			<tabBar title="企业详情" />
			<view class="headerBack">
				<view class="back" @click="goBack" />
				<view class="text">企业详情 </view>
			</view>
		</div>
		<!-- #endif -->
		<view v-if="show" class="enterpriceDetail">
			<!-- 企业详情 -->
			<view>
				<view class="firm">
					<!--  <view v-if="enterpriseList.historyRecommend" class="historyRecommendBox">历史推荐</view> -->
					<img :src="listicon[iconTypeid].icon" alt="" class="iconImg">
					<view class="texts">
						<view class="userNname">
							<span class="enterpriseName" @longpress="handleLongPress(enterpriseList.enterpriseName)">
								{{ enterpriseList.enterpriseName }}
							</span>
							<image
								v-if="IdentityType == 1 && !isDefault && orgName && (enterpriseList.isExist !== false)"
								class="attention"
								:src="enterpriseList?.isCollect ? '/static/AboutAi/attention.png' : '/static/AboutAi/noAttention.png'"
								@click.stop="changAttention(enterpriseList?.isCollect)" />
						</view>
						<view v-if="enterpriseList?.enterpriseLabelNames" class="enterpriseLabeltag">
							<view v-for="(it, ind) in showTag" :key="ind" class="enterpriseLabel">
								<div class="tagBox">
									{{ it }}
								</div>
							</view>
							<view v-if="enterpriseList?.enterpriseLabelNames.length > 4" class="tagBox" @click="more">
								<span class="tag-text">
									<span class="gd-te">{{ isshow ? "收起" : "更多" }}</span>
									<uni-icons class="gd-icon" :type="isshow ? 'top' : 'bottom'" size="10"
										color="#FF7D00" /></span>
							</view>
						</view>
						<view v-if="enterpriseList.historyRecommend" class="historyRecommendBox2">历史推荐</view>
						<view style="margin-top: 10rpx;margin-bottom: 30rpx;">
							<view class="recommend">
								<view class="phoneIcon" />
								<view>
									<span
										@longpress="handleLongPress(enterpriseList?.mobile)">{{ enterpriseList?.mobile }}</span>
								</view>
							</view>
							<view class="recommend">
								<view class="countryIcon" />
								<view v-if="enterpriseList?.enterpriseAddress == '-'"
									@longpress="handleLongPress(enterpriseList.province + (enterpriseList?.city != enterpriseList?.province ? enterpriseList?.city + enterpriseList?.area : enterpriseList?.area))">
									<span>{{ enterpriseList?.province }}</span>
									<span v-if="enterpriseList?.city != enterpriseList?.province">
										{{ enterpriseList?.city }}
									</span>
									<span>{{ enterpriseList?.area }}</span>
								</view>
								<view v-else @longpress="handleLongPress(enterpriseList?.enterpriseAddress)">
									<span>{{ enterpriseList?.enterpriseAddress }}</span>
								</view>
							</view>
							<view class="recommend">
								<view class="increaseIcon" />
								<view>
									快速成长指数
									<span style="margin-left: 8rpx;color: #34C759;">
										{{ (enterpriseList?.growthIndex == '0.0' || !enterpriseList?.growthIndex) ? '-' :
                      enterpriseList?.growthIndex }}
									</span>
								</view>
							</view>
							<view class="recommend">
								<view class="expandIcon" />
								<view>
									扩张意愿指数
									<span style="margin-left: 8rpx;color: #FF9500;">
										{{ (enterpriseList?.expansionIndex == '0.0' || !enterpriseList?.expansionIndex) ? '-' :
                      enterpriseList?.expansionIndex }}
									</span>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="line" />
			<view v-if="enterpriseList?.chainDTOS.length != 0" class="tag-p">
				<view class="tag-container">
					<view v-for="(item, index) in enterpriseList?.chainDTOS" :key="index" class="tag-title" :style="{
            'background-color': selectedTagIndex == index ? '#fff' : '#f5f5f5', color: selectedTagIndex == index ? '#417FFF' : '#72787d', 'font-weight': selectedTagIndex == index ? '600' : '500',
          }" @click="selectTag(index)">
						<view v-if="item.chainName !== null" class="chainName">
							{{ item.chainName.replace("产业金脑·", "") }}
						</view>
					</view>
				</view>
				<view class="tag-it">
					<view v-for="(item, inde) in enterpriseList?.chainDTOS" :key="inde">
						<view v-if="selectedTagIndex == inde" class="nodeNamesTag">
							<view v-for="(it, ind) in item.chainNodeList" :key="ind" class="tag-c"
								@click="pitchsecond(ind)">
								<uni-tag v-if="selectedTagIndex2 == ind" :text="it.nodeName"
									custom-style="background-color: #3370FF; border-color: #3370FF; color: #FFFFFF ;font-weight: 700;" />
								<uni-tag v-else :text="it.nodeName"
									custom-style="background-color: #eff4ff; border-color: #eff4ff; color: #417FFF ;font-weight: 700;" />
							</view>
						</view>
					</view>
				</view>
				<view v-if="enterpriseList?.chainDTOS[selectedTagIndex]?.chainNodeList[selectedTagIndex2]?.productNames"
					:class="(StringTotal > 38 && !showString) ? 'limitationNameList' : 'nameList'">
					<view
						v-for="(it, ind) in enterpriseList?.chainDTOS[selectedTagIndex]?.chainNodeList[selectedTagIndex2].productNames"
						:key="ind" class="nameItem">
						{{ it }}
					</view>
				</view>
				<div v-if="enterpriseList?.chainDTOS[selectedTagIndex]?.chainNodeList[selectedTagIndex2]?.productNames">
					<div v-if="StringTotal > 38 && enterpriseList?.chainDTOS[selectedTagIndex]?.chainNodeList[selectedTagIndex2].productNames.length > 2"
						class="parent-container">
						<image src="../../../static/user/right.png" :class="showString ? 'showIcon' : 'showIcons'"
							@click="showString = !showString" />
					</div>
				</div>
			</view>
			<view style="height: 20rpx;" />

			<view class="detials">

				<uni-collapse class="detialContent">
					<!-- 触达方式 -->
					<uni-collapse-item v-if="enterpriseList?.companyTouchpointMethods" title="触达方式"
						thumb="https://static.idicc.cn/cdn/zhaoShang/touch.svg" class="detialHeader" open
						:border="false">
						<view class="content">
							{{enterpriseList?.companyTouchpointMethods}}
						</view>
					</uni-collapse-item>
					<!-- 企业简介 -->
					<uni-collapse-item open title="企业简介" thumb="https://static.idicc.cn/cdn/zhaoShang/introduce.svg"
						class="detialHeader" :border="false" v-if="
              enterpriseList?.introduction &&
              enterpriseList?.introduction !== '-'
            ">
						<view class="content">
							<view v-if="
                enterpriseList?.introduction &&
                enterpriseList?.introduction !== '-'
              " class="introduction">
								{{ enterpriseList?.introduction }}
							</view>
						</view>
					</uni-collapse-item>
					<!-- 基本信息 -->
					<uni-collapse-item open title="基本信息" thumb="https://static.idicc.cn/cdn/zhaoShang/baseInfo.svg"
						:border="false" class="detialHeader">
						<view class="content">
							<view class="essentialinformation">
								<view class="one">
									<i class="yuan" />
									<span class="name">统一社会信用代码</span>
									<uni-tooltip :content="enterpriseList?.unifiedSocialCreditCode" placement="bottom">
										<span class="result">{{
                      enterpriseList?.unifiedSocialCreditCode || '-'
                      }}</span>
									</uni-tooltip>
								</view>
								<view class="one">
									<i class="yuan" />
									<span class="name">成立日期</span>
									<uni-tooltip :content="enterpriseList?.registerDate" placement="bottom">
										<span class="result">{{ enterpriseList?.registerDate || '-' }}</span>
									</uni-tooltip>
								</view>
							</view>
							<view class="essentialinformation">
								<view class="one">
									<i class="yuan" />
									<span class="name">企业规模</span>
									<uni-tooltip :content="enterpriseList?.scale" placement="bottom">
										<span class="result">{{ enterpriseList?.scale || '-' }}</span>
									</uni-tooltip>
								</view>
								<view class="one">
									<i class="yuan" />
									<span class="name">融资轮次</span>
									<uni-tooltip v-if="enterpriseList.enterpriseFinancingRoundsNames"
										:content="enterpriseList?.enterpriseFinancingRoundsNames.join('、')"
										placement="bottom">
										<span class="result">{{
                      enterpriseList?.enterpriseFinancingRoundsNames.join('、') || '-'
                      }}</span>
									</uni-tooltip>
									<span v-else class="result">-</span>
								</view>
							</view>
							<view class="essentialinformation">
								<view class="one">
									<i class="yuan" />
									<span class="name">法定代表人</span>
									<uni-tooltip :content="enterpriseList?.legalPerson" placement="bottom">
										<span class="result">{{ enterpriseList?.legalPerson || '-' }}</span>
									</uni-tooltip>
								</view>
								<view class="one">
									<i class="yuan" />
									<span class="name">注册资本</span>
									<uni-tooltip :content="enterpriseList?.registeredCapital" placement="bottom">
										<span class="result">{{
                      enterpriseList?.registeredCapital || '-'
                      }}</span>
									</uni-tooltip>
								</view>
							</view>
							<view class="essentialinformation">
								<view class="one">
									<i class="yuan" />
									<span class="name">企业类型</span>
									<uni-tooltip :content="enterpriseList?.enterpriseType" placement="bottom">
										<span class="result">{{
                      enterpriseList?.enterpriseType || '-'
                      }}</span>
									</uni-tooltip>
								</view>
								<view class="one">
									<i class="yuan" />
									<span class="name">登记状态</span>
									<uni-tooltip :content="enterpriseList?.registerStatus" placement="bottom">
										<span class="result">{{
                      enterpriseList?.registerStatus || '-'
                      }}</span>
									</uni-tooltip>
								</view>
							</view>
							<view class="essentialinformation">
								<view class="one">
									<i class="yuan" />
									<span class="name">所在地区</span>
									<uni-tooltip :content="enterpriseList.province != enterpriseList.city
                    ? enterpriseList.province +
                    enterpriseList.city +
                    enterpriseList.area
                    : enterpriseList.province + enterpriseList.area
                    " placement="bottom">
										<span class="result">{{ enterpriseList.province || '-'
                      }}<span v-if="enterpriseList?.province != enterpriseList?.city">{{ enterpriseList.city
                        }}</span>{{
                          enterpriseList.area }}</span>
									</uni-tooltip>
								</view>
								<view class="one">
									<i class="yuan" />
									<span class="name">所属行业</span>
									<uni-tooltip :content="(enterpriseList.nationalStandardIndustry || '') +
                    (enterpriseList.nationalStandardIndustryBig || '') +
                    (enterpriseList.nationalStandardIndustryMiddle || '') +
                    (enterpriseList.nationalStandardIndustrySmall || '')
                    " placement="bottom">
										<span class="result">
											{{ enterpriseList.nationalStandardIndustry }}
											{{ enterpriseList.nationalStandardIndustryBig }}
											{{ enterpriseList.nationalStandardIndustryMiddle }}
											{{ enterpriseList.nationalStandardIndustrySmall }}
										</span>
									</uni-tooltip>
								</view>
							</view>
							<view class="essentialinformation">
								<view class="ones">
									<i class="yuan" />
									<span class="name">注册地址</span>
									<uni-tooltip :content="enterpriseList.enterpriseAddress" placement="bottom">
										<span class="result">{{
                      enterpriseList.enterpriseAddress || '-'
                      }}</span>
									</uni-tooltip>
								</view>
							</view>
							<view class="essentialinformations">
								<view class="ones">
									<i class="yuan" />
									<span class="name">经营范围</span>
									<span class="result">{{ enterpriseList.businessScope || '-' }}</span>
								</view>
							</view>
						</view>
					</uni-collapse-item>
					<!-- 亲缘关系 -->
					<uni-collapse-item
						v-if="enterpriseList && enterpriseList.associationList && enterpriseList.associationList.length != 0"
						title="亲缘关系" thumb="https://static.idicc.cn/cdn/zhaoShang/relation.svg" open :border="false"
						class="detialHeader">
						<view class="content">
							<!-- 商会/协会/学会/校友会 -->
							<view
								v-if="enterpriseList && enterpriseList.associationList && enterpriseList.associationList.length != 0"
								class="chamberBox">
								<!-- <div class="chambertitle">商会/协会/学会/校友会</div> -->
								<div v-for="(item, index) in enterpriseList?.associationList" :key="index"
									class="associationItem">
									<div class="titleBox">
										<!-- 					<span class="indexs">{{index+1}}</span> -->
										<image class="us" src="/static/AboutAi/us.png" />
										{{ item.relateName }}
										<div v-if="item?.positions?.length != 0 && item?.positions != null"
											class="longString">|</div>
										<div v-for="(it, ind) in item?.positions" :key="ind" class="chamberTag">
											{{ it }}
										</div>
									</div>
									<div v-if="item?.ancestorHome" class="Singleline">
										<div class="chamberKey">
											籍贯：
										</div>
										<div class="chamberValue">{{ item?.ancestorHome ? item?.ancestorHome : '-' }}
										</div>
									</div>
									<div v-if="item.schools?.length != 0 && item.schools != null" class="Singleline">
										<div class="chamberKey">
											毕业院校：
										</div>
										<div class="chamberValue">
											{{ item?.schools?.length != 0 && item?.schools != null ? item?.schools?.join('、') : '-' }}
										</div>
									</div>
									<div v-if="item.commerceNames?.length != 0 && item.commerceNames != null"
										class="Singleline">
										<div class="chamberKey">
											所属商会：
										</div>
										<div class="chamberValue">
											{{ item?.commerceNames?.length != 0 && item?.commerceNames != null ?
                        item?.commerceNames?.join('、') :
                        '-' }}
										</div>
									</div>
									<div v-if="item.alumniNames?.length != 0 && item?.alumniNames != null"
										class="Singleline">
										<div class="chamberKey">
											所属校友会：
										</div>
										<div class="chamberValue">
											{{ item?.alumniNames?.length != 0 && item?.alumniNames != null ? item?.alumniNames?.join('、') :
                        '-' }}
										</div>
									</div>
								</div>
							</view>
						</view>
					</uni-collapse-item>
					<!-- 企业动态 -->
					<uni-collapse-item open v-if="businessDataList.length != 0" title="企业动态"
						thumb="https://static.idicc.cn/cdn/zhaoShang/news.svg" :border="false" class="detialHeader">
						<view class="content" v-if="businessDataList.length != 0">
							<view class="chamberBox">
								<div v-for="(item, index) in businessDataList" :key="index" class="dynamicItem">
									<div class="buleCircle">
										<div class="kernel" />
									</div>
									<div class="title">
										{{ item.eventTitle }}
									</div>
									<div class="publishDate">
										{{ item.publishDate }}
									</div>
									<div class="eventContent">
										<span>{{ item.Expand ? item.eventContent : item.showEventContent + (item.eventContent?.length > 38 ? '...' : '') }}</span>
										<span v-if="item.eventContent?.length > 38" style="color: #3370FF;"
											@click="RetractionExpansion(index)">{{
                        item.Expand ? '收起' : '展开' }}<!-- #ifndef H5 --><uni-icons :type="item.Expand ? 'up' : 'down'"
												color="#3370FF" size="18" />
											<!-- #endif -->
										</span>
									</div>
								</div>
							</view>
							<div v-if="businessDataList.length != 0 && pages > pageNum" class="showMore"
								@click="showMore">
								查看更多
								<image class="showMoreIcon" src="https://static.idicc.cn/cdn/zhaoShang/showMore.svg" />
							</div>
						</view>
					</uni-collapse-item>
					<!-- 项目信息 -->
					<!-- 					<uni-collapse-item title="项目信息" thumb="hfttps://static.idicc.cn/cdn/zhaoShang/project.svg"
						:border="false" class="detialHeader">
						<view class="content">
							<text class="text">折叠内容主体，可自定义内容及样式</text>
						</view>
					</uni-collapse-item>
					<uni-collapse-item title="链式关系信息" thumb="https://static.idicc.cn/cdn/zhaoShang/chainType.svg"
						:border="false" class="detialHeader">
						<view class="content">
							<view class="essentialinformations">
								<view class="ones">
									<i class="yuan" />
									<span class="name">链式关系1</span>
									<span class="result">{{ enterpriseList.businessScope }}</span>
								</view>
							</view>
						</view>
					</uni-collapse-item> -->

				</uni-collapse>
			</view>
			<view style="height: 140rpx" />
		</view>
		<div class="bottomBtnBox"
			:style="{ justifyContent: (enterpriseList.historyRecommend && (IdentityType == 1 && !isDefault && orgName)) ? 'space-between' : 'center' }">
			<div v-if="enterpriseList.historyRecommend" class="strategyBtn" @click="goDetail">一企一策</div>
			<div v-if="IdentityType == 1 && !isDefault && orgName" :class="[
        enterpriseList.isInvestClue ? 'hasbeenIncluded' : 'noIncluded',
        getWidthClass()
      ]" @click="Intention(enterpriseList.isInvestClue)">
				{{ enterpriseList.isInvestClue ? '已纳入招商意向' : '纳入招商意向' }}
			</div>
		</div>
		<bringInto ref="bringInto" @operatePopup="operatePopup" />
	</view>
</template>

<script>
	import industryinformation from './industryinformation.vue';
	import tabBar from '../../../components/tabBar/index.vue';
	import
	bringInto from '../../../components/pop/bringInto.vue';
	import {
		getQueryParam,
		listicon
	} from '@/utils/utils.js';
	import UniCollapseItem from '../../../uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue';
	export default {
		components: {
			industryinformation,
			tabBar,
			bringInto,
			UniCollapseItem
		},
		data() {
			return {
				showString: false,
				showHeader: false,
				listicon,
				enterpriseList: {
					chainDTOS: []
				},
				informationList: [],
				pageNum: 1,
				pageSize: 10,
				pages: 0,
				total: 0, //企业动态
				total2: 0, //走访记录
				showTag: [],
				isshow: false,
				selectedTagIndex: 0,
				selectedTagIndex2: 0,
				messageText: '', //提示弹层
				Successmessage: {},
				companyID: '', //企业id
				Intentionornot: false, //是否纳入意向
				IntenID: '', //线索id
				name: '', //企业名称
				assign: false, //是否有指派权限
				IdentityType: uni.getStorageSync('userIdentityType') || 1,
				orgName: uni.getStorageSync('orgName') || '',
				isDefault: uni.getStorageSync('isDefault') || false,
				tabChack: 1,
				tabIsShow: false,
				tabList: [],
				time: null,
				isScrollByTab: false,
				bottomHeight: 0,
				enterpriseLabelIds: [],
				popupShow: false,
				names: '',
				iconTypeid: 0,
				showChain: '',
				clientVersion: 0,
				show: false,
				productNamesLength: 0, // 存储数组长度
				StringTotal: 0,

				clueSource: '', // 意向企业来源
				clueId: '',
				isShowMore: true,
				businessDataList: []
			};
		},
		// 详情页接收参数
		onLoad(options) {
			// #ifdef H5
			this.requestOrgCode();
			if (getQueryParam('clientVersion')) {
				this.clientVersion = Number(getQueryParam('clientVersion'));
			}
			if (getQueryParam('enterpriseLabelIds')) {
				this.enterpriseLabelIds = JSON.parse(
					decodeURIComponent(getQueryParam('enterpriseLabelIds'))
				);
			}
			if (getQueryParam('showChain')) {
				this.showChain = getQueryParam('showChain');
			}
			if (getQueryParam('id')) {
				this.companyID = getQueryParam('id');
			}
			if (getQueryParam('iconTypeid')) {
				this.iconTypeid = getQueryParam('iconTypeid');
			}
			if (getQueryParam('enterpriseName')) {
				this.name = getQueryParam('enterpriseName');
			}
			if (getQueryParam('clueSource')) {
				this.clueSource = getQueryParam('clueSource');
			}
			if (getQueryParam('clueId')) {
				this.clueId = getQueryParam('clueId');
			}
			// #endif
			// #ifdef MP-WEIXIN
			let app = uni.getSystemInfoSync();
			this.bottomHeight = app.safeAreaInsets.bottom; //屏幕底部安全距离
			this.names = wx.getStorageSync('NAME-USER');
			if (options.enterpriseLabelIds) {
				this.enterpriseLabelIds = JSON.parse(
					decodeURIComponent(options.enterpriseLabelIds)
				);
			}
			this.showChain = options.showChain;
			this.companyID = options.id;
			this.clueId = options.clueId;
			if (options.iconTypeid) {
				this.iconTypeid = options.iconTypeid || 0;
			}
			this.name = options.enterpriseName;
			if (options.clueSource) {
				this.clueSource = options.clueSource;
			}
			// #endif
			if (this.clueSource == 2) {
				this.getDetailClue(this.clueId);
			} else {
				this.getdetail(this.companyID);
			}

			if (this.name) {
				uni.setNavigationBarTitle({
					title: this.name
				});
			}
		},
		computed: {
			currentProductNames() {
				return this.enterpriseList?.chainDTOS[this.selectedTagIndex]?.chainNodeList[this.selectedTagIndex2]
					?.productNames || [];
			}
		},
		watch: {
			selectedTagIndex() {
				this.updateProductNamesLength();
			},
			selectedTagIndex2() {
				this.updateProductNamesLength();
			},
			currentProductNames(newVal) {
				this.productNamesLength = newVal.length;
			}
		},
		methods: {
			requestOrgCode() {
				this.$api.ownList({}).then((res) => {
					this.orgName = res.result.selected?.orgName;
					this.isDefault = res.result.selected?.isDefault;
					uni.setStorageSync('orgCode', res.result.selected?.orgCode)
					uni.setStorageSync('orgName', res.result.selected?.orgName)
					uni.setStorageSync('isDefault', res.result.selected?.isDefault)
				})
			},
			getWidthClass() {
				return (this.enterpriseList.historyRecommend &&
					(this.IdentityType == 1 &&
						!this.isDefault &&
						this.orgName)) ? 'narrow-width' : 'wide-width';
			},
			Intention(isInvestClue) {
				if (isInvestClue) {
					return;
				}
				let data = {
					uniCode: this.enterpriseList.unifiedSocialCreditCode,
					clueSource: 1
				};
				this.$api.addEnterpriseAPI({
					data,
					method: 'post'
				}).then(res => {
					if (res.code == 'SUCCESS') {
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						});
						this.operatePopup(true);
						this.$refs.bringInto.opens();
						this.getdetail(this.enterpriseList.id);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						});
					}
				});
			},
			changAttention(isCollect) {
				let data = {
					enterpriseUniCode: this.enterpriseList.unifiedSocialCreditCode,
					status: !isCollect
				};
				this.$api.collectionAPI_investment({
					data,
					method: 'post'
				}).then(res => {
					if (isCollect) {
						uni.showToast({
							title: '取消收藏成功！',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: '收藏成功！已添加至\'招商管理-我的收藏\'',
							icon: 'none'
						});
					}
					this.enterpriseList.isCollect = !this.enterpriseList.isCollect;
					//this.getdetail(this.enterpriseList.id);
				});
			},
			handleLongPress(data) {
				uni.setClipboardData({
					data: data,
					success: function() {
						console.log('success');
					}
				});
			},
			goDetail(item) {
				let data = {
					enterpriseId: this.enterpriseList.id,
					enterpriseName: this.enterpriseList.enterpriseName,
					uniCode: this.enterpriseList.unifiedSocialCreditCode,
					recommendRegionCode: this.enterpriseList.recommendRegionCode || ''
				};
				this.$api.reportGenerateAPI({
					data,
					method: 'post'
				}).then(res => {
					// #ifndef MP-WEIXIN
					window?.ReactNativeWebView?.postMessage(
						JSON.stringify({
							type: 'changePath',
							value: {
								url: 'previewPdf',
								name: '报告预览',
								params: {
									reportId: res.result,
									type: 'pdf',
									enterpriseName: this.enterpriseList.enterpriseName
								}
							},
							path: 'webViewPage'
						})
					);
					// #endif
					// #ifndef H5
					uni.navigateTo({
						url: `/pages/excelView/index?id=${res.result}&type=pdf&enterpriseName=${this.enterpriseList.enterpriseName}`
					});
					// #endif
				});
			},
			RetractionExpansion(index) {
				this.businessDataList[index].Expand = !this.businessDataList[index].Expand;
			},
			goBack() {
				/* window?.ReactNativeWebView?.postMessage(
				  JSON.stringify({
				    type: "showMenue",
				    value: "",
				    path: "",
				  })
				);
				this.showHeader = false; */
				uni.navigateBack({
					delta: 1
				});
			},
			operatePopup(show) {
				this.popupShow = show;
			},
			getTotalStringLength(strings) {
				let num = 0;
				strings.forEach(i => {
					num += i.length;
				});
				return num;
			},
			// 统一更新长度
			updateProductNamesLength() {
				this.$nextTick(() => {
					this.showString = false;
					const names = this.currentProductNames;
					this.productNamesLength = names?.length || 0;
					let num = this.getTotalStringLength(names);
					this.StringTotal = num;
				});
			},
			// 展开与关闭
			more() {
				if (this.isshow) {
					this.showTag = this.enterpriseList.enterpriseLabelNames.slice(0, 4);
					this.isshow = !this.isshow;
				} else {
					this.showTag = this.enterpriseList.enterpriseLabelNames;
					this.isshow = !this.isshow;
				}
			},
			// 切换产业画像
			selectTag(index) {
				this.selectedTagIndex = index;
			},
			pitchsecond(index) {
				this.selectedTagIndex2 = index;
			},
			scrolltolowerFn() {
				if (this.pages > this.pageNum) {
					this.pageNum++;
					this.getBusinessDataList()
				}
			},
			getBusinessDataList(uniCode) {
				if (this.isLoading) {
					return;
				}
				this.isLoading = true;
				this.$api.enterpriseEventListAPI({
					data: {
						uniCode,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
					},
					method: 'get'
				}).then((res) => {
					if (res.result) {
						this.businessDataList = this.businessDataList.concat(res.result.records.map(it => {
							return {
								eventContent: it.eventContent,
								eventTitle: it.eventTitle,
								publishDate: it.publishDate,
								Expand: false,
								showEventContent: it?.eventContent?.slice(0, 38) || ''
							};
						}))
						this.pages = res.result.pages > 10 ? 10 : res.result.pages
					}
				}).finally(() => {
					this.isLoading = false;
				})
			},
			// 获取企业详情
			async getdetail(id) {
				let data = {
					enterpriseId: id
					//enterpriseId: '734425',
					//chainId: this.showChain,
					//enterpriseLabelIds: this.enterpriseLabelIds || []
				};
				if (this.showChain && this.showChain != 'undefined') {
					data.chainId = this.showChain;
				} else {
					await this.$api.choosechainAPI({
						method: 'get'
					}).then((obj) => {
						data.chainId = obj.result.chain2Id;
						this.showChain = obj.result.chain2Id;
					});
				}
				this.$api
					.enterprisedetail({
						data,
						method: 'GET'
					})
					.then((res) => {
						this.enterpriseList = res.result;
						this.getBusinessDataList(this.enterpriseList.unifiedSocialCreditCode)
						if (this.enterpriseList.chainDTOS) {
							this.enterpriseList.chainDTOS = this.enterpriseList.chainDTOS.filter(
								(chain) => chain.chainId == this.showChain
							);
						} else {
							this.enterpriseList.chainDTOS = [];
						}
						this.updateProductNamesLength();
						if (this.enterpriseList.enterpriseLabelNames) {
							this.showTag = this.enterpriseList.enterpriseLabelNames.slice(0, 4);
						}
						this.show = true;
					});
			},
			// 获取意向企业详情
			async getDetailClue(id) {
				let data = {
					clueId: id
				};
				if (this.showChain && this.showChain != 'undefined') {
					data.chainId = this.showChain;
				} else {
					await this.$api.choosechainAPI({
						method: 'get'
					}).then((obj) => {
						data.chainId = obj.result.chain2Id;
						this.showChain = obj.result.chain2Id;
					});
				}
				this.$api
					.getInvestEnterpriseDetailAPI_investment({
						data,
						method: 'GET'
					})
					.then((res) => {
						this.enterpriseList = res.result;
						this.getBusinessDataList(this.enterpriseList.unifiedSocialCreditCode)
						if (this.enterpriseList.chainDTOS) {
							this.enterpriseList.chainDTOS = this.enterpriseList.chainDTOS.filter(
								(chain) => chain.chainId == this.showChain
							);
						} else {
							this.enterpriseList.chainDTOS = [];
						}
						this.updateProductNamesLength();
						if (this.enterpriseList.enterpriseLabelNames) {
							this.showTag = this.enterpriseList.enterpriseLabelNames.slice(0, 4);
						}
						this.show = true;
					});
			},
			showMore() {
				if (this.pages > this.pageNum) {
					this.pageNum++;
					this.getBusinessDataList(this.enterpriseList.unifiedSocialCreditCode)
				}
			}
		}
	};
</script>

<style lang="scss">
	.attention {
		width: 40rpx;
		height: 40rpx;
		min-width: 40rpx;
		min-height: 40rpx;
		margin-top: 32rpx;
		margin-left: 72rpx;
		//margin-right: 24rpx;
	}

	/* #ifndef MP-WEIXIN */
	body,
	uni-page-body {
		background-color: transparent !important;
	}

	/* #endif */
	.subtitle {
		font-weight: 600;
		font-size: 30rpx;
		color: #1d2129;
	}

	.bottomBtnBox {
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		height: auto;
		width: 100%;
		background: #FFFFFF;
		padding: 16rpx 24rpx;
		padding-bottom: 46rpx;
		box-sizing: border-box;
		display: flex;
		z-index: 99;

		.narrow-width {
			width: 340rpx;
		}

		.wide-width {
			width: 654rpx;
		}
	}

	.strategyBtn {
		width: 340rpx;
		height: 80rpx;
		border-radius: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #FFFFFF;
		font-size: 28rpx;
		font-weight: 500;
		border: 2rpx solid #2D6BFF;
		color: #2D6BFF;
		z-index: 200;
	}

	.hasbeenIncluded {
		width: 340rpx;
		height: 80rpx;
		border-radius: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #F6F6F6;
		font-size: 28rpx;
		border: 1px solid #B7BFC7;
		font-weight: normal;
		color: #909599;
		z-index: 200;
	}

	.noIncluded {
		width: 340rpx;
		height: 80rpx;
		border-radius: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(156deg, #AEC6FF -28%, #1F61FF 89%), linear-gradient(90deg, #FF9E45 -7%, #FF5C00 100%), linear-gradient(156deg, #AEC6FF -28%, #1F61FF 89%), #3370FF;
		font-size: 28rpx;
		border: 1px solid #B7BFC7;
		font-weight: normal;
		color: #FFFFFF;
		z-index: 200;
	}

	.historyRecommendBox {
		width: 128rpx;
		height: 48rpx;
		background: #3370FF;
		border-radius: 0 20rpx 0 20rpx;
		font-weight: 400;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFFFFF;
		position: absolute;
		top: 0;
		right: 0;
	}

	.historyRecommendBox2 {
		width: 96rpx;
		height: 40rpx;
		padding: 0 12rpx;
		font-size: 22rpx;
		border-radius: 4rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		margin-left: 1rpx;
		justify-content: center;
		border: 1rpx solid #3370FF;
		background-color: #EAF0FF;
		color: #3370FF;
	}


	.limitationNameList {
		display: flex;
		flex-wrap: wrap;
		height: 120rpx;
		overflow: hidden;
	}

	.nameList {
		display: flex;
		flex-wrap: wrap;
	}

	.parent-container {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.showIcon,
	.showIcons {
		width: 40rpx;
		height: 40rpx;
		text-align: center;
	}

	.showIcon {
		transform: rotate(-90deg);
	}

	.showIcons {
		transform: rotate(90deg);
	}

	.nameItem {
		//width: 128rpx;
		height: auto;
		background: #eef1f5;
		border-radius: 22rpx 22rpx 22rpx 22rpx;
		font-weight: 400;
		font-size: 24rpx;
		padding: 2rpx 16rpx;
		color: #3f4a59;
		line-height: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		margin-bottom: 20rpx;
	}

	.introduction {
		font-size: 24rpx;
		color: #767676;
		font-weight: 400;
		line-height: 40rpx;

	}

	.enterpriceDetail {
		position: absolute;
		z-index: 60;
		width: 750rpx;
		/* #ifdef MP-WEIXIN */
		top: 140rpx;
		/* #endif */
		/* #ifdef H5 */
		margin-top: 20rpx;
		top: 100rpx;
		/* #endif */
		margin-top: 80rpx;
		height: 90vh;
		padding-bottom: 160rpx;
		margin-bottom: 160px;
		background-color: #f5f8fb;
	}

	.search-tab {
		background: #fff;
		height: 80rpx;
		display: flex;
		justify-content: space-around;
		padding: 0 60rpx;
		width: 100%;
		box-sizing: border-box;
		position: fixed;
		z-index: 40;
		top: 0;
		left: 0;

		&-list {
			font-size: 28rpx;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #1f2228;
			line-height: 33rpx;
			line-height: 40rpx;
			padding-top: 20rpx;
			text-align: center;

			&.on {
				position: relative;

				&::before {
					content: "";
					width: 64rpx;
					height: 8rpx;
					background: #417fff;
					border-radius: 8rpx 8rpx 8rpx 8rpx;
					opacity: 1;
					position: absolute;
					left: 50%;
					top: 72rpx;
					transform: translateX(-50%);
				}
			}
		}
	}

	.noindustry {
		background-color: #fff;
		width: 92%;
		margin-left: 4%;
		height: 150rpx;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;

		.img-nodata {
			width: 500rpx;
			height: 396rpx;
		}
	}

	.operation {
		width: 752rpx;
		height: 112rpx;
		background: #ffffff;
		position: fixed;
		display: flex;
		z-index: 9;
		justify-content: space-around;
		bottom: 0;
		background-color: #fff;

		.butlogout {
			background-color: #427fff;
			width: 700rpx;
			height: 80rpx;
			margin-top: 20rpx;
			background: #417fff;
			color: #fff;
			font-size: 30rpx;
		}

		.assign {
			width: 200rpx;
			height: 64rpx;
			margin-top: 20rpx;
			background: #ffffff;
			font-size: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			border: 2rpx solid #c9cdd4;
		}
	}

	.aaass {
		display: inline-block;
		width: 100px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.custom-tag {
		background-color: #fff2e6;
		border-color: #fff2e6;
		color: #ff7d00;
		font-weight: 700;
		margin-right: 5rpx;
		height: 52rpx;
		border-radius: 4rpx;
		margin-bottom: 12rpx;

		.tag-text {
			font-size: 24rpx;

			.gd-te {
				padding-left: 12rpx;
				padding-top: 4rpx;
			}

			.gd-icon {
				padding-right: 12rpx;
				padding-top: 8rpx;
			}
		}
	}

	.line {
		margin-left: 5%;
		width: 90%;
		height: 1rpx;
		background-color: #f5f5f5;
	}

	.firm {
		background-color: #fff;
		width: 92%;
		//height: 220rpx;
		margin-left: 4%;
		height: auto;
		//margin-top: 60rpx;
		display: flex;
		border-radius: 20rpx 20rpx 0 0;
		position: relative;

		.iconImg {
			width: 48rpx;
			height: 48rpx;
			padding: 26rpx 22rpx;
		}

		.texts {
			.userNname {
				display: flex;

				.enterpriseName {
					display: flex;
					margin-top: 32rpx;
					font-weight: 600;
					line-height: 40rpx;
					color: #1d2129;
					font-size: 34rpx;
					width: 460rpx;
					margin-bottom: 15rpx;
				}

				.focus {
					width: 100rpx;
					height: 52rpx;
					background: #417fff;
					border-radius: 6rpx;
					font-size: 26rpx;
					margin-top: 36rpx;
					color: #fff;
					margin-left: 10rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.isfocus {
					width: 100rpx;
					height: 52rpx;
					background: #c8ccd5;
					border-radius: 6rpx;
					font-size: 26rpx;
					margin-top: 36rpx;
					color: #fff;
					margin-left: 10rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.enterpriseLabeltag {
				display: flex;
				width: 570rpx;
				flex-wrap: wrap;
				margin-top: 5rpx;

				.tagBox {
					margin-left: 5rpx;
					width: auto;
					height: 46rpx;
					padding: 0 12rpx;
					font-size: 22rpx;
					background-color: #fff2e6;
					//border-color: #fff2e6;
					border-radius: 4rpx;
					color: #FF7D00;
					font-weight: 500;
					display: flex;
					align-items: center;
				}

				.enterpriseLabel {
					margin-right: 5rpx;
					margin-bottom: 12rpx;
				}
			}

			.recommend {
				display: flex;
				font-size: 26rpx;
				width: 570rpx;
				font-weight: 400;
				color: #4e5969;
				margin-bottom: 8rpx;
				align-items: flex-start;
			}
		}
	}

	.tag-p {
		padding: 20rpx;
		border-radius: 0 0 20rpx 20rpx;
		background-color: #fff;
		width: 92%;
		margin-left: 4%;
		height: auto;
		box-sizing: border-box;

		.nodeNamesTag {
			display: flex;
			margin-top: 10rpx;
			flex-wrap: wrap;
			margin-bottom: 30rpx;

			.tag-c {
				margin-right: 5rpx;
				margin-top: 14rpx;
				margin-right: 32rpx;
				margin-bottom: 12rpx;
			}
		}

		.tag-container {
			display: flex;
			white-space: nowrap;
			overflow-x: auto;
			flex-shrink: 0;

			.tag-title {
				width: 1000rpx;
				min-width: 200rpx;
				height: 56rpx;
				border: 4rpx solid #f7f7f7;
				//border-radius: 10rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.chainName {
					font-size: 28rpx;
				}
			}
		}

		.tag-it {
			//padding-left: 30rpx;
			//padding-bottom: 10rpx;
		}

		.tag-container::-webkit-scrollbar {
			width: 0;
			background-color: transparent;
		}
	}

	.portrayal {
		background-color: #fff;
		width: 92%;
		//height: 220rpx;
		margin-left: 4%;
		height: auto;
		//margin-top: 30rpx;
		border-radius: 20rpx;
		padding-top: 20rpx;
	}

	.chamberBox {
		background-color: #fff;
		height: auto;
		padding: 0 10px;
		// margin-left: 4%;
		// padding-top: 48rpx;
		// border-top: 2rpx solid #E5E6EB;

		.dynamicItem {

			padding-left: 44rpx;
			position: relative;

			&:not(:last-child) {
				border-left: 2rpx solid #E5E6EB;
			}

			.buleCircle {
				position: absolute;
				top: 0;
				left: -12rpx;
				width: 24rpx;
				height: 24rpx;
				min-width: 24rpx;
				min-height: 24rpx;
				background: #BEDAFF;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;

				.kernel {
					width: 12rpx;
					height: 12rpx;
					min-width: 12rpx;
					min-height: 12rpx;
					background: #3370FF;
					border-radius: 50%;
				}
			}

			.eventContent {
				font-weight: 400;
				font-size: 28rpx;
				color: #3F4A59;
				line-height: 40rpx;
				padding-bottom: 20px;
			}

			.publishDate {
				font-weight: 400;
				font-size: 26rpx;
				color: #86909C;
				line-height: 36rpx;
				margin: 16rpx 0rpx;
			}

			.title {
				font-weight: 500;
				font-size: 28rpx;
				color: #3F4A59;
				line-height: 40rpx;
			}
		}


		.chambertitle {
			font-weight: 600;
			font-size: 30rpx;
			color: #1d2129;
			margin-bottom: 32rpx;
		}

		.associationItem {
			width: 100%;
			height: auto;
			background: #F8FAFF;
			border-radius: 10px 10px 10px 10px;
			padding: 24rpx;
			box-sizing: border-box;
			margin-bottom: 20rpx;
			position: relative;

			.Singleline {
				display: flex;
				font-weight: 400;
				font-size: 26rpx;
				color: #7E8791;
				line-height: 44rpx;

				.chamberKey {

					width: auto;
					white-space: nowrap;
				}

				.chamberValue {
					width: auto;
				}
			}

			.indexs {
				width: 66rpx;
				height: 48rpx;
				background: #3370FF;
				border-radius: 0px 20rpx 0px 20rpx;
				position: absolute;
				top: 0;
				right: 0;
				font-weight: 500;
				font-size: 24rpx;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.titleBox {
				display: flex;
				align-items: center;
				padding-bottom: 10rpx;
				padding-right: 42rpx;
				box-sizing: border-box;

				.longString {
					width: 28rpx;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #CED4DB;
				}

				.us {
					width: 32rpx;
					height: 32rpx;
					min-width: 32rpx;
					min-height: 32rpx;
					margin-right: 4rpx;
					align-self: flex-start;
					margin-top: 4rpx;
				}

				.chamberTag {
					height: 36rpx;
					background: #FFF2E6;
					border-radius: 4rpx;
					font-weight: 500;
					font-size: 22rpx;
					color: #FF7D00;
					padding: 0 8rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}

	}

	.showMore {
		display: flex;
		justify-content: center;
		align-items: center;
		color: #3370FF;
		padding: 20rpx 0;

		.showMoreIcon {
			width: 24rpx;
			height: 24rpx;
		}
	}

	// .details {
	// background-color: #fff;
	// width: 92%;
	// //height: 220rpx;
	// //margin-bottom: 40rpx;
	// margin-left: 4%;
	// height: auto;
	// margin-top: 30rpx;
	// border-radius: 10rpx;

	.essentialinformation {
		display: flex;
	}

	.essentialinformations {
		display: flex;
	}

	.ones {
		display: flex;
		flex-direction: column;
		width: 100%;
		margin-top: 30rpx;
		margin-bottom: 30rpx;

		.name {
			font-size: 28rpx;
			margin-left: 15rpx;
		}

		.result {
			font-size: 28rpx;
			margin-left: 12rpx;
			color: #86909c;
			margin-top: 20rpx;
		}
	}

	.one {
		display: flex;
		flex-direction: column;
		width: 50%;
		margin-top: 30rpx;

		.name {
			font-size: 28rpx;
			margin-left: 15rpx;
		}

		.result {
			font-size: 28rpx;
			margin-left: 12rpx;
			color: #86909c;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			margin-top: 20rpx;
			display: inline-block;
			width: 280rpx;
		}
	}

	// }

	.yuan {
		background-color: #c9cdd4;
		content: "";
		position: relative;
		top: 24rpx;
		width: 8rpx;
		height: 8rpx;
		border-radius: 50%;
	}

	page {
		background-color: rgb(247, 247, 247);
	}

	.countryIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/country.png");
	}

	.phoneIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/phone.png");
	}

	.expandIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/expand.png");
	}

	.increaseIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/increase.png");
	}

	.headerBack {
		display: flex;
		justify-self: flex-start;
		align-items: center;
		padding: 0 35rpx 10px 35rpx;
		height: 57rpx;
		// margin-bottom: 10rpx;
	}

	.text {
		font-size: 34rpx;
		font-weight: 600;
		color: #1d2129;
		// line-height: 57rpx;
		width: 100%;
		text-align: center;
		padding-right: 40rpx;
	}

	.back {
		width: 57rpx;
		height: 57rpx;
		// margin-right: 5px;
		// margin-top: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/back.png");
	}

	.detials {
		//padding: 32rpx;
		//background: #FAFCFF;
		// background: #072c64;
		width: 92%;
		margin-left: 4%;

		.uni-collapse-item__wrap.is--transition {
			// height: auto !important;
			overflow: scroll;
			border-radius: 12rpx;
		}

		.uni-collapse-item {
			margin-bottom: 20rpx;
			border-radius: 6px;
			box-sizing: border-box;
			//background: #FFFFFF;
			box-shadow: 0px 4px 12px 0px rgba(191, 195, 203, 0.1);
			border-bottom: 0;

			.uni-collapse-item__title.uni-collapse-item-border {
				border-bottom: 0;
				background: #ffffff;
			}
		}

		.uni-collapse-item__title-img {
			width: 32rpx;
			height: 32rpx;
			margin-right: 20rpx;
		}

		.detialHeader {
			background: #f5f8fb;
			// background: #072c64;
		}

		// .uni-collapse-item {

		// }

		.uni-collapse-item__title-box {
			border-radius: 6px;
		}

		.content {
			white-space: pre-wrap;
			word-break: break-all;
			padding: 24rpx 24rpx 48rpx 24rpx;
		}
	}

	/* #ifdef H5 */
	::v-deep {
		.uni-collapse {
			background-color: transparent !important;
		}

		.detialHeader {
			background: #f5f8fb;
			// background: #072c64;
		}

		.uni-collapse-item__wrap.is--transition {
			// height: auto !important;
			overflow: scroll;
			border-radius: 12rpx;
		}

		.uni-collapse-item {
			margin-bottom: 20rpx;
			border-radius: 6px;
			box-sizing: border-box;
			//background: #FFFFFF;
			box-shadow: 0px 4px 12px 0px rgba(191, 195, 203, 0.1);
			border-bottom: 0;

			.uni-collapse-item__title.uni-collapse-item-border {
				border-bottom: 0;
				background: #ffffff;
			}
		}

		.uni-collapse-item__title-img {
			width: 32rpx;
			height: 32rpx;
			margin-right: 20rpx;
		}

		.uni-collapse-item__title-box {
			border-radius: 6px;
		}


	}

	/* #endif */
</style>