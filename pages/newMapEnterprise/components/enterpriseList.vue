<template>
	<!-- 企业列表 -->
	<view>
		<view :style="itemStyle(index)" class="firm" v-for="(item, index) in enterpriseLists" :key="index">
			<view style="display: flex" @click="detailpage(item)">
				<img :src="item.enterpriseIconLabelId == null
          ? listicon[0].icon
          : listicon[item.enterpriseIconLabelId].icon
          " alt="" class="iconImg" />
				<view class="texts">
					<view class="enterpriseNameBox">
						<view class="enterpriseName">
							<span>{{ item.enterpriseName }}</span>
							<span v-if="isWithinLastWeek(item.lastModifyDate)" class="upBgc">新</span>
							<!-- <image class="upBgc" src="/static/AboutAi/up.png"></image> -->
						</view>
						<image v-if="IdentityType == 1 && !isDefault && orgName"
							@click.stop="changAttention(item, item?.isCollect, index)" class="attention"
							:src="item?.isCollect ? '/static/AboutAi/attention.png' : '/static/AboutAi/noAttention.png'">
						</image>
					</view>
					<view v-if="item.enterpriseLabelNames?.length >= 1" class="enterpriseLabeltag">
						<!-- 重点企业标签 -->
						<view class="enterpriseLabel" v-if="item.enterpriseLabelNames">
							<view
								v-for="(it, ind) in item.enterpriseLabelNames.slice(0, getMaxLabelCount(item.enterpriseLabelNames))"
								:key="ind">
								<div class="tagBox">
									{{ it }}
								</div>
							</view>
							<div class="tagBox"
								v-if="item.enterpriseLabelNames.length > getMaxLabelCount(item.enterpriseLabelNames)">
								...
							</div>
						</view>
					</view>
					<view class="recommendIcon">
						<view class="expandIcon"></view>
						<view>
							快速成长指数<span style="margin-left: 8rpx;color: #34C759;">
								{{ (!item?.growthIndex || item?.growthIndex == '0.0') ? '-' : item?.growthIndex }}
							</span>
						</view>
					</view>
					<view class="recommendIcon">
						<view class="increaseIcon"></view>
						<view>
							扩张意愿指数
							<span style="margin-left: 8rpx;color: #FF9500;">
								{{ (!item?.expansionIndex || item?.expansionIndex == '0.0') ? '-' : item?.expansionIndex }}
							</span>
						</view>
					</view>
					<view class="recommendIcon">
						<view class="countryIcon"></view>
						<span>{{ item.province }}<span v-if="item.province != item.city">{{ item.city }}</span>{{ item.area
            }}</span>
					</view>

				</view>
			</view>
			<view v-if="item.historyRecommend || item.clueDealState!=null" class="footer">
				<uni-tooltip v-if="item.historyRecommend" :content="'推荐时间:' + item.historyRecommendDate"
					placement="top">
					<view class="histroy">
						<image class="footerBgc" src="https://static.idicc.cn/cdn/zhaoShang/历史推荐.svg"></image>
						历史推荐
					</view>
				</uni-tooltip>
				<uni-tooltip v-if="item.clueDealState!=null && item.clueDealState==0"
					:content="'跟进人:' + item.beAssignPerson" placement="top">
					<view class="pendding">
						<image class="footerBgc" src="https://static.idicc.cn/cdn/zhaoShang/跟进中.svg"></image>
						跟进中
					</view>
				</uni-tooltip>
				<view v-if="item.clueDealState!=null && item.clueDealState==2" class="error">
					<image class="footerBgc" src="https://static.idicc.cn/cdn/zhaoShang/error.svg"></image>
					签约失败
				</view>
				<view v-if="item.clueDealState!=null && item.clueDealState==1" class="success">
					<image class="footerBgc" src="https://static.idicc.cn/cdn/zhaoShang/success.svg"></image>
					签约成功
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		listicon,
		isWithinLastWeek,
		getQueryParam
	} from '@/utils/utils.js'
	export default {
		props: {
			Jurisdiction: {
				type: Boolean,
				default: false,
			},
			showChain: {
				type: String,
				default: "",
			},
			enterpriseLabelIds: {
				type: Array,
				default: () => [],
			},
			// 列表
			enterpriseList: {
				type: Array,
				default: () => [],
			}
		},
		data() {
			return {
				enterpriseLists: [],
				clientVersion: '', // APP版本号

				listicon,
				itId: "", //企业id
				clueId: "", //线索id
				itName: "",
				taskstate: "",
				IdentityType: uni.getStorageSync("userIdentityType"),
				orgName: uni.getStorageSync('orgName'),
				isDefault: uni.getStorageSync('isDefault'),
				Successmessage: {},
				assign: false, //"是否有指派权限"
			};
		},
		watch: {
			enterpriseList() {
				this.enterpriseLists = JSON.parse(JSON.stringify(this.enterpriseList));
			},
		},
		created() {
			this.enterpriseLists = JSON.parse(JSON.stringify(this.enterpriseList));
			// #ifdef H5
			// h5中获取参数
			if (getQueryParam("clientVersion")) {
				this.clientVersion = getQueryParam("clientVersion");
				uni.setStorageSync("clientVersion", this.clientVersion);
			}
			// #endif
		},

		methods: {
			isWithinLastWeek,
			changAttention(item, isCollect, index) {
				let data = {
					enterpriseUniCode: item.unifiedSocialCreditCode,
					status: !isCollect
				}
				this.$api.collectionAPI_investment({
					data,
					method: "post"
				}).then(res => {
					if (isCollect) {
						uni.showToast({
							title: '取消收藏成功！',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: "收藏成功！已添加至'招商管理-我的收藏'",
							icon: 'none'
						});
					}
					this.enterpriseLists[index].isCollect = !isCollect
				})
			},
			getMaxLabelCount(labels) {
				let totalChars = 0;
				let maxCount = 0;
				for (let i = 0; i < labels.length; i++) {
					totalChars += labels[i].length;
					if (totalChars > 20) break;
					maxCount = i + 1;
				}
				return maxCount;
			},
			itemStyle(index) {
				if (!this.Jurisdiction && index > 1) {
					return 'filter: blur(3px)'
				}
				return '';
			},
			eventfree() {
				return;
			},
			async detailpage(item) {
				if (!this.Jurisdiction) {
					return this.$emit('openJurisdictionPop')
				}
				// #ifndef MP-WEIXIN
				const clientVersion = this.clientVersion;
				if (clientVersion) {
					window?.ReactNativeWebView?.postMessage(
						JSON.stringify({
							type: "changePath",
							value: {
								id: item.enterpriseId || item.id,
								iconTypeid: item.enterpriseIconLabelId,
								enterpriseName: item.enterpriseName,
								enterpriseLabelIds: JSON.stringify(this.enterpriseLabelIds || []),
								clientVersion: clientVersion,
								showChain: this.showChain
							},
							path: "industryDetail",
						})
					);
				} else {
					uni.navigateTo({
						url: `/pages/newMapEnterprise/components/enterprise?id=${item.enterpriseId || item.id
            }&iconTypeid=${item.enterpriseIconLabelId}&showChain=${this.showChain
            }&enterpriseName=${item.enterpriseName
            }&enterpriseLabelIds=${JSON.stringify(this.enterpriseLabelIds || [])}`,
					});
				}
				// #endif
				// #ifndef H5
				uni.navigateTo({
					url: `/pages/newMapEnterprise/components/enterprise?id=${item.enterpriseId || item.id
          }&iconTypeid=${item.enterpriseIconLabelId}&showChain=${this.showChain
          }&enterpriseName=${item.enterpriseName
          }&enterpriseLabelIds=${JSON.stringify(this.enterpriseLabelIds || [])}`,
				});
				// #endif

			},
		},
	};
</script>

<style lang="scss">
	/* #ifndef MP-WEIXIN */
	body,
	uni-page-body {
		background-color: transparent !important;
	}

	/* #endif */
	.triangle {
		position: absolute;
		top: 0;
		right: 0;
		width: 0;
		height: 0;
		width: 72rpx;
		height: 72rpx;
	}

	.text {
		position: absolute;
		top: 16rpx;
		right: -16rpx;
		width: 0;
		height: 0;
		display: flex;
		width: 52rpx;
		transform: rotate(45deg);
		font-size: 22rpx;
		color: #ffffff;
	}

	.firm {
		background-color: #fff;
		width: 100%;
		//height: 220rpx;
		height: auto;
		//margin-top: 30rpx;
		//position: relative;
		margin-bottom: 30rpx;
		//display: flex;
		box-shadow: 0rpx 8rpx 24rpx 0rpx #eef1f8;
		border-radius: 20rpx 20rpx 20rpx 20rpx;

		.dispose {
			width: 100%;
			height: 88rpx;
			background: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 10rpx;
			border-radius: 0rpx 0rpx 12rpx 12rpx;
			border-top: 2rpx solid #e5e6eb;

			.task {
				color: #417fff;
				font-size: 32rpx;
			}
		}

		.iconImg {
			width: 48rpx;
			height: 48rpx;
			min-width: 48rpx;
			min-height: 48rpx;
			padding: 25rpx 15rpx;
		}

		.texts {
			width: 100%;

			.enterpriseNameBox {
				width: 100%;
				display: flex;
				justify-content: space-between;

				.enterpriseName {
					display: flex;
					margin-top: 30rpx;
					margin-bottom: 6rpx;
					width: calc(100% - 60rpx);
					align-items: center;
				}
			}


			.attention {
				width: 40rpx;
				height: 40rpx;
				min-width: 40rpx;
				min-height: 40rpx;
				margin-top: 26rpx;
				margin-right: 24rpx;
			}

			.state {
				//width: 100rpx;
				//height: 52rpx;
				font-size: 26rpx;
				//margin-top: 38rpx;
				margin-top: 30rpx;
				margin-left: 10rpx;
				display: flex;

				.red {
					background: #f53f3f;
					content: "";
					position: relative;
					width: 16rpx;
					height: 16rpx;
					right: 10rpx;
					top: 10rpx;
					border-radius: 50%;
				}

				.blue {
					background: #417fff;
					content: "";
					position: relative;
					width: 16rpx;
					height: 16rpx;
					right: 10rpx;
					top: 10rpx;
					border-radius: 50%;
				}

				.green {
					background: #10aa38;
					content: "";
					position: relative;
					width: 16rpx;
					right: 10rpx;
					top: 10rpx;
					height: 16rpx;
					border-radius: 50%;
				}

				.gray {
					background: #c9cdd4;
					content: "";
					position: relative;
					width: 16rpx;
					right: 10rpx;
					top: 10rpx;
					height: 16rpx;
					border-radius: 50%;
				}
			}

			.enterpriseLabeltag {
				width: 600rpx;
				height: 55rpx;
				margin-top: 10rpx;
				display: flex;
				overflow-x: auto;
				white-space: nowrap;

				.enterpriseLabel {
					display: flex;
					//margin-right: 5rpx;
					flex-shrink: 0;

					.tagBox {
						margin-left: 5rpx;
						width: auto;
						height: 46rpx;
						padding: 0 12rpx;
						font-size: 22rpx;
						background-color: #fff2e6;
						//border-color: #fff2e6;
						border-radius: 4rpx;
						color: #FF7D00;
						font-weight: 500;
						display: flex;
						align-items: center;
					}
				}
			}

			.enterpriseLabeltag::-webkit-scrollbar {
				width: 0;
				height: 0;
				background-color: transparent;
			}

			.recommend,
			.recommendIcon {
				font-size: 26rpx;
				margin-top: 12rpx;
				margin-bottom: 20rpx;

				.btn-box {
					/* 					display: flex;
					justify-content: space-between;
					margin-top: 12rpx;
					margin-right: 22rpx;
					margin-bottom: 20rpx; */
					display: flex;
					justify-content: flex-end;
					margin-top: 12rpx;
					margin-right: 22rpx;
					margin-bottom: 20rpx;

					.buttom {
						width: 180rpx;
						height: 64rpx;
						background: #ffffff;
						margin-right: 6rpx;
						font-size: 14px;
						border-radius: 8rpx 8rpx 8rpx 8rpx;
						opacity: 1;
						border: 2rpx solid #c9cdd4;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.buttom2 {
						width: 180rpx;
						height: 64rpx;
						background: #ffffff;
						margin-right: 6rpx;
						font-size: 14px;
						border-radius: 8rpx 8rpx 8rpx 8rpx;
						opacity: 1;
						color: #86909c;
						border: 2rpx solid #c9cdd4;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}
		}
	}

	.recommendIcon {
		display: flex;
		flex-wrap: nowrap;
		font-weight: 400;
		color: #4e5969;
	}

	.expandIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/expand.png");
	}

	.increaseIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/increase.png");
	}

	.countryIcon {
		width: 32rpx;
		height: 32rpx;
		min-width: 32rpx;
		min-height: 32rpx;
		margin-right: 5px;
		background: center / contain no-repeat url("https://static.idicc.cn/cdn/aiChat/country.png");
	}

	.upBgc {
		padding: 2rpx 16rpx;
		background: #EE0A24;
		height: 29rpx;
		border-radius: 24rpx;
		margin-left: 10rpx;
		color: #fff;
		font-size: 20rpx;
	}

	.footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		color: #2551B9;
		font-size: 26rpx;
		margin: 10rpx 20rpx;
		padding: 35rpx 20rpx;
		border-top: 1px solid #E6EBF0;

		.histroy,
		.pendding,
		.fail,
		.success,
		.error {
			display: flex;
			align-items: center;
			justify-content: center;
			min-width: 50%;
		}

		.success {
			color: #2551B9;
		}

		.error {
			color: #EA0000;
		}

		// .pendding {}

		// .fail {}
		.footerBgc {
			margin-right: 10rpx;
			width: 32rpx;
			height: 32rpx;
		}
	}
</style>