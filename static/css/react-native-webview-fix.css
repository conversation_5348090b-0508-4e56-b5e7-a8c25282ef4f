/* React Native WebView 兼容性修复样式 */

/* 针对H5环境下的React Native WebView滚动问题修复 */
/* #ifdef H5 */

/* 修复弹窗内滚动区域的触摸滚动 */
.uni-popup .region-list,
.uni-popup .filtrate {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  /* 确保在iOS WebView中能正常滚动 */
  transform: translateZ(0);
  /* 启用硬件加速 */
  will-change: scroll-position;
}

/* 修复弹窗遮罩层的触摸事件 */
.uni-popup .uni-popup__mask {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 修复弹窗内容区域的滚动 */
.uni-popup .uni-popup__wrapper {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 修复列表项的触摸反馈 */
.region-list .item {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 修复筛选项的触摸反馈 */
.filtrateList .filtrateItem,
.filtrateList .filtrateItems {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 修复按钮的触摸反馈 */
.btnbox .clear,
.btnbox .affirm,
.btnBox .canle,
.btnBox .submit {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 修复主页面滚动区域 */
.listBox {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  transform: translateZ(0);
  will-change: scroll-position;
}

/* 防止页面在弹窗打开时滚动 */
body.popup-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* 修复在React Native WebView中的滚动性能 */
* {
  -webkit-overflow-scrolling: touch;
}

/* 修复弹窗动画在WebView中的性能 */
.uni-transition {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* 修复滚动条样式 */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* 修复触摸滚动的惯性 */
.region-list,
.filtrate {
  -webkit-overflow-scrolling: touch;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/* #endif */

/* 通用修复 - 适用于所有平台 */

/* 修复弹窗层级问题 */
.uni-popup {
  z-index: 999999 !important;
}

/* 修复弹窗内容的最大高度 */
.uni-popup .uni-popup__wrapper {
  max-height: 80vh;
  overflow: hidden;
}

/* 修复滚动区域的边界 */
.region-list,
.filtrate {
  border-radius: 8rpx;
  overflow: hidden;
}

/* 修复列表项的间距 */
.region-list .item {
  border-bottom: 1rpx solid #f5f5f5;
}

.region-list .item:last-child {
  border-bottom: none;
}

/* 修复按钮的活跃状态 */
.btnbox .clear:active,
.btnbox .affirm:active,
.btnBox .canle:active,
.btnBox .submit:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 修复选中状态的视觉反馈 */
.region-list .item.on {
  background-color: #f0f8ff !important;
  border-color: #417FFF !important;
}

.filtrateList .filtrateItems {
  background-color: #3370FF !important;
  color: #FFFFFF !important;
}
