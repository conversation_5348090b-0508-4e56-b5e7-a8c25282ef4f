# React Native WebView 下拉窗口滑动兼容性修复方案

## 问题描述

乡贤企业页面在嵌入React Native App后，三个下拉窗口（高管籍贯、企业注册地、更多查询）弹出后无法滑动。

## 问题原因

1. **WebView滚动冲突**：React Native WebView与uni-app的滚动事件存在冲突
2. **触摸事件传递问题**：弹窗内的触摸事件被WebView容器拦截
3. **CSS滚动属性不兼容**：部分CSS滚动属性在WebView环境中不生效
4. **层级和定位问题**：弹窗的z-index和定位在WebView中可能被重置

## 解决方案

### 1. 修改uni-popup组件配置

**文件：** `pages/industry/xiangxian.vue`

```vue
<!-- 添加 :safe-area="false" 属性 -->
<uni-popup ref="clueModelPop" background-color="#fff" :safe-area="false">
<uni-popup ref="popup2" background-color="#fff" :safe-area="false">
```

**作用：** 禁用安全区域适配，避免在WebView中的定位问题。

### 2. 添加CSS兼容性样式

**文件：** `static/css/react-native-webview-fix.css`

主要修复内容：
- 添加 `-webkit-overflow-scrolling: touch` 启用iOS平滑滚动
- 使用 `transform: translateZ(0)` 启用硬件加速
- 修复触摸事件和用户选择问题
- 优化弹窗层级和动画性能

### 3. 添加滚动控制方法

**文件：** `pages/industry/xiangxian.vue`、`components/360screen/moreScreen.vue`

```javascript
// React Native WebView 兼容性处理方法
disableBodyScroll() {
  const isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
  if (isInApp) {
    // 禁用body滚动
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.height = '100%';
    
    // 向React Native发送消息，禁用WebView滚动
    window?.ReactNativeWebView?.postMessage(
      JSON.stringify({
        type: "disableScroll",
        value: true
      })
    );
  }
},

enableBodyScroll() {
  const isInApp = navigator?.userAgent.indexOf("airApp/1.0.0") > -1;
  if (isInApp) {
    // 恢复body滚动
    document.body.style.overflow = '';
    document.body.style.position = '';
    document.body.style.width = '';
    document.body.style.height = '';
    
    // 向React Native发送消息，启用WebView滚动
    window?.ReactNativeWebView?.postMessage(
      JSON.stringify({
        type: "enableScroll",
        value: true
      })
    );
  }
}
```

### 4. 在弹窗生命周期中调用滚动控制

```javascript
// 打开弹窗时禁用背景滚动
screenFn(type) {
  // React Native WebView 兼容性处理
  // #ifdef H5
  this.disableBodyScroll();
  // #endif
  
  // 打开对应弹窗...
}

// 关闭弹窗时恢复背景滚动
affirm1(data) {
  this.$refs.popup2.close()
  
  // React Native WebView 兼容性处理 - 恢复滚动
  // #ifdef H5
  this.enableBodyScroll();
  // #endif
  
  // 其他处理...
}
```

### 5. 优化滚动区域CSS

**文件：** `components/areaList.vue`、`components/mapList.vue`、`components/360screen/moreScreen.vue`

```css
&-list {
  max-height: 50vh;
  overflow: auto;
  /* React Native WebView 兼容性处理 */
  -webkit-overflow-scrolling: touch;
}

.filtrate {
  overflow-y: scroll;
  max-height: 900rpx;
  /* React Native WebView 兼容性处理 */
  -webkit-overflow-scrolling: touch;
}
```

## 修改文件清单

1. **pages/industry/xiangxian.vue**
   - 添加 `:safe-area="false"` 属性
   - 添加滚动控制方法
   - 在弹窗生命周期中调用滚动控制
   - 引入兼容性CSS文件

2. **components/areaList.vue**
   - 添加 `-webkit-overflow-scrolling: touch` 样式

3. **components/mapList.vue**
   - 添加 `-webkit-overflow-scrolling: touch` 样式

4. **components/360screen/moreScreen.vue**
   - 添加 `-webkit-overflow-scrolling: touch` 样式
   - 添加滚动控制方法
   - 在弹窗生命周期中调用滚动控制

5. **static/css/react-native-webview-fix.css** (新建)
   - 全局兼容性样式修复

6. **docs/react-native-webview-fix.md** (新建)
   - 修复方案说明文档

## 测试建议

1. **在React Native App中测试**：
   - 打开乡贤企业页面
   - 分别点击三个下拉选项
   - 验证弹窗内容可以正常滚动
   - 验证选择功能正常工作

2. **在普通H5环境中测试**：
   - 确保修改不影响原有功能
   - 验证弹窗正常显示和交互

3. **在微信小程序中测试**：
   - 确保条件编译正确，不影响小程序功能

## 注意事项

1. 所有滚动控制方法都使用了条件编译 `#ifdef H5`，只在H5环境中生效
2. 通过检测 `navigator.userAgent` 中的 "airApp/1.0.0" 来判断是否在React Native WebView环境中
3. 向React Native发送消息使用了可选链操作符，确保在非WebView环境中不会报错
4. CSS样式使用了条件编译，确保只在需要的平台生效

## 后续优化建议

1. 可以考虑将滚动控制方法提取为全局mixin，方便其他页面复用
2. 可以监听React Native的消息回调，确保滚动控制生效
3. 可以添加更多的用户体验优化，如加载状态、错误处理等
