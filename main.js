import App from './App';

import api from '@/api/index';
import mxin from '@/utils/commonShare.js';
import itrace from '@/utils/miniapp-reporter.js';
// #ifndef VUE3
import Vue from 'vue';
Vue.config.productionTip = false;
App({
	onLounch() {},
	onShow() {},
	onHide() {},
	onError(error) {}
});
App.mpType = 'app';
const app = new Vue({
	...App
});

//itrace.wrapApp(app);
app.$mount();
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue';
export function createApp() {
	//let wrapApp = itrace.wrapApp(App);
	//const app = createSSRApp(wrapApp);
	const app = createSSRApp(App);
	app.mixin(mxin);
	app.use(api, {
		app
	});

	return {
		app
	};
}
// #endif