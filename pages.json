{
  "pages": [
    {
      "path": "pages/repository/capacity",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/property",
      "style": {
        "navigationBarTitleText": "产研",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/capacitys",
      "style": {
        "navigationBarTitleText": "产发",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/information/index",
      "style": {
        "navigationBarTitleText": "消息",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/information",
      "style": {
        "navigationBarTitleText": "数据产品",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/informationDel",
      "style": {
        "navigationBarTitleText": "数据产品详情",
        "navigationBarBackgroundColor": "#dde6ff",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/attract",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/enterpriseService",
      "style": {
        "navigationBarTitleText": "哒达企服",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/PdfLoading",
      "style": {
        "navigationBarTitleText": "pdf预览",
        "navigationBarBackgroundColor": "#FAFCFF"
      }
    },
    {
      "path": "pages/newMapEnterprise/index2",
      "style": {
        "navigationBarTitleText": "360",
        "navigationBarBackgroundColor": "#FAFCFF",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/newMapEnterprise/components/enterprise",
      "style": {
        "navigationBarTitleText": "企业详情",
        "navigationBarBackgroundColor": "#FAFCFF",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/chat/index",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#dde6ff"
      }
    },
    {
      "path": "components/feedbackDel",
      "style": {
        "navigationBarTitleText": "反馈详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/attractLiist/feedbackList",
      "style": {
        "navigationBarTitleText": "反馈列表",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/transition/index",
      "style": {
        "navigationBarTitleText": "欢迎页",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/Goofficial",
      "style": {
        "navigationBarTitleText": "公众号",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/memberarticle",
      "style": {
        "navigationBarTitleText": "会员中心",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/index",
      "style": {
        "navigationBarTitleText": "个人中心",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/Coursedetails",
      "style": {
        "navigationBarTitleText": "课程详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/attractDel",
      "style": {
        "navigationBarTitleText": "招商详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/ApplicationDel",
      "style": {
        "navigationBarTitleText": "申请详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/demandDel",
      "style": {
        "navigationBarTitleText": "需求详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/WithdrawalRecord",
      "style": {
        "navigationBarTitleText": "提现记录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/commissionList",
      "style": {
        "navigationBarTitleText": "佣金明细",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "components/withdraw",
      "style": {
        "navigationBarTitleText": "提现",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/history/index",
      "style": {
        "navigationBarTitleText": "历史记录",
        "navigationBarBackgroundColor": "#dde6ff"
      }
    },
    {
      "path": "pages/pdfShow/index",
      "style": {
        "navigationBarTitleText": "预览",
        "navigationBarBackgroundColor": "#dde6ff"
      }
    },
    {
      "path": "pages/user/userSet",
      "style": {
        "navigationBarTitleText": "设置",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/changename",
      "style": {
        "navigationBarTitleText": "编辑个人信息",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/perfectionInfo",
      "style": {
        "navigationBarTitleText": "信息完善",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/perfectionInfo2",
      "style": {
        "navigationBarTitleText": "信息完善",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/repository/BillingRule",
      "style": {
        "navigationBarTitleText": "计费规则",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/MyQRCode",
      "style": {
        "navigationBarTitleText": "我的二维码",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/MemberCenter",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#FFE6C0"
      }
    },
    {
      "path": "pages/user/payment",
      "style": {
        "navigationBarTitleText": "会员中心支付",
        "navigationBarBackgroundColor": "#f8e0be"
      }
    },
    {
      "path": "pages/user/AboutAi",
      "style": {
        "navigationBarTitleText": "关于小AIR",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationBarBackgroundColor": "#FAFCFF"
      }
    },
    {
      "path": "pages/login/components/passwordLogin",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationBarBackgroundColor": "#FAFCFF"
      }
    },
    {
      "path": "pages/login/components/verificationcodeLogin",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationBarBackgroundColor": "#FAFCFF"
      }
    },
    {
      "path": "pages/login/components/scanCodeLogin",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/components/agreement",
      "style": {
        "navigationBarTitleText": "用户协议"
      }
    },
    {
      "path": "pages/user/association",
      "style": {
        "navigationBarTitleText": "加入社群",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/excelShow/index",
      "style": {
        "navigationBarTitleText": "表格下载",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/excelView/index",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#dde6ff"
      }
    },
    {
      "path": "pages/user/team",
      "style": {
        "navigationBarTitleText": "团队管理",
        "navigationBarBackgroundColor": "#dde6ff"
      }
    },
    {
      "path": "pages/webView/identity",
      "style": {
        "navigationBarTitleText": "身份切换",
        "navigationBarBackgroundColor": "#dde6ff"
      }
    },
    {
      "path": "pages/webView/InformationDetails",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#FFFFFF"
      }
    },
    {
      "path": "pages/search/index",
      "style": {
        "navigationBarTitleText": "搜索",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/strategy/index",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/deriveExcel/index",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user/IdentityStatement",
      "style": {
        "navigationBarTitleText": "哒达招商",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/industry/xiangxian",
      "style": {
        "navigationBarTitleText": "乡贤企业",
        "navigationBarBackgroundColor": "#dde6ff",
        "disableScroll": true,
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "attractionManage/",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "招商管理",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/addCompany",
          "style": {
            "navigationBarTitleText": "添加企业",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/targetDetail",
          "style": {
            "navigationBarTitleText": "企业详情",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/addWeekly",
          "style": {
            "navigationBarTitleText": "周报详情",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "goToSea/",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "哒达招商",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "historyStrategy",
          "style": {
            "navigationBarTitleText": "历史报告",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/enterpriseDel",
          "style": {
            "navigationBarTitleText": "企业详情",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/gardenDel",
          "style": {
            "navigationBarTitleText": "园区详情",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/businessDirectory",
          "style": {
            "navigationBarTitleText": "企业园区名录",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "strategyPdf",
          "style": {
            "navigationBarTitleText": "生成报告",
            "navigationBarBackgroundColor": "#dde6ff"
          }
        },
        {
          "path": "askIndex",
          "style": {
            "navigationBarTitleText": "哒达招商",
            "navigationBarBackgroundColor": "#dde6ff"
          }
        },
        {
          "path": "serviceInfo",
          "style": {
            "navigationBarTitleText": "哒达招商",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "mineOrder",
          "style": {
            "navigationBarTitleText": "哒达出海",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "serviceList",
          "style": {
            "navigationBarTitleText": "服务方",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/orderDetail",
          "style": {
            "navigationBarTitleText": "订单详情",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/servicerDetail",
          "style": {
            "navigationBarTitleText": "服务商详情",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "elementPage/addService",
          "style": {
            "navigationBarTitleText": "新增服务",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "agreement/index",
          "style": {
            "navigationBarTitleText": "新增服务",
            "navigationBarBackgroundColor": "#dde6ff",
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "color": "#3F4A59",
    "selectedColor": "#417FFF",
    "backgroundColor": "#FFFFFF",
    "list": [
      {
        "pagePath": "pages/repository/capacity",
        "text": "首页",
        "iconPath": "static/navigation/home.png",
        "selectedIconPath": "static/navigation/selected-home.png"
      },
      /* 			{
      				"pagePath": "pages/newMapEnterprise/index2",
      				"text": "产业360",
      				"iconPath": "static/navigation/360.png",
      				"selectedIconPath": "static/navigation/selected-360.png"
      			}, */
      {
        "pagePath": "pages/information/index",
        "text": "消息",
        "iconPath": "static/navigation/news.png",
        "selectedIconPath": "static/navigation/selected-news.png"
      },
      {
        "pagePath": "pages/user/index",
        "text": "我的",
        "iconPath": "static/navigation/my.png",
        "selectedIconPath": "static/navigation/selected-my.png"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  }
}