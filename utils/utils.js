export const formatDate = function(format, _date) {
	let date = new Date(+_date);
	var o = {
		'M+': date?.getMonth() + 1, //月份
		'd+': date.getDate(), //日
		'H+': date.getHours(), //小时
		'm+': date.getMinutes(), //分
		's+': date.getSeconds(), //秒
		'q+': Math.floor((date?.getMonth() + 3) / 3), //季度
		S: date.getMilliseconds() //毫秒
	};
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
	for (var k in o) {
		if (new RegExp('(' + k + ')').test(format)) {
			format = format.replace(
				RegExp.$1,
				RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
			);
		}
	}
	return format;
};
/**
 * 根据指定的键获取URL查询参数的值
 *
 * 此函数使用正则表达式来匹配URL查询字符串中的指定键的值它首先构造一个正则表达式，
 * 用于匹配查询字符串中的键值对然后使用这个正则表达式去匹配当前窗口位置的查询字符串部分
 * 如果找到匹配项，则使用decodeURI函数解码找到的值，以处理URL中的特殊字符
 * 如果没有找到匹配项，则返回空字符串
 *
 * @param {string} key - 要获取的查询参数的键名
 * @returns {string} - 查询参数的值，如果不存在则返回空字符串
 */
export const getQueryParam = function(key) {
	let url = window.location.href;
	// 获取查询字符串部分（即'?'后面的字符串）
	let queryStringArr = url.split('?');
	let queryString = '';
	if (queryStringArr.length > 1) {
		queryString = url.split('?')[1];
	}
	// 将查询字符串分割成键值对数组
	let paramsArray = queryString.split('&');
	// 创建一个对象来保存所有的键值对
	let paramsObj = {};
	paramsArray.forEach(function(param) {
		let pair = param.split('=');
		if (pair.length > 1) {
			paramsObj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
		}
	});
	// 返回指定key的值，如果没有找到则返回null
	return paramsObj.hasOwnProperty(key) ? paramsObj[key] : null;
};
export const listicon = [{
		name: '其他',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/0.png'
	},
	{
		name: '上市',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png'
	},
	{
		name: '独角兽',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/2.png'
	},
	{
		name: '专精特新',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png'
	},
	{
		name: '隐形冠军',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/4.png'
	},
	{
		name: '瞪羚',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/5.png'
	},
	{
		name: '创新',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/6.png'
	},
	{
		name: '技术先进',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/7.png'
	},
	{
		name: '科技',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/8.png'
	},
	{
		name: '雏鹰',
		icon: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/9.png'
	}
];

//判断是否完全为空
export const isEmptyValue = value => {
	if (typeof value === 'string') {
		return value.trim() === '';
	}
	if (typeof value === 'number') {
		return value === 0;
	}
	if (typeof value === 'boolean') {
		return value === false;
	}
	if (Array.isArray(value)) {
		return value.every(this.isEmptyValue);
	}
	if (value !== null && typeof value === 'object') {
		return Object.values(value).every(this.isEmptyValue);
	}
	return false;
};
export const isWithinLastWeek = dateString => {
	if (!dateString) {
		return false
	}
	const targetDate = new Date(dateString);
	const currentDate = new Date();
	const oneWeekAgo = new Date();
	oneWeekAgo.setDate(currentDate.getDate() - 7);
	return targetDate >= oneWeekAgo && targetDate <= currentDate;
};