const prefix = "/ai";
const url = {
	getUserKnowledgeRelation: prefix + "/userKnowledgeRelation/getUserKnowledgeRelation", //知识库列表
	firstLoginAdd: prefix + "/userKnowledgeRelation/firstLoginAdd", //首次登录选择知识库
	isPopFirstLoginChooseKnowledge: prefix + "/userKnowledgeRelation/isPopFirstLoginChooseKnowledge", //首次登录选择知识库
	login: "/sso/admin/login", //登录
	logout: "/sso/admin/logout", //登出
	getVerifyCode: prefix + "/verifyCode/getVerifyCode", //获取验证码
	userMessage: prefix + "/user/center/userMessage", //获取用户信息
	updateHeadImg: prefix + "/user/center/updateCompletion", //更新用户信息
	getUserKnowledgeRelationBestNewByLimit: "/ai/userKnowledgeRelation/getUserKnowledgeRelationBestNewByLimit", //获取当前用户最新的指定条数的关联知识记录列表
	knowledgeCenter: "/ai/knowledgeLibrary/knowledgeCenter", //知识库首页
	fetchAllCapabilities: "/ai/capabilityPool/fetchAllCapabilities", //知识库能力
	listChains: "/ai/goods/listChains", //获取产业知识库/综合知识库的套餐
	getIntroduction: "/ai/llm/getFirstChatIntroduce", // 获取首次进入产业小艾聊天页面的介绍内容
	listCoupons: "/ai/user/center/listCoupons", // 我的优惠券
	submitBilling: "/ai/billings/submitBilling", //提交订单
	queryBillingFromWx: "/ai/billings/queryBillingFromWx", //查询订单状态
	listPrices: "/ai/billings/calculateDiscountPrice", //获取套餐
	redeemKnowledge: "/ai/userKnowledgeRelation/redeemKnowledge", //用户兑换知识库
	generateUserInvitationCode: "/ai/invitation/generateUserInvitationCode", //生成邀请码
	listInvitedUser: "/ai/invitation/listInvitedUser", //邀请记录查询
	queryCapabilityListWithoutPermission: "/ai/capabilityPool/queryCapabilityListWithoutPermission", //能力库列表
	tableDownload: "/ai/llm/tableDownload", //excel下载
	pdfDownload: "/pdf/render-url", //pdfDownload
	downloadAll: prefix + "/oss/policy/pdf", //pdfDownload
	administrativeDivision: "/admin/administrativeDivision/list", //获取行政区划
	completeUserInfo: "/ai/user/center/completeUserInfo", //信息补充
	deleteRedisCache: "/ai/user/center/deleteRedisCache", //清空产业小助手缓存
	isPopUserInfoCompletion: "/ai/userKnowledgeRelation/isPopUserInfoCompletion", //是否需要补全信息
	submitEntrust: "/ai/demo/submitEntrust", //新建委托书
	enterpriseClasses: "/ai/demo/get/enterpriseClasses", //获取企业课程类型和列表
	submitContact: "/ai/demo/submitContact", //企业服务课程提交联系方式
	productData: "/ai/demo/get/productData", //获取产品数据列表
	submitProductContact: "/ai/demo/submitProductContact", //数据产品、知识库提交
	addSubmit: "/ai/entrust/client/action/submit", //新建招商委托单
	billingList: "/ai/entrust/client/mine/billing", //委托人列表
	workerList: "/ai/entrust/worker/all/list", //委托大厅
	claim: "/ai/entrust/worker/claim", //接单
	claimList: "/ai/entrust/worker/mine/list", //委托人接单列表
	courseList: "/ai/enterprise/class/type/list", //课程列表
	courseItem: "/ai/enterprise/class/list", //课程详情查询
	Mycourses: "/ai/enterprise/class/mine/list", //已购买的课程
	productList: "/ai/data/product/list", //数据产品列表
	downloadUrl: "/ai/upload/downloadUrl", //下载pdf
	typeList: "/ai/enterprise/class/type/list", //企业课程列表
	classlist: "/ai/enterprise/class/list", //根据类型查询企业课程列表
	mineList: "/ai/enterprise/class/mine/list", //已购买课程列表
	billingCount: "/ai/entrust/client/mine/billingCount", //委托人数量
	searchenterprise: "/ai/entrust/client/search/enterprise", //企业类型
	billingSubmit: "/ai/payment/billing/submit", //购买
	classdetail: "/ai/enterprise/class/detail", //企业课程详情
	syncUnion: "/ai/payment/syncUnion", //小程序 同步union
	ownList: "/sso/admin/institution/ownList", //获取机构列表
	renewAPI: "/sso/admin/institution/renew", //变更机构
	closeAPI: "/ai/entrust/client/close", //退单
	finishAPI: "/ai/entrust/client/finish", //结单
	complainAPI: "/ai/entrust/client/complain", //委托人申诉
	appealAPI: "/ai/entrust/worker/appeal", //结单人申诉
	closeDelAPI: "/ai/entrust/client/detail", //委托人委托单详情
	workerdetailAPI: "/ai/entrust/worker/detail", //产业顾问委托单详情
	countAPI: "/ai/entrust/worker/mine/count", //顾问数量
	workerfollowAPI: "/ai/entrust/worker/follow", //跟进
	getUserBalance: "/ai/user/account/getUserBalance", //账户余额
	balanceFlowList: "/ai/user/account/balanceFlowList", //提现列表
	withdrawalAPI: "/ai/user/account/withdrawal", //用户提现
	visit: "/ai/common/visit", //访问统计
	getVipList: "/ai/user/center/vipListOwn", //用户vip详情
	listAllPurchaseChain: "/ai/knowledgeLibrary/listAllPurchaseChain", //可购买的知识库
	calculateDiscountPriceList: "/ai/payment/calculateDiscountPrice", //vip支付获取价格列表
	pageByChainNode: "/ai/industryChain/enterpriseByChainNode", //根据节点查企业
	// industrychainList: '/ai/industryChain/chainList', //产业链首页GET
	// industrychainList: '/ai/industryChain/chainList', //产业链首页GET
	industrychainList: '/ai/industryChain/chainTreeList',
	industryChainNodetree: '/ai/industryChain/nodeTree', //产业链节点树
	listImportantEnterpriseLabel: '/ai/industryChain/importantEnterpriseLabel', //获取重点企业标签
	enterprisedetail: '/ai/industryChain/enterpriseDetail', //企业详情get
	informationList: '/miniapps/information/page', //资讯分页接口POST
	queryUserMenuResourceTree: "/sso/admin/roleResource/queryUserMenuResourceTree", //获取权限树
	queryResourceByType: "/sso/admin/roleResource/queryResourceByType", //获取按钮权限
	isPrompt: "/ai/user/center/isPrompt", //小助手是否展示红点
	paymentAPI: "/ai/entrust/client/payment", //委托单支付
	reminderAPI: "/ai/entrust/client/reminder", //委托人 催办
	commentAPI: "/ai/entrust/client/comment", // 委托人 评论
	clientReviewtAPI: "/ai/entrust/client/clientReview", //委托人审核结单
	getEnterpriseLabelAPI: '/ai/entrust/client/getEnterpriseLabel', //查询企业类型价格
	submitReviewAPI: "/ai/entrust/worker/submitReview", //顾问申请结单
	workerapplyAPI: "/ai/invitationRecord/worker/apply", //C端顾问申请，传递邀请码
	applyNumAPI: "/ai/invitationRecord/worker/applyNum", //C端顾问未处理申请数量， 消息红点用
	geneCodeAPI: "/ai/invitationRecord/worker/generateUserInvitationCode", //C端顾问分销生成二维码
	userTitleAPI: "/ai/invitationRecord/worker/userTitle", //C端顾问称号，可能有多个
	getTeamByInvitationCodeAPI: "/ai/invitationRecord/worker/getTeamByInvitationCode", //C端根据邀请码查询团队
	enumListAPI: "/ai/entrust/client/enumList", //枚举类型 企业类型 融资情况
	demandListAPI: "/ai/entrust/client/demand/list", //委托人需求列表
	demandcancelAPI: "/ai/entrust/client/demand/cancel", //委托人 撤销需求单
	recommendlistAPI: "/ai/entrust/client/recommend/list", //委托人 查看需求的反馈列表
	recommenddeleteAPI: "/ai/entrust/client/recommend/delete", //委托人 删除反馈
	workerdemandListAPI: "/ai/entrust/worker/demand/list", //顾问 需求大厅
	workerrecommendAPI: "/ai/entrust/worker/recommend", //顾问 反馈
	workerrecommendListAPI: "/ai/entrust/worker/recommend/list", //顾问 需求反馈列表
	demandmineAPI: "/ai/entrust/worker/demand/mine", //顾问 已反馈需求列表
	demandSubmit: "/ai/entrust/client/demand/submit", //委托人 新建需求
	commissionBreakdownAPI: "/ai/user/account/commissionBreakdown", //全部佣金明细
	threeDetailsAPI: "/ai/user/account/threeDetails", //近三条佣金明细
	workerParentAPI: "/ai/invitationRecord/worker/parent", //查询上级名称
	getHelperPromptInfoAPI: "/ai/user/center/getHelperPromptInfo", //小助手
	displayHelperPromptInfoAPI: "/ai/user/center/displayHelperPromptInfo", //红点
	homeAPI: "/ai/home/<USER>", //红点
	choosechainAPI: "/ai/industryChain/chooseChain", //产业链回显
	updatechoosechainAPI: "/ai/industryChain/updateChooseChain", //切换产业链
	messagePageAPI: "/ai/message/page", //消息
	receiveAPI: "/ai/message/receive", //读取消息
	newsAdminPage: "/ai/news/page", //分页查询资讯

	generateAPI: "/ai/oversea/report/generate", //生成报告
	generateDetailAPI: "/ai/oversea/report/detail", //报告详情
	reportPageAPI: "/ai/oversea/intelligence/report/getReportList", //报告分页查询
	overseasPermission: "/ai/capabilityPool/overseas/queryCapabilityListWithoutPermission", //能力库列表

	getCountryList: "/ai/oversea/countryList", // 获取国家名称以及国家ID（下拉框）
	getNavigation: "/ai/service/admin/catalog/list", // 出海智服获取一级导航信息
	getNavigationSubset: "/ai/service/admin/item/list", // 出海智服获取二级导航信息
	getTextContent: "/ai/service/market/detail", // 出海智服获取页面文本内容
	checkByKnowIdAPI: '/ai/industryChain/checkByKnowId', // 根据知识库id 判断是否有臻享版
	getSearchParamAPI: '/ai/industryChain/getSearchParam', //产业360 更多查询参数
	getKinshipParamAPI: '/ai/industryChain/getKinshipParam', //产业360 更多查询参数
	getSearchHistory: '/ai/industryChain/getSearchHistory', //产业360搜索记录
	deleteSearchHistory: '/ai/industryChain/deleteSearchHistory', //删除产业360搜索记录
	nodeTreeListAPI: '/ai/industryChain/nodeTreeList', //新的节点树
	updateStatusLoginAPI: '/sso/third/qrcode/wechat/updateStatus', //更新二维码状态、授权登录
	getStatusLoginAPI: '/sso/third/qrcode/wechat/getStatus', //获取二维码状态
	showPasswordAPI: '/sso/common/password/login', //是否展示账号密码阿登录
	messagereceiveAPI: '/ai/message/delete', //删除消息
	reportListAPI: '/ai/invest/report/enterprise/pageList', //一企一策列表
	reportGenerateAPI: '/ai/invest/report/generate', //一企一策生成
	getReportAPI: '/ai/llm/getReport', //一企一策
	getExampleById: '/ai/user/industry/report/getExampleById', //一企一策
	countryListAPI: '/ai/oversea/area/countryList', //哒达出海国家列表
	getDefaultCountryAPI: '/ai/oversea/area/getDefaultCountry', //已选择的国家
	updateChooseCountryAPI: '/ai/oversea/area/updateChooseCountry', //更新已选择的国家
	parkGetSearchParamAPI: '/ai/overseas/park/getSearchParam', //园区搜索参数
	enterpriseallChainAPI: '/ai/overseas/enterprise/allChain', //产业链列表
	getAdminRegionListAPI: '/ai/overseas/enterprise/getAdminRegionList', //地区
	enterprisegetSearchParamAPI: '/ai/overseas/enterprise/getSearchParam', //企业搜索参数
	getChainNodeTreeListAPI: '/ai/overseas/enterprise/getChainNodeTreeList', //哒达出海产业360树
	enterprisePageListAPI: '/ai/overseas/enterprise/enterprisePageList', //哒达出海产业360列表
	getEnterpiseDetailAPI: '/ai/overseas/enterprise/getEnterpriseDetail', //哒达出海产业360详情
	parkPageListAPI: '/ai/overseas/park/parkPageList', //园区列表
	getParkDetailAPI: '/ai/overseas/park/getParkDetail', //园区详情
	identityTypeAPI: '/ai/userKnowledgeRelation/identityType', //新版判断是否完善信息
	catalogListAPI: '/ai/service/admin/catalog/list', //查询服务类目列表

	// 哒达出海
	getOverseasOrderListAPI: '/ai/overseasSmartService/client/orderList', // 订单列表
	getOverseasServicerListAPI: '/ai/service/market/provider/list', // 服务的服务方列表
	incomeFlowListAPI: '/ai/user/account/incomeFlowList', //钱包入账记录

	//服务服务管理
	serverCatalogListAPI: "/ai/service/catalog/list", //查询一级
	marketPageAPI: "/ai/service/market/page", //查询二级
	serviceItemListAPI: "/ai/service/item/list", //查询类目下的服务项
	addMarketAPI: "/ai/service/market/add", ///新增服务市场数据
	marketModifyAPI: "/ai/service/market/modify", //编辑服务市场数据
	overseasSubmitApi: '/ai/overseasSmartService/client/submit', // 提交出海订单
	closeSeaAPI: "/ai/overseasSmartService/client/refund", //退单
	finishSeaAPI: "/ai/overseasSmartService/client/statement", //结单
	closeCloseSeaAPI: "/ai/overseasSmartService/client/reverseRefund", //撤销退单
	overseaIntelligenceCacheAPI: "/ai/oversea/intelligence/report/cache", // 保存智策表单缓存
	getOverseaIntelligenceCacheAPI: "/ai/oversea/intelligence/report/getCache", // 获取智策表单缓存
	overseaIntelligenceReportAPI: "/ai/oversea/intelligence/report/generate", // 提交智策表单
	newEnterpriseSearchAPI: "/ai/enterprise/search", // 新版搜索企业
	overseasNodeTreeAPI: "/ai/industryChain/overseasNodeTree", // 获取产业链节点
	providerOrderListAPI: "/ai/overseasSmartService/provider/orderList", //服务方列表
	providerProcessAPI: "/ai/overseasSmartService/provider/process", //服务方跟进
	providerStatementAPI: "/ai/overseasSmartService/provider/statement", //服务方申请结单
	overseasSmartServiceDelAPI: "/ai/overseasSmartService/orderDetail", //三端通用-智服订单详情

	providerDetailAPI: "/ai/service/market/provider/detail", //服务方详情
	clientOrderListAPI: "/ai/overseasSmartService/client/orderList", //出海方订单列表
	clientStatementAPI: "/ai/overseasSmartService/client/statement", //出海方申请结单
	clientRefundAPI: "/ai/overseasSmartService/client/refund", //出海方退单
	clientReverseRefund: "/ai/overseasSmartService/client/reverseRefund", //撤销退单
	clientComment: "/ai/overseasSmartService/client/comment", //评论
	clientAuditStatement: "/ai/overseasSmartService/client/auditStatement", //结单审核

	providerDetailAPI: "/ai/service/market/provider/detail", //服务方详情
	userCatalogListAPI: '/ai/service/market/provider/catalog/list', //用户服务类目列表
	getTemplateAPI: '/ai/oversea/intelligence/report/getTemplate', // 出海智策摸版
	getReportParamAPI: '/ai/oversea/intelligence/report/getReportParam', // 出海智策价格
	capabilityPoolDetailAPI: "/ai/capabilityPool/detail", //知识库详情
	enterpriseExportAPI: "/ai/invest/report/enterprise/export", //线索一键导出
	checkIsDefaultOrNullAPI: "/ai/user/center/checkIsDefaultOrNull", //是否可编辑招商属地
	paymentApply: "/ai/entrust/client/payment/apply", //申请机构支付
	paymentApplyListAPI: "/ai/entrust/client/payment/apply/list", //申请列表
	paymentdetailAPI: "/ai/entrust/client/payment/apply/detail", //申请详情
	collectionAPI: "/ai/investment/enterprise/collection", //收藏/取消收藏
	addEnterpriseAPI: "/ai/investment/enterprise/addEnterprise", //纳入招商意向
	addWeeklyReportAPI: "/ai/weeklyReport/add", //周报新增
	weeklyReportListAPI: "/ai/weeklyReport/list", //周报列表
	weeklyReportDelAPI: "/ai/weeklyReport/detail", //周报详情
	weeklyReportDeleteAPI: "/ai/weeklyReport/delete", //周报删除
	weeklyReportWeeklyStatsAPI: "/ai/weeklyReport/weeklyStats", //周报统计
	// 招商管理
	getSearchParamAPI_investment: "/ai/investment/enterprise/getSearchParam", // 招商搜索参数
	addEnterpriseAPI_investment: "/ai/investment/enterprise/addEnterprise", // 新增纳入意向企业
	enterpriseListAPI_investment: "/ai/investment/enterprise/pageList", // 意向企业列表
	removeEnterpriseAPI_investment: "/ai/investment/enterprise/remove", // 删除意向企业
	exportEnterpriseListAPI_investment: "/ai/investment/enterprise/export", // 导出意向企业列表
	checkApplyAPI_entrust: "/ai/entrust/client/payment/apply/check", // 审批校验
	auditApplyAPI_entrust: "/ai/entrust/client/payment/apply/audit", // 审批
	listApplyAPI_entrust: "/ai/entrust/client/payment/apply/audit/list", // 审批列表
	clientDetailAPI_entrust: "/ai/entrust/client/clue/detail", // 委托信息
	listAllChildUserAPI_investment: "/ai/investment/enterprise/listAllChildUser", // 下属人员列表(去除出海相关)
	changeAssignPersonAPI_investment: "/ai/investment/enterprise/changeAssignPerson", // 线索指派
	getLatestAssignClueAPI_investment: "/ai/investment/enterprise/getLatestAssignClue", // 获取最新一次指派记录
	getAssignPathAPI_investment: "/ai/investment/enterprise/getAssignPath", // 指派路径
	addFollowUpRecordAPI_investment: "/ai/investment/enterprise/addFollowUpRecord", //  跟进
	getFollowUpRecordsAPI_investment: "/ai/investment/enterprise/getFollowUpRecords", // 跟进记录
	staticInfoByPeriodAPI_investment: "/ai/investment/enterprise/staticInfoByPeriod", // 统计分析
	getInvestEnterpriseDetailAPI_investment: "/ai/investment/enterprise/getInvestEnterpriseDetail", // 招商管理企业详情
	collectionAPI_investment: "/ai/investment/enterprise/collection", // 收藏
	listCollectionAPI_investment: "/ai/investment/enterprise/collection/list", // 收藏列表
	homeDataModuleAPI: "/ai/home/<USER>", //首页数据模块
	enterpriseEventListAPI: "/ai/industryChain/enterpriseEventList", //企业动态
	ancestorListAPI: "/ai/ancestor/list", //乡贤企业
	countByDivisionAPI: "/ai/ancestor/countByDivision", //乡贤企业高管籍贯筛选
}
export default url;