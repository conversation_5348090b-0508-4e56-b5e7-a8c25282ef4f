import url from './url.js';
//export let baseUrl = "https://pangustg.idicc.cn";
export let baseUrl = uni.getStorageSync("portUrl") || "https://pangutest.idicc.cn";
const request = opt => {
	return new Promise((resolve, reject) => {
		if (opt.loading !== 'no') {
			uni.showLoading({
				title: '加载中'
			});
		}
		uni.request({
			url: opt.url,
			data: opt.data ? opt.data : {},
			method: opt.method ? opt.method : 'GET',
			header: {
				appId: 12,
				token: opt.token != undefined ? opt.token : uni.getStorageSync('token')
				//'Content-Type': 'application/json'
			},
			success: res => {
				uni.hideLoading();
				if (opt.url.indexOf('/common/password/login') !== -1) {
					resolve(res.data);
					return;
				}
				if (opt.url.indexOf('/entrust/client/action/submit') !== -1) {
					resolve(res.data);
					return;
				}
				if (opt.url.indexOf('/ai/investment/enterprise/addEnterprise') !== -1) {
					resolve(res.data);
					return;
				}
				if (res.statusCode == 200) {
					if (opt.url.indexOf('tableDownload') === -1) {
						if (res.data.code == 'SUCCESS' || res.data.status == 0) {
							resolve(res.data);
						} else {
							if (opt.error !== 'no') {
								if (res.data?.code != 41005) {
									uni.showToast({
										title: res.data.msg || '网络异常',
										icon: 'none',
										duration: 2000
									});
								}
							}
							reject(res.data);
						}
					} else {
						resolve(res.data);
					}
				} else if (res.statusCode == 401) {
					uni.removeStorage({
						key: 'token'
					});
					uni.removeStorage({
						key: 'invitationCode'
					});
					uni.removeStorage({
						key: 'userId'
					});
					uni.removeStorage({
						key: 'orgCode'
					});
					uni.removeStorage({
						key: 'orgName'
					});
					uni.removeStorage({
						key: 'isDefault'
					});
					uni.removeStorage({
						key: 'userIdentityType'
					});
					wx.setStorageSync('identity', 1);
					uni.showToast({
						title: res.data.msg || '未登录或已超时',
						icon: 'none'
					});
					setTimeout(() => {
						let isInApp = navigator?.userAgent.indexOf('airApp/1.0.0') > -1;
						if (isInApp) {
							window?.ReactNativeWebView?.postMessage(
								JSON.stringify({
									type: 'changePath',
									value: '',
									path: 'login'
								})
							);
						} else {
							uni.redirectTo({
								url: '/pages/login/index'
							});
						}
					}, 1000);
				} else {
					uni.showModal({
						content: res.data.msg || '网络异常',
						showCancel: false,
						success: e => {
							uni.redirectTo({
								url: '/pages/login/index'
							});
						}
					});
				}
			},
			fail: err => {
				uni.hideLoading();
				uni.showModal({
					content: err.msg || '网络异常',
					showCancel: false
				});
				reject();
			}
		});
	});
};

export default function(Vue) {
	const handleApi = function() {
		let api = {};
		for (let key in url) {
			if (url.hasOwnProperty(key)) {
				api[key] = (opt = {}) => {
					if (url[key].substring(0, 4) === '/sso') {
						let ssoUrl = baseUrl.replace('/jh', '');
						opt.url = ssoUrl + url[key];
					} else {
						opt.url = baseUrl + url[key];
					}
					return request(opt);
				};
			}
		}
		return api;
	};
	// Vue.prototype.$api = window.$api = handleApi()
	wx.$api = handleApi();
	Vue.config.globalProperties.$api = handleApi();
}