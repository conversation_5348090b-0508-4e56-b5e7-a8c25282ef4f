<template>
  <view class="uni-tooltip">
    <slot></slot>
    <view v-if="content || $slots.content" class="uni-tooltip-popup" :style="initPlacement">
      <slot name="content">
        {{ content }}
      </slot>
    </view>
  </view>
</template>


<script>
/**
 * Tooltip 提示文字
 * @description 常用于展示鼠标 hover 时的提示信息。
 * @tutorial https://uniapp.dcloud.io/component/uniui/uni-tooltip
 * @property {String} content   弹出层显示的内容
 * @property {String}  placement出现位置, 目前支持：left right top bottom
 */
export default {
  name: "uni-tooltip",
  data() {
    return {

    };
  },
  methods: {
  },
  computed: {
    initPlacement() {
      let style = {};
      switch (this.placement) {
        case 'left':
          style = {
            top: '50%',
            transform: 'translateY(-50%)',
            right: '100%',
            "margin-right": '10rpx',
          }
          break;
        case 'right':
          style = {
            top: '50%',
            transform: 'translateY(-50%)',
            left: '100%',
            "margin-left": '10rpx',
          }
          break;
        case 'top':
          style = {
            bottom: '100%',
            transform: 'translateX(-50%)',
            left: '50%',
            "margin-bottom": '10rpx',
          }
          break;
        case 'bottom':
          style = {
            top: '100%',
            transform: 'translateX(-50%)',
            left: '50%',
            "margin-top": '10rpx',
          }
          break;
      }
      return style;
    }
  },
  props: {
    content: {
      type: String,
      default: ''
    },

    placement: {
      type: String,
      default: 'left'
    },
  }
}
</script>

<style>
.uni-tooltip {
  position: relative;
  cursor: pointer;
}

.uni-tooltip-popup {
  z-index: 1;
  display: none;
  position: absolute;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  text-align: center;
  min-width: 140px;
  line-height: 22px;
  padding: 10px;
  font-weight: normal;
  overflow: auto;
  white-space: pre-line;
  word-break: break-all;
}


.uni-tooltip:hover .uni-tooltip-popup {
  display: block;
}
</style>
